﻿using CRM.Models.Enums;
using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для ClosedOrderTemplate.xaml
    /// </summary>
    public partial class ClosedOrderTemplate : UserControl
    {
        public ClosedOrderTemplate(Order order)
        {
            InitializeComponent();
            Model = order;

            returnedItemFrame.Visibility = order.WasReturned ? Visibility.Visible : Visibility.Hidden;
            closedAtSpan.Text = order.ClosedAt?.ToString("dd.MM.yyyy HH:mm");

            switch (order.PaymentType)
            {
                case PaymentTypeEnum.Cash:
                    cashIcon.Visibility = Visibility.Visible;
                    break;
                case PaymentTypeEnum.Card:
                    cardIcon.Visibility = Visibility.Visible;
                    break;
                case PaymentTypeEnum.Mixed:
                    mixedIcon.Visibility = Visibility.Visible;
                    break;
                case PaymentTypeEnum.Bonuses:
                    bonusesIcon.Visibility = Visibility.Visible;
                    break;
            }
        }


        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(Order), typeof(ClosedOrderTemplate));
        public Order Model
        {
            get { return (Order)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        public event EventHandler<Order> Tapped;
        private void onTapped(object sender, MouseButtonEventArgs e)
        {
            Tapped?.Invoke(sender, Model);
        }
    }
}
