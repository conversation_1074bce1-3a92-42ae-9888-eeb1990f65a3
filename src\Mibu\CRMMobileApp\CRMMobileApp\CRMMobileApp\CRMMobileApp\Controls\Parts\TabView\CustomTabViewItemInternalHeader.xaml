﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Parts.TabView.CustomTabViewItemInternalHeader">
  <ContentView.Content>

        <Grid 
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <Frame 
                x:Name="unselectedStateFrame"
                 Padding="0"
                 HasShadow="False"
                 IsClippedToBounds="False"
                 HorizontalOptions="Fill"
                 VerticalOptions="Fill"
                 BackgroundColor="Transparent"
                 Background="Transparent">
                <Frame.GestureRecognizers>
                    <TapGestureRecognizer Tapped="onTapped"/>
                </Frame.GestureRecognizers>
                <Frame.Style>
                    <Style TargetType="Frame">
                        <Style.Triggers>
                            <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsSelected}" Value="False">
                                <Setter Property="IsVisible" Value="True"/>
                            </DataTrigger>
                            <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsSelected}" Value="True">
                                <Setter Property="IsVisible" Value="False"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Frame.Style>
            </Frame>

            <Frame 
               x:Name="selectedStateFrame"
               Padding="0"
               HasShadow="False"
               IsClippedToBounds="False"
               HorizontalOptions="Fill"
               VerticalOptions="Fill"
               BackgroundColor="Transparent"
               Background="Transparent">
                <Frame.GestureRecognizers>
                    <TapGestureRecognizer Tapped="onTapped"/>
                </Frame.GestureRecognizers>
                <Frame.Style>
                  <Style TargetType="Frame">
                      <Style.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsSelected}" Value="True">
                            <Setter Property="IsVisible" Value="True"/>
                        </DataTrigger>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsSelected}" Value="False">
                            <Setter Property="IsVisible" Value="False"/>
                        </DataTrigger>
                    </Style.Triggers>
                  </Style>
              </Frame.Style>
            </Frame>




            <RadioButton 
                x:Name="hiddenRb"
                IsChecked="{Binding Source={x:Reference this},Path=IsSelected, Mode=TwoWay}"
                InputTransparent="True"
                IsVisible="False" />

        </Grid>
    </ContentView.Content>
</ContentView>