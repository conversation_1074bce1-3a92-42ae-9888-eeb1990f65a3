﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Reports.SalesOnDutyReportRow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Reports"
             x:Name="this"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="0.5*"/>
            <ColumnDefinition Width="0.5*"/>
        </Grid.ColumnDefinitions>

        <TextBlock
            Grid.Column="0"
            Margin="20,3,0,0"
            VerticalAlignment="Center"
            HorizontalAlignment="Left"
            Foreground="{StaticResource text_gray}"
            FontFamily="TTFirsNeue-Regular"
            FontSize="16"     
            Text="{Binding ElementName=this,Path=Model.ProductTitle}"/>


        <TextBlock
            Grid.Column="1"
            Margin="0,3,0,0"
            VerticalAlignment="Center"
            HorizontalAlignment="Left"
            Foreground="{StaticResource text_gray}"
            FontFamily="TTFirsNeue-Regular"
            FontSize="16"    
            Text="{Binding ElementName=this,Path=Model.Sum}"/>

        <TextBlock
            Grid.Column="2"
            Margin="0,3,0,0"
            VerticalAlignment="Center"
            HorizontalAlignment="Left"
            Foreground="{StaticResource text_gray}"
            FontFamily="TTFirsNeue-Regular"
            FontSize="16"    
            Text="{Binding ElementName=this,Path=Model.Amount}"/>

        <Rectangle
            Margin="10,0,20,0"
            Grid.ColumnSpan="3"
            VerticalAlignment="Top"
            HorizontalAlignment="Stretch"
            Fill="{StaticResource light_grey}"
            Height="1"/>


    </Grid>
</UserControl>
