﻿using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.Orders
{
    /// <summary>
    /// Логика взаимодействия для ActiveOrdersPopup.xaml
    /// </summary>
    public partial class ActiveOrdersPopup : BaseWindow
    {
        public ActiveOrdersPopup()
        {
            InitializeComponent();
            Loaded += ActiveOrdersPopup_Loaded;
        }

        private async void ActiveOrdersPopup_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {  
            await OrdersHelper.GetActiveOrders();

            ordersStackLayout.Children.Clear();
            foreach (var order in OrdersHelper.ActiveOrders)
            {
                var control = new TableOrderTemplate(order)
                {
                    Height = 38,
                    Margin = new Thickness(0, 4, 0, 0)
                };
                control.ItemTapped += Control_ItemTapped;
                ordersStackLayout.Children.Add(control);
            }
        }
        private async void Control_ItemTapped(object sender, CRM.Models.Stores.Order e)
        {
            this.Close();

            OrdersHelper.SetActiveOrder(e);
            //  await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
        }



        private ICommand openNewOrder;
        public ICommand OpenNewOrder
        {
            get => openNewOrder ??= new RelayCommand(async obj =>
            {
                await OrdersHelper.OpenOrder();
                this.Close();
            });
        }
    }
}
