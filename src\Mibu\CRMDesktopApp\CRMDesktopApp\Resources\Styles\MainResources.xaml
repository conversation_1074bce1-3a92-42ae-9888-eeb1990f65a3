﻿
<ResourceDictionary
     xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
     xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <SolidColorBrush x:Key="blueColor" Color="#333B4E"></SolidColorBrush>
    <SolidColorBrush x:Key="grayBg"  Color="#F9F9F9"></SolidColorBrush>
    <SolidColorBrush x:Key="grayBlack" Color="#444444"></SolidColorBrush>

    <SolidColorBrush x:Key="textBlack" Color="#2C2C2C"></SolidColorBrush>
    <SolidColorBrush x:Key="textBlackLight" Color="#565656"></SolidColorBrush>
    <SolidColorBrush x:Key="textLightBlack" Color="#6B6B6B"></SolidColorBrush>
    <SolidColorBrush x:Key="textLight" Color="#e5e5e5"></SolidColorBrush>

    <SolidColorBrush x:Key="btn_bg_main" Color="#333B4E"></SolidColorBrush>

    <SolidColorBrush x:Key="blue_black" Color="#283651"></SolidColorBrush>


    <SolidColorBrush x:Key="color_red" Color="#E04435"></SolidColorBrush>
    <SolidColorBrush x:Key="color_red_dark" Color="#681F19"></SolidColorBrush>

    <SolidColorBrush x:Key="color_ligth_bg" Color="#B8B8B8"></SolidColorBrush>



    <SolidColorBrush x:Key="purple" Color="#7265FB"></SolidColorBrush>
    <SolidColorBrush x:Key="dark_purple" Color="#524E7D"></SolidColorBrush>
    <SolidColorBrush x:Key="green" Color="#26E27C"></SolidColorBrush>
    <SolidColorBrush x:Key="bg_purple" Color="#F6F6FB"></SolidColorBrush>
    <SolidColorBrush x:Key="text_gray" Color="#9795B1"></SolidColorBrush>
    <SolidColorBrush x:Key="icon_gray" Color="#AFADC5"></SolidColorBrush>
    <SolidColorBrush x:Key="light_grey" Color="#CBCAD8"></SolidColorBrush>
    <SolidColorBrush x:Key="text_green" Color="#23CF72"></SolidColorBrush>
    <SolidColorBrush x:Key="light_purple" Color="#F5F4FF"></SolidColorBrush>
    <LinearGradientBrush x:Key="purple_gradient" EndPoint="1,0">
        <GradientStop Color="#9A90FF"
                          Offset="0.1" />
        <GradientStop Color="#594DD2"
                          Offset="1.0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="green_gradient" EndPoint="1,0">
        <GradientStop Color="#7CE9AE"
                          Offset="0.1" />
        <GradientStop Color="#22BA68"
                          Offset="1.0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="orange_gradient" EndPoint="1,0">
        <GradientStop Color="#F5B276"
                          Offset="0.1" />
        <GradientStop Color="#DF8534"
                          Offset="1.0" />
    </LinearGradientBrush>

    <Style x:Key="lbItemContainerStyle" TargetType="{x:Type ListBoxItem}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <ContentPresenter />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>