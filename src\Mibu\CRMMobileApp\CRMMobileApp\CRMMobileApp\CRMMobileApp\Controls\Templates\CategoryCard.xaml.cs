﻿using CRM.Models.Network;
using CRMMoblieApiWrapper;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CategoryCard : ContentView
    {
        public CategoryCard()
        {
            InitializeComponent();
        }
        public CategoryCard(MenuCategoriesTreeItem category)
        {
            Model = category;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                if (string.IsNullOrEmpty(category.Category.ImgPath)
                || category.Category.ImgPath == MobileAPI.MAIN_HOST)
                {
                    try
                    {
                        var firstLetter = category.Category.Title.Substring(0, 1).ToUpper();
                        var secondLetter = category.Category.Title.Substring(1, 1).ToLower();
                        lettersLabel.Text = firstLetter + secondLetter;
                    }
                    catch { }
                }
            });
        }


        public static readonly BindableProperty ModelProperty =
              BindableProperty.Create(nameof(Model), typeof(MenuCategoriesTreeItem), typeof(CategoryCard));
        public MenuCategoriesTreeItem Model
        {
            get { return (MenuCategoriesTreeItem)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


        public event EventHandler<MenuCategoriesTreeItem> CardTapped;
        private void onCardTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                CardTapped?.Invoke(this, Model);
            });
        }
    }
}