﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.ClosedOrderTemplate">
    <ContentView.Content>
        <Frame
            BackgroundColor="Transparent"
            HasShadow="False"
            Padding="0"
            CornerRadius="0">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Frame.GestureRecognizers>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="100"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">

                    <StackLayout Orientation="Horizontal">
                        <Label 
                            FontSize="18"
                            VerticalOptions="Start"
                            HorizontalOptions="Center"
                            FontAttributes="Bold"
                            TextColor="{DynamicResource blueColor}">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="№"/>
                                        <Span Text="{Binding Source={x:Reference this},Path=Model.Id}"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>

                  
                        <Frame
                            x:Name="returnedItemFrame"
                            IsVisible="False"
                            CornerRadius="5"
                            HeightRequest="20"
                            WidthRequest="105"
                            HasShadow="False"
                            Padding="0"
                            VerticalOptions="Start"
                            Margin="0,3,0,0"
                            BackgroundColor="{DynamicResource blueColor}">
                            <Label 
                                Text="Возвращен"
                                FontSize="16"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                TextColor="White"/>
                        </Frame>
                        
                     
                    </StackLayout>
                    
                </Grid>
                
              
                
                <Label 
                    Grid.Column="1"
                    FontSize="18"
                    VerticalOptions="Start"
                    TextColor="{DynamicResource blueColor}"
                    HorizontalOptions="Center">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.Sum}"/>
                                <Span Text=" руб."/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
                <Label 
                    Grid.Column="2"
                    FontSize="16"
                    TextColor="{DynamicResource blueColor}"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.User.Name}"/>
                                <Span Text=" "/>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.User.Surname}"/>
                                <Span Text="   "/>
                                <Span x:Name="closedAtSpan" Text=""/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>


                <Image
                    Grid.Column="3"
                    x:Name="cashIcon"
                    IsVisible="False"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    WidthRequest="25"
                    HeightRequest="25"
                    Source="{OnPlatform Default=cash.png, WPF='pack://application:,,,/Images/cash.png'}"/>
                <Image
                    Grid.Column="3"
                    x:Name="cardIcon"
                    IsVisible="False"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    WidthRequest="25"
                    HeightRequest="25"
                    Source="{OnPlatform Default=card.png, WPF='pack://application:,,,/Images/card.png'}"/>
                <Image
                    Grid.Column="3"
                    x:Name="mixedIcon"
                    IsVisible="False"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    WidthRequest="43"
                    HeightRequest="25"
                    Source="{OnPlatform Default=mixed.png, WPF='pack://application:,,,/Images/mixed.png'}"/>
                <Image
                    Grid.Column="3"
                    x:Name="bonusesIcon"
                    IsVisible="False"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    WidthRequest="18"
                    HeightRequest="25"
                    Source="{OnPlatform Default=bonuses.png, WPF='pack://application:,,,/Images/bonuses.png'}"/>
            </Grid>
        </Frame>
  </ContentView.Content>
</ContentView>