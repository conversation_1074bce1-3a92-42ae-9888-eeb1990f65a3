﻿using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Popups;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.Parts
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Header : ContentView
    {
        public Header()
        {
            InitializeComponent();

            Render();
		}

        public void Render()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                avatarImg.Source = MobileAPI.GetFullFilePath(Auth.User.AvatarPath);
            });
        }

        private ICommand openUserMenu;
        public ICommand OpenUserMenu
        {
            get => openUserMenu ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new UserMenuPopup());
                });
            });
        }

        private ICommand openMenu;
        public ICommand OpenMenu
        {
            get => openMenu ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new MenuPopup());
                });    
            });
        }
    }
}