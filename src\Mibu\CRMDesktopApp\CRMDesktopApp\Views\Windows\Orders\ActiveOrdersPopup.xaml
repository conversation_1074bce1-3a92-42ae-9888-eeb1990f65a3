﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.Orders.ActiveOrdersPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.Orders" xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls"
    mc:Ignorable="d"
        Title="Открытые заказы" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="450" 
        Width="800">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="450"
        Height="350"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="0,70,0,0"
        Background="#FFFFFF"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="70"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <TextBlock
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    Text="Открытые заказы"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="20"/>

                <controls:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,20,20,0"/>
            </Grid>

            <Grid Grid.Row="1">
                <ScrollViewer>
                    <StackPanel
                        Margin="10,0,10,30"
                        x:Name="ordersStackLayout">



                    </StackPanel>
                </ScrollViewer>
            </Grid>


            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Button 
                    Grid.Column="1"
                    Command="{Binding ElementName=this,Path=OpenNewOrder}"
                    Style="{StaticResource purple_gradient_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,20,0"
                    Content="Открыть новый заказ"
                    Width="200"
                    Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
