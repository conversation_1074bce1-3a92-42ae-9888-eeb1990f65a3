﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Views.Pages;
using CRMDesktopApp.Views.Pages.Main;
using CRMDesktopApp.Views.Windows;
using CRMDesktopApp.Views.Windows.CountSelections;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {

        public MainWindow()
        {
            InitializeComponent();
            Load();
        }

        private async void Load()
        {
            await ApplicationState.LoadData();



            if (Auth.POSTerminal == null)
            {
                frame.Navigate(new LoginPage());
            }
            else
            {
                frame.Navigate(new MainPage());
            }
        }
    }
}
