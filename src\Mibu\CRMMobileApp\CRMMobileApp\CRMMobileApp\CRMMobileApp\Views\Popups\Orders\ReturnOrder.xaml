﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.ReturnOrder">
    <Grid>


        <Frame
              HasShadow="{OnPlatform WPF=True, Default=False}"
              BackgroundColor="White"
              Background="White"
              WidthRequest="670"
              HeightRequest="310"
              Padding="0"
              HorizontalOptions="Center"
              VerticalOptions="Start"
              Margin="0,40,0,0"
              CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>

                <Grid 
                      Grid.Row="0"
                      Margin="0,20,0,0">
                    <StackLayout Orientation="Horizontal">
                        <Image 
                              WidthRequest="40"
                              HeightRequest="40"/>
                        <Label
                              TextColor="{StaticResource dark_purple}"
                              FontFamily="TTFirsNeue-Regular"
                              FontSize="18"
                              FontAttributes="Bold"
                              Text="Возврат заказа"/>
                    </StackLayout>


                    <ImageButton 
                          Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                          Command="{Binding Source={x:Reference this},Path=Close}"
                          BackgroundColor="Transparent"
                          VerticalOptions="Start"
                          HorizontalOptions="End"
                          Padding="10"
                          WidthRequest="36"
                          HeightRequest="36"
                          Margin="0,5,10,0"/>
                </Grid>


                <Grid Grid.Row="1">
                    <controls:EntryOutlined
                          Margin="20,0,20,0"
                          Placeholder="Введите причину возврата"
                          IsMultiline="True"
                          Text="{Binding Source={x:Reference this},Path=Reason,Mode=TwoWay}"
                          Style="{StaticResource white_cornered_entry}"
                          HorizontalOptions="FillAndExpand"
                          HeightRequest="120"
                          VerticalPlaceholderAlignment="Start"
                          VerticalTextAlignment="Start"
                          PlaceholderMargin="10,10,0,0"
                          TextMargin="10,10,0,0"
                          VerticalOptions="Start"/>
                </Grid>



                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <StackLayout
                          Grid.Column="0"
                          Margin="20,0,0,0">
                        <Label
                          Margin="5,0,0,0"
                          HorizontalOptions="Start"
                          TextColor="{StaticResource dark_purple}"
                          FontFamily="TTFirsNeue-Regular"
                          FontSize="14"
                          Text="Напечатать чек"/>
                        <Switch 
                          HorizontalOptions="Start"
                          IsToggled="{Binding Source={x:Reference this},Path=PrintCheque,Mode=TwoWay}"/>
                    </StackLayout>


                    <Button 
                          Grid.Column="1"
                          Command="{Binding Source={x:Reference this},Path=ReturnCommand}"
                          Style="{StaticResource purple_gradient_btn}"
                          VerticalOptions="Center"
                          HorizontalOptions="End"
                          Margin="0,0,20,0"
                          Text="Вернуть"
                          WidthRequest="170"
                          HeightRequest="40"/>

                </Grid>
            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>