﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Models.Wrappers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для DiscountPopup.xaml
    /// </summary>
    public partial class DiscountPopup : BaseWindow
    {
        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }
        public DiscountPopup(Order order)
        {
            Order = order;
            InitializeComponent();

            Loaded += DiscountPopup_Loaded;

        }

        private async void DiscountPopup_Loaded(object sender, RoutedEventArgs e)
        {
            var discountSchedules = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetDiscountsSchedules(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            var discountss = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetDiscounts(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            foreach (var schedule in discountSchedules)
            {
                var discount = discountss.FirstOrDefault(o => o.Id == schedule.Id);
                if (discount != null)
                {
                    if (!discount.CheckDiscountSchedule(schedule))
                    {
                        discountss.Remove(discount);
                    }
                }
            }
            Discounts = new ObservableCollection<DiscountWrapper>();
            foreach (var discount in discountss)
            {
                Discounts.Add(new DiscountWrapper { Discount = discount });
            }

            if (!Discounts.Any())
            {
                noItemsTextBlock.Visibility = Visibility.Visible;
            }
        }



        private ObservableCollection<DiscountWrapper> discounts = new ObservableCollection<DiscountWrapper>();
        public ObservableCollection<DiscountWrapper> Discounts
        {
            get => discounts;
            set { discounts = value; OnPropertyChanged(nameof(Discounts)); }
        }

        private DiscountWrapper _selectedDiscount;
        public DiscountWrapper SelectedDiscount
        {
            get => _selectedDiscount;
            set
            { 
                _selectedDiscount = value; 
                OnPropertyChanged(nameof(SelectedDiscount));

                foreach (var discount in Discounts)
                {
                    discount.IsSelected = false;
                }

                if (value != null)
                {
                    value.IsSelected = true;
                }
            }
        }


        private ICommand applyDiscount;
        public ICommand ApplyDiscount
        {
            get => applyDiscount ??= new RelayCommand(async obj =>
            {
                if (_selectedDiscount is null)
                {
                    new OneButtonedPopup("Выберите скидку, чтобы продолжить", "Ошибка").ShowDialog();
                    return;
                }
                OrdersHelper.SetDiscount(_selectedDiscount.Discount);
                this.Close();
            });
        }
    }
}
