﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.StockListItem">
    <ContentView.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame CornerRadius="5"
           BorderColor="Transparent"
           BackgroundColor="Transparent"
           HasShadow="False"
           Padding="0">

            <Grid RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="40" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Grid
                    Grid.Row="0"
                    Padding="8,6">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="HeaderContent_OnTapped" />
                    </Grid.GestureRecognizers>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="6*"/>
                            <ColumnDefinition Width="4*"/>
                        </Grid.ColumnDefinitions>

                        <Label 
                            Grid.Column="0"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            LineBreakMode="TailTruncation"
                            Text="{Binding Source={x:Reference this},Path=Model.Title}"/>
                        <Label 
                            Grid.Column="1"
                            Margin="15,0,0,0"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="{Binding Source={x:Reference this},Path=Model.Sum}"/>


                    </Grid>

                    
                    <Image
                        Margin="0,17,20,0"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        WidthRequest="14"
                        HeightRequest="14">
                         <Image.Source>
                            <FileImageSource File="{Binding ExpandIcon}" />
                        </Image.Source>
                    </Image>

                </Grid>


                <!-- Content View -->
                <Frame 
                    Margin="20,0,20,0"
                    Padding="10,10,10,20"
                    CornerRadius="5"
                    Background="#F6F6FB"
                    Grid.Row="1"
                    HasShadow="False"
                    x:Name="BodyContentView"
                    IsVisible="False" >

                    <StackLayout x:Name="rowsStackLayout">
                        <Grid HeightRequest="50">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="6*"/>
                                <ColumnDefinition Width="4*"/>
                            </Grid.ColumnDefinitions>

                            <Label 
                                 Grid.Column="0"
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="14"
                                 TextColor="{x:StaticResource dark_purple}"
                                 VerticalOptions="Center"
                                 Text="Товар"/>
                            <Label 
                                 Grid.Column="1"
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="14"
                                 TextColor="{x:StaticResource dark_purple}"
                                 VerticalOptions="Center"
                                 Text="Кол-во"/>
                        </Grid>

                        

                    </StackLayout>
                </Frame>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>