﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.OneButtonedPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        mc:Ignorable="d"
        Title="{Binding ElementName=this,Path=Caption}" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="300"
        Height="200">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="290"
        Height="195"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        Background="White"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <StackPanel>
            <TextBlock 
                FontSize="20"
                FontWeight="Bold"
                HorizontalAlignment="Center"
                Margin="0,12,0,0"
                TextAlignment="Center"
                Text="{Binding ElementName=this,Path=Caption}"/>
            <TextBlock 
                FontSize="14"
                HorizontalAlignment="Center"
                Margin="15,28,15,0"
                TextAlignment="Center"
                TextWrapping="Wrap"
                Text="{Binding ElementName=this,Path=Message}"/>
            <Button 
                Command="{Binding ElementName=this,Path=CloseWindow}"
                Content="Ок"
                Height="35"
                Margin="70,28,70,8"
                Style="{StaticResource blue_cornered_filled_btn}"/>
        </StackPanel>
    </Border>
</abstactions:BaseWindow>
