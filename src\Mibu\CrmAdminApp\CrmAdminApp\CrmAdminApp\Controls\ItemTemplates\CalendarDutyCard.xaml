﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.CalendarDutyCard">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame
            CornerRadius="15"
            Padding="0"
            HasShadow="False"
            BackgroundColor="{x:StaticResource blue_black}">
            <StackLayout
                Margin="20,10,0,0"
                Spacing="1">
                <Label 
                    FontSize="{OnPlatform Android=14,iOS=10}"
                    TextColor="White"
                    Text="Смена 1"/>
                <Label   
                    Margin="0,3,0,0"
                    FontAttributes="Bold"
                    FontSize="{OnPlatform Android=16,iOS=12}"
                    TextColor="White"
                    Text="Оператор"/>
                <Label   
                    FontAttributes="Bold"
                    FontSize="{OnPlatform Android=16,iOS=12}"
                    TextColor="White"
                    Text="Обычный"/>
                <StackLayout
                     VerticalOptions="Center"
                     Spacing="3"
                     Orientation="Horizontal">
                    <Image
                        Source="clock.png"
                        HeightRequest="25"
                        WidthRequest="25"/>
                    <Label 
                        FontSize="{OnPlatform Android=20,iOS=15}"
                        TextColor="White"
                        Text=""/>
                </StackLayout>
                <Button
                    Text="Выбрать"
                    Background="White"
                    FontSize="{OnPlatform Android=16,iOS=12}"
                    TextColor="{x:StaticResource blue_black}"
                    HeightRequest="35"
                    CornerRadius="15"
                    Margin="0,0,25,0"
                    Style="{x:StaticResource blue_cornered_filled_btn}"/>
            </StackLayout>
        </Frame>
  </ContentView.Content>
</ContentView>