﻿using CrmAdminApp.iOS.UI.Effects;
using Foundation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UIKit;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;

[assembly: ExportEffect(typeof(ScrollBarColorEffect), nameof(CrmAdminApp.Effects.ScrollBarColorEffect))]
namespace CrmAdminApp.iOS.UI.Effects
{
    public class ScrollBarColorEffect : PlatformEffect
    {
        UIScrollView _view;
        UIColor _scrollBarColor;
        protected override void OnAttached()
        {
            Initialize();
        }

        protected override void OnDetached()
        {
            Uninitialize();
        }

        void Initialize()
        {
            _view = (UIScrollView)(Control ?? Container);
            _view.Scrolled += Container_Scrolled;

            var effect = (CrmAdminApp.Effects.ScrollBarColorEffect)Element.Effects.FirstOrDefault(e => e is CrmAdminApp.Effects.ScrollBarColorEffect);
            if (effect != null)
            {
                _scrollBarColor = effect.ScrollBarColor.ToUIColor();
            }
        }

        void Uninitialize()
        {
            _view.Scrolled -= Container_Scrolled;
        }

        private void Container_Scrolled(object sender, System.EventArgs e)
        {
            var subViews = _view.Subviews.ToList();
            var verticalIndicator = subViews.LastOrDefault();

            if (verticalIndicator != null)
            {
                verticalIndicator.BackgroundColor = _scrollBarColor;
            }
        }
    }
}