﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.AboutSoftwarePopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="О программе"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="590"
        Height="210">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Background="{DynamicResource bg_purple}"
        Margin="5,0,5,0"
        CornerRadius="15"
        Width="580"
        BorderThickness="1"
        BorderBrush="LightGray"
        Height="205">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3.5*"/>
                <ColumnDefinition Width="6.5*"/>
            </Grid.ColumnDefinitions>

            <Grid.RowDefinitions>
                <RowDefinition Height="7*"/>
                <RowDefinition Height="3*"/>
            </Grid.RowDefinitions>

            <Grid>
                <Image
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Source="pack://application:,,,/Resources/Images/logo.png"
                    Height="120"
                    Width="120"/>
            </Grid>

            <Grid Grid.Column="1">

                <StackPanel 
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center">
                    <TextBlock
                        Foreground="{StaticResource text_gray}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="10"
                        Text="Домен:"/>
                    <TextBlock
                        x:Name="domainTextBlock"
                        Foreground="{StaticResource text_gray}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="16"
                        Text="XXXXXXXXXXXX"/>
                    <TextBlock
                        Foreground="{StaticResource text_gray}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="10"
                        Text="Адрес:"/>
                    <TextBlock 
                         x:Name="addressTextBlock"
                         Foreground="{StaticResource text_gray}"
                         FontFamily="{StaticResource TTFirsNeue-Regular}"
                         FontSize="16"
                         Text="XXXXXXXXXXXX"/>
                </StackPanel>

                <customcontrols:ImageButton 
                     Command="{Binding ElementName=this,Path=CloseWindow}"
                     Margin="0,25,25,0"
                     HorizontalAlignment="Right"
                     VerticalAlignment="Top"
                     Width="18"
                     Height="18"
                     Source="pack://application:,,,/Resources/Images/close_main.png"/>


            </Grid>

            <Grid
                Grid.ColumnSpan="2"
                Grid.Row="1">

                <StackPanel 
                    Margin="60,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <TextBlock
                        Foreground="{StaticResource text_gray}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="12"
                        VerticalAlignment="Center"
                        Text="Телефон поддержки:"/>
                    <TextBlock 
                         Margin="20,0,0,0"
                         Foreground="{StaticResource text_gray}"
                         FontFamily="{StaticResource TTFirsNeue-Regular}"
                         VerticalAlignment="Center"
                         FontSize="16"
                         Text="8 800 746 30 31"/>
                </StackPanel>

                <Image
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Margin="0,0,50,0"
                    Source="pack://application:,,,/Resources/Images/availableOnPlayMarket.png"
                    Height="30"
                    Width="100"/>
            </Grid>



        </Grid>
    </Border>
</abstactions:BaseWindow>
