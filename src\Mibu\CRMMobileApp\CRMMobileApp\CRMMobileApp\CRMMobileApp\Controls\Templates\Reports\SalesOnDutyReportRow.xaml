﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             MinimumHeightRequest="50"
             x:Class="CRMMobileApp.Controls.Templates.Tables.SalesOnDutyReportRow">
    <ContentView.Content>
      <StackLayout>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="0.5*"/>
                    <ColumnDefinition Width="0.5*"/>
                </Grid.ColumnDefinitions>

                <Label
                    Grid.Column="0"
                    Margin="10,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    TextColor="{StaticResource text_gray}"
                    FontFamily="TTFirsNeue-Regular"
                    MaxLines="2"
                    LineBreakMode="TailTruncation"
                    FontSize="14"     
                    Text="{Binding Source={x:Reference this},Path=Model.ProductTitle}"/>


                <Label
                    Grid.Column="1"
                    Margin="15,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    TextColor="{StaticResource text_gray}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"    
                    Text="{Binding Source={x:Reference this},Path=Model.Sum}"/>

                <Label
                    Grid.Column="2"
                    Margin="15,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    TextColor="{StaticResource text_gray}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"    
                    Text="{Binding Source={x:Reference this},Path=Model.Amount}"/>
            </Grid>




            <BoxView
                Opacity="0.3"
                Margin="10,5,10,0"
                BackgroundColor="{StaticResource text_gray}"
                HeightRequest="0.5"
                HorizontalOptions="Fill"
                VerticalOptions="Start"/>

        </StackLayout>
  </ContentView.Content>
</ContentView>