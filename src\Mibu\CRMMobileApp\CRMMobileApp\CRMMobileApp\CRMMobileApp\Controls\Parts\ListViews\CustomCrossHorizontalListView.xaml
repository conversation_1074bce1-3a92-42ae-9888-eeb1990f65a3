﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:effects="http://sharpnado.com" 
             xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Parts.ListViews.CustomCrossHorizontalListView">
  <ContentView.Content>
        <Grid>

            <effects:HorizontalListView
                x:Name="forAndroidIOSlistView"
                IsVisible="{OnPlatform Android=True, iOS=True, Default=False}"
                ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource, Mode=OneWay}"
                ItemTemplate="{Binding Source={x:Reference this},Path=ItemTemplateForMobile}"
                TapCommand="{Binding Source={x:Reference this},Path=TapCommand}"
                ItemWidth="{Binding Source={x:Reference this},Path=ItemWidth}"
                ItemHeight="{Binding Source={x:Reference this},Path=ItemHeight}"
                ItemSpacing="{Binding Source={x:Reference this},Path=ItemSpacing}"
                Background="{Binding Source={x:Reference this},Path=Background, Mode=OneWay}"
                BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor, Mode=OneWay}"/>



            <listviews:HorizontalListViewForWPF
                x:Name="forWPFlistView"
                ItemSelected="onWpfListViewItemSelected"
                IsVisible="{OnPlatform WPF=True, Default=False}"
                HorizontalScrollBarVisibility="Never"
                VerticalScrollBarVisibility="Never"
                RowHeight="{Binding Source={x:Reference this},Path=ItemHeight}"
                ItemWidth="{Binding Source={x:Reference this},Path=ItemWidth}"
                ItemHeight="{Binding Source={x:Reference this},Path=ItemHeight}"
                ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource, Mode=OneWay}"
                ItemTemplate="{Binding Source={x:Reference this},Path=ItemTemplateForWPF}"
                Background="{Binding Source={x:Reference this},Path=Background, Mode=OneWay}"
                BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor, Mode=OneWay}"/>

        </Grid>
    </ContentView.Content>
</ContentView>