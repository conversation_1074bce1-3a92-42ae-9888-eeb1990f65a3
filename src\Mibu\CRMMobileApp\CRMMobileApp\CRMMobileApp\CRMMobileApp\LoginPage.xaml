﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:BasePage  
             xmlns:abstractions="clr-namespace:SharedGamificationBarista.Abstractions;assembly=SharedGamificationBarista"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             xmlns:controls1="clr-namespace:CRMMobileApp.Controls"
             x:Name="ThisPage"
             x:Class="CRMMobileApp.LoginPage"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             xmlns:crmMobileApp="clr-namespace:CRMMobileApp;assembly=CRMMobileApp"
             ios:Page.UseSafeArea="true"
             NavigationPage.HasNavigationBar="False"
             x:DataType="crmMobileApp:LoginPage"
             BackgroundColor="#236D86">
    <Grid BackgroundColor="{StaticResource bg_purple}">
        
        <Image
            Aspect="Fill"
            HorizontalOptions="Fill"
            VerticalOptions="Fill"
            Source="{OnPlatform Default=main_decor.png, WPF='pack://application:,,,/Images/main_decor.png'}"/>

        <!--<ImageButton 
            HorizontalOptions="Start"
            VerticalOptions="Start"
            Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"
            WidthRequest="10"
            HeightRequest="20"
            BackgroundColor="Transparent"
            Margin="30,40,0,0"/>-->

        <StackLayout   
            Spacing="1"
            x:Name="authStackLayout"
            Margin="0,-20,0,0"
            VerticalOptions="Center"
            HorizontalOptions="Center">
            <Image
                HorizontalOptions="Center"
                VerticalOptions="Start"
                Source="{OnPlatform Default=logo.png, WPF='pack://application:,,,/Images/logo.png'}"
                HeightRequest="120"
                WidthRequest="120"/>
            <Label
                FontSize="20"
                FontFamily="TTFirsNeue-Regular"
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                Text="Введите ваши данные"/>
            <Label
                FontSize="20"
                FontFamily="TTFirsNeue-Regular"
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                Text="для авторизации"/>

            <StackLayout
                Margin="0,25,0,0"
                WidthRequest="280"
                VerticalOptions="StartAndExpand"
                HorizontalOptions="Center" >

                <custom:AndroidStyleEntry 
                    TextFontSize="{OnPlatform Android=17,iOS=14}"
                    PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                    HeightRequest="{OnPlatform Android=40,iOS=32}"
                    PlaceholderMargin="{OnPlatform Android='0,6,0,0',iOS='0,10,0,0'}"  
                    Text="{Binding Source={x:Reference ThisPage},Path=Domain,Mode=TwoWay}"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Домен"/>
                <custom:AndroidStyleEntry  
                    TextFontSize="{OnPlatform Android=17,iOS=14}"
                    PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                    HeightRequest="{OnPlatform Android=40,iOS=32}"
                    PlaceholderMargin="{OnPlatform Android='0,6,0,0',iOS='0,10,0,0'}"            
                    Text="{Binding Source={x:Reference ThisPage},Path=Username,Mode=TwoWay}"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Логин"/>
                <custom:AndroidStyleEntry  
                    TextFontSize="{OnPlatform Android=17,iOS=14}"
                    PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                    HeightRequest="{OnPlatform Android=40,iOS=32}"
                    PlaceholderMargin="{OnPlatform Android='0,6,0,0',iOS='0,10,0,0'}"  
                    IsPassword="True"
                    Text="{Binding Source={x:Reference ThisPage},Path=Password,Mode=TwoWay}"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Пароль"/>
                
             

                <Button
                        StyleId="Login"
                       x:Name="loginBtn"
                       Command="{Binding Source={x:Reference ThisPage},Path=Auth}"
                       CornerRadius="20"
                       Margin="0,40,0,0"
                       HorizontalOptions="Fill"
                       VerticalOptions="Start"
                       TextColor="White"
                       Text="Войти"
                       FontSize="20"
                       TextTransform="None"
                       Background="{StaticResource purple_gradient}"
                       MinimumHeightRequest="50"
                       MinimumWidthRequest="100"
                       HeightRequest="50"/>

                <Label
                    x:Name="errorMessageLabel"
                    FontSize="14"
                    Margin="0,20,0,0"
                    VerticalOptions="Start"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Center"
                    TextColor="Red"
                    Text=""/>

            </StackLayout>

        </StackLayout>

        <Button 
            Command="{Binding Source={x:Reference ThisPage},Path=OpenSupportPopup}"
            Style="{StaticResource transparent_btn}"
            Margin="0,0,0,5"
            Text="Помощь"
            VerticalOptions="End"
            HorizontalOptions="Center" />


        <!--<Grid IsVisible="False">

            <forms:CachedImage
                Aspect="Fill"
                Source="{OnPlatform Default=main_gradient.png, WPF='pack://application:,,,/Images/main_gradient.png'}"/>


            <StackLayout
                VerticalOptions="Center"
                HorizontalOptions="Center">

                <Label
                    FontSize="20"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Center"
                    TextColor="White"
                    Text="Устанавливается соединение с сервером..."/>


                <forms:CachedImage
                    x:Name="loadingWheel"
                    Aspect="Fill"
                    Margin="0,70,0,0"
                    HorizontalOptions="Center"
                    WidthRequest="90"
                    HeightRequest="90"
                    Source="{OnPlatform Default=loading_wheel.png, WPF='pack://application:,,,/Images/loading_wheel.png'}"/>

            </StackLayout>

        </Grid>-->


    </Grid>


</abstractions:BasePage>
