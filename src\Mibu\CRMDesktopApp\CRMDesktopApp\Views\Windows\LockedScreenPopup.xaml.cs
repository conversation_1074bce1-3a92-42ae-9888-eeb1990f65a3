﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для LockedScreenPopup.xaml
    /// </summary>
    public partial class LockedScreenPopup : BaseWindow
    {
         public LockedScreenPopup()
        {
            InitializeComponent();
            keyboard.ButtonTapped += Keyboard_ButtonTapped;
            Closing += LockedScreenPopup_Closing;
        }

   

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (e == "backspace" && Password.Length > 0)
                Password = Password.Remove(Password.Length - 1);
            else if (e != "backspace")
                Password += e;

        }

        private string password = string.Empty;
        public string Password
        {
            get => password;
            set
            {
                if (Password != "0000" && Password.Length == 4)
                {
                    Task.Run(async () =>
                    {
                        await Task.Delay(300);
                        Password = "";
                    });
                    new OneButtonedPopup("Неверный пинкод", "Mibu CRM").ShowDialog();
                }
                else if (Password == "0000")
                {
                    canClose = true;
                    this.Close();
                }
                password = value;
                OnPropertyChanged(nameof(Password));

            }
        }

        bool canClose = false;
        private void LockedScreenPopup_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            if(!canClose)
                e.Cancel = true;
        }

        private ICommand logout;
        public ICommand Logout
        {
            get => logout ??= new RelayCommand(async obj =>
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if (nav != null)
                {
                    nav.Navigate(new MainPage());
                }
                await Auth.LogoutUser();
                canClose = true;
                this.Close();
            });
        }
    }
}
