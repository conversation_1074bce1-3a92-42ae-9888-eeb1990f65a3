﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using Fiscalization;
using MarkupCreator.Converters;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class FiscalRegisterItemTemplate : ContentView
    {
        public Fiscalizer Driver { get; private set; }



        public static readonly BindableProperty RegistratorProperty =
            BindableProperty.Create(nameof(Registrator), typeof(FiscalRegistrator), typeof(FiscalRegisterItemTemplate));
        public FiscalRegistrator Registrator
        {
            get { return (FiscalRegistrator)GetValue(RegistratorProperty); }
            set { SetValue(RegistratorProperty, value); }
        }

        public FiscalRegisterItemTemplate(FiscalRegistrator registrator, Fiscalizer driver)
        {
            InitializeComponent();
            Registrator = registrator;
            Driver = driver;
        }




        public event EventHandler ItemTapped;
        private void onItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ItemTapped?.Invoke(this, EventArgs.Empty);
            });
        }
    }
}