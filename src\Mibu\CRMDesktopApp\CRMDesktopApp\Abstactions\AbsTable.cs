﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace CRMMobileApp.Abstractions
{
    public class AbsTable : UserControl
    {
        private Table table;
        public Table Table
        {
            get => table;
            set { table = value; OnPropertyChanged(nameof(Table)); }
        }
  
        public AbsTable() { }

        public AbsTable(Table table)
        {
            Table = table;

            this.HorizontalAlignment = System.Windows.HorizontalAlignment.Left;
            this.VerticalAlignment = System.Windows.VerticalAlignment.Top;
            this.Margin = new Thickness(Table.X, Table.Y, 0, 0);

            this.Width = Table.Width;
            this.Height = Table.Height;

            GetTableOrders();
        }

        protected virtual void SetStyle() { }


        public List<Order> TableOrders { get; protected set; } = new List<Order>();
        protected async void GetTableOrders()
        {

            await Task.Run(async () =>
            {
                TableOrders = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetTableCurrentOrders(ApplicationState.CurrentDomain,
                                                                                                 ApplicationState.CurrentStore.Id,
                                                                                                 ApplicationState.CurrentDuty.Id,
                                                                                                 Table.Id);
            });
            SetStyle();
        }
        public void AddOrder(Order order)
        {
            TableOrders.Add(order);
            SetStyle();
        }
        public void RemoveOrder(Order order)
        {
            TableOrders.Remove(order);
            SetStyle();
        }


        public event EventHandler<Table> TableTapped;
        public void onCardTapped(object sender, MouseButtonEventArgs e)
        {
            TableTapped?.Invoke(this, Table);
        }


        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }


    }
}
