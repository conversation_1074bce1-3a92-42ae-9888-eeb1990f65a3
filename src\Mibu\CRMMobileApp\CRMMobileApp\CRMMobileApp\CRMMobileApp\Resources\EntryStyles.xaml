﻿<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls">

    <Style TargetType="controls:EntryOutlined" x:Key="white_cornered_entry">
        <Setter Property="EntryBackground" Value="#F6F6FB"/>
        <Setter Property="PlaceholderColor" Value="#9795B1"/>
        <Setter Property="BorderColor" Value="Transparent"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="TextMargin" Value="15,0,0,0"/>
        <Setter Property="PlaceholderMargin" Value="15,0,0,0"/>
    </Style>

    <Style TargetType="controls:EntryOutlined" x:Key="gray_bordered_entry">
        <Setter Property="EntryBackground" Value="White"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="BorderColor" Value="#333B4E"/>
        <!--<Setter Property="BorderWidth" Value="1"/>-->
    </Style>

    <Style TargetType="controls:EntryOutlined" x:Key="fullgray_bordered_entry">
        <Setter Property="EntryBackground" Value="White"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="BorderColor" Value="#e5e5e5"/>
        <Setter Property="PlaceholderColor" Value="#e5e5e5"/>
        <!--<Setter Property="BorderWidth" Value="1"/>-->
    </Style>

</ResourceDictionary>