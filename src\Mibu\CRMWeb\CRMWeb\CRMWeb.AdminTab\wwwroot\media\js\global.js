/* --- TEXT/PASSWORD LOGIN --- */
$( document ).ready(function() {
	$('#iconpassword').click(function(){
		var type = $('#password-input').attr('type') == "text" ? "password" : 'text',
			c = $(this).html() == "<a href=\"#\" class=\"passwordform-control\"></a>" ? "<a href=\"#\" class=\"passwordform-control2\"></a>" : "<a href=\"#\" class=\"passwordform-control\"></a>";
		$(this).html(c);
		$('#password-input').prop('type', type);
	});
});

/* --- TEXT/PASSWORD REGISTER --- */
$( document ).ready(function() {
	$('#iconpasswordreg1').click(function(){
		var type = $('#password-inputreg1').attr('type') == "text" ? "password" : 'text',
			c = $(this).html() == "<a href=\"#\" class=\"passwordform-control\"></a>" ? "<a href=\"#\" class=\"passwordform-control2\"></a>" : "<a href=\"#\" class=\"passwordform-control\"></a>";
		$(this).html(c);
		$('#password-inputreg1').prop('type', type);
	});
});

/* --- TEXT/PASSWORD REGISTER --- */
$( document ).ready(function() {
	$('#iconpasswordreg2').click(function(){
		var type = $('#password-inputreg2').attr('type') == "text" ? "password" : 'text',
			c = $(this).html() == "<a href=\"#\" class=\"passwordform-control\"></a>" ? "<a href=\"#\" class=\"passwordform-control2\"></a>" : "<a href=\"#\" class=\"passwordform-control\"></a>";
		$(this).html(c);
		$('#password-inputreg2').prop('type', type);
	});
});

/* --- DATE BIRTHDAY (REGISTER) --- */
$( function() {
	$( "#datebirthday" ).datepicker({
		dateFormat: 'd MM yy',
		changeMonth: true,
		changeYear: true
	});
} );

$( function() {
	$( "#changeday" ).datepicker({
		dateFormat: 'Смена #52 от dd.mm.yy',
		changeMonth: true,
		changeYear: true
	});
} );

$(document).ready(function(){
    $('.progressWrapper progress').each(function(){
      var prgsVal = $(this).data('value');
      var maxN = $(this).attr('max');
      var pop = prgsVal/maxN * 100
      
      $(this).prev().css('left', pop + '%').text(prgsVal);
      $(this).val(prgsVal);
    });
});

/* --- CIRCULAR PROGRESS --- */
$(document).ready(function(){
    $('.progressWrapper progress').each(function(){
      var prgsVal = $(this).data('value');
      var maxN = $(this).attr('max');
      var pop = prgsVal/maxN * 100
      
      $(this).prev().css('left', pop + '%').text(prgsVal);
      $(this).val(prgsVal);
    });
});

/* --- PERIOD INPUT --- */
$('#preiod_date').datepicker({
    range: 'period', // режим - выбор периода
    numberOfMonths: 1,
	changeMonth: true,
	changeYear: true,
	dateFormat: 'd MM yy',
    onSelect: function(dateText, inst, extensionRange) {
      // extensionRange - объект расширения
      $('#preiod_date').val(extensionRange.startDateText + ' - ' + extensionRange.endDateText);
    }
});

/* --- SEARCH TABLE --- */
$(document).ready(function(){
	$("#search").keyup(function(){
	_this = this;
	
	$.each($("#table tbody tr"), function() {
		if($(this).text().toLowerCase().indexOf($(_this).val().toLowerCase()) === -1) {
			$(this).hide();
		} else {
			$(this).show();                
		}});
	});
});