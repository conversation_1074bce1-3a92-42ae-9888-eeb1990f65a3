﻿using CRM.Database.Core;
using CRM.Models.Network.Delivery;
using CRM.Models.Stores.Settings.Tables;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaDeliveryController : AbsController
    {
        public BaristaDeliveryController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpGet, Route("GetDeliveryServices")]
        public List<DeliveryService> GetDeliveryServices(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                         .Include(o => o.DeliveryServices)
                         .FirstOrDefault(o => o.Id == networkId);

                    return network.DeliveryServices.Where(o => !o.IsDeleted && o.IsActive).ToList();
                }
                catch { return new List<DeliveryService>(); }
            }
        }
        [HttpGet, Route("GetDeliverymen")]
        public List<DeliveryMan> GetDeliverymen(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                         .Include(o => o.DeliveryMen)
                         .FirstOrDefault(o => o.Id == networkId);

                    return network.DeliveryMen.Where(o => !o.IsDeleted && o.IsActive).ToList();
                }
                catch { return new List<DeliveryMan>(); }
            }
        }



    }
}
