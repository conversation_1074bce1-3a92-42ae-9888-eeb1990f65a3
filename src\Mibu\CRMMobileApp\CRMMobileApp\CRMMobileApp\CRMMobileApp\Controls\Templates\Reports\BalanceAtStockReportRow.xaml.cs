﻿using CRM.Models.Reports.Mobile.BalancesAtStock;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class BalanceAtStockReportRow : ContentView
    {
        public BalanceAtStockReportRow()
        {
            InitializeComponent();
        }


        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(BalancesAtStockReportRow), typeof(BalanceAtStockReportRow));
        public BalancesAtStockReportRow Model
        {
            get { return (BalancesAtStockReportRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
       
    }
}