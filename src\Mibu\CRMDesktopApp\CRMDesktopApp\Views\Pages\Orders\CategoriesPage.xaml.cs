﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates;
using CRMDesktopApp.Views.Windows;
using CRMDesktopApp.Views.Windows.CountSelections;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Tech = CRM.Models.Network.ReferenceBooks.Menu.TechnicalCard;
using TechnicalCard = CRMDesktopApp.Controls.Templates.TechnicalCard;

namespace CRMDesktopApp.Views.Pages.Orders
{
    /// <summary>
    /// Логика взаимодействия для CategoriesPage.xaml
    /// </summary>
    public partial class CategoriesPage : BasePage
    {
        bool fromRoot = true;


        public MenuCategoriesTreeItem CurrentCategory { get; set; }
        public CategoriesPage()
        {
            InitializeComponent();
            Loaded += CategoriesPage_Loaded;
        }
        public CategoriesPage(MenuCategoriesTreeItem currentCategory)
        {
            InitializeComponent();

            CurrentCategory = currentCategory;

            fromRoot = false;
            Loaded += CategoriesPage_Loaded;
        }

        private async void CategoriesPage_Loaded(object sender, RoutedEventArgs e)
        {
            if (fromRoot)
            {
                var tree = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetMenuCategoriesTree(
                                                                                    ApplicationState.CurrentDomain,
                                                                                    ApplicationState.CurrentTradeNetwork.Id,
                                                                                    ApplicationState.CurrentStore.Id);
                allCategories = new ObservableCollection<MenuCategoriesTreeItem>(tree.Nodes.FirstOrDefault().ChildNodes);
                CurrentCategory = tree.Nodes.FirstOrDefault();
            }
            else
            {
                allCategories = new ObservableCollection<MenuCategoriesTreeItem>(CurrentCategory.ChildNodes);
            }

            var goods = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetProducts(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            allProducts = new ObservableCollection<Product>(goods.Where(o => o.MenuCategoryId == CurrentCategory.Category.Id));

            var techCards = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetTechnicalCards(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            allTechnicalCards = new ObservableCollection<Tech>(techCards.Where(o => o.MenuCategoryId == CurrentCategory.Category.Id));

            catTitle.Text = CurrentCategory.Category.Title;

            CreateControls();
        }

        private ObservableCollection<MenuCategoriesTreeItem> allCategories = new ObservableCollection<MenuCategoriesTreeItem>();
        private ObservableCollection<Product> allProducts = new ObservableCollection<Product>();
        private ObservableCollection<Tech> allTechnicalCards = new ObservableCollection<Tech>();

        private List<UserControl> allControls = new List<UserControl>();
        private void CreateControls()
        {
            multiList.Children.Clear();
            allControls.Clear();

            foreach (var card in allCategories)
            {
                var control = new CategoryCard(card)
                {
                    Margin = new Thickness(30, 30, 0, 0),
                    Width = 200,
                    Height = 200
                };
                control.CardTapped += CategoryTapped;
                multiList.Children.Add(control);
                allControls.Add(control);
            }
            foreach (var product in allProducts)
            {
                var control = new ProductCard(product)
                {
                    Margin = new Thickness(30, 30, 0, 0),
                    Width = 200,
                    Height = 200
                };
                control.CardTapped += ProductCardTapped;
                multiList.Children.Add(control);
                allControls.Add(control);
            }
            foreach (var product in allTechnicalCards)
            {
                var control = new TechnicalCard(product)
                {
                    Margin = new Thickness(30, 30, 0, 0),
                    Width = 200,
                    Height = 200
                };
                control.CardTapped += TechCardTapped;
                multiList.Children.Add(control);
                allControls.Add(control);
            }


            if (multiList.Children.Count == 0)
            {
                noItemsLabel.Visibility = Visibility.Visible;
            }
        }

        private async void CategoryTapped(object sender, MenuCategoriesTreeItem e)
        {
            NavigationService nav = NavigationService.GetNavigationService(this);
            if (nav != null)
            {
                nav.Navigate(new CategoriesPage(e));
            }
        }

        private async void TechCardTapped(object sender, Tech e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление").ShowDialog();
                return;
            }
            var availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForTechCard(ApplicationState.CurrentDomain,
                                                                                                                 ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                 e.Id);
            var orderItem = new OrderItem()
            {
                TechnicalCard = e,
                TechnicalCardId = e.Id
            };
            if (availableModifiers.Any())
            {
                var popup = new ModifiersPopup(orderItem, availableModifiers,false);
                popup.ShowDialog();

                while (!popup.IsCompleted)
                    await Task.Delay(200);

                if (!popup.IsSuccessfully) return;
            }


            if (e.IsWeightProduct)
            {
                new WeightTechCardOptionsPopup(orderItem).ShowDialog();
            }
            else
            {
                new TechCardOptionsPopup(orderItem).ShowDialog();
            }
        }

        private async void ProductCardTapped(object sender, Product e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление").ShowDialog();
                return;
            }
            var availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForProduct(ApplicationState.CurrentDomain,
                                                                                                                        ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                        e.Id);
            var orderItem = new OrderItem()
            {
                Product = e,
                ProductId = e.Id
            };

            if (availableModifiers.Any())
            {
                var popup = new ModifiersPopup(orderItem, availableModifiers, false);
                popup.ShowDialog();

                while (!popup.IsCompleted)
                    await Task.Delay(200);

                if (!popup.IsSuccessfully) return;
            }

            if (e.IsWeightProduct)
            {
                new WeightProductOptionsPopup(orderItem).ShowDialog();
            }
            else
            {
                new ProductOptionsPopup(orderItem).ShowDialog();
            }
        }







        private string searchQuery;
        public string SearchQuery
        {
            get => searchQuery;
            set
            {
                var filteredControls = new List<UserControl>();
                multiList.Children.Clear();

                if (string.IsNullOrEmpty(value))
                {
                    filteredControls = new List<UserControl>(allControls);
                }
                else
                {
                    var productControls = allControls.Where(o => o is ProductCard).Cast<ProductCard>().Where(o => o.Model.Title.Contains(value));
                    var techCardControls = allControls.Where(o => o is Controls.Templates.TechnicalCard).Cast<Controls.Templates.TechnicalCard>().Where(o => o.Model.Title.Contains(value));
                    var categories = allControls.Where(o => o is CategoryCard);

                    filteredControls.AddRange(categories);
                    filteredControls.AddRange(productControls);
                    filteredControls.AddRange(techCardControls);

                }

                foreach (var view in filteredControls)
                {
                    multiList.Children.Add(view);
                }

                if(multiList.Children.Count == 0)
                {
                    noItemsLabel.Visibility = Visibility.Visible;
                }

                searchQuery = value;
                OnPropertyChanged(nameof(SearchQuery));
            }
        }

    }
}
