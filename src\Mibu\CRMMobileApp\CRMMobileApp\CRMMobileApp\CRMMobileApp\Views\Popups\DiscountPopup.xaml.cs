﻿using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Models.Wrappers;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DiscountPopup : PopupPage
    {
        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }
        public DiscountPopup(Order order)
        {
            Order = order;
            InitializeComponent();
        }
        protected override async void OnAppearing()
        {
            base.OnAppearing();

            List<DiscountSchedule> discountSchedules = new List<DiscountSchedule>();
            List<Discount> discountss = new List<Discount>();

            await Task.Run(async () =>
            {
                discountSchedules = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetDiscountsSchedules(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                discountss = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetDiscounts(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            });

            await Device.InvokeOnMainThreadAsync(async () =>
            {
                foreach (var schedule in discountSchedules)
                {
                    var discount = discountss.FirstOrDefault(o => o.Id == schedule.Id);
                    if (discount != null)
                    {
                        if (!discount.CheckDiscountSchedule(schedule))
                        {
                            discountss.Remove(discount);
                        }
                    }
                }

                noItemsLabel.IsVisible = discountss.Count == 0;
                useBtn.IsVisible = discountss.Count > 0;

             

                var discounts = new List<DiscountWrapper>();
                foreach (var discount in discountss.OrderBy(o => o.Percent))
                {
                    var wrapper = new DiscountWrapper
                    {
                        Discount = discount,
                        IsSelected = Order.DiscountId == discount.Id
                    };

                    if (wrapper.IsSelected)
                    {
                        _selectedDiscount = wrapper;
                    }

                    discounts.Add(wrapper);
                }
                Discounts = new ObservableCollection<DiscountWrapper>(discounts);
                listView.ItemsSource = Discounts;
            });

        }

        private ObservableCollection<DiscountWrapper> discounts = new ObservableCollection<DiscountWrapper>();
        public ObservableCollection<DiscountWrapper> Discounts
        {
            get => discounts;
            set { discounts = value; OnPropertyChanged(nameof(Discounts)); }
        }
        private DiscountWrapper _selectedDiscount;


        private ICommand onDiscountSelected;
        public ICommand OnDiscountSelected
        {
            get => onDiscountSelected ??= new RelayCommand(async obj =>
            {
                foreach(var discount in Discounts)
                {
                    discount.IsSelected = false;
                }
                _selectedDiscount = obj as DiscountWrapper;


                if (_selectedDiscount != null)
                {
                    _selectedDiscount.IsSelected = true;
                }
            });
        }



        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand applyDiscount;
        public ICommand ApplyDiscount
        {
            get => applyDiscount ??= new RelayCommand(async obj =>
            {
                if (_selectedDiscount is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Выберите скидку, чтобы продолжить", "Ошибка"));
                    return;
                }
                OrdersHelper.SetDiscount(_selectedDiscount.Discount);
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}