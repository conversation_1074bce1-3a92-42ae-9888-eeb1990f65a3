﻿using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Orders
{
    /// <summary>
    /// Логика взаимодействия для OrderWithDeliveryTemplate.xaml
    /// </summary>
    public partial class OrderWithDeliveryTemplate : UserControl
    {
        public OrderWithDeliveryTemplate()
        {
            InitializeComponent();
        }
        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(Order), typeof(OrderWithDeliveryTemplate));
        public Order Model
        {
            get { return (Order)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
