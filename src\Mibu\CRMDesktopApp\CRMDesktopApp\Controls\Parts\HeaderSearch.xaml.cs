﻿using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Parts
{
    /// <summary>
    /// Логика взаимодействия для HeaderSearch.xaml
    /// </summary>
    public partial class HeaderSearch : UserControl
    {
        public HeaderSearch()
        {
            InitializeComponent();
        }

        public static readonly DependencyProperty TitleProperty =
           DependencyProperty.Register(nameof(Title), typeof(string), typeof(HeaderSearch));
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if (nav != null)
                {
                    nav.GoBack();
                }
            });
        }
    }
}
