﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using MarkupCreator.Converters;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AcquiringItemTemplate : ContentView
    {
        public static readonly BindableProperty AcquiringProperty =
            BindableProperty.Create(nameof(Acquiring), typeof(Acquiring), typeof(AcquiringItemTemplate));
        public Acquiring Acquiring
        {
            get { return (Acquiring)GetValue(AcquiringProperty); }
            set { SetValue(AcquiringProperty, value); }
        }

        public AcquiringItemTemplate(Acquiring acquiring)
        {
            InitializeComponent();
            Acquiring = acquiring;

            Device.InvokeOnMainThreadAsync(() =>
            {
                providerLabel.Text = EnumDescriptionHelper.GetDescription(Acquiring.AcquiringProvider);
            });
        }




        public event EventHandler<Acquiring> ItemTapped;
        private void onItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ItemTapped?.Invoke(this, Acquiring);
            });
        }
    }
}