﻿using CRM.Models.Enums.General;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.CustomControls;
using CRMDesktopApp.Views.Pages.Orders;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Mibu.Landing.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages
{
    /// <summary>
    /// Логика взаимодействия для MainPage.xaml
    /// </summary>
    public partial class MainPage : BasePage
    {
        public MainPage()
        {
            InitializeComponent();
            keyboard.ButtonTapped += Keyboard_ButtonTapped;

            Loaded += MainPage_Loaded;
        }

        private void MainPage_Loaded(object sender, RoutedEventArgs e)
        {

            Task.Run(async () =>
            {
                await Task.Delay(15);
                imgRotate.Angle += 2;
                return true;
            });

            passwordEntries = new List<AndroidStyleEntry>
            {
                passwordEntry1,
                passwordEntry2,
                passwordEntry3,
                passwordEntry4
            };
        }


        List<AndroidStyleEntry> passwordEntries;
        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (e == "backspace" && Password.Length > 0)
            {
                passwordEntries[Password.Length - 1].Text = "";
            }
            else if (e == "close")
            {
                passwordEntry1.Text = "";
                passwordEntry2.Text = "";
                passwordEntry3.Text = "";
                passwordEntry4.Text = "";
            }
            else if (e != "backspace")
            {
                if(Password.Length == 4)
                {
                    passwordEntries.ForEach(o => o.Text = "");
                    Password = "";
                }

                passwordEntries[Password.Length].Text = e;
            }
        }
        private void onPasswordEntryTextChanged(object sender, TextChangedEventArgs e)
        {
            Password = passwordEntry1.Text + passwordEntry2.Text + passwordEntry3.Text + passwordEntry4.Text;
        }

        private string password = string.Empty;
        public string Password
        {
            get => password;
            set
            {
                if (value.Length == 4)
                {
                    if (ApplicationState.Subscription.IsExpired)
                    {
                        new OneButtonedPopup("Необходимо оплатить подписку", "Ошибка").ShowDialog();
                        return;
                    }

                    bool authResult = Auth.AuthorizeByPIN(value).Result;
                    if (authResult)
                    {
                        passwordEntries.ForEach(o => o.Text = "");
                        keyboard.IsEnabled = false;
                        ApplicationState.GetLastDuty().Wait();


                        ApplicationState.LoadFiscalizers();

                        if (ApplicationState.CurrentStore.StoreMode == StoreMode.Point)
                        {
                            this.NavigationService.Navigate(new CategoriesPage());
                        }


                        if (ApplicationState.CurrentDuty is null || ApplicationState.CurrentDuty.ClosedAt.HasValue)
                        {
                            new DutyOpeningPopup().ShowDialog();
                        }
                        else
                        {
                            OrdersHelper.GetCurrentOrder().Wait();
                            if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe
                               && ApplicationState.Subscription.Subscription.Type != SubscriptionType.Start)
                            {
                                this.NavigationService.Navigate(new HallPage());
                            }
                            else
                            {
                                this.NavigationService.Navigate(new CategoriesPage());
                            }
                        }

                        keyboard.IsEnabled = true;
                    }
                    else
                    {
                        Task.Run(async () =>
                        {
                            await Task.Delay(50);
                            passwordEntries.ForEach(o => o.Text = "");
                        });
                        new OneButtonedPopup("Не удалось авторизоваться", "Ошибка").ShowDialog();
                    }

                }
                password = value;
                OnPropertyChanged(nameof(Password));

            }
        }

        private ICommand openSupportPopup;
        public ICommand OpenSupportPopup
        {
            get => openSupportPopup ??= new RelayCommand(async obj =>
            {
                new SupportPopup().ShowDialog();
            });
        }
    }
}
