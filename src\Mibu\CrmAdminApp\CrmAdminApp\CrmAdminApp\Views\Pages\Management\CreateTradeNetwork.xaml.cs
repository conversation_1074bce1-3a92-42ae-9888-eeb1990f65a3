﻿using CRM.Models.Enums.Info;
using CRM.Models.Network;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using MarkupCreator.Helpers.Converters;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMAdminMoblieApp
{
    public partial class CreateTradeNetwork : ContentPage
    {
        private TradeNetwork newTradeNetwork;
        public TradeNetwork NewTradeNetwork
        {
            get => newTradeNetwork;
            set { newTradeNetwork = value; OnPropertyChanged(nameof(NewTradeNetwork)); }
        }
        public CreateTradeNetwork()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            NewTradeNetwork = new TradeNetwork();
            NewTradeNetwork.NetworkSettings = new CRM.Models.Network.Settings.NetworkSettings();

            PosAuthTypesStrings = new List<string>
            {
                "Карта",
                "Пин код (4 цифры)"
            };
            SalesmanStrings = new List<string>
            {
                "Официант",
                "Бариста",
                "Флорист",
                "Администратор",
            };
        }



        private string selectedPosAuthTypesString = "";
        public string SelectedPosAuthTypesString
        {
            get => selectedPosAuthTypesString;
            set { selectedPosAuthTypesString = value; OnPropertyChanged(nameof(SelectedPosAuthTypesString)); }
        }

        private List<string> posAuthTypesStrings = new List<string>();
        public List<string> PosAuthTypesStrings
        {
            get => posAuthTypesStrings;
            set { posAuthTypesStrings = value; OnPropertyChanged(nameof(PosAuthTypesStrings)); }
        }
        




        private string selectedSalesmanString = "";
        public string SelectedSalesmanString
        {
            get => selectedSalesmanString;
            set { selectedSalesmanString = value; OnPropertyChanged(nameof(SelectedSalesmanString)); }
        }

        private List<string> salesmanStrings = new List<string>();
        public List<string> SalesmanStrings
        {
            get => salesmanStrings;
            set { salesmanStrings = value; OnPropertyChanged(nameof(SalesmanStrings)); }
        }



        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }

        private ICommand createNetwork;
        public ICommand CreateNetwork
        {
            get => createNetwork ??= new RelayCommand(async obj =>
            {
                switch (SelectedPosAuthTypesString)
                {
                    case "Карта":
                        NewTradeNetwork.NetworkSettings.PosAuthType = PosAuthType.Card;
                        break;
                    case "Пин код (4 цифры)":
                        NewTradeNetwork.NetworkSettings.PosAuthType = PosAuthType.PIN;
                        break;
                }

                switch (SelectedSalesmanString)
                {
                    case "Официант":
                        NewTradeNetwork.NetworkSettings.SalesmanTitle = SalesmanTitle.Waiter;
                        break;
                    case "Бариста":
                        NewTradeNetwork.NetworkSettings.SalesmanTitle = SalesmanTitle.Barista;
                        break;
                    case "Флорист":
                        NewTradeNetwork.NetworkSettings.SalesmanTitle = SalesmanTitle.Florist;
                        break;
                    case "Администратор":
                        NewTradeNetwork.NetworkSettings.SalesmanTitle = SalesmanTitle.Admin;
                        break;
                }


                await MobileAPI.AdminMethods.AdminTradeNetworkMethods.CreateTradeNetwork(ApplicationState.CurrentDomain, NewTradeNetwork);
                await App.Current.MainPage.Navigation.PopAsync();
                //await App.Current.MainPage.Navigation.PushAsync(new TradeNetworks());
            });
        }
    }
}
