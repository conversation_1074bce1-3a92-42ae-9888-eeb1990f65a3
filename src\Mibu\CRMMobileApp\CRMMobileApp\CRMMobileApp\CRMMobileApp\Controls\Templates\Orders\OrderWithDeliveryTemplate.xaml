﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:converters="clr-namespace:MarkupCreator.Converters"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.OrderWithDeliveryTemplate">
    <ContentView.Resources>
        <ResourceDictionary>
            <converters:DateTimeToStringConverter x:Key="DateTimeToStringConverter"/>
            <converters:DateTimeToStringWithoutDateConverter x:Key="DateTimeToStringWithoutDateConverter"/>
            <converters:DateTimeToStringWithoutTimeConverter x:Key="DateTimeToStringWithoutTimeConverter"/>
            <converters:DoubleToStringConverter x:Key="DoubleToStringConverter"/>
            <converters:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter"/>

            <converters:NotNullToBooleanConverter x:Key="NotNullToBooleanConverter"/>
            <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
        </ResourceDictionary>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame
            BackgroundColor="Transparent"
            HasShadow="False"
            Padding="0"
            CornerRadius="20">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Frame.GestureRecognizers>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="3*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="3*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">

                    <StackLayout
                        VerticalOptions="Center"
                        Margin="30,0,0,0">
                        <StackLayout
                            VerticalOptions="Center"
                            Orientation="Horizontal">
                            <Label 
                                Margin="0,0,0,0"       
                                x:Name="orderNumberLabel"
                                FontFamily="TTFirsNeue-Regular"
                                HorizontalOptions="Start"
                                FontSize="14"
                                TextColor="{StaticResource text_gray}">
                                <Label.FormattedText>
                                    <FormattedString>
                                        <FormattedString.Spans>
                                            <Span Text="#"/>
                                            <Span Text="{Binding Source={x:Reference this},Path=Model.OrderNumber}"/>
                                        </FormattedString.Spans>
                                    </FormattedString>
                                </Label.FormattedText>
                            </Label>

                            <Label 
                                Margin="40,0,0,0"
                                x:Name="orderDateLabel"
                                Text="{Binding Source={x:Reference this},Path=Model.Delivery.Date,Converter={StaticResource DateTimeToStringConverter}}"
                                FontFamily="TTFirsNeue-Regular"
                                HorizontalOptions="Start"
                                FontSize="14"
                                TextColor="{StaticResource text_gray}"/>
                        </StackLayout>

                        <StackLayout VerticalOptions="Center">
                            <Label  
                                Margin="0,0,0,0"
                                x:Name="orderCustomerNameLabel"
                                FontFamily="TTFirsNeue-Regular"
                                HorizontalOptions="Start"
                                FontSize="14"
                                TextColor="{StaticResource text_gray}">
                                <Label.FormattedText>
                                    <FormattedString>
                                        <FormattedString.Spans>
                                            <Span Text="{Binding Source={x:Reference this},Path=Model.Customer.Name}"/>
                                            <Span Text=" "/>
                                            <Span Text="{Binding Source={x:Reference this},Path=Model.Customer.Surname}"/>
                                        </FormattedString.Spans>
                                    </FormattedString>
                                </Label.FormattedText>
                            </Label>

                            <Label 
                                Margin="0,0,0,0"
                                Text="{Binding Source={x:Reference this},Path=Model.Customer.Phone}"
                                x:Name="orderCustomerPhoneLabel"
                                FontFamily="TTFirsNeue-Regular"
                                HorizontalOptions="Start"
                                FontSize="14"
                                TextColor="{StaticResource text_gray}"/>
                        </StackLayout>
                    </StackLayout>
                    
                </Grid>

                <Grid Grid.Column="1">
                    <Label 
                        Margin="0,0,0,0"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalOptions="Start"
                        VerticalOptions="Start"
                        FontSize="14"
                        TextColor="{StaticResource text_gray}">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span Text="{Binding Source={x:Reference this},Path=Model.Delivery.PersonsCount}"/>
                                    <Span Text=" персон"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </Grid>

                <Grid Grid.Column="2">
                    <StackLayout
                        IsVisible="{Binding Source={x:Reference this},Path=Model.Delivery.Service,Converter={StaticResource NotNullToBooleanConverter}}"
                        VerticalOptions="Center">
                        <Label 
                            Margin="0,0,0,0"
                            VerticalOptions="Center"
                            FontFamily="TTFirsNeue-Regular"
                            HorizontalOptions="Start"
                            FontSize="14"
                            FontAttributes="Bold"
                            Text="Доставка:"
                            TextColor="{StaticResource text_gray}"/>

                        <Label 
                            Margin="0,0,0,0"
                            VerticalOptions="Center"
                            x:Name="deliveryServiceLable"
                            FontFamily="TTFirsNeue-Regular"
                            HorizontalOptions="Start"
                            FontSize="14"
                            TextColor="{StaticResource text_gray}">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="{Binding Source={x:Reference this},Path=Model.Delivery.Service.Title}"/>
                                        <Span Text="    "/>
                                        <Span Text="{Binding Source={x:Reference this},Path=Model.Delivery.DeliveryMan.Name}"/>
                                        <Span Text=" "/>
                                        <Span Text="{Binding Source={x:Reference this},Path=Model.Delivery.DeliveryMan.Surname}"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                    </StackLayout>
                </Grid>

                <Grid Grid.Column="3">
                    <Label 
                        Margin="0,0,30,0"
                        x:Name="orderSumLabel"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        FontSize="20"
                        FontAttributes="Bold"
                        TextColor="{StaticResource text_gray}">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span Text="{Binding Source={x:Reference this},Path=Model.Sum,Converter={StaticResource DoubleToStringConverter}}"/>
                                    <Span Text=" р"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </Grid>


            </Grid>
        </Frame>
  </ContentView.Content>
</ContentView>