﻿using CRMDesktopApp.CustomControls.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.CustomControls
{
    /// <summary>
    /// Логика взаимодействия для AndroidStyleEntry.xaml
    /// </summary>
    public partial class AndroidStyleEntry : BaseEntry
    {
        public AndroidStyleEntry()
        {
            InitializeComponent();
            placeHolderLabel = PlaceHolderLabel;
            textBox = TextBox;

        }
        public static readonly DependencyProperty BorderColorProperty =
             DependencyProperty.Register(nameof(BorderColor), typeof(Brush), typeof(AndroidStyleEntry), new PropertyMetadata(new BrushConverter().ConvertFromString("#CBCAD8")));
        public Brush BorderColor
        {
            get { return (Brush)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }


    }
}
