﻿using CRMMobileApp.Models.Reports;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SalesOnDutyReportRow : ContentView
    {
        public SalesOnDutyReportRow()
        {
            InitializeComponent();
        }
        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(SalesOnDutyRow), typeof(SalesOnDutyReportRow));
        public SalesOnDutyRow Model
        {
            get { return (SalesOnDutyRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
       
    }
}