﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom" 
             xmlns:extensions="http://xamarin.com/schemas/2020/toolkit"
             xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" 
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             ios:Page.UseSafeArea="true"
             x:Class="CRMAdminMoblieApp.Views.Pages.Profile">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/RadioButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid BackgroundColor="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <parts:Header x:Name="header" Grid.Row="0"/>

            <Grid Grid.Row="1">
                <ScrollView Padding="0,0,0,50">
                    <StackLayout>
                        
                        
                        <Grid HeightRequest="130">

                            <Grid 
                                HeightRequest="100"
                                WidthRequest="100"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">

                                <Frame 
                                    CornerRadius="50"
                                    Padding="0"
                                    IsClippedToBounds="True"
                                    BackgroundColor="{StaticResource text_gray}"
                                    HasShadow="False">
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding Source={Reference this},Path=OpenGallery}"/>
                                    </Frame.GestureRecognizers>
                                    <Image
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Fill"
                                        x:Name="userAvatar"
                                        Aspect="Fill"/>
                                </Frame>

                                <ImageButton
                                    Source="photo.png"
                                    Command="{Binding Source={Reference this},Path=OpenGallery}"
                                    BackgroundColor="{StaticResource purple}"
                                    VerticalOptions="End"
                                    HorizontalOptions="End"
                                    CornerRadius="12"
                                    WidthRequest="24"
                                    HeightRequest="24"/>

                            </Grid>

                        </Grid>

                        <StackLayout 
                            Margin="25,0,25,0">


                            <StackLayout
                                 Margin="0,20,0,15"
                                 HorizontalOptions="Start"
                                 VerticalOptions="Start"
                                 Orientation="Horizontal">
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                                </StackLayout.GestureRecognizers>
                                <ImageButton 
                                     Source="arrowBack.png"
                                     HeightRequest="10"
                                     WidthRequest="5"
                                     BackgroundColor="Transparent"
                                     VerticalOptions="Center"
                                     HorizontalOptions="Start"/>
                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     TextColor="{x:StaticResource dark_purple}"
                                     VerticalOptions="Center"
                                     Text="Профиль"/>
                            </StackLayout>


                            <Label 
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="{x:StaticResource text_gray}"
                                 VerticalOptions="Center"
                                 Text="Имя"/>
                            <custom:AndroidStyleEntry 
                                FontFamily="TTFirsNeue-Regular"
                                TextFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                Text="{Binding Source={x:Reference this},Path=User.Name,Mode=TwoWay}"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Введите имя"/>
                            
                            
                            
                            <Label 
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="{x:StaticResource text_gray}"
                                 VerticalOptions="Center"
                                 Text="Фамилия"/>
                            <custom:AndroidStyleEntry  
                                FontFamily="TTFirsNeue-Regular"
                                TextFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                Text="{Binding Source={x:Reference this},Path=User.Surname,Mode=TwoWay}"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Введите фамилию"/>

                            <Label 
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="{x:StaticResource text_gray}"
                                 VerticalOptions="Center"
                                 Text="Дата рождения"/>
                            <editors:DateEdit
                                BorderThickness="0"
                                x:Name="dateEdit"
                                Margin="0,3,0,0"
                                BoxPadding="0"
                                HeightRequest="27"
                                HorizontalOptions="Fill"
                                VerticalOptions="Start"
                                DisplayFormat="dd.MM.yyyy"
                                PlaceholderText=""
                                PlaceholderColor="{x:StaticResource text_gray}"
                                BackgroundColor="Transparent"
                                TextColor="{x:StaticResource dark_purple}"
                                BorderColor="{x:StaticResource text_gray}"/>
                            <BoxView
                                BackgroundColor="#CBCAD8"
                                HeightRequest="1"/>




                            <!--<Label 
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="{x:StaticResource text_gray}"
                                 VerticalOptions="Center"
                                 Text="Должность"/>
                            <Grid HeightRequest="200">
                                <custom:Combobox
                                    CornerRadius="10"
                                    Margin="0,10,0,0"
                                    HasUnderLine="True"
                                    HeaderBackgroundColor="Transparent"
                                    UnderLineColor="#CBCAD8"/>
                             
                                --><!--<ImageButton 
                                    Source="arrowDown.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    Margin="0,0,10,0"
                                    WidthRequest="10"
                                    HeightRequest="5"
                                    BackgroundColor="Transparent"/>--><!--
                            </Grid>-->


                            <!--<Label 
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="{x:StaticResource text_gray}"
                                 VerticalOptions="Center"
                                 Text="Телефон"/>
                            <Entry 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Введите телефон"/>-->


                            <Label                  
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="{x:StaticResource text_gray}"
                                 VerticalOptions="Center"
                                 Text="E-mail"/>
                            <custom:AndroidStyleEntry  
                                FontFamily="TTFirsNeue-Regular"
                                TextFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                Text="{Binding Source={x:Reference this},Path=User.Email,Mode=TwoWay}"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Введите e-mail"/>


                            <Button 
                                x:Name="saveChangesBtn"
                                Margin="0,10,0,0"
                                Command="{Binding Source={x:Reference this},Path=SaveChanges}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                Text="Сохранить изменения"
                                Style="{x:StaticResource purple_gradient_btn}"
                                HorizontalOptions="Start"
                                WidthRequest="220"
                                HeightRequest="40"/>



                            <StackLayout
                                Margin="0,30,0,0">
                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="20"
                                     TextColor="{x:StaticResource dark_purple}"
                                     VerticalOptions="Center"
                                     Text="Смена пароля"/>



                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="12"
                                     TextColor="{x:StaticResource text_gray}"
                                     VerticalOptions="Center"
                                     Text="Старый пароль"/>
                                <Grid HeightRequest="40">
                                    <custom:AndroidStyleEntry  
                                        x:Name="oldPwdEntry"
                                        FontFamily="TTFirsNeue-Regular"
                                        IsPassword="True"
                                        TextFontSize="{OnPlatform Android=17,iOS=14}"
                                        PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                        Text="{Binding Source={x:Reference this},Path=OldPassword,Mode=TwoWay}"
                                        PlaceholderColor="{x:StaticResource text_gray}"
                                        Placeholder=""/>
                                    <ImageButton 
                                        x:Name="hideOldPwdBtn"
                                        Clicked="showOrHideOldPassword"
                                        Source="hidePassword.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        Margin="0,-12,10,0"
                                        WidthRequest="20"
                                        HeightRequest="20"
                                        BackgroundColor="Transparent"/>
                                </Grid>


                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="12"
                                     TextColor="{x:StaticResource text_gray}"
                                     VerticalOptions="Center"
                                     Text="Новый пароль"/>
                                <Grid HeightRequest="40">
                                    <custom:AndroidStyleEntry  
                                        x:Name="newPwdEntry"
                                        IsPassword="True"
                                        FontFamily="TTFirsNeue-Regular"
                                        TextFontSize="{OnPlatform Android=17,iOS=14}"
                                        PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                        Text="{Binding Source={x:Reference this},Path=NewPassword,Mode=TwoWay}"
                                        PlaceholderColor="{x:StaticResource text_gray}"
                                        Placeholder=""/>
                                    <ImageButton 
                                        x:Name="hideNewPwdBtn"
                                        Clicked="showOrHideNewPassword"
                                        Source="hidePassword.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        Margin="0,-12,10,0"
                                        WidthRequest="20"
                                        HeightRequest="20"
                                        BackgroundColor="Transparent"/>
                                </Grid>
                               

                                <Button 
                                    x:Name="savePasswordBtn"
                                    Margin="0,10,0,0"
                                    Command="{Binding Source={x:Reference this},Path=ChangePassword}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="12"
                                    Text="Сохранить новый пароль"
                                    Style="{x:StaticResource purple_gradient_btn}"
                                    HorizontalOptions="Start"
                                    WidthRequest="220"
                                    HeightRequest="40"/>

                                <Label 
                                    x:Name="changePwdErrorText"
                                    TextColor="Red"
                                    FontSize="14"
                                    HorizontalTextAlignment="Center"
                                    HorizontalOptions="Center"/>

                            </StackLayout>

                            <StackLayout
                                x:Name="notificationsLayout"
                                Margin="0,30,0,0">
                                <Label 
                                   FontFamily="TTFirsNeue-Regular"
                                   FontSize="20"
                                   TextColor="{StaticResource dark_purple}"
                                   VerticalOptions="Center"
                                   Text="Уведомления"/>


                                <StackLayout
                                      Margin="-10,5,0,0"
                                      Orientation="Horizontal">

                                    <Switch 
                                          x:Name="shouldSentPushNotificationsToggledShitcher"
                                          OnColor="#9A90FF"
                                          ThumbColor="#594DD2"
                                          VerticalOptions="Center"
                                          Toggled="onShouldSentPushNotificationsToggled"/>      

                                    <Label                 
                                         Margin="0,0,0,0"
                                         FontFamily="TTFirsNeue-Regular"
                                         FontSize="14"
                                         TextColor="{StaticResource text_gray}"
                                         VerticalOptions="Center"
                                         Text="Получать уведомления о событиях"/>
                                </StackLayout>


                            </StackLayout>

                        </StackLayout>

                    </StackLayout>
                    
                </ScrollView>
            </Grid>
            
            
        </Grid>
    </ContentPage.Content>
</ContentPage>