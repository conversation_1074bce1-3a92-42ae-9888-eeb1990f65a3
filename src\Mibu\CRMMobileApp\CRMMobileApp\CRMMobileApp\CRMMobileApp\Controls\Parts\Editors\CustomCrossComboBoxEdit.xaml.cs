﻿using CRMMobileApp.Controls.Parts.ListViews;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.Editors
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CustomCrossComboBoxEdit : ContentView
    {
        public CustomCrossComboBoxEdit()
        {
            this.BindingContext = this;
            InitializeComponent();

            PropertyChanged += CustomCrossComboBoxEdit_PropertyChanged;
        }

        private void CustomCrossComboBoxEdit_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
      
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
        }



        #region Main props

        public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(nameof(CornerRadius), typeof(CornerRadius), typeof(CustomCrossComboBoxEdit), new CornerRadius(0));
        public CornerRadius CornerRadius
        {
            get { return (CornerRadius)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }
        public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(nameof(BorderColor), typeof(Color), typeof(CustomCrossComboBoxEdit), Color.Gray);
        public Color BorderColor
        {
            get { return (Color)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }
        public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameof(TextColor), typeof(Color), typeof(CustomCrossComboBoxEdit), Color.Black);
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }
        public static readonly BindableProperty TextFontFamilyProperty = BindableProperty.Create(nameof(TextFontFamily), typeof(string), typeof(CustomCrossComboBoxEdit),"");
        public string TextFontFamily
        {
            get { return (string)GetValue(TextFontFamilyProperty); }
            set { SetValue(TextFontFamilyProperty, value); }
        }




        public static readonly BindableProperty LabelTextProperty = BindableProperty.Create(nameof(LabelText), typeof(string), typeof(CustomCrossComboBoxEdit),"");
        public string LabelText
        {
            get { return (string)GetValue(LabelTextProperty); }
            set { SetValue(LabelTextProperty, value); }
        }
        #endregion

        #region Items

        public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(IEnumerable), typeof(CustomCrossComboBoxEdit), defaultBindingMode: BindingMode.TwoWay, propertyChanged: (view, objA, objB) =>
        {
            var thisView = view as CustomCrossComboBoxEdit;

            var list = new List<object>();
            foreach(var item in thisView.ItemsSource)
            {
                list.Add(item);
            }
            
            if(thisView.SelectedIndex == -1 && list.Any())
            {
                thisView.SelectedIndex = 0;
                thisView.SelectedItem = list[0];
            }
            else if(thisView.SelectedIndex > -1 && list.Count >= thisView.SelectedIndex + 1)
            {
                thisView.SelectedItem = list[thisView.SelectedIndex];
            }

        });
        public IEnumerable ItemsSource
        {
            get { return (IEnumerable)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }
        public static readonly BindableProperty SelectedItemProperty = BindableProperty.Create(nameof(SelectedItem), typeof(object), typeof(CustomCrossComboBoxEdit), defaultBindingMode: BindingMode.TwoWay);
        public object SelectedItem
        {
            get { return (object)GetValue(SelectedItemProperty); }
            set { SetValue(SelectedItemProperty, value); }
        }
        public static readonly BindableProperty SelectedIndexProperty = BindableProperty.Create(nameof(SelectedIndex), typeof(int), typeof(CustomCrossComboBoxEdit), -1,defaultBindingMode: BindingMode.TwoWay, propertyChanged: (view,objA,objB) =>
        {
            //(view as CustomCrossComboBoxEdit).forAndroidIOSdateEdit.BorderColor = Color.Red;
            //(view as CustomCrossComboBoxEdit).BorderColor = Color.Yellow;

        });
        public int SelectedIndex
        {
            get { return (int)GetValue(SelectedIndexProperty); }
            set { SetValue(SelectedIndexProperty, value); }
        }

        #endregion

        #region Placeholder props

        public static readonly BindableProperty PlaceholderColorProperty = BindableProperty.Create(nameof(PlaceholderColor), typeof(Color), typeof(CustomCrossComboBoxEdit),Color.Gray);
        public Color PlaceholderColor
        {
            get { return (Color)GetValue(PlaceholderColorProperty); }
            set { SetValue(PlaceholderColorProperty, value); }
        }

        public static readonly BindableProperty PlaceholderTextProperty = BindableProperty.Create(nameof(PlaceholderText), typeof(string), typeof(CustomCrossComboBoxEdit),"Select item");
        public string PlaceholderText
        {
            get { return (string)GetValue(PlaceholderTextProperty); }
            set { SetValue(PlaceholderTextProperty, value); }
        }
        #endregion

        public event EventHandler SelectionChanged;

        private void onSelectionChanged(object sender, EventArgs e)
        {
            SelectionChanged?.Invoke(this, e);
        }
    }




    public class ComboBoxEditForWPF : Picker
    {
        /// <summary>
        /// В WPF проекте будет написан рендерер, где будет задаваться стиль
        /// </summary>
        /// <exception cref="NotSupportedException"></exception>
        public ComboBoxEditForWPF()
        {

        }


        public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(nameof(CornerRadius), typeof(CornerRadius), typeof(ComboBoxEditForWPF));
        public CornerRadius CornerRadius
        {
            get { return (CornerRadius)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }


        public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(nameof(BorderColor), typeof(Color), typeof(ComboBoxEditForWPF));
        public Color BorderColor
        {
            get { return (Color)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }






        public static readonly BindableProperty PlaceholderColorProperty = BindableProperty.Create(nameof(PlaceholderColor), typeof(Color), typeof(ComboBoxEditForWPF));
        public Color PlaceholderColor
        {
            get { return (Color)GetValue(PlaceholderColorProperty); }
            set { SetValue(PlaceholderColorProperty, value); }
        }

        public static readonly BindableProperty PlaceholderTextProperty = BindableProperty.Create(nameof(PlaceholderText), typeof(string), typeof(ComboBoxEditForWPF));
        public string PlaceholderText
        {
            get { return (string)GetValue(PlaceholderTextProperty); }
            set { SetValue(PlaceholderTextProperty, value); }
        }

    }
}