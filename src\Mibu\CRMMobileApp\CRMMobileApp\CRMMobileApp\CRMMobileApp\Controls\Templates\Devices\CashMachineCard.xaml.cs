﻿using CRM.Models.Enums.Equipment;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Views.Popups;
using Fiscalization;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Devices
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CashMachineCard : ContentView
    {
        protected double ExpandedHeightRequest = -1;
        protected double CollapsedHeightRequest = 60;

        private Fiscalizer fiscalizer;
        public CashMachineCard(FiscalRegistrator fiscalRegistrator, Fiscalizer driver)
        {
            Model = fiscalRegistrator;
            fiscalizer = driver;

            ExpandedHeightRequest = 165;
            InitializeComponent();
        }

        public static readonly BindableProperty ModelProperty =
              BindableProperty.Create(nameof(Model), typeof(FiscalRegistrator), typeof(CashMachineCard));
        public FiscalRegistrator Model
        {
            get { return (FiscalRegistrator)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        #region Экспандер 
        private bool isExpanded;
        public bool IsExpanded
        {
            get => isExpanded;
            set
            {
                isExpanded = value;
                OnPropertyChanged(nameof(IsExpanded));

                menuLayout.IsVisible = IsExpanded;

                if (value)
                {
                    this.HeightRequest = ExpandedHeightRequest;
                }
                else
                {
                    this.HeightRequest = CollapsedHeightRequest;
                }
            }
        }

        private ICommand toggleExpand;
        public ICommand ToggleExpand
        {
            get => toggleExpand ??= new RelayCommand(async obj =>
            {
                IsExpanded = !IsExpanded;
            });
        }
        #endregion



        #region Функции
        private ICommand checkConnection;
        public ICommand CheckConnection
        {
            get => checkConnection ??= new RelayCommand(obj =>
            {
                if (!ApplicationState.IsFiscalizerAvailableOnThisPlatform())
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("На данном устройстве нельзя подключиться к ККТ", "Ошибка", "Хорошо"));
                    return;
                }

                try
                {
                    bool isConn = fiscalizer.CheckConnection();
                    if (!isConn)
                    {
                        isConn = fiscalizer.TryOpenConnection();
                    }


                    if (isConn)
                    {
                        connectionCircleFrame.BackgroundColor = Color.Green;
                    }
                    else
                    {
                        connectionCircleFrame.BackgroundColor = Color.Red;
                    }
                }
                catch (Exception ex)
                {
                    connectionCircleFrame.BackgroundColor = Color.Red;
                }

            });
        }
        private ICommand closeShift;
        public ICommand CloseShift
        {
            get => closeShift ??= new RelayCommand(obj =>
            {
                if (!ApplicationState.IsFiscalizerAvailableOnThisPlatform())
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("На данном устройстве нельзя подключиться к ККТ", "Ошибка", "Хорошо"));
                    return;
                }
    
                try
                {
                    var res = fiscalizer.CloseShift();
                    if (!res)
                    {
                        App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Фискальная смена уже закрыта", "Ошибка", "Хорошо"));
                    }
                }
                catch (Exception ex)
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Проверьте подключение к ФР и/или установите драйвера с официального сайта", "Ошибка", "Хорошо"));
                }
            });
        }
        private ICommand cancelCheque;
        public ICommand CancelCheque
        {
            get => cancelCheque ??= new RelayCommand(obj =>
            {
                if (!ApplicationState.IsFiscalizerAvailableOnThisPlatform())
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("На данном устройстве нельзя подключиться к ККТ", "Ошибка", "Хорошо"));
                    return;
                }

                try
                {
                    var res = fiscalizer.CancelReceipt();
                    if (!res)
                    {
                        App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Чек уже закрыт или еще не открыт", "Ошибка", "Хорошо"));
                    }
                }
                catch (Exception ex)
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Проверьте подключение к ФР и/или установите драйвера с официального сайта", "Ошибка", "Хорошо"));
                }
            });
        }
        #endregion


    }
}