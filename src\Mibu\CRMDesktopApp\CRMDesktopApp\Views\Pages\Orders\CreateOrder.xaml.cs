﻿using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Stores.Settings;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Windows;
using CRMDesktopApp.Views.Windows.CountSelections;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Models;
using CRMMoblieApiWrapper;
using DevExpress.Xpf.Core.ReflectionExtensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Orders
{
    /// <summary>
    /// Логика взаимодействия для CreateOrder.xaml
    /// </summary>
    public partial class CreateOrder : BasePage
    {
        public double _bonusesToAdd = 0;
        private ClientsAndBonusesStoreSettings _clientSettings;

        public CreateOrder()
        {
            InitializeComponent();
            Loaded += CreateOrder_Loaded;
        }

        private async void CreateOrder_Loaded(object sender, RoutedEventArgs e)
        {
            keyboard.ButtonTapped += Keyboard_ButtonTapped;

            var types = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetPaymentTypes(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);


            _clientSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetClientsAndBonusesStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id);


            PaymentTypes = new ObservableCollection<PaymentTypeWrapper>();
            foreach (var type in types)
            {
                try
                {
                    var wrapper = new PaymentTypeWrapper();
                    wrapper.PaymentType = type;
                    wrapper.Title = type.Title;
                    wrapper.ImgPath = type.ImgPath;
                    wrapper.OnSumChanged += Wrapper_OnSumChanged;
                    PaymentTypes.Add(wrapper);
                }
                catch (Exception ex)
                {

                }
            }


            //Оплата бонусами
            if (ApplicationState.CurrentStore.IsLoyaltySystemEnabled
                && OrdersHelper.CurrentOrder.Customer != null)
            {
                var wrapper = new PaymentTypeWrapper();
                wrapper.PaymentType = null;
                wrapper.Title = "Бонусы";
                wrapper.ImgPath = "pack://application:,,,/Resources/Images/bonuses.png";
                wrapper.OnSumChanged += Wrapper_OnSumChanged;
                PaymentTypes.Add(wrapper);



                clientBonusesLabel.Visibility = Visibility.Visible;
                clientBonusesCountSpan.Text = OrdersHelper.CurrentOrder.Customer.Bonuses.ToString();

                if (OrdersHelper.CurrentOrder.Customer.LoyaltyProgram != null)
                {
                    var program = OrdersHelper.CurrentOrder.Customer.LoyaltyProgram;
                    var possiblePercent = program.GetClientMaxPossiblePercent(OrdersHelper.CurrentOrder.Customer);


                    _bonusesToAdd = OrdersHelper.CurrentOrder.SumWithDiscount / 100 * possiblePercent;
                    bonusesToAddCountSpan.Text = $"{Math.Round(_bonusesToAdd, 2)}";

                    bonusesToAddLabel.Visibility = Visibility.Visible;
                }

            }



            if (OrdersHelper.CurrentOrder.PrepaymentsSum > 0)
            {
                prepaymentSumLabel.Text = $"Предоплата: {Math.Round(OrdersHelper.CurrentOrder.PrepaymentsSum)}₽";
            }

            totalSumLabel.Text = $"Итого: {Math.Round(OrdersHelper.CurrentOrder.Sum, 2)}₽";
            if (OrdersHelper.CurrentOrder.Customer != null)
            {
                var customer = OrdersHelper.CurrentOrder.Customer;
                clientLabel.Text = $"Клиент: {customer.Name} {customer.Surname}";
            }
            else
            {
                clientLabel.Text = $"Клиент: Гость";
            }

            if (OrdersHelper.CurrentOrder.Discount != null)
                discountLabel.Text = $"Скидка: {OrdersHelper.CurrentOrder.Discount.Percent}%";
            else
                discountLabel.Text = $"Скидка: 0%";


            toPayLabel.Text = $"К оплате: {Math.Round(OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens, 2)}₽";
            changeLabel.Text = $"Сдача: {Math.Round(Change, 2)}₽";

            orderNumberSpan.Text = $"{OrdersHelper.CurrentOrder.Id}";
        }

       


        private void Wrapper_OnSumChanged(object sender, double e)
        {
            Change = CurrentSum - OrdersHelper.CurrentOrder.SumAfterPrepayments;
            if (Change < 0) Change = 0;
            changeLabel.Text = $"Сдача: {Math.Round(Change, 2)}₽";
        }




        private double change;
        public double Change
        {
            get => change;
            set { change = value; OnPropertyChanged(nameof(Change)); }
        }

        public double CurrentSum => PaymentTypes.Sum(o => o.Sum);






        private PaymentTypeWrapper selectedPaymentType;
        public PaymentTypeWrapper SelectedPaymentType
        {
            get => selectedPaymentType;
            set
            {
                selectedPaymentType = value;
                OnPropertyChanged(nameof(SelectedPaymentType));

                foreach (var discount in PaymentTypes)
                {
                    discount.IsSelected = false;
                }

                if(selectedPaymentType != null)
                {
                    selectedPaymentType.IsSelected = true;
                }
              
            }
        }

        private ObservableCollection<PaymentTypeWrapper> paymentTypes;
        public ObservableCollection<PaymentTypeWrapper> PaymentTypes
        {
            get => paymentTypes;
            set { paymentTypes = value; OnPropertyChanged(nameof(PaymentTypes)); }
        }



        private ICommand addSumBtnPressed;
        public ICommand AddSumBtnPressed
        {
            get => addSumBtnPressed ??= new RelayCommand(async obj =>
            {
                int val = Convert.ToInt32(obj.ToString());
                if (SelectedPaymentType != null)
                {
                    if (CurrentSum + val > OrdersHelper.CurrentOrder.SumAfterPrepayments)
                    {
                        if (SelectedPaymentType.PaymentType?.OldPaymentType == OldPaymentType.Card)
                            return;

                        //Если способ оплаты - бонусы
                        if (SelectedPaymentType.PaymentType == null)
                            return;


                        var sum = SelectedPaymentType.Sum + val;
                        SelectedPaymentType.SumStr = sum.ToString();
                    }
                    else
                    {
                        //Если бонусов недостаточно
                        if (SelectedPaymentType.PaymentType == null)
                        {
                            if (OrdersHelper.CurrentOrder.Customer.Bonuses < val)
                            {
                                return;
                            }
                        }

                        var sum = SelectedPaymentType.Sum + val;
                        SelectedPaymentType.SumStr = sum.ToString();
                    }
                }
            });
        }

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (SelectedPaymentType is null) return;

            if (e == "backspace" && SelectedPaymentType.SumStr.Length > 0)
            {
                SelectedPaymentType.SumStr = SelectedPaymentType.SumStr.Remove(SelectedPaymentType.SumStr.Length - 1, 1);
            }
            else if (e == "close")
            {
                SelectedPaymentType.SumStr = "0";
            }
            else if (e != "backspace")
            {
                if (e == "." && SelectedPaymentType.SumStr.Contains(".")) return;

                if (SelectedPaymentType.PaymentType == null) // если оплата бонусами
                {
                    var sum = Convert.ToDouble(SelectedPaymentType.SumStr + e);

                    var bonusesPercent = sum / (OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens / 100);
                    if(bonusesPercent > _clientSettings.MaxBonusPaymentPercent)
                    {
                        new OneButtonedPopup($"Процент, оплачиваемый бонусами превышает заданное значение ({Math.Round(_clientSettings.MaxBonusPaymentPercent,2)}) в настройках точки","Уведомление").ShowDialog();
                        return;
                    }

                    if (OrdersHelper.CurrentOrder.Customer.Bonuses >= sum)
                    {
                        if (SelectedPaymentType.SumStr == "0")
                        {
                            SelectedPaymentType.SumStr = e;
                        }
                        else
                        {
                            SelectedPaymentType.SumStr += e;
                        }
                    }
                }
                else
                {
                    if (SelectedPaymentType.SumStr == "0")
                    {
                        SelectedPaymentType.SumStr = e;
                    }
                    else
                    {
                        SelectedPaymentType.SumStr += e;
                    }
                }

            }
        }









        private async void CancelOrder(object sender, MouseButtonEventArgs e)
        {
            await OrdersHelper.CancelOrder();
            (App.Current.MainWindow as MainWindow).frame.GoBack();
        }



        private ICommand payOrder;
        public ICommand PayOrder
        {
            get => payOrder ??= new RelayCommand(async obj =>
            {
                if (CurrentSum < OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens)
                {
                    new OneButtonedPopup("Сумма оплат меньше, чем сумма \"К оплате\"", "Ошибка").ShowDialog();
                }
                else
                {
                    var payments = PaymentTypes.Where(o => o.Sum > 0);

                    //Очищаем старые оплаты, на случай, если вдруг эквайринг сорвется или что-то произойдет
                    OrdersHelper.CurrentOrder.OrderPayments = OrdersHelper.CurrentOrder.OrderPayments.Where(o => o.IsPrepeyment).ToList();

                    foreach (var payment in payments)
                    {
                        OrdersHelper.CurrentOrder.OrderPayments.Add(new CRM.Models.Stores.OrderPayment
                        {
                            PaymentType = payment.PaymentType,
                            PaymentTypeId = payment.PaymentType?.Id,
                            Sum = payment.Sum
                        });
                    }

                    var sum = OrdersHelper.CurrentOrder.OrderPayments.Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Card && !o.IsPrepeyment)
                                                                        .Sum(o => o.Sum);
                    if (ApplicationState.StoreAcquirings.Count == 0 || sum == 0)
                    {
                        await CloseOrder();

                    }
                    else
                    {

                        if (ApplicationState.StoreAcquirings.Count == 1)
                        {
                            new AcquringWaitingPopup(ApplicationState.StoreAcquirings[0], sum).ShowDialog();
                        }
                        else
                        {
                            new AvailableAcquiringPopup().ShowDialog();
                        }
                    }



                }
            });
        }

        public async Task CloseOrder()
        {

            var fiscalizer = ApplicationState.StoreFiscalRegistrators.FirstOrDefault();

            var driver = fiscalizer.Value;
            if (driver != null)
            {
                driver.FiscalizeOrder(OrdersHelper.CurrentOrder);
            }


            new OrderPaidPopup(OrdersHelper.CurrentOrder.Sum, Change).ShowDialog();
            await NavigationHelper.GoBackIfCafe();


            //Изменяем бонусный баланс
            var client = OrdersHelper.CurrentOrder.Customer;
            if (client != null)
            {
                _bonusesToAdd -= OrdersHelper.CurrentOrder.PaidByBonuses;

                OrdersHelper.CurrentOrder.CustomerId = client.Id;
                OrdersHelper.CurrentOrder.Customer = null;

                await MobileAPI.BaristaMethods.BaristaClientsMethods.AddBonuses(ApplicationState.CurrentDomain,
                                                                                client.Id,
                                                                                _bonusesToAdd);
            }

            await OrdersHelper.CloseOrder();

   

        }

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => (App.Current.MainWindow as MainWindow).frame.GoBack());
        }

        
    }
}
