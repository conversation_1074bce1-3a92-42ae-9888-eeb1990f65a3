﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Orders.ClosedOrdersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Orders" 
      xmlns:controls1="clr-namespace:CRMDesktopApp.Controls.Parts" 
      xmlns:templates="clr-namespace:CRMDesktopApp.Controls.Templates"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="ClosedOrdersPage">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="350"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <controls1:LeftOrderPanel 
            x:Name="leftOrdersPanel"
            Grid.Column="0"/>

        <Grid 
            Grid.Column="1"
            Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <controls1:Header 
                    Title="Завершенные заказы"
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Stretch" />
            </Grid>

           

            <Grid Grid.Row="1">

                <ScrollViewer
                     Margin="30,10,30,0"
                     VerticalScrollBarVisibility="Hidden">
                    <StackPanel
                         x:Name="closedOrdersStackLayout">


                    </StackPanel>
                </ScrollViewer>

                <TextBlock
                     x:Name="noItemsTextBlock"
                     Visibility="Hidden"
                     FontFamily="{StaticResource TTFirsNeue-Regular}"
                     TextAlignment="Center"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center"
                     Foreground="{StaticResource text_gray}"
                     FontSize="15"
                     Width="300"
                     TextWrapping="Wrap"
                     Text="На данный момент нет закрытых заказов"/>

            </Grid>

        </Grid>
    </Grid>
</abstactions:BasePage>
