﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.NewClientPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls"
        xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        mc:Ignorable="d"
        Title="Создание нового клиента" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="770"
        Height="280">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        Padding="0"
        CornerRadius="20"
        Width="760"
        Height="275">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="65"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="75"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">

                <StackPanel
                    Margin="30,38,0,0"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal">

                    <controls:ImageButton
                        Command="{Binding ElementName=this,Path=CloseWindow}"
                        Source="pack://application:,,,/Resources/Images/arrowBack.png"
                        Background="Transparent"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Width="10"
                        Height="20"/>

                    <Image 
                        Margin="38,-2,0,0"
                        Stretch="Fill"
                        Source="pack://application:,,,/Resources/Images/client.png"
                        Width="20"
                        Height="20"
                        VerticalAlignment="Center"/>

                    <TextBlock
                        Margin="15,-2,0,0"
                        VerticalAlignment="Center"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Новый клиент"/>

                </StackPanel>


                <controls:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,35,35,0"/>


            </Grid>


            <Grid Grid.Row="1">

                <StackPanel
                    Margin="30,20,20,0">
                    <StackPanel 
                        Margin="20,5,0,0"
                        Orientation="Horizontal">

                        <controls:EntryOutlined 
                            Margin="0,0,0,0"
                            Style="{StaticResource white_cornered_entry}"
                            Text="{Binding ElementName=this,Path=NewClient.Surname,Mode=TwoWay}"
                            Placeholder="Фамилия"
                            Width="220"
                            Height="50"/>
                        <controls:EntryOutlined 
                            Margin="10,0,0,0"
                            Style="{StaticResource white_cornered_entry}"
                            Text="{Binding ElementName=this,Path=NewClient.Name,Mode=TwoWay}"
                            Placeholder="Имя"
                            Width="225"
                            Height="50"/>
                        <controls:EntryOutlined 
                            Margin="10,0,0,0"
                            Style="{StaticResource white_cornered_entry}"
                            Text="{Binding ElementName=this,Path=NewClient.Patronymic,Mode=TwoWay}"
                            Placeholder="Отчество"
                            Width="225"
                            Height="50"/>

                    </StackPanel>

                    <StackPanel 
                        Margin="0,5,0,0"
                        Orientation="Horizontal">

                        <controls:EntryOutlined 
                            Margin="20,0,0,0"
                            Style="{StaticResource white_cornered_entry}"
                            Text="{Binding ElementName=this,Path=NewClient.Phone,Mode=TwoWay}"
                            Placeholder="Номер телефона"
                            Width="220"
                            Height="50"/>

                        <Grid
                            Margin="10,3,0,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            Width="260"
                            Height="47">
                            <editors:DateEdit
                                x:Name="dateEdit"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Width="260"
                                Height="47"                          
                                DisplayFormatString="dd.MM.yyyy"
                                Text="Дата рождения"
                                AllowDefaultButton="False"
                                FontSize="15"
                                Padding="7,0,7,0"
                                MaskPlaceHolder=""
                                CornerRadius="20"
                                Background="#F6F6FB"
                                Foreground="{StaticResource dark_purple}"
                                BorderBrush="#F6F6FB"/>

                            <Image
                                VerticalAlignment="Center"
                                HorizontalAlignment="Right"
                                Margin="0,0,10,0"
                                Source="pack://application:,,,/Resources/Images/calendar.png"
                                Width="15"
                                Height="15"/>
                        </Grid>
                        
                       

                        <editors:ComboBoxEdit   
                            Margin="10,3,0,0"
                            Background="#F6F6FB"
                            Foreground="{StaticResource dark_purple}"
                            BorderBrush="#F6F6FB"
                            Width="187"
                            Height="47"
                            IsTextEditable="False"
                            Padding="7,0,7,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            SelectedIndex="0"
                            CornerRadius="20"
                            Text="Пол"
                            SelectedItem="{Binding ElementName=this,Path=SelectedGenderString,Mode=TwoWay}"
                            ItemsSource="{Binding  ElementName=this,Path=GenderStrings,Mode=TwoWay}" >
                            <editors:ComboBoxEdit.ItemTemplate>
                                <DataTemplate>
                                    <Grid Height="40">
                                        <TextBlock
                                            Margin="8,8,0,0"
                                            Foreground="{StaticResource dark_purple}"
                                            Text="{Binding}"/>
                                    </Grid>
                                </DataTemplate>
                            </editors:ComboBoxEdit.ItemTemplate>
                        </editors:ComboBoxEdit>

                    </StackPanel>

                </StackPanel>

            </Grid>

            <Grid Grid.Row="2">

                <Button 
                     Command="{Binding ElementName=this,Path=CreateClient}"
                     Style="{StaticResource bg_purple_btn}"
                     VerticalAlignment="Center"
                     HorizontalAlignment="Right"
                     Margin="0,0,20,0"
                     Content="Создать"
                     Width="140"
                     Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
