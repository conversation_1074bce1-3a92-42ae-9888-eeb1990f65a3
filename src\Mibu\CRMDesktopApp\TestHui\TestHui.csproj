<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\CRMWeb\CRMWeb\Fiscalizer\Fiscalizer.csproj" />
    <ProjectReference Include="..\..\CRMWeb\CRMWeb\ScalesRecognizer\ScalesRecognizer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="martaDLLLib.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
