﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Parts;
using CRMDesktopApp.Controls.Templates;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Orders
{
    /// <summary>
    /// Логика взаимодействия для ClosedOrdersPage.xaml
    /// </summary>
    public partial class ClosedOrdersPage : BasePage
    {
        public ClosedOrdersPage()
        {
            InitializeComponent();

            Loaded += ClosedOrdersPage_Loaded;

            leftOrdersPanel.SetClosedOrderMode();
        }


        #region Инициализация
        private async void ClosedOrdersPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadOrders();
        }

        public async Task LoadOrders()
        {
            var closedOrders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersByDuty(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id, ApplicationState.CurrentDuty.Id);
            closedOrders = closedOrders.Where(o => o.ClosedAt != null).ToList();
            Orders = new ObservableCollection<Order>(closedOrders);

            if (!Orders.Any())
            {
                noItemsTextBlock.Visibility = Visibility.Visible;
            }

          

            RenderItems();
        }

        private void RenderItems()
        {
            closedOrdersStackLayout.Children.Clear();
            foreach (var order in Orders)
            {
                var view = new ClosedOrderTemplate(order)
                {
                    Height = 43,
                    Margin = new Thickness(0,7,0,0)
                };
                view.Tapped += View_Tapped;
                closedOrdersStackLayout.Children.Add(view);
            }
        }
        #endregion
        private void View_Tapped(object sender, Order e)
        {
            leftOrdersPanel.SetItems(e);
        }

        private ObservableCollection<Order> orders = new ObservableCollection<Order>();
        public ObservableCollection<Order> Orders
        {
            get => orders;
            set { orders = value; OnPropertyChanged(nameof(Orders)); }
        }
    }
}
