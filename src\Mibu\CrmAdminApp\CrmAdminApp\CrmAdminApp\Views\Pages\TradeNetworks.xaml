﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates" 
             xmlns:b="clr-namespace:Corcav.Behaviors;assembly=Corcav.Behaviors" 
             xmlns:extensions="http://xamarin.com/schemas/2020/toolkit"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Views.Pages.TradeNetworks">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid BackgroundColor="#ffffff">
             <Grid.Padding>
                <OnPlatform x:TypeArguments="Thickness">
                    <OnPlatform.Platforms>
                        <On Platform="iOS" Value="0, 28, 0, 0" />
                        <On Platform="Android" Value="0, 0, 0, 0" />
                    </OnPlatform.Platforms>
                </OnPlatform>
            </Grid.Padding>
            <!--<Image
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Aspect="Fill"
                Source="blue_gradient.jpg"/>-->


            <Grid Padding="0,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">
                    <parts:Header x:Name="header"/>
                </Grid>

                <Grid Grid.Row="1">
                    
                <StackLayout
                    Margin="15,0,0,0"
                    Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Logout}"/>
                    </StackLayout.GestureRecognizers>
                    
                    <Image 
                        Source="arrowBack.png"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        HeightRequest="10"
                        WidthRequest="5"/>

                    <Label 
                        HorizontalOptions="End"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        VerticalOptions="Center"
                        Text="Сети продаж"/>
                </StackLayout>

                <Frame
                    Margin="0,0,20,0"
                    VerticalOptions="Center"
                    HorizontalOptions="End"
                    CornerRadius="5"
                    HeightRequest="30"
                    WidthRequest="150"
                    Background="{x:StaticResource purple_gradient}"
                    HasShadow="False"
                    Padding="0">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToCreateNetwork}"/>
                    </Frame.GestureRecognizers>
                    <Grid>
                        <StackLayout 
                            Spacing="5"
                            Orientation="Horizontal">
                            <Image
                                Margin="15,0,0,0"
                                Source="plus.png"
                                HeightRequest="10"
                                WidthRequest="10"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"/>
                            <Label 
                                HorizontalOptions="End"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="White"
                                VerticalOptions="Center"
                                Text="Добавить сеть"/>

                        </StackLayout>
                    </Grid>
                </Frame>
                
                </Grid>


                <Grid Grid.Row="2">

                    <ScrollView
                         Margin="20,0,20,0"
                         Background="#ffffff">
                        <StackLayout x:Name="networksLayout">

                        </StackLayout>
                    </ScrollView>

                    <Label 
                        x:Name="noItemsLabel"
                        IsVisible="False"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="200"
                        Text="На данный момент нет торговых сетей"/>

                </Grid>
                
               
              
            </Grid>
        </Grid>
    </ContentPage.Content>
</ContentPage>