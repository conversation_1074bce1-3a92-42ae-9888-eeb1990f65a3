﻿using CRM.Models.Network;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRM.Models.Reports.Mobile.Admin.Warehouse;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class StockListItem : ContentView
    {
        public static readonly BindableProperty ModelProperty =
          BindableProperty.Create(nameof(Model), typeof(WarehouseReportCategoryGroup), typeof(TradeNetworkExpandableCard));
        public WarehouseReportCategoryGroup Model
        {
            get { return (WarehouseReportCategoryGroup)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        public StockListItem(WarehouseReportCategoryGroup group)
        {
            InitializeComponent();
            Init();

            Model = group;

            RenderRows();
        }

        public StockListItem()
        {
            InitializeComponent();
            Init();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            
            if(BindingContext is WarehouseReportCategoryGroup group)
            {
                Model = group;

                IsExpanded = false;
                BodyContentView.Opacity = 0;
                BodyContentView.IsVisible = false;


                RenderRows();
            }
        }



        private void RenderRows()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                rowsStackLayout.Children.Clear();

                foreach (var row in Model.Rows)
                {
                    var item = new StockProductRowItem(row)
                    {
                        HorizontalOptions = LayoutOptions.FillAndExpand
                    };
                    rowsStackLayout.Children.Add(item);
                }
            });
        }


        private static readonly string ArrowDown = "arrowForward.png";
        private static readonly string ArrowUp = "arrowDown.png";

        private string _expandIcon;

        public static readonly BindableProperty IsExpandedProperty =
            BindableProperty.CreateAttached(nameof(IsExpanded), typeof(bool), typeof(TradeNetworkExpandableCard), false, BindingMode.TwoWay);
        public bool IsExpanded
        {
            get => (bool)GetValue(IsExpandedProperty);
            set => SetValue(IsExpandedProperty, value);
        }



      



 
   




        public string ExpandIcon
        {
            get => _expandIcon;
            set
            {
                _expandIcon = value;
                OnPropertyChanged(nameof(ExpandIcon));
            }
        }


        private void Init()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ExpandIcon = ArrowDown;
                BodyContentView.IsVisible = false;
            });
        }



        private void HeaderContent_OnTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!Model.Rows.Any())
                {
                    return;
                }

                IsExpanded = !IsExpanded;     

                if (!IsExpanded)
                {
                    BodyContentView.Opacity = 0;
                }
                else
                {
                    BodyContentView.Opacity = 1;
                }

             
                ExpandIcon = IsExpanded ? ArrowUp : ArrowDown;
                BodyContentView.IsVisible = IsExpanded;
            });
        }
    }
}