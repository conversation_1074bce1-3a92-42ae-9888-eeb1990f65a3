﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             Background="White"
             BackgroundColor="White"
             x:Class="CRMAdminMoblieApp.WelcomePage">
    <ContentPage.Resources>
        <ResourceDictionary Source="Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid>
            <Image 
                Aspect="Fill"
                Source="main_decor.png"/>


            <StackLayout
                HorizontalOptions="Center"
                Margin="0,150,0,0">

                <Image 
                    WidthRequest="240"
                    HeightRequest="140"
                    Source="logo.png"/>

                <StackLayout
                    HorizontalOptions="Center">
                    <Label 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        TextColor="{x:StaticResource dark_purple}"
                        HorizontalOptions="Center"
                        Text="Добро пожаловать!"/>
                    <Label 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        HorizontalOptions="Center"
                        Text="MIBU помогает систематизировать"/>
                    <Label 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        HorizontalOptions="Center"
                        Text="ваш бизнес"/>

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=GoToAuth}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        Text="Войти"
                        Style="{x:StaticResource purple_gradient_btn}"
                        WidthRequest="240"
                        HeightRequest="40"
                        Margin="0,70,0,0"/>

                    <!--<Button 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        Text="Создать аккаунт"
                        Style="{x:StaticResource bg_purple_btn}"
                        WidthRequest="240"
                        HeightRequest="40"
                        Margin="0,30,0,0"/>-->
                </StackLayout>
                
            </StackLayout>
            
        </Grid>
    </ContentPage.Content>
</ContentPage>