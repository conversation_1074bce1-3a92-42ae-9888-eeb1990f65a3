﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.HallPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages" 
      xmlns:converters="clr-namespace:MarkupCreator.Converters"
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts" 
      xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="HallPage">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary>
                    <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
                    <converters:EnumListToStringsConverter x:Key="EnumListToStringsConverter" />
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <parts:Header 
            Title="{Binding ElementName=this,Path=CurrentHall.Title}"
            Grid.Row="0"/>

        <Grid
            Background="White"
            Grid.Row="1">



            <Grid x:Name="tablesLayout">


            </Grid>

            <StackPanel
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Margin="0,20,20,0">
                <!--<TextBlock 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="Medium"
                        Text="Залы"/>-->
                <editors:ComboBoxEdit   
                    x:Name="hallsComboBox"
                    Margin="0,3,0,0"
                    Background="#F6F6FB"
                    Foreground="{StaticResource dark_purple}"
                    BorderBrush="#F6F6FB"
                    Width="140"
                    Height="40"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Padding="7,0,0,0"
                    IsTextEditable="False"
                    SelectedIndex="0"
                    CornerRadius="15"
                    PopupContentSelectionChanged="onHallSelected"
                    ItemsSource="{Binding ElementName=this,Path=Halls,Mode=TwoWay}" >
                    <editors:ComboBoxEdit.ItemTemplate>
                        <DataTemplate>
                            <Grid Height="40">
                                <TextBlock
                                    Margin="8,8,0,0"
                                    FontFamily="TTFirsNeue-Regular"
                                    Foreground="{StaticResource dark_purple}"
                                    Text="{Binding Title}"/>
                            </Grid>
                        </DataTemplate>
                    </editors:ComboBoxEdit.ItemTemplate>
                </editors:ComboBoxEdit>

            </StackPanel>

        </Grid>

    </Grid>
</abstactions:BasePage>
