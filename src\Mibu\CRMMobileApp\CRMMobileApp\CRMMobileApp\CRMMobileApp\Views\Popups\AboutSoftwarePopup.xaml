﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
        xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
        xmlns:behaviors="http://xamarin.com/schemas/2020/toolkit"
        xmlns="http://xamarin.com/schemas/2014/forms"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="CRMMobileApp.Views.Popups.AboutSoftwarePopup"
        BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
        CloseWhenBackgroundIsClicked="False"
        x:Name="this">
    <Grid>
        <Frame
              HasShadow="{OnPlatform WPF=True, Default=False}"
              Padding="0"
              HorizontalOptions="Center"
              VerticalOptions="{OnPlatform Android=Start, iOS=Start, WPF=Center}"
              BackgroundColor="{DynamicResource bg_purple}"
              Background="{DynamicResource bg_purple}"
              Margin="{OnPlatform Android='0,200,0,0',iOS='0,200,0,0',WPF='0,0,0,0'}"
              CornerRadius="15"
              WidthRequest="590"
              HeightRequest="210">
            <Grid>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="3.5*"/>
                    <ColumnDefinition Width="6.5*"/>
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition Height="7*"/>
                    <RowDefinition Height="3*"/>
                </Grid.RowDefinitions>

                <Grid>
                    <Image
                      HorizontalOptions="Center"
                      VerticalOptions="Center"
                      Source="{OnPlatform Default=logo.png, WPF='pack://application:,,,/Images/logo.png'}"
                      HeightRequest="120"
                      WidthRequest="120"/>
                </Grid>

                <Grid Grid.Column="1">

                    <StackLayout 
                          HorizontalOptions="Start"
                          VerticalOptions="Center">
                        <Label
                          TextColor="{StaticResource text_gray}"
                          FontFamily="TTFirsNeue-Regular"
                          FontSize="10"
                          Text="Домен:"/>
                        <Label
                          x:Name="domainLabel"
                          TextColor="{StaticResource text_gray}"
                          FontFamily="TTFirsNeue-Regular"
                          FontSize="14"
                          Text="cofix-nal.mibu1.ru"/>
                        <Label
                          TextColor="{StaticResource text_gray}"
                          FontFamily="TTFirsNeue-Regular"
                          FontSize="10"
                          Text="Адрес:"/>
                        <Label 
                           x:Name="addressLabel"
                           TextColor="{StaticResource text_gray}"
                           FontFamily="TTFirsNeue-Regular"
                           FontSize="14"
                           Text="XXXXXXXXXXXXXXXXXXXXXX"/>
                    </StackLayout>

                    <ImageButton 
                       Background="Transparent"
                       BackgroundColor="Transparent"
                       Command="{Binding Source={x:Reference this},Path=Close}"
                       Margin="0,10,10,0"
                       HorizontalOptions="End"
                       VerticalOptions="Start"
                       Padding="10"
                       WidthRequest="42"
                       HeightRequest="42"
                       Source="{OnPlatform Default=close_main.png, WPF='pack://application:,,,/Images/close_main.png'}"/>


                </Grid>

                <Grid
                  Grid.ColumnSpan="2"
                  Grid.Row="1">

                    <StackLayout 
                      Margin="60,0,0,0"
                      HorizontalOptions="Start"
                      VerticalOptions="Center"
                      Orientation="Horizontal">
                        <Label
                          TextColor="{StaticResource text_gray}"
                          FontFamily="TTFirsNeue-Regular"
                          FontSize="10"
                          VerticalOptions="Center"
                          Text="Телефон поддержки:"/>
                        <Label 
                           Margin="20,0,0,0"
                           TextColor="{StaticResource text_gray}"
                           FontFamily="TTFirsNeue-Regular"
                           VerticalOptions="Center"
                           FontSize="14"
                           Text="8 XXX XXX XX XX"/>
                    </StackLayout>

                    <Image
                      HorizontalOptions="End"
                      VerticalOptions="Center"
                      Margin="0,0,50,0"
                      Source="{OnPlatform Default=availableOnPlayMarket.png, WPF='pack://application:,,,/Images/availableOnPlayMarket.png'}"
                      HeightRequest="30"
                      WidthRequest="100"/>
                </Grid>



            </Grid>
        </Frame>
    </Grid>
    
   

</animations:PopupPage>