﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{672AA8E7-1A0C-4136-8360-C9FF1867B74E}</ProjectGuid>
    <ProjectTypeGuids>{FEACFBD2-3405-455C-9665-78FE426C6842};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{6143fdea-f3c2-4a09-aafa-6e230626515e}</TemplateGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>CrmAdminApp.iOS</RootNamespace>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <AssemblyName>CrmAdminApp.iOS</AssemblyName>
    <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <ProvisioningType>manual</ProvisioningType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>x86_64</MtouchArch>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchDebug>true</MtouchDebug>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchFloat32>false</MtouchFloat32>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchLink>None</MtouchLink>
    <MtouchArch>x86_64</MtouchArch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchInterpreter>-all</MtouchInterpreter>
    <MtouchI18n>cjk, mideast, other, rare, west</MtouchI18n>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignKey>Apple Distribution: Adam Ibragimov (TMY7WZZK67)</CodesignKey>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <CodesignProvision>com alfateam youmiadmin push appstore</CodesignProvision>
    <MtouchInterpreter>-all</MtouchInterpreter>
    <MtouchI18n>cjk, mideast, other, rare, west</MtouchI18n>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Default' ">
    <AppExtensionDebugBundleId />
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup />
  <ItemGroup>
    <Compile Include="CustomEditorRenderer.cs" />
    <Compile Include="CustomEntryRenderer.cs" />
    <Compile Include="EnhancedButtonRenderer.cs" />
    <Compile Include="Main.cs" />
    <Compile Include="AppDelegate.cs" />
    <Compile Include="TouchableFrameRenderer.cs" />
    <Compile Include="UI\Effects\ScrollBarColorEffect.cs" />
    <Compile Include="UI\Renderers\BorderlessEntryRenderer.cs" />
    <Compile Include="UI\Renderers\BorderlessPickerRenderer.cs" />
    <ImageAsset Include="Assets.xcassets\.DS_Store">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcons.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification1.imageset\active_ach.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification1.imageset\active_level.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification1.imageset\article.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification10.imageset\menuAssignmentIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification10.imageset\menuMessagesSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification10.imageset\menuRatingSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification11.imageset\menuAssignmentSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification11.imageset\menuNotificationsActiveIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification11.imageset\menuNotificationsIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification12.imageset\filterArrowDown.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification12.imageset\filterArrowUp.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification12.imageset\menuMessagesIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification13.imageset\arrowBackGamification.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification13.imageset\gamificationFilter.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification13.imageset\gamificationProfileBack.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification14.imageset\gamificationDeadlineIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification14.imageset\gamificationTaskAddUser.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification14.imageset\gamificationTaskDescription.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification15.imageset\gamificationTaskCoins.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification15.imageset\gamificationTaskKarma.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification15.imageset\gamificationTaskTitle.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification16.imageset\gamificationPostsIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification16.imageset\gamificationTestsIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification16.imageset\gamificationTestsSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification17.imageset\gamificationLevelUpIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification17.imageset\gamificationLevelUpSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification17.imageset\gamificationPostsSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification18.imageset\gamificationSendMessage.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification18.imageset\gamificationTaskIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification18.imageset\gamificationTaskSelectedIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification19.imageset\personGamificationMenuIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification19.imageset\readMsgIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification19.imageset\userAdd.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification2.imageset\attachmentIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification2.imageset\Avatar.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification2.imageset\avatar_2.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification20.imageset\gamificationCheckMarkSet.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification20.imageset\tasksIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification20.imageset\tasksIconActive.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification21.imageset\closeWhite.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification21.imageset\gay_logo.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification3.imageset\best.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification3.imageset\bronze.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification3.imageset\fire.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification4.imageset\gold.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification4.imageset\idea.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification4.imageset\Karma.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification5.imageset\karmas.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification5.imageset\plusIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification5.imageset\send.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification6.imageset\shareIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification6.imageset\silver.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification6.imageset\small_coin.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification7.imageset\small_karma.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification7.imageset\smile.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification7.imageset\star.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification8.imageset\menuProfileIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification8.imageset\menuProfileIconActive.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification8.imageset\searchPurple.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification9.imageset\arrowDownWhiteGamification.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification9.imageset\gamificationIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification9.imageset\menuRatingIcon.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images1.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images10.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images11.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images12.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images13.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images14.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images15.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images16.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images17.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images18.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images19.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images2.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images20.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images21.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images22.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images23.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images24.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification1.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification2.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification3.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification4.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification5.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification6.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification7.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification8.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification9.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification10.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification11.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification12.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification13.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification14.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification15.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification16.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification17.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification18.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification19.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images25.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images25.imageset\userAvatar.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification20.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\gamification21.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images3.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images4.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images5.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images6.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images7.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images8.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\Images9.imageset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <None Include="Entitlements.plist" />
    <BundleResource Include="GoogleService-Info.plist" />
    <None Include="Info.plist" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <InterfaceDefinition Include="Resources\LaunchScreen.storyboard" />
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon1024.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon180.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon167.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon152.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon120.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon87.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon80.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon76.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon60.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon58.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon40.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon29.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon20.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.iOS" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="BottomSheetXF">
      <Version>1.1.0</Version>
    </PackageReference>
    <PackageReference Include="DevExpress.XamarinForms.Editors">
      <Version>22.1.3</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client">
      <Version>6.0.21</Version>
    </PackageReference>
    <PackageReference Include="Plugin.FirebasePushNotification">
      <Version>3.4.35</Version>
    </PackageReference>
    <PackageReference Include="Plugin.XCalendar">
      <Version>2.1.0</Version>
    </PackageReference>
    <PackageReference Include="Sharpnado.CollectionView">
      <Version>2.1.0</Version>
    </PackageReference>
    <PackageReference Include="Sharpnado.MaterialFrame">
      <Version>1.3.0</Version>
    </PackageReference>
    <PackageReference Include="SVGChart.Nuget">
      <Version>1.0.1</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfImageEditor">
      <Version>25.1.37</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfPicker">
      <Version>25.1.37</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfProgressBar">
      <Version>25.1.37</Version>
    </PackageReference>
    <PackageReference Include="System.IO.Pipelines">
      <Version>7.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Buffers">
      <Version>4.5.1</Version>
      <IncludeAssets>none</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Memory">
      <Version>4.5.4</Version>
      <IncludeAssets>none</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xamarin.CommunityToolkit">
      <Version>2.0.1</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2401" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.1" />
    <PackageReference Include="Xamarin.Forms.PancakeView">
      <Version>2.3.0.759</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Plugin.Calendar">
      <Version>2.0.9699</Version>
    </PackageReference>
    <PackageReference Include="XamForms.Controls.Calendar">
      <Version>1.1.1</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\iOS\Xamarin.iOS.CSharp.targets" />
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\MibuGame\SharedGamificationAdmin\App1\App1\App1.csproj">
      <Project>{5D85399D-42F5-4A82-B985-2C5056A69C02}</Project>
      <Name>App1</Name>
    </ProjectReference>
    <ProjectReference Include="..\CrmAdminApp\CrmAdminApp.csproj">
      <Project>{4F686E5B-C618-463A-8B81-0246BBCE3D99}</Project>
      <Name>CrmAdminApp</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\wallet.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\LeftArrow.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\location.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\logo_gray.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\logo_start.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\logo_start_gray.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\logout.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\main_decor.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\networks.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\notifications.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\orders.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\orders_black.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\ordersSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\payment.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\pen.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\photo.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\plus.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\profile.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\profit.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\realTime.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\realTimeSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\report.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\revenue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\sales.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\sales_black.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\salesSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\scheduling.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\scheduling_black.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\settings.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\shadowLine.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\showPassword.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\sign_in_apple.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\sign_in_google.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\stock.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\stock_black.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\stocks.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\stocksSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\unlock.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\visitors_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\incassation_sign.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrowBack.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrowDown.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrowForward.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\average_check_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\blue_gradient.jpg" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\burger.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\by_bonuses_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cafe_bg.jpg" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\calendar.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\calendarCloud.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\calendarViolet.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\calender.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\card.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cash.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cash_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cashless_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cashRegister.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cashRegisterSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cassa.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\cassa_black.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\check.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\check_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\client.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\clock.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\clock_gray.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\close.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\criticalBalance.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Default-568h%402x.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Default-Portrait.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Default-Portrait%402x.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Default.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Default%402x.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\dutyClosing.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\dutyOpening.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\hamburger.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\hidePassword.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\ico.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\icon.png" />
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon1024.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon120.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon152.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon167.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon180.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon20.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon29.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon40.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon58.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon60.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon76.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon80.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\Icon87.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\1024.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\120.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\1201.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\152.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\167.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\180.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\20.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\40.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\401.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\402.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\58.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\581.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\60.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\76.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\80.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\801.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\AppIcon.appiconset\87.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images.imageset\arrowBack.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images.imageset\arrowDown.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images.imageset\arrowForward.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images1.imageset\average_check_blue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images1.imageset\blue_gradient.jpg">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images1.imageset\burger.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images10.imageset\icon.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images10.imageset\LeftArrow.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images10.imageset\location.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images11.imageset\logo.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images11.imageset\logo_gray.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images11.imageset\logo_start.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images12.imageset\logo_start_gray.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images12.imageset\logout.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images12.imageset\main_decor.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images13.imageset\networks.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images13.imageset\notifications.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images13.imageset\orders.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images14.imageset\orders_black.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images14.imageset\payment.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images14.imageset\pen.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images15.imageset\photo.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images15.imageset\plus.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images15.imageset\profile.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images16.imageset\profit.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images16.imageset\realTime.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images16.imageset\report.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images17.imageset\revenue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images17.imageset\sales.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images17.imageset\sales_black.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images18.imageset\scheduling.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images18.imageset\scheduling_black.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images18.imageset\settings.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images19.imageset\showPassword.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images19.imageset\sign_in_apple.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images19.imageset\sign_in_google.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images2.imageset\by_bonuses_blue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images2.imageset\cafe_bg.jpg">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images2.imageset\calendar.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images20.imageset\stock.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images20.imageset\stock_black.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images20.imageset\stocks.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images21.imageset\unlock.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images21.imageset\visitors_blue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images21.imageset\wallet.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images22.imageset\calendarCloud.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images22.imageset\incassation_sign.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images23.imageset\realTimeSelected.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images23.imageset\salesSelected.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images23.imageset\stocksSelected.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images24.imageset\cashRegisterSelected.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images24.imageset\ordersSelected.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images24.imageset\shadowLine.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images3.imageset\calendarViolet.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images3.imageset\calender.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images3.imageset\card.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images4.imageset\cash.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images4.imageset\cash_blue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images4.imageset\cashless_blue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images5.imageset\cashRegister.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images5.imageset\cassa.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images5.imageset\cassa_black.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images6.imageset\check.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images6.imageset\check_blue.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images6.imageset\client.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images7.imageset\clock.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images7.imageset\clock_gray.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images7.imageset\close.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images8.imageset\criticalBalance.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images8.imageset\dutyClosing.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images8.imageset\dutyOpening.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images9.imageset\hamburger.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images9.imageset\hidePassword.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <ImageAsset Include="Assets.xcassets\Images9.imageset\ico.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrow_right.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\active_ach.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\active_level.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\add_circle.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrow_down.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\article.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\attachmentIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Avatar.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\avatar_2.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\best.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\back.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\book.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\bookActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\bronze.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\chart.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\chartActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\coins.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\close.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\deadline.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\down.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\down_arrow.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\edit.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\filter.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\fire.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gold.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\idea.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Karma.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\karmas.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\notificationActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\notification.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menu.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\plusIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\profile.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\profile_back.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\profileActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\readable.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\riseActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\rise.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\send.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\send_message.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\shareIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\star.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\smsActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\sms.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\smile.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\small_karma.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\small_coin.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\silver.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\taskActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\task.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\user.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\up.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\vector.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\vectorActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\searchPurple.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuProfileIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuProfileIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrowDownWhiteGamification.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuAssignmentIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuAssignmentSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuMessagesSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuMessagesIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuNotificationsActiveIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuNotificationsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuRatingIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\menuRatingSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\arrowBackGamification.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\filterArrowDown.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\filterArrowUp.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationFilter.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationDeadlineIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationProfileBack.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskAddUser.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskCoins.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskDescription.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskTitle.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskKarma.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationLevelUpIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationLevelUpSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationPostsSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationPostsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationSendMessage.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTaskSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTestsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationTestsSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\readMsgIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\userAvatar.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\personGamificationMenuIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\userAdd.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\gamificationCheckMarkSet.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\closeWhite.png" />
    <BundleResource Include="Resources\gay_logo.png" />
    <BundleResource Include="Resources\tasksIcon.png" />
    <BundleResource Include="Resources\tasksIconActive.png" />
  </ItemGroup>
</Project>