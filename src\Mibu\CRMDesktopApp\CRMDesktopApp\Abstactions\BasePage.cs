﻿using CRMDesktopApp.Views.Pages;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Navigation;

namespace CRMDesktopApp.Abstactions
{
    public class BasePage : Page, INotifyPropertyChanged
    {

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if(nav != null)
                {
                    if (nav.CanGoBack)
                    {
                        nav.GoBack();
                    }
                    else
                    {
                        nav.Navigate(new LoginPage());
                    }
                }
            });
        }



        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }
    }
}
