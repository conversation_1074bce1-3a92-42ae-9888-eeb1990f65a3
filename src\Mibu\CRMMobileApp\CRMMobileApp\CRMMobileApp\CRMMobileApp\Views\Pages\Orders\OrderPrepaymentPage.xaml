﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CRMMobileApp.Controls;assembly=CRMMobileApp" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts"
             xmlns:effects="http://sharpnado.com"
             xmlns:other="clr-namespace:CRMMobileApp.Controls.Templates.Other"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Orders.OrderPrepaymentPage">
    <ContentPage.Content>
        <Grid BackgroundColor="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="130"/>
            </Grid.RowDefinitions>


            <parts:Header Grid.Row="0"/>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="6*"/>
                    <ColumnDefinition Width="4*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">

                    <StackLayout 
                        VerticalOptions="Center"
                        HorizontalOptions="Center">

                        <Label
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text="Предоплата по заказу"/>



                        <listviews:CustomCrossHorizontalListView
                            x:Name="listView"
                            Margin="0,20,0,0"
                            ItemSpacing="10"
                            ItemWidth="160"
                            ItemHeight="160"
                            TapCommand="{Binding Source={x:Reference this},Path=OnSelectedPaymentType}"
                            WidthRequest="580"
                            VerticalOptions="Start"
                            HeightRequest="220">
                            <listviews:CustomCrossHorizontalListView.ItemTemplateForMobile>
                                <DataTemplate>
                                    <ViewCell>
                                        <other:PaymentTypeView Model="{Binding}"/>
                                    </ViewCell>
                                </DataTemplate>
                            </listviews:CustomCrossHorizontalListView.ItemTemplateForMobile>
                            <listviews:CustomCrossHorizontalListView.ItemTemplateForWPF>
                                <DataTemplate>
                                    <ViewCell>
                                        <other:PaymentTypeView Model="{Binding}"/>
                                    </ViewCell>
                                </DataTemplate>
                            </listviews:CustomCrossHorizontalListView.ItemTemplateForWPF>
                        </listviews:CustomCrossHorizontalListView>


                    </StackLayout>
                  
                </Grid>

                <Grid Grid.Column="1">

                    <Frame 
                        HasShadow="False"
                        CornerRadius="10"
                        BackgroundColor="{StaticResource bg_purple}"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        WidthRequest="330"
                        HeightRequest="420">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="40"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">

                                
                            </Grid>

                            <Grid Grid.Row="0">

                                <controls:AuthKeyboard
                                    x:Name="keyboard"
                                    WidthRequest="300"
                                    HeightRequest="300"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center"/>


                            </Grid>

                            <Grid Grid.Row="1">

                                <FlexLayout
                                    Direction="Row"
                                    AlignItems="Center"
                                    JustifyContent="SpaceAround"
                                    HorizontalOptions="FillAndExpand"
                                    AlignContent="SpaceAround">
                                    <Button 
                                         CornerRadius="10"
                                         Command="{Binding Source={x:Reference this},Path=AddSumBtnPressed}"
                                         CommandParameter="100"
                                         TextColor="{StaticResource dark_purple}"
                                         BackgroundColor="White"
                                         Text="100₽"
                                         Padding="0"
                                         FontSize="12"
                                         TextTransform="None"
                                         WidthRequest="70"
                                         HeightRequest="40"/>
                                    <Button 
                                         CornerRadius="10"
                                         Command="{Binding Source={x:Reference this},Path=AddSumBtnPressed}"
                                         CommandParameter="500"
                                         TextColor="{StaticResource dark_purple}"
                                         BackgroundColor="White"
                                         Text="500₽"
                                         Padding="0"
                                         FontSize="12"
                                         TextTransform="None"
                                         WidthRequest="70"
                                         HeightRequest="40"/>
                                    <Button 
                                         CornerRadius="10"
                                         Command="{Binding Source={x:Reference this},Path=AddSumBtnPressed}"
                                         CommandParameter="1000"
                                         TextColor="{StaticResource dark_purple}"
                                         BackgroundColor="White"
                                         Text="1000₽"
                                         Padding="0"
                                         FontSize="12"
                                         TextTransform="None"
                                         WidthRequest="70"
                                         HeightRequest="40"/>
                                </FlexLayout>
                                
                            </Grid>

                        </Grid>
                    </Frame>
                    
                    
                </Grid>


            </Grid>

            <Grid Grid.Row="2">
                <Button 
                     Command="{Binding Source={x:Reference this},Path=AddPrepayment}"
                     CornerRadius="30"
                     VerticalOptions="Start"
                     HorizontalOptions="Center"
                     TextColor="White"
                     Text="Внести предоплату"
                     FontSize="20"
                     TextTransform="None"
                     Background="{StaticResource purple_gradient}"
                     WidthRequest="280"
                     HeightRequest="60"/>

            </Grid>


        </Grid>
    </ContentPage.Content>
</ContentPage>