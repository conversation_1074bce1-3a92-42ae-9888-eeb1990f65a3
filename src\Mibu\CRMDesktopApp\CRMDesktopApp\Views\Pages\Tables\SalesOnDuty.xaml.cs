﻿using CRM.Models.Reports.Mobile.SalesOnDuty;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Models.Reports;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Tables
{
    /// <summary>
    /// Логика взаимодействия для SalesOnDuty.xaml
    /// </summary>
    public partial class SalesOnDuty : BasePage
    {
        public SalesOnDuty()
        {
            InitializeComponent();
            Loaded += SalesOnDuty_Loaded;
        }
        SalesPerDutyReport _report;
        private async void SalesOnDuty_Loaded(object sender, RoutedEventArgs e)
        {
            _report = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetSalesAtDuty(ApplicationState.CurrentDomain,
                                                                                          ApplicationState.CurrentStore.Id,
                                                                                          ApplicationState.CurrentDuty.Id);

            if (_report is null) return;

            if (!_report.ProductRows.Any() && !_report.TechCardRows.Any())
            {
                noItemsTextBlock.Visibility = Visibility.Visible;
            }

            foreach (var product in _report.ProductRows)
            {
                Rows.Add(new SalesOnDutyRow
                {
                    Amount = product.Amount.ToString(),
                    Sum = product.Sum.ToString(),
                    ProductTitle = product.Product.Title
                });
            }
            foreach (var product in _report.TechCardRows)
            {
                Rows.Add(new SalesOnDutyRow
                {
                    Amount = product.Amount.ToString(),
                    Sum = product.Sum.ToString(),
                    ProductTitle = product.TechnicalCard.Title
                });
            }
        }

        

        private ObservableCollection<SalesOnDutyRow> rows = new ObservableCollection<SalesOnDutyRow>();
        public ObservableCollection<SalesOnDutyRow> Rows
        {
            get => rows;
            set { rows = value; OnPropertyChanged(nameof(Rows)); }
        }
    }
}
