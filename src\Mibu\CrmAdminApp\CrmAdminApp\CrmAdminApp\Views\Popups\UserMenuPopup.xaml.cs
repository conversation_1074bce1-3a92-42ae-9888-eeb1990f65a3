﻿using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMMobileApp.Core;
using MarkupCreator.Helpers.Converters;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System.Linq;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class UserMenuPopup : PopupPage
    {
        public UserMenuPopup()
        {
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                var s = Auth.User.Name;
                var f = Auth.User.Role;

                userNameLabel.Text = $"{Auth.User.Name} {Auth.User.Surname}";
                userRoleLabel.Text = EnumDescriptionHelper.GetDescription(Auth.User.Role);
            });
        }


        private ICommand goToProfile;
        public ICommand GoToProfile
        {
            get => goToProfile ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushAsync(new Profile());
            });
        }

        private ICommand logout;
        public ICommand Logout
        {
            get => logout ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                App.Current.MainPage.Navigation.InsertPageBefore(new MainPage(), App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault());
                await Auth.Logout();
                ApplicationState.SaveChangesToMemory();
            });
        }

    }
}