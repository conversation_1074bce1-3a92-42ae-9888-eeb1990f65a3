﻿<abstractions:AbsAcquiringCard
             xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions"
             x:Class="CRMDesktopApp.Controls.Templates.Devices.Acquiring.UCSCardsAcquringCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Devices.Acquiring"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450" d:DesignWidth="800">
    <abstractions:AbsAcquiringCard.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstractions:AbsAcquiringCard.Resources>
    <Border
        CornerRadius="10"
        Padding="0"
        MouseDown="ToggleExpand"
        Background="{StaticResource bg_purple}">
        <Grid>
            <StackPanel>

                <StackPanel
                    Margin="20,0,0,0"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center">
                    <Image
                        Margin="0,10,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Height="25"
                        Width="25"
                        Source="pack://application:,,,/Resources/Images/pos.png"/>
                    <StackPanel
                         Margin="5,10,0,0">
                        <StackPanel Orientation="Horizontal">
                            <Border
                                x:Name="connectionCircleFrame"
                                Background="Gray"
                                Padding="0"
                                CornerRadius="4"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Height="8"
                                Width="8"/>
                            <TextBlock
                                Margin="6,0,0,0"
                                FontSize="16"
                                VerticalAlignment="Center"
                                Foreground="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                Text="UCS"/>
                        </StackPanel>

                        <TextBlock
                            FontSize="16"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            Text="{Binding ElementName=this,Path=Acquiring.IPAddress}"/>
                    </StackPanel>

                </StackPanel>

                <StackPanel
                    Margin="0,10,0,0"
                    HorizontalAlignment="Center">
                    <Button 
                        Height="30"
                        Command="{Binding ElementName=this,Path=CheckConnection}"
                        Content="Проверка связи"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Height="30"
                        Command="{Binding ElementName=this,Path=ShowShortReport}"
                        Content="Короткий отчет"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Height="30"
                        Command="{Binding ElementName=this,Path=ShowDetailReport}"
                        Content="Полный отчет"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Height="30"
                        Command="{Binding ElementName=this,Path=ReturnMoney}"
                        Content="Возврат денег"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Height="30"
                        Command="{Binding ElementName=this,Path=CancelReturn}"
                        Content="Отмена возврата"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Height="30"
                        Command="{Binding ElementName=this,Path=Incassation}"
                        Content="Инкассация"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>

                </StackPanel>

            </StackPanel>

        </Grid>
    </Border>
</abstractions:AbsAcquiringCard>
