﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.OrderCard">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame 
          Opacity="0.9"
          Padding="0"
          HasShadow="False"
          BackgroundColor="{x:StaticResource bg_purple}"
          CornerRadius="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">

                    <Label 
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Margin="10,10,0,0"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource text_gray}">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span Text="№"/>
                                    <Span Text="{Binding Source={x:Reference this},Path=Model.OrderNumber}"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>


                    <StackLayout
                        Spacing="6"
                        Margin="0,10,10,0"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Orientation="Horizontal">
                        <Image 
                            VerticalOptions="Center"
                            WidthRequest="16"
                            HeightRequest="16"
                            Source="clock_gray.png"/>
                        <Label 
                             x:Name="hoursLabel"
                             VerticalOptions="Center"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="12"
                             TextColor="{x:StaticResource text_gray}"/>
                        <Frame
                            CornerRadius="5"                  
                            BackgroundColor="{x:StaticResource purple}"
                            Padding="5,3,5,3"
                            x:Name="orderStatusFrame"
                            HasShadow="False">
                            <Label 
                                 x:Name="orderStatusLabel"
                                 HorizontalOptions="Center"
                                 VerticalOptions="Center"
                                 FontFamily="TTFirsNeue-Regular"
                                 FontSize="12"
                                 TextColor="White"
                                 Text=""/>
                        </Frame>


                    </StackLayout>


                </Grid>

                <Grid Grid.Row="1">


                    <StackLayout 
                        Margin="10,0,0,0"
                        Orientation="Horizontal">

                        <Label 
                            VerticalOptions="Center"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            MaxLines="1"
                            LineBreakMode="MiddleTruncation"
                            TextColor="{x:StaticResource dark_purple}">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="{Binding Source={x:Reference this},Path=Model.User.Name}"/>
                                        <Span Text=" "/>
                                        <Span Text="{Binding Source={x:Reference this},Path=Model.User.Surname}"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Image 
                            x:Name="paymentTypeIcon"
                            Margin="5,0,0,0"
                            VerticalOptions="Center"
                            WidthRequest="19"
                            HeightRequest="13"
                            Source="cash.png"/>
                    </StackLayout>


                    <Label 
                        Margin="0,0,20,0"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span Text="{Binding Source={x:Reference this},Path=Model.Sum}"/>
                                    <Span Text="₽"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>



                </Grid>

            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>