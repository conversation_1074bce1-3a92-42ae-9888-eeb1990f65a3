﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.CategoryCard">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            CornerRadius="10"
            Padding="0"
            BackgroundColor="{StaticResource bg_purple}">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onCardTapped"/>
            </Frame.GestureRecognizers>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="8*"/>
                    <RowDefinition Height="3*"/>
                </Grid.RowDefinitions>

                <Image 
                    Aspect="Fill"
                    Source="{Binding Source={x:Reference this},Path=Model.Category.ImgPath}"
                    Grid.Row="0"/>
                <Label 
                    x:Name="lettersLabel"
                    Grid.Row="0"
                    FontSize="65"
                    TextTransform="None"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"/>



                <Label 
                    Text="{Binding Source={x:Reference this},Path=Model.Category.Title}"
                    Grid.Row="1"
                    Margin="3,0,3,0"
                    MaxLines="2"
                    LineBreakMode="TailTruncation"
                    FontSize="14"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"/>
            </Grid>
        </Frame>
  </ContentView.Content>
</ContentView>