﻿<?xml version="1.0" encoding="UTF-8"?>
<abstractions:AbsAcquiringCard
             xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.Devices.SberAcquringCard">
    <abstractions:AbsAcquiringCard.Content>
        <Frame
            HasShadow="False"
            CornerRadius="10"
            Padding="0"
            IsClippedToBounds="True"
            BackgroundColor="{StaticResource bg_purple}">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ToggleExpand}"/>
            </Frame.GestureRecognizers>
            <Grid>
                <StackLayout>

                    <StackLayout
                        Margin="20,0,0,0"
                        Spacing="20"
                        Orientation="Horizontal"
                        HorizontalOptions="Start"
                        VerticalOptions="Center">

                        <Image
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            HeightRequest="25"
                            WidthRequest="25"
                            Source="{OnPlatform Default=pos.png, WPF='pack://application:,,,/Images/pos.png'}"/>
                        <StackLayout
                            Spacing="0">
                            <StackLayout Orientation="Horizontal">
                                <Frame
                                    x:Name="connectionCircleFrame"
                                    BackgroundColor="Gray"
                                    Padding="0"
                                    HasShadow="False"
                                    CornerRadius="4"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="8"
                                    WidthRequest="8"/>
                                <Label
                                    FontSize="16"
                                    VerticalOptions="Center"
                                    TextColor="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    Text="Sber"/>
                            </StackLayout>

                            <Label
                                FontSize="16"
                                VerticalOptions="Center"
                                TextColor="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                Text="{Binding Source={x:Reference this},Path=Acquiring.IPAddress}"/>
                        </StackLayout>
                      
                    </StackLayout>

                    <StackLayout
                        Spacing="0"
                        Margin="0,10,0,0"
                        IsVisible="{Binding Source={x:Reference this},Path=IsExpanded}"
                        HorizontalOptions="Center">
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=CheckConnection}"
                            Text="Проверка связи"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=ShowShortReport}"
                            Text="Короткий отчет"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=ShowDetailReport}"
                            Text="Полный отчет"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=ReturnMoney}"
                            Text="Возврат денег"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=CancelReturn}"
                            Text="Отмена возврата"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=Incassation}"
                            Text="Инкассация"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>

                    </StackLayout>

                </StackLayout>
               
            </Grid>
        </Frame>
    </abstractions:AbsAcquiringCard.Content>
</abstractions:AbsAcquiringCard>