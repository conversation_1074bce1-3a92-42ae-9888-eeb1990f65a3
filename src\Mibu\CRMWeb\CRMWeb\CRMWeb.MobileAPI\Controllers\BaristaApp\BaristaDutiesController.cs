﻿using CRM.Database.Core;
using CRM.Models.Enums;
using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Enums.Statuses;
using CRM.Models.Network;
using CRM.Models.Stores;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaDutiesController : AbsController
    {
        public BaristaDutiesController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }
        [HttpGet, Route("GetDuties")]
        public List<Duty> GetDuties(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Duties).ThenInclude(o => o.OpenedBy)
                        .FirstOrDefault(o => o.Id == storeId);

                    var duties = store.Duties.Where(o => !o.IsDeleted).ToList();
                    foreach(var duty in duties)
                    {
						duty.OpenedBy.Store = null;
					}

                    return duties;
				}
                catch { return new List<Duty>(); }
            }
        }
    
        [HttpGet, Route("GetCurrentDuty")]
        public async Task<Duty> GetCurrentDuty(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Duties).ThenInclude(o => o.OpenedBy)
                        .FirstOrDefault(o => o.Id == storeId);

                    var duty = store.Duties.Where(o => !o.IsDeleted).LastOrDefault();
					duty.OpenedBy.Store = null;
                    return duty;
				}
                catch { return null; }
            }
        }


        [HttpGet, Route("OpenDuty")]
        public async Task<Duty> OpenDuty(string domain, int networkId,int storeId,int openedById,double cashInCassa)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.NetworkNotifications)
                        .FirstOrDefault(o => o.Id == networkId);
                    var store = db.Stores
                        .Include(o => o.AccountForCard).ThenInclude(o => o.Account)
                        .Include(o => o.AccountForCash).ThenInclude(o => o.Account)
                        .Include(o => o.CommonStoreSettings)
                        .Include(o => o.Duties).ThenInclude(o => o.OpenedBy)
                        .FirstOrDefault(o => o.Id == storeId);


        


                    network.NetworkNotifications.Add(new NetworkNotification
                    {
                        Category = NotificationCategory.DutyOpening,
                        Description = store.CommonStoreSettings.Title,
                        CreatedAt = DateTime.UtcNow,
                    });
                    db.TradeNetworks.Update(network);


                    store.Transactions.Add(new CRM.Models.Network.Finances.Transaction
                    {
                        TransactionDate = DateTime.UtcNow,
                        Type = TransactionType.Cassa,
                        Operation = FinanceOperation.DutyOpening,
                        Sum = cashInCassa,
                        UserId = openedById,
                        ToAccountId = store.AccountForCash.AccountId,
                        CategoryId = store.AccountForCard.FinanceCategoryId,
                        TradeNetworkId = networkId
                    });

                    store.AccountForCash.Account.Balance += cashInCassa;



                    int number = 1;
                    if (store.Duties.Any())
                        number = store.Duties.Max(o => o.DutyNumber) + 1;

                    var duty = new Duty
                    {
                        OpenedById = openedById,
                        CashInCassaAtOpening = cashInCassa,
                        DutyNumber = number
                    };

                    store.Duties.Add(duty);
                    db.Stores.Update(store);

                    await db.SaveChangesAsync();

                    var newDuty = store.Duties.LastOrDefault();
                    newDuty.OpenedBy.Store = null;

					return newDuty;

				}
                catch { return null; }
            }
        }
     
        [HttpGet, Route("CloseDuty")]
        public async Task<Duty> CloseDuty(string domain, int networkId, int storeId, int dutyId,double sum,string comment)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.NetworkNotifications)
                        .FirstOrDefault(o => o.Id == networkId);
                    var store = db.Stores
                        .Include(o => o.CommonStoreSettings)
                        .Include(o => o.AccountForCard).ThenInclude(o => o.Account)
                        .Include(o => o.AccountForCash).ThenInclude(o => o.Account)
                        .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                        .Include(o => o.Duties).ThenInclude(o => o.OpenedBy)
                        .FirstOrDefault(o => o.Id == storeId);

                    var duty = store.Duties.FirstOrDefault(o => o.Id == dutyId);
                    duty.ClosedAt = DateTime.UtcNow;
                    duty.Comment = comment;
                    duty.SumAtClosing = sum;

  

                    network.NetworkNotifications.Add(new NetworkNotification
                    {
                        Category = NotificationCategory.DutyClosing,
                        Description = store.CommonStoreSettings.Title,
                        CreatedAt = DateTime.UtcNow,
                    });
                    db.TradeNetworks.Update(network);


                    //Транзакции
                    //1-я транзакция - с суммой, указанной пользователем, остальное, по расчетам продаж
                    store.Transactions.Add(new CRM.Models.Network.Finances.Transaction
                    {
                        TransactionDate = DateTime.UtcNow,
                        Type = TransactionType.Cassa,
                        Operation = FinanceOperation.DutyClosing,
                        Comment = comment,
                        ToAccountId = store.AccountForCash.AccountId,
                        CategoryId = store.AccountForCard.FinanceCategoryId,
                        Sum = sum,
                        UserId = duty.OpenedById,
                        TradeNetworkId = networkId
                     });
                    store.AccountForCash.Account.Balance += sum;


                    var orders = store.Orders.Where(o => o.DutyId == dutyId);

                    double sumByCash = orders.Sum(o => o.PaidByCash);
                    store.Transactions.Add(new CRM.Models.Network.Finances.Transaction
                    {
                        TransactionDate = DateTime.UtcNow,
                        Type = TransactionType.Cassa,
                        Operation = FinanceOperation.DutyClosing,
                        ToAccountId = store.AccountForCash.AccountId,
                        CategoryId = store.AccountForCard.FinanceCategoryId,
                        Comment = comment,
                        Sum = sumByCash,
                        UserId = duty.OpenedById,
                        TradeNetworkId = networkId
                    });
                    store.AccountForCash.Account.Balance += sum;

                    double sumByAquiring = orders.Sum(o => o.PaidByCard);
                    store.Transactions.Add(new CRM.Models.Network.Finances.Transaction
                    {
                        TransactionDate = DateTime.UtcNow,
                        Type = TransactionType.Cassa,
                        Operation = FinanceOperation.DutyClosing,
                        ToAccountId = store.AccountForCard.AccountId,
                        CategoryId = store.AccountForCard.FinanceCategoryId,
                        Comment = comment,
                        Sum = sumByAquiring,
                        UserId = duty.OpenedById,
                        TradeNetworkId = networkId
                    });
                    store.AccountForCard.Account.Balance += sum;


                    db.Stores.Update(store);
                    db.Duties.Update(duty);
                    await db.SaveChangesAsync();




                    var closedDuty = store.Duties.LastOrDefault();
					closedDuty.OpenedBy.Store = null;

                    return closedDuty;
				}
                catch (Exception ex) { return new Duty() { Comment = ex.Message + ex.StackTrace}; }
            }
        }
    }
}
