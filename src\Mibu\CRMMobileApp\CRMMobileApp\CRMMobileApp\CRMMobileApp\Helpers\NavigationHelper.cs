﻿using CRM.Models.Enums.General;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Views.Pages.Delivery;
using CRMMobileApp.Views.Pages;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;
using System.Linq;

namespace CRMMobileApp.Helpers
{
    public static class NavigationHelper
    {
        public static async Task GoBackIfCafe(bool goBackIfPoint)
        {
            if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe)
            {
                if (OrdersHelper.FromTable != null && OrdersHelper.CurrentOrder?.Delivery == null)
                {
                    for (int i = App.Current.MainPage.Navigation.NavigationStack.Count - 1; i > -1; i--)
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack[i];
                        if (page is not HallPage)
                        {
                            App.Current.MainPage.Navigation.RemovePage(page);
                        }
                        else
                        {
                            //какая-то хуиня с дроидом
                            if (Device.RuntimePlatform == Device.Android)
                            {
                                await App.Current.MainPage.Navigation.PushAsync(new HallPage(), false);
                            }
                            break;
                        }
                    }
                    OrdersHelper.FromTable = null;
                }
                else if (OrdersHelper.CurrentOrder?.Delivery != null)
                {
                    for (int i = App.Current.MainPage.Navigation.NavigationStack.Count - 1; i > -1; i--)
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack[i];
                        if (page is not DeliveriesPage)
                        {
                            App.Current.MainPage.Navigation.RemovePage(page);
                        }
                        else
                        {
                            //какая-то хуиня с дроидом
                            if (Device.RuntimePlatform == Device.Android)
                            {
                                await App.Current.MainPage.Navigation.PushAsync(new DeliveriesPage(), false);
                            }
                            break;
                        }
                    }
                }
            }
            else
            {
                if (goBackIfPoint)
                {
                    await App.Current.MainPage.Navigation.PopAsync();
                }         
            }
        }

    }
}
