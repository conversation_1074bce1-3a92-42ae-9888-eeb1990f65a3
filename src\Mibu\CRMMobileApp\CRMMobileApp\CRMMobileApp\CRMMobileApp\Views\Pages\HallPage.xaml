﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts" 
             xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" 
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:converters="clr-namespace:MarkupCreator.Converters" 
             xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
             ios:Page.UseSafeArea="true"
             Background="White"
             BackgroundColor="White"
             x:Class="CRMMobileApp.Views.Pages.HallPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
            <converters:EnumListToStringsConverter x:Key="EnumListToStringsConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <parts:Header 
                Title="{Binding Source={x:Reference this},Path=CurrentHall.Hall.Title}"
                Grid.Row="0"/>

            <Grid
                Background="White"
                BackgroundColor="White"
                Grid.Row="1">

                

                <Grid x:Name="tablesLayout">
                    
                    
                </Grid>

                <StackLayout
                    VerticalOptions="Start"
                    HorizontalOptions="End"
                    Margin="0,20,20,0">
                    <!--<Label 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"
                        Text="Залы"/>-->
                    <editors1:CustomCrossComboBoxEdit  
                            x:Name="hallsComboBox"
                            Margin="0,3,0,0"
                            BackgroundColor="#F6F6FB"
                            TextColor="{StaticResource dark_purple}"
                            BorderColor="#F6F6FB"
                            WidthRequest="240"
                            HeightRequest="40"
                            TextFontFamily="TTFirsNeue-Regular"
                            HorizontalOptions="Start"
                            VerticalOptions="Start"
                            CornerRadius="15"
                            PlaceholderText="Пол"
                            PlaceholderColor="#9795B1"
                            SelectionChanged="onHallSelected"
                            ItemsSource="{Binding Source={x:Reference this},Path=Halls,Mode=TwoWay}" >
                        <!--<editors:ComboBoxEdit.ItemTemplate>
                            <DataTemplate>
                                <Grid HeightRequest="40">
                                    <Label
                                        Margin="8,8,0,0"
                                        FontFamily="TTFirsNeue-Regular"
                                        TextColor="{StaticResource dark_purple}" Text="{Binding Hall.Title}"/>
                                </Grid>
                            </DataTemplate>
                        </editors:ComboBoxEdit.ItemTemplate>-->
                    </editors1:CustomCrossComboBoxEdit>

                    <Label
                      WidthRequest="240"
                      HorizontalOptions="Start"
                      VerticalOptions="Start"
                      TextColor="{StaticResource dark_purple}"
                      FontFamily="TTFirsNeue-Regular"
                      FontSize="16"
                      Text="{Binding Source={x:Reference this},Path=CurrentHall.Hall.Description}"/>

                </StackLayout>
                
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>