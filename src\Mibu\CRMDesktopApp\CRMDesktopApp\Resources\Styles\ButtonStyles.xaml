﻿
<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Fonts.xaml"></ResourceDictionary>
    </ResourceDictionary.MergedDictionaries>

    
    <Style TargetType="Button" x:Key="transparent_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        
        <Setter Property="Foreground" Value="#524E7D"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="0" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="bg_purple_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        <Setter Property="BorderBrush" Value="#F6F6FB"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="#F6F6FB"/>
        <Setter Property="Background" Value="#7265FB"/>
        
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border CornerRadius="20" 
                        Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="0" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style TargetType="Button" x:Key="purple_gradient_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="1,0">
                    <GradientStop Color="#9A90FF"
                          Offset="0.1" />
                    <GradientStop Color="#594DD2"
                          Offset="1.0" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="auth_blue_rounded_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Medium}"/>
        
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="#524E7D"/>
        <Setter Property="FontSize" Value="36"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="0" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="auth_white_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Medium}"/>
        
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="36"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="0" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>







    <Style TargetType="Button" x:Key="white_rounded_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="BorderBrush" Value="White"/>
        <Setter Property="Foreground" Value="#05AEF1"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="white_rounded_light_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="BorderBrush" Value="White"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="black_rounded_light_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="FontSize" Value="35"/>
        <Setter Property="BorderBrush" Value="#e5e5e5"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="35" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="button_back">
        <Setter Property="Background" Value="Transparent"></Setter>
        
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="0" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="blue_cornered_filled_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="Background" Value="#283651"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style TargetType="Button" x:Key="green_cornered_filled_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="Background" Value="#6CB86D"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="gray_cornered_filled_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="Background" Value="Gray"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="blue_bordered_btn">
        <Setter Property="FontFamily" Value="{StaticResource TTFirsNeue-Regular}"/>
        
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="#333B4E"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border 
                        CornerRadius="20" 
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="{TemplateBinding BorderThickness}" >
                        <ContentPresenter
                            x:Name="contentPresenter" 
                            ContentTemplate="{TemplateBinding ContentTemplate}" 
                            Content="{TemplateBinding Content}" 
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Margin="{TemplateBinding Padding}" 
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    
</ResourceDictionary>