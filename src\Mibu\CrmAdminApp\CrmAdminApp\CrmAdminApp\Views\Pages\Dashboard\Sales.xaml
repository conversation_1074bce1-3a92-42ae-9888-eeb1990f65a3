﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:BaseDashboardPage
             xmlns:abstractions="clr-namespace:CRMAdminMoblieApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Views.Pages.Dashboard.Sales"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             ios:Page.UseSafeArea="true"
             xmlns:xct="http://xamarin.com/schemas/2020/toolkit" 
             Background="#ffffff"
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts" xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview">
    <abstractions:BaseDashboardPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </abstractions:BaseDashboardPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="160" />
                <RowDefinition Height="*" />
                <RowDefinition Height="80" />
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">
                <parts:Header x:Name="header"/>
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout Spacing="0">


                    <Frame
                        Margin="20,20,20,0"
                        HasShadow="False"
                        CornerRadius="5"
                        Background="{x:StaticResource purple_gradient}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Padding="0"
                        HeightRequest="40">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenDutyPopup}"/>
                        </Frame.GestureRecognizers>
                        <Grid>
                            <StackLayout
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,21,0"
                                Spacing="21"
                                Orientation="Horizontal">

                                <Label 
                                     x:Name="dutyLabel"
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     TextColor="White"
                                     VerticalOptions="Center"
                                     Text=""/>
                                <Image 
                                    WidthRequest="22"
                                    HeightRequest="22"
                                    Source="calendar.png"/>

                            </StackLayout>
                        </Grid>
                    </Frame>



                    <Grid 
                        Margin="20,20,20,0"
                        HorizontalOptions="Fill"
                        HeightRequest="30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="4*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                        </Grid.ColumnDefinitions>

                        <Label 
                             Grid.Column="0"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             TextColor="{x:StaticResource dark_purple}"
                             VerticalOptions="Center"
                             Text="Товар"/>


                        <Label 
                             Grid.Column="1"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             TextColor="{x:StaticResource dark_purple}"
                             VerticalOptions="Center"
                             Text="Цена"/>

                        <Label 
                             Grid.Column="2"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             TextColor="{x:StaticResource dark_purple}"
                             VerticalOptions="Center"
                             Text="Кол-во"/>

                        <Label 
                             Grid.Column="3"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             TextColor="{x:StaticResource dark_purple}"
                             VerticalOptions="Center"
                             Text="Сумма"/>


                    </Grid>

                    <Frame
                        Margin="20,20,20,0"
                        HasShadow="False"
                        CornerRadius="5"
                        Background="{x:StaticResource green}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Padding="0"
                        HeightRequest="40">
                        <Grid>
                            <Label 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="White"
                                Margin="10,0,0,0"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Text="Итого"/>
                            <Label 
                                x:Name="totalSumLabel"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="White"
                                Margin="0,0,10,0"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Text=""/>
                        </Grid>
                    </Frame>
                </StackLayout>

            </Grid>

            <Grid Grid.Row="2">
                <collectionview:DXCollectionView 
                    x:Name="list"
                    Margin="20,10,20,0"
                    HorizontalOptions="Fill"
                    SelectionMode="None"
                    MinItemSize="38"
                    IsScrollBarVisible="False"
                    VerticalOptions="Fill"
                    Background="#ffffff"
                    BackgroundColor="#ffffff"
                    ItemsSource="{Binding Source={x:Reference this},Path=Rows,Mode=TwoWay}">
                    <collectionview:DXCollectionView.ItemTemplate>
                        <DataTemplate>
                            <itemtemplates:SaleListItem
                                HorizontalOptions="Fill"
                                Model="{Binding}"
                                Margin="0,0,0,0"/>
                        </DataTemplate>
                    </collectionview:DXCollectionView.ItemTemplate>
                </collectionview:DXCollectionView>

                <Label 
                    x:Name="noItemsLabel"
                    IsVisible="False"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"
                    TextColor="{x:StaticResource dark_purple}"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Text="На данный момент нет продаж"/>

            </Grid>


            <Grid Grid.Row="3">
                <parts:Footer IsSalesPage="True"/>
            </Grid>

        </Grid>
    </ContentPage.Content>
</abstractions:BaseDashboardPage>