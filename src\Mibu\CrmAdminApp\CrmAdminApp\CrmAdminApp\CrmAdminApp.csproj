﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>
    <ProduceReferenceAssemblyInOutDir>true</ProduceReferenceAssemblyInOutDir>
    <LangVersion>latest</LangVersion>
<!--    <BuildWithMSBuildOnMono>true</BuildWithMSBuildOnMono>-->
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Bottom\SelectProfile.xaml.cs" />
    <Compile Remove="Bottom\SelectProfileWithIcons.xaml.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="Bottom\SelectProfile.xaml" />
    <EmbeddedResource Remove="Bottom\SelectProfileWithIcons.xaml" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Fonts\noto_sans_bold.ttf" />
    <None Remove="Fonts\noto_sans_light.ttf" />
    <None Remove="Fonts\noto_sans_medium.ttf" />
    <None Remove="Fonts\noto_sans_regular.ttf" />
    <None Remove="Fonts\TTFirsNeue-DemiBold.ttf" />
    <None Remove="Fonts\TTFirsNeue-Light.ttf" />
    <None Remove="Fonts\TTFirsNeue-Medium.ttf" />
    <None Remove="Fonts\TTFirsNeue-Regular.ttf" />
    <None Remove="Resources\Fonts\noto_sans_bold.ttf" />
    <None Remove="Resources\Fonts\noto_sans_light.ttf" />
    <None Remove="Resources\Fonts\noto_sans_medium.ttf" />
    <None Remove="Resources\Fonts\noto_sans_regular.ttf" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Fonts\noto_sans_bold.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_light.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_medium.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_regular.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-DemiBold.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-Light.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-Medium.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-Regular.ttf" />
  </ItemGroup>

    <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.21" />
    <PackageReference Include="Plugin.FirebasePushNotification" Version="3.4.35" />
    <PackageReference Include="RestSharp" Version="106.12.0" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="Alfateam.Plugins.Popup" Version="2.1.0" />
    <PackageReference Include="BottomSheetXF" Version="1.1.0" />
    <PackageReference Include="Corcav.Behaviors" Version="2.3.7" />
    <PackageReference Include="DevExpress.XamarinForms.Editors" Version="22.1.3" />
    <PackageReference Include="Plugin.XCalendar" Version="2.1.0" />
    <PackageReference Include="Sharpnado.CollectionView" Version="2.1.0" />
    <PackageReference Include="Sharpnado.MaterialFrame" Version="1.3.0" />
    <PackageReference Include="SVGChart.Nuget" Version="1.0.1" />
    <PackageReference Include="Syncfusion.Xamarin.SfImageEditor" Version="25.1.37" />
    <PackageReference Include="Syncfusion.Xamarin.SfPicker" Version="25.1.37" />
    <PackageReference Include="Syncfusion.Xamarin.SfProgressBar" Version="25.1.37" />
    <PackageReference Include="Xamarin.CommunityToolkit" Version="2.0.1" />
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2401" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.1" />
    <PackageReference Include="Xamarin.Forms.PancakeView" Version="2.3.0.759" />
    <PackageReference Include="Xamarin.Plugin.Calendar" Version="2.0.9699" />
    <PackageReference Include="XamForms.Controls.Calendar" Version="1.1.1" />
	  <PackageReference Include="System.Buffers">
		  <Version>4.5.1</Version>
		  <IncludeAssets>none</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="System.Memory">
		  <Version>4.5.4</Version>
		  <IncludeAssets>none</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\MibuGame\Gamification\CRM.Models.GamificationMobileAPI\CRM.Models.GamificationMobileAPI.csproj" />
    <ProjectReference Include="..\..\..\..\MibuGame\Gamification\CRM.Models.Gamification\CRM.Models.Gamification.csproj" />
    <ProjectReference Include="..\..\..\..\MibuGame\Gamification\CRMGamificationAPIWrapper\CRMGamificationAPIWrapper.csproj" />
    <ProjectReference Include="..\..\..\..\MibuGame\SharedGamificationAdmin\App1\App1\App1.csproj" />
    <ProjectReference Include="..\..\..\..\MibuShared\CRM.Models.Core\CRM.Models.Core.csproj" />
    <ProjectReference Include="..\..\..\..\MibuShared\CRM.Models.Reports.Mobile\CRM.Models.Reports.Mobile.csproj" />
    <ProjectReference Include="..\..\..\..\MibuShared\CRM.Models\CRM.Models.csproj" />
    <ProjectReference Include="..\..\..\..\MibuShared\CRMMoblieApiWrapper\CRMMoblieApiWrapper.csproj" />
    <ProjectReference Include="..\..\..\..\MibuShared\Mibu.Landing.Models\Mibu.Landing.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="SplashPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Bottom\" />
    <Folder Include="Resources\Fonts\" />
    <Folder Include="Views\Popups\Common\" />
  </ItemGroup>
</Project>