﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Devices.PosTerminalCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Devices"
             x:Name="this"
             mc:Ignorable="d" 
             d:DesignHeight="150"
             d:DesignWidth="150">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        CornerRadius="10"
        Padding="0"
        Background="{StaticResource bg_purple}">
        <Grid>
            <StackPanel
                Margin="20,0,0,0"
                Orientation="Horizontal"
                HorizontalAlignment="Left"
                VerticalAlignment="Center">
                <Image
                    Margin="20,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Height="25"
                    Width="25"
                    Source="pack://application:,,,/Resources/Images/pos.png"/>
                <TextBlock
                    Margin="20,0,0,0"
                    FontSize="14"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    Text="127.0.0.1"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
