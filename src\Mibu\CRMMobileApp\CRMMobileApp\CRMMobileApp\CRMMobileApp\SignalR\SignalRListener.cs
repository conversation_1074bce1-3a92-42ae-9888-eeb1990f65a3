﻿using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;

namespace SH.Forms.Services
{
    public class SignalRListener
    {


        protected virtual void OnHubCreated()
        {

        }

        protected virtual void OnHubDisposing()
        {

        }

        protected virtual void OnFailedStartingHub()
        {
            Connected = false;
        }

        protected virtual void OnConnected()
        {
            Connected = true;
        }

        protected virtual void OnDisconnected()
        {

        }

        private bool lockInit;
        public async Task<bool> InitAgainAndStart()
        {
            if (string.IsNullOrEmpty(_url) || string.IsNullOrEmpty(_hubname) || lockInit)
                return false;

            lockInit = true;

            try
            {
                await InitAndStart(_url, _hubname, _tenantId, _deviceUid, _token, _lang, _profile);
                return true;

            }
            catch (Exception e)
            {
                Console.WriteLine(e);

            }
            finally
            {
                lockInit = false;
            }

            return false;
        }

        public async Task InitAndStart(string url, string hubname, string tenantId, string deviceUid, string token, string lang, string profile)
        {
            bool needKill = _lang != lang
                            || _url != url
                            || _hubname != hubname ||
                            _tenantId != tenantId ||
                            _deviceUid != deviceUid ||
                            _token != token ||
                            _profile != profile;

            _lang = lang;
            _url = url;
            _hubname = hubname;
            _tenantId = tenantId;
            _deviceUid = deviceUid;
            _token = token;
            _profile = profile;


            if (needKill || Hub == null)
            {
                if (Hub != null)
                {
                    // Clean up previous connection
                    Hub.Closed -= OnDisconnected;
                }

                await KillHubAsync();
                await Task.Delay(1500);
                await CreateHubAsync();

            }
            else
            {
                if (Hub != null && Hub.State == HubConnectionState.Connected)
                {
                    return;
                }
            }

            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " + $"Starting..");
            await StartAsync();
        }

        protected async Task CreateHubAsync()
        {

            var fullUrl = BuildQueryString(_url);

            Hub = new HubConnectionBuilder()
                .WithUrl(fullUrl, options =>
                {
                    options.HttpMessageHandlerFactory = (message) =>
                    {
                        if (message is HttpClientHandler clientHandler)
                            // always verify the SSL certificate
                            clientHandler.ServerCertificateCustomValidationCallback +=
                                (sender, certificate, chain, sslPolicyErrors) => { return true; };
                        return message;
                    };

                    options.HttpMessageHandlerFactory = factory => new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; }
                    };

                    options.Headers["Authorization"] = "Bearer " + _token;

                    options.CloseTimeout = TimeSpan.FromSeconds(30);

                    //options.AccessTokenProvider = () => Task.FromResult(_token);
                    //options.Transports = HttpTransportType.WebSockets |
                    //                     HttpTransportType.LongPolling;

                    //options.HttpMessageHandlerFactory = (message) =>
                    //{
                    //    if (message is HttpClientHandler clientHandler)
                    //        // bypass SSL certificate
                    //        clientHandler.ServerCertificateCustomValidationCallback +=
                    //            (sender, certificate, chain, sslPolicyErrors) =>
                    //            {
                    //                return true;
                    //            };
                    //    return message;
                    //};
                })

                //     .WithAutomaticReconnect() fuck this bugged shit, we'll do it manually as inbefore

                ////                .WithConsoleLogger()
                ////            .WithMessagePackProtocol()
                ////              .WithTransport(TransportType.WebSockets)
                /// 
#if DEBUG
                .ConfigureLogging(logging =>
                {
                    // Log to the Console
                    logging.AddConsole();
                    // This will set ALL logging to Debug level
                    logging.SetMinimumLevel(LogLevel.Error);
                })
#endif
                .Build();


            Hub.Closed += OnDisconnected;

            Hub.Reconnecting += OnReconnecting;

            Hub.Reconnected += OnReconnected;

            OnHubCreated();

            //            Hub.StateChanged += OnStateChanged;
            //            Proxy = Hub.CreateHubProxy(_hubname);
            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Created new hub.");



            return;
        }

        private bool killingHub;
        protected string _profile;

        protected async Task KillHubAsync()
        {
            if (Hub != null && !killingHub)
            {
                killingHub = true;

                try
                {
                    await Hub.StopAsync();

                    Hub.Closed -= OnDisconnected;
                    Hub.Reconnecting -= OnReconnecting;
                    Hub.Reconnected -= OnReconnected;

                    OnHubDisposing();

                    await Hub.DisposeAsync();

                }
                catch (Exception e)
                {
                    Debug.WriteLine(e.ToString());
                }
                finally
                {
                    killingHub = false;
                }

                Hub = null;
            }
        }

        ///--------------
        /// 
        //private async Task ConnectToServer()
        //{
        //    // keep trying until we manage to connect
        //    while (true)
        //    {
        //        try
        //        {
        //            await CreateHubConnection();
        //            await this.Connection.StartAsync();
        //            return; // yay! connected
        //        }
        //        catch (Exception e) { /* bugger! */}
        //    }
        //}


        /// --------------

        public async Task StartAsync()
        {

            // If this fails, the 'Closed' event (OnDisconnected) is fired
            try
            {
                Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                                $"Starting hub...");
                await Hub.StartAsync();

                Debug.Write("[SignalR]: HUB STARTED OK");
                OnConnected();
            }
            catch (Exception e)
            {

                //#if DEBUG
                //                App.ShowToast("SIGNALR ERROR");
                //                return;
                //#endif

                // !Connected
                OnFailedStartingHub();
                Console.WriteLine($"[SignalR]: {e}");

                try
                {
                    var mess = e.GetInnerExceptionsMessages().ToTags();
                }
                catch (Exception exception)
                {
                    Console.WriteLine(exception);
                }

                //    await OnDisconnected(e);
                return;
            }


            // Connected => re-subscribe to groups etc.
        }

        public HubConnection Hub { get; private set; }

        //public IHubProxy Proxy { get; private set; }

        protected string _url { get; set; }
        private string _lang { get; set; }
        protected string _tenantId { get; set; }
        private string _deviceUid { get; set; }
        private string _hubname { get; set; }
        protected string _token { get; set; }


        public void SetTenant(string tenantId)
        {
            _tenantId = tenantId;
        }

        public void SetLang(string lang)
        {
            _lang = lang;
        }

        private Dictionary<string, string> CreateQuerystringData()
        {
            var querystringData = new Dictionary<string, string>();
            querystringData.Add("tenantId", _tenantId);
            querystringData.Add("lang", _lang);
            querystringData.Add("deviceUid", _deviceUid);
            querystringData.Add("InProfile", _profile);
            return querystringData;
        }

        protected string BuildQueryString(string url)
        {
            var builder = new UriBuilder(url);
            var query = HttpUtility.ParseQueryString(builder.Query);
            query["tenantId"] = _tenantId;
            query["lang"] = _lang;
            query["deviceUid"] = _deviceUid;
            query["InProfile"] = _profile;
            builder.Query = query.ToString();
            return builder.ToString();
        }

        protected string BuildQueryString(string url, Dictionary<string, string> parameters)
        {
            var builder = new UriBuilder(url);
            var query = HttpUtility.ParseQueryString(builder.Query);
            foreach (var row in parameters)
            {
                query[row.Key] = row.Value;
            }
            builder.Query = query.ToString();
            return builder.ToString();
        }


        //-----------------------------------------------------------------
        public bool Connected
        //-----------------------------------------------------------------
        {
            get;
            protected set;
        }

        ////-----------------------------------------------------------------
        //private void OnStateChanged(StateChange obj)
        ////-----------------------------------------------------------------
        //{
        //    Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
        //                    $"State changed to {obj.NewState}..");

        //    if (obj.NewState == ConnectionState.Connected)
        //    {
        //        OnSignalRConnected.Invoke(this, null);
        //        Connected = true;
        //        return;
        //    }
        //    Connected = false;
        //}


        private async Task OnReconnected(string arg)
        {
            Connected = true;
            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Reconnected..");

            await Task.Run(() =>
            {
                OnConnected();
            }).ConfigureAwait(false);
        }

        private async Task OnReconnecting(Exception arg)
        {
            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Reconnecting!.");
            Connected = false;
            OnDisconnected();
        }

        protected bool lockReconnecting;

        protected bool needReconnect;


        private async Task OnDisconnected(Exception arg)
        {
            Connected = false;

            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Disconnected");

            if (lockReconnecting)
            {
                return;
            }

            if (!needReconnect)
            {
                OnDisconnected();
                return;
            }

            await Task.Run(async () =>
            {
                lockReconnecting = true;
                try
                {
                    // Small delay before retrying connection

                    await Task.Delay(500);

                    // Need to recreate connection
                    Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                                    $"Reconnecting...");

                    await InitAgainAndStart();

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    lockReconnecting = false;
                }

            }).ConfigureAwait(false); ;


        }
    }
}