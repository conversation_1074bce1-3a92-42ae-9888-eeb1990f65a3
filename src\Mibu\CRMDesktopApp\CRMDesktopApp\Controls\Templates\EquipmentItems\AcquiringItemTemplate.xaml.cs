﻿using CRM.Models.Stores.Settings.Equipment;
using CRMMobileApp.Core;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using AcquiringTerminal = CRM.Models.Stores.Settings.Equipment.Acquiring;

namespace CRMDesktopApp.Controls.Templates.Devices
{
    /// <summary>
    /// Логика взаимодействия для PrinterCard.xaml
    /// </summary>
    public partial class AcquiringItemTemplate : UserControl
    {

        public static readonly DependencyProperty AcquiringProperty =
          DependencyProperty.Register(nameof(Acquiring), typeof(AcquiringTerminal), typeof(AcquiringItemTemplate));
        public AcquiringTerminal Acquiring
        {
            get { return (AcquiringTerminal)GetValue(AcquiringProperty); }
            set { SetValue(AcquiringProperty, value); }
        }

        public AcquiringItemTemplate(AcquiringTerminal acquiring)
        {
            InitializeComponent();
            Acquiring = acquiring;

            providerLabel.Text = EnumDescriptionHelper.GetDescription(Acquiring.AcquiringProvider);
        }




        public event EventHandler<AcquiringTerminal> ItemTapped;
        private void onItemTapped(object sender, MouseButtonEventArgs e)
        {
            ItemTapped?.Invoke(this, Acquiring);
        }

    }
}
