﻿
using CRM.Models.Stores;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderCard : ContentView
    {
        public OrderCard(Order order)
        {
            InitializeComponent();

            Model = order;
            Render();
        }

        public OrderCard()
        {
            InitializeComponent();
        }


        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if(BindingContext is Order order)
            {
                Model = order;
                Render();
            }
        }

        private void Render()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (Model.PaymentType == CRM.Models.Enums.PaymentTypeEnum.NotPaid)
                    paymentTypeIcon.Source = null;
                if (Model.PaymentType == CRM.Models.Enums.PaymentTypeEnum.Cash)
                    paymentTypeIcon.Source = ImageSource.FromFile("cash.png");
                else if (Model.PaymentType == CRM.Models.Enums.PaymentTypeEnum.Card)
                    paymentTypeIcon.Source = ImageSource.FromFile("card.png");


                hoursLabel.Text = Model.OpenedAt.ToString("HH:mm");


                if (Model.IsCanceled)
                {
                    orderStatusFrame.BackgroundColor = Color.FromHex("#7265FB");
                    orderStatusLabel.TextColor = Color.FromHex("#FFFFFF");
                    orderStatusLabel.Text = "Отменен";
                }
                else if (Model.ReturnDate != null)
                {
                    orderStatusFrame.BackgroundColor = Color.FromHex("#7265FB");
                    orderStatusLabel.TextColor = Color.FromHex("#FFFFFF");
                    orderStatusLabel.Text = "Возвращен";
                }
                else if (!Model.ClosedAt.HasValue)
                {
                    orderStatusFrame.BackgroundColor = Color.FromHex("#DF8534");
                    orderStatusLabel.TextColor = Color.FromHex("#FFFFFF");
                    orderStatusLabel.Text = "Не оплачен";
                }
                else
                {
                    orderStatusFrame.BackgroundColor = Color.FromHex("#26E27C");
                    orderStatusLabel.TextColor = Color.FromHex("#FFFFFF");
                    orderStatusLabel.Text = "Оплачен";
                }
            });
        }






        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(Order), typeof(OrderCard));
        public Order Model
        {
            get { return (Order)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}