﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.CountSelections.AcquringWaitingPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.CountSelections" 
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls" 
        xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        mc:Ignorable="d"
        Title="Ожидание оплаты"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="410"
        Height="230">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="400"
        Height="225"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="70"/>
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">

                <StackPanel
                    Margin="15,30,0,0"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal">

                    <!--<Image 
                        Margin="25,0,0,0"
                        Aspect="Fill"
                        Source="client.png"
                        WidthRequest="30"
                        HeightRequest="30"
                        VerticalOptions="Center"/>-->

                    <TextBlock
                        Margin="15,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Оплата картой"/>

                </StackPanel>



                <controls:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,35,35,0"/>



            </Grid>


            <StackPanel Grid.Row="1">

                <TextBlock
                    x:Name="sumLabel"
                    Margin="35,0,0,0"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="16">
                    <Run Text="Сумма: "/>
                    <Run Text="{Binding ElementName=this,Path=Sum}"/>
                    <Run Text=" руб."/>
                </TextBlock>
                <TextBlock
                    Margin="35,5,0,0"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="16"
                    Text="Ожидание ответа"/>

            </StackPanel>

            <Grid Grid.Row="2">

                <Button 
                     Command="{Binding ElementName=this,Path=ContinueWithoutAquiring}"
                     Style="{StaticResource bg_purple_btn}"
                     VerticalAlignment="Center"
                     HorizontalAlignment="Left"
                     Margin="20,0,0,0"
                     Content="Продолжить без экваринга"
                     Width="250"
                     Height="40"/>

                <Button 
                     Command="{Binding ElementName=this,Path=CloseWindow}"
                     Style="{StaticResource bg_purple_btn}"
                     VerticalAlignment="Center"
                     HorizontalAlignment="Right"
                     Margin="0,0,20,0"
                     Content="Отмена"
                     Width="100"
                     Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
