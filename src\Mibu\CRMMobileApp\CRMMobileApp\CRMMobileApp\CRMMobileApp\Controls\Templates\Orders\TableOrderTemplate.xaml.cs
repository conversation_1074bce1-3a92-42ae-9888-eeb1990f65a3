﻿using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TableOrderTemplate : ContentView
    {
        public static readonly BindableProperty OrderProperty =
            BindableProperty.Create(nameof(Order), typeof(Order), typeof(TableOrderTemplate));
        public Order Order
        {
            get { return (Order)GetValue(OrderProperty); }
            set { SetValue(OrderProperty, value); }
        }

        public TableOrderTemplate(Order order)
        {
            InitializeComponent();
            Order = order;
            InitControl();
        }

        private void InitControl()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                orderNumberLabel.Text = $"№ {Order.OrderNumber}";


                if (Order.Items.Count == 0)
                {
                    orderItemsLabel.Text = "Не выбрано ни одной позиции";
                }
                else
                {
                    List<string> titles = new List<string>();
                    foreach (var item in Order.Items)
                    {
                        if (item.TechnicalCard != null)
                        {
                            titles.Add(item.TechnicalCard.Title);
                        }
                        else if (item.Product != null)
                        {
                            titles.Add(item.Product.Title);
                        }
                    }
                    orderItemsLabel.Text = string.Join(", ", titles);
                }


                orderTimeLabel.Text = $"{Order.OpenedAtLocalAuto.ToString("HH:mm")}";
                orderWaiterLabel.Text = $"{Order.User.Name} {Order.User.Surname}";
                orderPriceLabel.Text = $"{Math.Round(Order.Sum, 2)} Р";
            });
        }




        public event EventHandler<Order> ItemTapped;
        private void onItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ItemTapped?.Invoke(this, Order);
            });
        }
    }
}