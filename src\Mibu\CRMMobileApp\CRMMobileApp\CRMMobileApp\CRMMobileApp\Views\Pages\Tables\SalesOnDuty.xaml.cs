﻿using CRM.Models.Network;
using CRM.Models.Reports.Mobile.SalesOnDuty;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Models.Reports;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Pages.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SalesOnDuty : ContentPage
    {
        public SalesOnDuty()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        SalesPerDutyReport _report;
        protected override void OnAppearing()
        {
            base.OnAppearing();

            Device.InvokeOnMainThreadAsync(async () =>
            {
                _report = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetSalesAtDuty(ApplicationState.CurrentDomain,
                                                                                              ApplicationState.CurrentStore.Id,
                                                                                              ApplicationState.CurrentDuty.Id);
                foreach (var product in _report.ProductRows)
                {
                    Rows.Add(new SalesOnDutyRow
                    {
                        Amount = product.Amount.ToString(),
                        Sum = product.Sum.ToString(),
                        ProductTitle = product.Product.Title
                    });
                }
                foreach (var product in _report.TechCardRows)
                {
                    Rows.Add(new SalesOnDutyRow
                    {
                        Amount = product.Amount.ToString(),
                        Sum = product.Sum.ToString(),
                        ProductTitle = product.TechnicalCard.Title
                    });
                }
                noItemsLabel.IsVisible = Rows.Count == 0;
            });
        }
        private ObservableCollection<SalesOnDutyRow> rows = new ObservableCollection<SalesOnDutyRow>();
        public ObservableCollection<SalesOnDutyRow> Rows
        {
            get => rows;
            set { rows = value; OnPropertyChanged(nameof(Rows)); }
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
    }
}