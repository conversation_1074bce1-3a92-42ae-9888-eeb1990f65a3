﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage 
             xmlns:animations="http://rotorgames.com"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             
             x:Class="CRMAdminMoblieApp.Views.Popups.UserMenuPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </animations:PopupPage.Resources>

    <Frame 
        HasShadow="False"
        CornerRadius="5"
        HeightRequest="140"
        WidthRequest="200"
        VerticalOptions="Start"
        HorizontalOptions="End"
        Padding="0"
        Margin="0,70,30,0"
        BackgroundColor="#FFFFFF">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">

                <Frame
                    HasShadow="False"
                    BackgroundColor="#F6F6FB"
                    HeightRequest="40"
                    WidthRequest="160"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    CornerRadius="5"
                    Padding="0">
                    <StackLayout
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Spacing="0"
                        Margin="20,0,0,0">
                        <Label 
                            x:Name="userNameLabel"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="12"
                            TextColor="{x:StaticResource dark_purple}"
                            HorizontalOptions="Start"
                            Text=""/>
                        <Label 
                            x:Name="userRoleLabel"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="10"
                            TextColor="{x:StaticResource dark_purple}"
                            HorizontalOptions="Start"
                            Text=""/>
                    </StackLayout>
                </Frame>
                
            </Grid>

            <Grid Grid.Row="1">

                <StackLayout
                    HeightRequest="40"
                    WidthRequest="160"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToProfile}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="profile.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Профиль"/>
                    </StackLayout>


                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Logout}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="logout.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Выйти"/>
                    </StackLayout>
                </StackLayout>

            </Grid>
            
        </Grid>
    </Frame>
  
</animations:PopupPage>