﻿<UserControl x:Class="CRMDesktopApp.Controls.Parts.Header"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Parts"
             xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="60">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid 
        Background="{StaticResource bg_purple}"
        Grid.Row="0">

        <StackPanel 
            Height="60"
            VerticalAlignment="Center"
            Orientation="Horizontal">

            <customcontrols:ImageButton 
                Command="{Binding ElementName=this,Path=GoBack}"
                Margin="30,0,0,0"
                VerticalAlignment="Center"
                Width="10"
                Height="20"
                Source="pack://application:,,,/Resources/Images/arrowBack.png"/>

            <Image
                Margin="30,5,0,0"
                VerticalAlignment="Center"
                Stretch="Fill"
                Source="pack://application:,,,/Resources/Images/logo.png"
                Height="50"
                Width="60"/>

        </StackPanel>


        <TextBlock
            VerticalAlignment="Center"
            HorizontalAlignment="Center"
            Foreground="{StaticResource dark_purple}"
            FontFamily="{StaticResource TTFirsNeue-Regular}"
            FontSize="20"
            Text="{Binding ElementName=this,Path=Title}"/>



        <Grid 
            Width="300"
            Height="60"
            VerticalAlignment="Center"
            Margin="0,0,30,0"
            HorizontalAlignment="Right">

            <StackPanel 
                VerticalAlignment="Center"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Border 
                    Background="White"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Left"
                    Height="30"
                    Width="30"
                    CornerRadius="15"
                    ClipToBounds="True"
                    MouseDown="GoToMainMenuBorderClicked"
                    Padding="0">
                    <Image
                        Stretch="Fill"
                        Source=""/>
                </Border>
                <StackPanel
                     Margin="8,0,0,0">
                    <TextBlock
                        x:Name="baristaFIOLabel"
                        HorizontalAlignment="Right"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="14"
                        Text="Мария Иванова"/>
                    <TextBlock
                        x:Name="baristaRoleLabel"
                        HorizontalAlignment="Right"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="14"
                        Text="Официант"/>
                </StackPanel>
                <customcontrols:ImageButton 
                    IsHitTestVisible="False"
                    Margin="8,0,0,0"
                    VerticalAlignment="Center"
                    Source="pack://application:,,,/Resources/Images/arrowDown.png"
                    Width="18"
                    Height="9"/>

            </StackPanel>


        </Grid>

    </Grid>
</UserControl>
