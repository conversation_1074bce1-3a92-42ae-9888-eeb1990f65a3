﻿using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Models;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRMMobileApp.Helpers;
using CRM.Models.Enums.Settings.Payments;
using Rg.Plugins.Popup.Extensions;
using CRMMobileApp.Views.Popups;
using CRM.Models.Network.LoyaltyProgram;
using CRMMobileApp.Models.Wrappers;
using CRM.Models.Stores.Settings;

namespace CRMMobileApp.Views.Pages.Orders
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CreateOrder : ContentPage
    {
        public double _bonusesToAdd = 0;
        private ClientsAndBonusesStoreSettings _clientSettings;
        public CreateOrder()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

     
        protected override void OnAppearing()
        {
            //base.OnAppearing();

            Device.InvokeOnMainThreadAsync(async () =>
            {

                keyboard.ButtonTapped += Keyboard_ButtonTapped;

                _clientSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetClientsAndBonusesStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                        ApplicationState.CurrentStore.Id);

                var types = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetPaymentTypes(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);

                PaymentTypes = new List<PaymentTypeWrapper>();
                foreach (var type in types)
                {
                    var wrapper = new PaymentTypeWrapper();
                    wrapper.PaymentType = type;
                    wrapper.Title = type.Title;
                    wrapper.ImgPath = type.ImgPath;
                    wrapper.OnSumChanged += Wrapper_OnSumChanged;

                    PaymentTypes.Add(wrapper);
                }



                //Оплата бонусами
                if (ApplicationState.CurrentStore.IsLoyaltySystemEnabled 
                    && OrdersHelper.CurrentOrder.Customer != null)
                {
                    var wrapper = new PaymentTypeWrapper();
                    wrapper.PaymentType = null;
                    wrapper.Title = "Бонусы";



                    if(Device.RuntimePlatform != Device.WPF)
                    {
                        wrapper.ImgPath = "bonuses.png";
                    }
                    else
                    {
                        wrapper.ImgPath = "pack://application:,,,/Images/bonuses.png";
                    }

                    wrapper.OnSumChanged += Wrapper_OnSumChanged;
                    PaymentTypes.Add(wrapper);



                    clientBonusesLabel.IsVisible = true;
                    clientBonusesCountSpan.Text = OrdersHelper.CurrentOrder.Customer.Bonuses.ToString();

                    if (OrdersHelper.CurrentOrder.Customer.LoyaltyProgram != null)
                    {
                        var program = OrdersHelper.CurrentOrder.Customer.LoyaltyProgram;
                        var possiblePercent = program.GetClientMaxPossiblePercent(OrdersHelper.CurrentOrder.Customer);

                        if(OrdersHelper.CurrentOrder.Customer.LoyaltyProgram.LoyaltyProgramType == CRM.Models.Enums.LoyaltyProgramType.Bonuses)
                        {
                            _bonusesToAdd = OrdersHelper.CurrentOrder.SumWithDiscount / 100 * possiblePercent;
                            bonusesToAddCountSpan.Text = $"{Math.Round(_bonusesToAdd, 2)}";
                            bonusesToAddLabel.IsVisible = true;
                        }      
                    }
                }


                listView.ItemsSource = PaymentTypes;




                if (OrdersHelper.CurrentOrder.PrepaymentsSum > 0)
                {
                    prepaymentSumLabel.Text = $"Предоплата: {Math.Round(OrdersHelper.CurrentOrder.PrepaymentsSum)}₽";
                }

                totalSumLabel.Text = $"Итого: {Math.Round(OrdersHelper.CurrentOrder.Sum, 2)}₽";
                if (OrdersHelper.CurrentOrder.Customer != null)
                {
                    var customer = OrdersHelper.CurrentOrder.Customer;
                    clientLabel.Text = $"Клиент: {customer.Name} {customer.Surname}";

                    if(customer.LoyaltyProgram?.LoyaltyProgramType == CRM.Models.Enums.LoyaltyProgramType.Discount)
                    {
                        clientLabel.Text += $", {customer.LoyaltyProgram.GetClientMaxPossiblePercent(customer)}%";
                    }
                }
                else
                {
                    clientLabel.Text = $"Клиент: Гость";
                }

                if (OrdersHelper.CurrentOrder.Discount != null)
                    discountLabel.Text = $"Скидка: {OrdersHelper.CurrentOrder.Discount.Percent}%";
                else
                    discountLabel.Text = $"Скидка: 0%";


                toPayLabel.Text = $"К оплате: {Math.Round(OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens, 2)}₽";
                changeLabel.Text = $"Сдача: {Math.Round(Change, 2)}₽";

                orderNumberSpan.Text = $"{OrdersHelper.CurrentOrder.Id}";
            });
        }


        private void Wrapper_OnSumChanged(object sender, double e)
        {
            Change = CurrentSum - OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens;
            if (Change < 0) Change = 0;
            changeLabel.Text = $"Сдача: {Math.Round(Change, 2)}₽";
        }




        private double change;
        public double Change
        {
            get => change;
            set { change = value; OnPropertyChanged(nameof(Change)); }
        }

        public double CurrentSum => PaymentTypes.Sum(o => o.Sum);






        private PaymentTypeWrapper selectedPaymentType;
        public PaymentTypeWrapper SelectedPaymentType
        {
            get => selectedPaymentType;
            set { selectedPaymentType = value; OnPropertyChanged(nameof(SelectedPaymentType)); }
        }

        private ICommand onSelectedPaymentType;
        public ICommand OnSelectedPaymentType
        {
            get => onSelectedPaymentType ??= new RelayCommand(obj =>
            {
                foreach (var discount in PaymentTypes)
                {
                    discount.IsSelected = false;
                }


                SelectedPaymentType = obj as PaymentTypeWrapper;
                if (SelectedPaymentType != null)
                {
                    SelectedPaymentType.IsSelected = true;
                }           
            });
        }
        private List<PaymentTypeWrapper> paymentTypes;
        public List<PaymentTypeWrapper> PaymentTypes
        {
            get => paymentTypes;
            set { paymentTypes = value; OnPropertyChanged(nameof(PaymentTypes)); }
        }



        private ICommand addSumBtnPressed;
        public ICommand AddSumBtnPressed
        {
            get => addSumBtnPressed ??= new RelayCommand(obj =>
            {
                int val = Convert.ToInt32(obj.ToString());
                if (SelectedPaymentType != null)
                {
                    if(CurrentSum + val > OrdersHelper.CurrentOrder.SumAfterPrepayments)
                    {
                        if (SelectedPaymentType.PaymentType?.OldPaymentType == OldPaymentType.Card)
                            return;

                        //Если способ оплаты - бонусы
                        if (SelectedPaymentType.PaymentType == null)
                            return;


                        var sum = SelectedPaymentType.Sum + val;
                        SelectedPaymentType.SumStr = sum.ToString();
                    }
                    else
                    {
                        //Если бонусов недостаточно
                        if (SelectedPaymentType.PaymentType == null)
                        {
                            if (OrdersHelper.CurrentOrder.Customer.Bonuses < val)
                            {
                                return;
                            }
                        }
                    
                        var sum = SelectedPaymentType.Sum + val;
                        SelectedPaymentType.SumStr = sum.ToString();
                    }
                }
            });
        }

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (SelectedPaymentType is null) return;

                if (e == "backspace" && SelectedPaymentType.SumStr.Length > 0)
                {
                    SelectedPaymentType.SumStr = SelectedPaymentType.SumStr.Remove(SelectedPaymentType.SumStr.Length - 1, 1);
                }
                else if (e == "close")
                {
                    SelectedPaymentType.SumStr = "0";
                }
                else if (e != "backspace")
                {
                    if (e == "." && SelectedPaymentType.SumStr.Contains("."))
                    {
                        return;
                    }


                    var sum = Convert.ToDouble(SelectedPaymentType.SumStr + e);

                    if (SelectedPaymentType.PaymentType == null) // если оплата бонусами
                    {
                        var bonusesPercent = sum / (OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens / 100);
                        if (bonusesPercent > _clientSettings.MaxBonusPaymentPercent)
                        {
                            var popup = new OneButtonedPopup($"Процент, оплачиваемый бонусами превышает заданное значение ({Math.Round(_clientSettings.MaxBonusPaymentPercent, 2)}) в настройках точки", "Уведомление");
                            await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                            return;
                        }

                        if (OrdersHelper.CurrentOrder.Customer.Bonuses < sum) return;

                    }
                    else if (SelectedPaymentType.PaymentType.OldPaymentType == OldPaymentType.Card) //если оплата картои
                    {
                        var otherPaymentsSum = PaymentTypes.Where(o => o.PaymentType?.OldPaymentType != OldPaymentType.Card).Sum(o => o.Sum);

                        if (otherPaymentsSum + sum > OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens) return;

                    }



                    if (SelectedPaymentType.SumStr == "0")
                    {
                        SelectedPaymentType.SumStr = e;
                    }
                    else
                    {
                        SelectedPaymentType.SumStr += e;
                    }


                }
            });
        }











        private ICommand cancelOrder;
        public ICommand CancelOrder
        {
            get => cancelOrder ??= new RelayCommand(async obj =>
            {
                await OrdersHelper.CancelOrder();
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }

        private ICommand payOrder;
        public ICommand PayOrder
        {
            get => payOrder ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {

                    if (CurrentSum < OrdersHelper.CurrentOrder.SumWithDiscountAfterPrepaymens)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Сумма оплат меньше, чем сумма \"К оплате\"", "Ошибка"));
                    }
                    else
                    {
                        var payments = PaymentTypes.Where(o => o.Sum > 0);

                        //Очищаем старые оплаты, на случай, если вдруг эквайринг сорвется или что-то произойдет
                        OrdersHelper.CurrentOrder.OrderPayments = OrdersHelper.CurrentOrder.OrderPayments.Where(o => o.IsPrepeyment).ToList();

                        foreach (var payment in payments)
                        {
                            double finalSum = payment.Sum;
                            if (payment.PaymentType?.OldPaymentType == OldPaymentType.Cash)
                            {
                                finalSum -= Change;
                            }

                            OrdersHelper.CurrentOrder.OrderPayments.Add(new CRM.Models.Stores.OrderPayment
                            {
                                PaymentType = payment.PaymentType,
                                PaymentTypeId = payment.PaymentType?.Id,
                                Sum = finalSum
                            });
                        }

                        var sum = OrdersHelper.CurrentOrder.OrderPayments.Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Card && !o.IsPrepeyment)
                                                                            .Sum(o => o.Sum);
                        if (ApplicationState.StoreAcquirings.Count == 0 || sum == 0)
                        {
                            await CloseOrder();
                        }
                        else
                        {

                            if (ApplicationState.StoreAcquirings.Count == 1)
                            {
                                await App.Current.MainPage.Navigation.PushPopupAsync(new AcquringWaitingPopup(ApplicationState.StoreAcquirings[0], sum));
                            }
                            else
                            {
                                await App.Current.MainPage.Navigation.PushPopupAsync(new AvailableAcquiringPopup());
                            }
                        }



                    }
                });
            });
        }

        public async Task CloseOrder()
        {

            if (ApplicationState.IsFiscalizationAvailable() == Enums.FiscalizationStatus.CanBeDone)
            {
                try
                {
                    ApplicationState.FiscalizerInUse.FiscalizeOrder(OrdersHelper.CurrentOrder);
                }
                catch (Exception ex)
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нет подключения к фискальному аппарату. Продолжение операции невозможно", "Ошибка", "Хорошо"));
                    return;
                }
            }



            //Изменяем бонусный баланс
            var client = OrdersHelper.CurrentOrder.Customer;
            if (client != null)
            {
                _bonusesToAdd -= OrdersHelper.CurrentOrder.PaidByBonuses;

                OrdersHelper.CurrentOrder.CustomerId = client.Id;
                OrdersHelper.CurrentOrder.Customer = null;

                await MobileAPI.BaristaMethods.BaristaClientsMethods.AddBonuses(ApplicationState.CurrentDomain,
                                                                                client.Id,
                                                                                _bonusesToAdd);
                await MobileAPI.BaristaMethods.BaristaClientsMethods.AddTotalSpentToClient(ApplicationState.CurrentDomain,
                                                                                client.Id,
                                                                                OrdersHelper.CurrentOrder.PaidByMoney);
            }

            var paidSumWithDiscount = OrdersHelper.CurrentOrder.SumWithDiscount;
            await OrdersHelper.CloseOrder();

            await NavigationHelper.GoBackIfCafe(true);
            await App.Current.MainPage.Navigation.PushPopupAsync(new OrderPaidPopup(paidSumWithDiscount, Change));
        }

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
    }
}