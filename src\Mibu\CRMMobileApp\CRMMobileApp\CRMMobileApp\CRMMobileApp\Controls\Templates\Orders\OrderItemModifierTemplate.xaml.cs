﻿using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRMMobileApp.Controls.Templates.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderItemModifierTemplate : ContentView
    {
        public OrderItemModifierTemplate(OrderItemModifier item)
        {
            Model = item;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                optionsLayout.Children.Clear();

                foreach (var option in Model.SelectedOptions)
                {
                    optionsLayout.Children.Add(new OrderItemModifierOptionTemplate(option));
                }
            });
        }
        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(OrderItemModifier), typeof(OrderItemModifierTemplate));
        public OrderItemModifier Model
        {
            get { return (OrderItemModifier)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


       

    }
}