﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.ClientsPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls" 
        xmlns:templates="clr-namespace:CRMDesktopApp.Controls.Templates"
        mc:Ignorable="d"
        Title="Клиенты"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="770"
        Height="360">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary>
                    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Padding="0"
        CornerRadius="20"
        Width="760"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        Height="355">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="290"/>
                <ColumnDefinition Width="480"/>
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">
                <StackPanel
                    Margin="40,34,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top">

                    <StackPanel
                        Orientation="Horizontal">
                        <Image 
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/client.png"
                            Width="25"
                            Height="25"/>
                        <TextBlock
                            Margin="15,5,0,0"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource dark_purple}"
                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                            FontSize="20"
                            Text="Клиенты"/>
                    </StackPanel>


                    <controls:EntryOutlined
                        Text="{Binding ElementName=this,Path=SearchQuery,Mode=TwoWay}"
                        Width="250"
                        Margin="0,20,0,0"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"
                        Height="40"
                        CornerRadius="20"
                        PlaceholderMargin="20,0,0,0"
                        TextMargin="20,0,0,0"
                        EntryBackground="{StaticResource bg_purple}"
                        BorderColor="{StaticResource bg_purple}"
                        PlaceholderColor="{StaticResource text_gray}"
                        Placeholder="Поиск"/>

                    <ListBox
                        ScrollViewer.VerticalScrollBarVisibility="Hidden"
                        ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                        BorderThickness="0"
                        HorizontalContentAlignment="Stretch"
                        ItemContainerStyle="{StaticResource lbItemContainerStyle}"
                        SelectedItem="{Binding ElementName=this,Path=SelectedClient,Mode=TwoWay}"
                        ItemsSource="{Binding ElementName=this,Path=Clients,Mode=TwoWay}" Height="220">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <templates:ClientListItem 
                                    Height="25"
                                    Margin="0,5,0,0"
                                    Model="{Binding}"/>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                </StackPanel>
            </Grid>

            <Grid Grid.Column="1">

                <controls:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,35,35,0"/>

                <Border 
                    CornerRadius="10"
                    Width="410"
                    Height="180"
                    Background="{StaticResource bg_purple}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">
                            <StackPanel
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Margin="50,0,0,0">
                                <TextBlock
                                    Margin="0,20,0,0"
                                    Height="{Binding ElementName=clientFIO,Path=ActualHeight}"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource text_gray}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16"
                                    Text="ФИО"/>
                                <TextBlock
                                    Margin="0,20,0,0"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource text_gray}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16"
                                    Text="Телефон"/>
                                <TextBlock
                                    Margin="0,20,0,0"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource text_gray}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16"
                                    Text="Дата рождения"/>
                                <TextBlock
                                    Margin="0,20,0,0"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource text_gray}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16"
                                    Text="Скидка"/>
                            </StackPanel>
                        </Grid>

                        <Grid Grid.Column="1">
                            <StackPanel
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top"
                                Margin="0,0,0,0">
                                <TextBlock
                                    x:Name="clientFIO"
                                    TextWrapping="NoWrap"
                                    Margin="0,20,10,0"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16">
                                    <Run Text="{Binding ElementName=this,Path=SelectedClient.Model.Surname}"/>
                                    <Run Text=""/>
                                    <Run Text="{Binding ElementName=this,Path=SelectedClient.Model.Name}"/>
                                    <Run Text=""/>
                                    <Run Text="{Binding ElementName=this,Path=SelectedClient.Model.Patronymic}"/>
                                </TextBlock>
                                <TextBlock
                                    Margin="0,20,0,0"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16"
                                    Text="{Binding ElementName=this,Path=SelectedClient.Model.Phone}"/>
                                <TextBlock
                                    Margin="0,20,0,0"
                                    HorizontalAlignment="Left"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="16"
                                    Text="{Binding ElementName=this,Path=SelectedClient.Model.BirthDate}"/>
                                <StackPanel
                                    Margin="0,20,0,0"
                                    Orientation="Horizontal">
                                    <TextBlock
                                        HorizontalAlignment="Left"
                                        Foreground="{StaticResource dark_purple}"
                                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                                        FontSize="16"
                                        Text="{Binding ElementName=this,Path=SelectedClient.Model.LoyaltyProgram.Title}"/>
                                    <Border 
                                        Background="{StaticResource green}"
                                        Width="40"
                                        Height="20"
                                        CornerRadius="3"
                                        Padding="0">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Foreground="White"
                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                            FontSize="16">
                                             <Run Text="{Binding ElementName=this,Mode=OneWay,Path=SelectedClient.Model.CurrentLoyaltyProgramPercent}"/>
                                             <Run Text="%"/>
                                        </TextBlock>
                                    </Border>
                                </StackPanel>
                            </StackPanel>
                        </Grid>

                    </Grid>
                </Border>


                <StackPanel
                    VerticalAlignment="Bottom"
                    HorizontalAlignment="Right"
                    Margin="0,0,30,15"
                    Orientation="Horizontal">

                    <Button 
                        Margin="0,0,20,0"
                        Visibility="{Binding ElementName=this,Path=ClientSettings.AllowToAddClientsInMobileApp,
                                                       Converter={StaticResource BooleanToVisibilityConverter}}"
                        Command="{Binding ElementName=this,Path=GoToNewClient}"
                        Style="{StaticResource bg_purple_btn}"
                        VerticalAlignment="Center"
                        Content="Создать нового"
                        Width="170"
                        Height="40"/>
                    <Button 
                        Margin="0,0,20,0"
                        Command="{Binding ElementName=this,Path=SelectClient}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Center"
                        Content="Выбрать клиента"
                        Width="170"
                        Height="40"/>

                </StackPanel>

            </Grid>



        </Grid>
    </Border>
</abstactions:BaseWindow>
