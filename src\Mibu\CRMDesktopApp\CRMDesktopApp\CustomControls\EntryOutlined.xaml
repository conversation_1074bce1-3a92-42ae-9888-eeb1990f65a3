﻿<abstractions:BaseEntry
             xmlns:abstractions="clr-namespace:CRMDesktopApp.CustomControls.Abstractions"
             x:Class="CRMDesktopApp.CustomControls.EntryOutlined"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             x:Name="this">
    <StackPanel>
        <Grid>
            <Border 
                x:Name="EntryFrame"
                BorderBrush="{Binding BorderColor, ElementName=this}"
                CornerRadius="{Binding CornerRadius,ElementName=this}"
                Background="{Binding EntryBackground, ElementName=this}"
                Padding="0,0,0,0"
                Margin="0,0,0,0" />

            <TextBlock 
                x:Name="PlaceHolderLabel"
                FontSize="{Binding ElementName=this,Path=PlaceholderFontSize}"
                HorizontalAlignment="{Binding ElementName=this,Path=PlaceholderHorizontalOptions}"
                Margin="{Binding PlaceholderMargin,ElementName=this}"      
                Foreground="{Binding PlaceholderColor, ElementName=this}"
                Text="{Binding Placeholder,ElementName=this}"
                VerticalAlignment="{Binding ElementName=this,Path=VerticalTextAlignment}" />

            <TextBox
                BorderThickness="0"
                Background="Transparent"    
                AcceptsReturn="{Binding ElementName=this,Path=AcceptsReturn}"
                IsReadOnly="{Binding ElementName=this,Path=IsReadOnly}"
                FontFamily="{Binding ElementName=this,Path=FontFamily}"
                FontSize="{Binding ElementName=this,Path=TextFontSize}"
                TextAlignment="{Binding ElementName=this,Path=HorizontalTextAlignment}"
                Height="{Binding ElementName=this,Path=Height}"
                x:Name="TextBox"
                VerticalContentAlignment="{Binding ElementName=this,Path=VerticalTextAlignment}"                           
                Text="{Binding Text,ElementName=this,Mode=TwoWay}"
                Foreground="{Binding TextColor,ElementName=this,Mode=TwoWay}"
                Margin="{Binding TextMargin,ElementName=this}"   
                KeyDown="onKeyDown"
                TextChanged="OnTextChanged" >
            </TextBox>
        </Grid>
    </StackPanel>
</abstractions:BaseEntry>
