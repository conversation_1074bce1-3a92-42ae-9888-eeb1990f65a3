﻿using CrmAdminApp.iOS;
using Foundation;
using MobPhone.Custom;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UIKit;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;

[assembly: ExportRenderer(typeof(CustomMenuButton), typeof(TouchableFrameRenderer))]
namespace CrmAdminApp.iOS
{
    [Foundation.Preserve]
    public class TouchableFrameRenderer : ViewRenderer
    {
        public static void Load() { }
        protected override void OnElementChanged(ElementChangedEventArgs<Xamarin.Forms.View> e)
        {
            base.OnElementChanged(e);
            var touchableRelativeLayout = e.NewElement as CustomMenuButton;

            if (Control == null)
            {
                return;
            }
        }

        public override void TouchesMoved(NSSet touches, UIEvent evt)
        {
            base.TouchesMoved(touches, evt);

            var touchableRelativeLayout = Element as CustomMenuButton;
            touchableRelativeLayout.OnPressed();
        }

        public override void TouchesEnded(NSSet touches, UIEvent evt)
        {
            base.TouchesEnded(touches, evt);


            var touchableRelativeLayout = Element as CustomMenuButton;
            touchableRelativeLayout.OnReleased();
        }

    }
}