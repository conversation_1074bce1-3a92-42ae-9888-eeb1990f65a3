﻿using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Network.ReferenceBooks;
using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Modifiers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ModifiersPopup : PopupPage
    {
        public bool IsCompleted { get; set; }
        public bool IsSuccessfully { get; set; }


        /// <summary>
        /// Если true - то редактирование уже существующего модификатора в позиции
        /// Иначе новая позиция
        /// </summary>
        private bool _isUpdate = false;

        private OrderItem orderItem;
        public OrderItem OrderItem
        {
            get => orderItem;
            set { orderItem = value; OnPropertyChanged(nameof(OrderItem)); }
        }
        public ModifiersPopup(OrderItem item, List<Modifier> modifiers, bool isUpdate)
        {
            _isUpdate = isUpdate;

            OrderItem = item;
            this.modifiers = new ObservableCollection<Modifier>(modifiers);

            InitializeComponent();
        }
        protected override void OnAppearing()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                foreach (var modifier in modifiers)
                {
                    var orderModifier = new OrderItemModifier()
                    {
                        Modifier = modifier,
                        ModifierId = modifier.Id,
                    };
                    foreach (var option in modifier.Options)
                    {
                        var orderModifierOption = OrderItem.SelectedModifiers.SelectMany(o => o.SelectedOptions)
                                                                             .FirstOrDefault(o => o.ModifierOptionId == option.Id);


                        var modifierOption = new OrderItemModifierOption
                        {
                            ModifierOption = option,
                            ModifierOptionId = option.Id,
                            Price = option.Price,
                        };

                        if (modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                        {
                            if (!_isUpdate)
                            {
                                modifierOption.Amount = 0;
                            }
                            else if (orderModifierOption != null)
                            {
                                modifierOption.Amount = orderModifierOption.Amount;
                            }
                        }
                        else
                        {
                            if (_isUpdate && orderModifierOption != null)
                            {
                                modifierOption.IsSelected = true;

                                var modifierFromItem = OrderItem.SelectedModifiers.FirstOrDefault(o => o.Id == modifier.Id);
                                if (modifierFromItem != null)
                                {
                                    orderModifier.SelectedOptions = modifierFromItem.SelectedOptions;
                                }

                            }
                        }

                        orderModifier.AllOptions.Add(modifierOption);
                    }
                    OrderModifiers.Add(orderModifier);
                }


                foreach (var modifier in OrderModifiers)
                {
                    if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                    {
                        modifiersLayout.Children.Add(new OneOptionModifierView(modifier));
                    }
                    else if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                    {
                        modifiersLayout.Children.Add(new SomeOptionsModifierView(modifier));
                    }
                }
            });   
        }

        private ObservableCollection<Modifier> modifiers = new ObservableCollection<Modifier>();


        public ObservableCollection<OrderItemModifier> OrderModifiers = new ObservableCollection<OrderItemModifier>();





        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                IsCompleted = true;
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand applyModifiers;
        public ICommand ApplyModifiers
        {
            get => applyModifiers ??= new RelayCommand(async obj =>
            {
                foreach(var modifier in OrderModifiers.Where(o => o.Modifier.IsRequired))
                {
                    if(modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                    {
                        if(!modifier.AllOptions.Any(o => o.IsSelected == true))
                        {
                            await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Не выбраны все обязательные модификаторы", "Ошибка"));
                            return;
                        }
                    }
                    if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                    {
                        if (!modifier.AllOptions.Any(o => o.Amount > 0))
                        {
                            await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Не выбраны все обязательные модификаторы", "Ошибка"));
                            return;
                        }
                    }
                }

                OrderItem.SelectedModifiers.Clear();
                foreach (var modifier in OrderModifiers)
                {
                    if (modifier.Modifier.IsRequired)
                    {
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.IsSelected == true));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.Amount > 0));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                    }
                    else if (modifier.IsSelected)
                    {
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.IsSelected == true));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.Amount > 0));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                    }
                }
                IsCompleted = true;
                IsSuccessfully = true;
                await App.Current.MainPage.Navigation.PopPopupAsync();

            });
        }
    }
}