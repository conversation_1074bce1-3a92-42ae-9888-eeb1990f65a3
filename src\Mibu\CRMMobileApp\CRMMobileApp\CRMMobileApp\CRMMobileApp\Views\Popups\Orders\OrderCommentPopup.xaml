﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.OrderCommentPopup">
    <Grid>


        <Frame
             HasShadow="{OnPlatform WPF=True, Default=False}"
             WidthRequest="670"
             HeightRequest="310"
             BackgroundColor="White"
             Background="White"
             Padding="0"
             HorizontalOptions="Center"
             VerticalOptions="Start"
             Margin="0,40,0,0"
             CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="80"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <StackLayout
                         Margin="15,30,0,0"
                         VerticalOptions="Start"
                         HorizontalOptions="Start"
                         Spacing="0"
                         Orientation="Horizontal">

                        <Image 
                             Margin="25,0,0,0"
                             Aspect="Fill"
                             Source="{OnPlatform Default=client.png, WPF='pack://application:,,,/Images/client.png'}"
                             WidthRequest="25"
                             HeightRequest="30"
                             VerticalOptions="Center"/>

                        <Label
                             Margin="15,0,0,0"
                             VerticalOptions="Center"
                             TextColor="{StaticResource dark_purple}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="20"
                             Text="Комментарий к чеку"/>

                    </StackLayout>



                    <ImageButton 
                         Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                         Command="{Binding Source={x:Reference this},Path=Close}"
                         BackgroundColor="Transparent"
                         VerticalOptions="Start"
                         HorizontalOptions="End"
                         Padding="10"
                         WidthRequest="36"
                         HeightRequest="36"
                         Margin="0,10,10,0"/>



                </Grid>


                <Grid Grid.Row="1">
                    <controls:EntryOutlined
                         Text="{Binding Source={x:Reference this},Path=Comment,Mode=TwoWay}"
                         Margin="20,0,20,20"
                         HeightRequest="160"
                         IsMultiline="True"
                         VerticalPlaceholderAlignment="Start"
                         VerticalTextAlignment="Start"
                         TextMargin="10,10,0,0"
                         PlaceholderMargin="10,10,0,0"
                         Style="{StaticResource white_cornered_entry}"
                         HorizontalOptions="FillAndExpand"
                         VerticalOptions="FillAndExpand"/>
                </Grid>

                <Grid Grid.Row="2">

                    <Button 
                          Command="{Binding Source={x:Reference this},Path=SaveChanges}"
                          Style="{StaticResource bg_purple_btn}"
                          VerticalOptions="End"
                          HorizontalOptions="End"
                          Margin="0,0,20,30"
                          Text="Создать"
                          WidthRequest="140"
                          HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>


    </Grid>
</animations:PopupPage>