﻿using CRM.Models.Network.ReferenceBooks;
using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Modifiers;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для ModifiersPopup.xaml
    /// </summary>
    public partial class ModifiersPopup : BaseWindow
    {
        public bool IsCompleted { get; set; }
        public bool IsSuccessfully { get; set; }

        /// <summary>
        /// Если true - то редактирование уже существующего модификатора в позиции
        /// Иначе новая позиция
        /// </summary>
        private bool _isUpdate = false;

        private OrderItem orderItem;
        public OrderItem OrderItem
        {
            get => orderItem;
            set { orderItem = value; OnPropertyChanged(nameof(OrderItem)); }
        }
        public ModifiersPopup(OrderItem item, List<Modifier> modifiers,bool isUpdate)
        {
            _isUpdate = isUpdate;

            OrderItem = item;
            this.modifiers = new ObservableCollection<Modifier>(modifiers);
            InitializeComponent();

            Loaded += ModifiersPopup_Loaded;
        }

        private void ModifiersPopup_Loaded(object sender, RoutedEventArgs e)
        {
            foreach (var modifier in modifiers)
            {
                var orderModifier = new OrderItemModifier()
                {
                    Modifier = modifier,
                    ModifierId = modifier.Id,
                };
                foreach (var option in modifier.Options)
                {
                    var orderModifierOption = OrderItem.SelectedModifiers.SelectMany(o => o.SelectedOptions)
                                                                         .FirstOrDefault(o => o.ModifierOptionId == option.Id);


                    var modifierOption = new OrderItemModifierOption
                    {
                        ModifierOption = option,
                        ModifierOptionId = option.Id,
                        Price = option.Price,
                    };

                    if (modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                    {
                        if (!_isUpdate)
                        {
                            modifierOption.Amount = 0;
                        }
                        else if(orderModifierOption != null)
                        {
                            modifierOption.Amount = orderModifierOption.Amount;
                        }
                    }
                    else
                    {
                        if (_isUpdate && orderModifierOption != null)
                        {
                            modifierOption.IsSelected = true;

                            var modifierFromItem = OrderItem.SelectedModifiers.FirstOrDefault(o => o.Id == modifier.Id);
                            if (modifierFromItem != null)
                            {
                                orderModifier.SelectedOptions = modifierFromItem.SelectedOptions;
                            }
                      
                        }
                    }

                    orderModifier.AllOptions.Add(modifierOption);
                }
                OrderModifiers.Add(orderModifier);
            }


            foreach (var modifier in OrderModifiers)
            {
                if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                {
                    modifiersLayout.Children.Add(new OneOptionModifierView(modifier));
                }
                else if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                {
                    modifiersLayout.Children.Add(new SomeOptionsModifierView(modifier));
                }
            }
        }


        private ObservableCollection<Modifier> modifiers = new ObservableCollection<Modifier>();


        public ObservableCollection<OrderItemModifier> OrderModifiers = new ObservableCollection<OrderItemModifier>();


        private ICommand applyModifiers;
        public ICommand ApplyModifiers
        {
            get => applyModifiers ??= new RelayCommand(async obj =>
            {
                foreach (var modifier in OrderModifiers.Where(o => o.Modifier.IsRequired))
                {
                    if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                    {
                        if (!modifier.AllOptions.Any(o => o.IsSelected == true))
                        {
                            new OneButtonedPopup("Не выбраны все обязательные модификаторы", "Ошибка").ShowDialog();
                            return;
                        }
                    }
                    if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                    {
                        if (!modifier.AllOptions.Any(o => o.Amount > 0))
                        {
                            new OneButtonedPopup("Не выбраны все обязательные модификаторы", "Ошибка").ShowDialog();
                            return;
                        }
                    }
                }


                OrderItem.SelectedModifiers.Clear();
                foreach (var modifier in OrderModifiers)
                {          
                    if (modifier.Modifier.IsRequired)
                    {
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.IsSelected == true));

                            OrderItem.SelectedModifiers.Add(modifier);
                         
                        }
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.Amount > 0));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                    }
                    else if (modifier.IsSelected)
                    {
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.OneOption)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.IsSelected == true));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                        if (modifier.Modifier.ModifierSelectionMode == CRM.Models.Enums.Info.ModifierSelectionMode.SomeOptions)
                        {
                            modifier.SelectedOptions.Clear();
                            modifier.SelectedOptions.AddRange(modifier.AllOptions.Where(o => o.Amount > 0));

                            OrderItem.SelectedModifiers.Add(modifier);

                        }
                    }
                }
                IsCompleted = true;
                IsSuccessfully = true;
                this.Close();
            });
        }
    }
}
