﻿using CrmAdminApp.Helpers;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.Parts
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Footer : ContentView
    {
        public Footer()
        {
            InitializeComponent();
        }





        private bool isOrdersPage;
        public bool IsOrdersPage
        {
            get => isOrdersPage;
            set {  isOrdersPage = value;OnPropertyChanged(nameof(IsOrdersPage));}
        }
        private void ordersTapped(object sender, EventArgs e)
        {
            AddNewRemoveOldPage(PagesHelper.GetOrdersPage());
        }





        private bool isSalesPage;
        public bool IsSalesPage
        {
            get => isSalesPage;
            set { isSalesPage = value; OnPropertyChanged(nameof(IsSalesPage)); }
        }
        private void salesTapped(object sender, EventArgs e)
        {
            AddNewRemoveOldPage(PagesHelper.GetSalesPage());
        }




        private bool isRealTimePage;
        public bool IsRealTimePage
        {
            get => isRealTimePage;
            set { isRealTimePage = value; OnPropertyChanged(nameof(IsRealTimePage)); }         
        }
        private void realTimePageTapped(object sender, EventArgs e)
        {
            AddNewRemoveOldPage(PagesHelper.GetRealTimePage());
        }



        private bool isStocksPage;
        public bool IsStocksPage
        {
            get => isStocksPage;
            set { isStocksPage = value; OnPropertyChanged(nameof(IsStocksPage)); }
        }
        private void stockTapped(object sender, EventArgs e)
        {
            AddNewRemoveOldPage(PagesHelper.GetStockPage());
        }



        
        private bool isCassaPage;
        public bool IsCassaPage
        {
            get => isCassaPage;
            set { isCassaPage = value; OnPropertyChanged(nameof(IsCassaPage)); }
        }
        private void cassaTapped(object sender, EventArgs e)
        {
            AddNewRemoveOldPage(PagesHelper.GetCassaPage());
        }



        private async Task AddNewRemoveOldPage(Page page)
        {
            try
            {
               // await App.Current.MainPage.Navigation.PopAsync(false);
                if (App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() != page)
                {
                    await App.Current.MainPage.Navigation.PushAsync(page, true);
                    for (int i = App.Current.MainPage.Navigation.NavigationStack.Count - 2; i > -1; i--)
                    {
                        App.Current.MainPage.Navigation.RemovePage(App.Current.MainPage.Navigation.NavigationStack[i]);
                    }
                }

            }
            catch { }
        }

      
    }
}