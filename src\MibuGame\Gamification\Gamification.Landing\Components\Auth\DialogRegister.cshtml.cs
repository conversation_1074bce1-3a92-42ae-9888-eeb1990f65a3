using AppoMobi.Specials;
using CRM.Database.Core;
using CRMGamification.Models.Profiles;
using CRMGamification.Services;
using CRMGamification.Services.Сommon;
using Gamification.Landing.Components.Popups;
using Hydro.Utils;
using Mibu.Domain.Dto;
using Mibu.Domain.Models.Emails;
using Mibu.Game.Application.Common;
using Mibu.Game.Application.Services;
using Mibu.Landing.Admin.Abstractions.Services;
using Mibu.Services.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Gamification.Landing.Components.Auth
{
    public class DialogRegister : BaseDialog
    {
        private readonly GamificationCoreDBContext _db;
        private readonly ICache _cache;

        public RegisterModelDto Item { get; set; }

        public DialogRegister(
            GamificationCoreDBContext db,
            GamificationContext gamificationContext,
            FinanceService manager,
            ITemplatedEmailsService emails,
            ICache cache)
        {
            _db = db;
            _gamifications = gamificationContext;
            _cache = cache;
            _emails = emails;
            _manager = manager;
        }

        public override Task RenderAsync()
        {
            if (firstTime)
            {
                firstTime = false;

                Item = new();
            }

            return base.RenderAsync();
        }

        public override void Bind(PropertyPath property, object value)
        {
            base.Bind(property, value);
        }

        private bool firstTime;
        private readonly GamificationContext _gamifications;
        private readonly ITemplatedEmailsService _emails;
        private readonly FinanceService _manager;

        public override void Mount()
        {
            firstTime = true;
        }



        public async Task Submit()
        {

            //auto!
            var isValid = await BindFormAndValidateAsync(Item, (model, modelState) =>
            {
                try
                {
                    if (string.IsNullOrEmpty(model.Password)
                        || string.IsNullOrEmpty(model.Password)
                        || string.IsNullOrEmpty(model.BusinessName)
                        || string.IsNullOrEmpty(model.FIO)
                        || string.IsNullOrEmpty(model.Phone)
                        || string.IsNullOrEmpty(model.Email)
                        )
                    {
                        modelState.AddModelError("Required", "Не заполнены необходимые поля");
                        return;
                    }

                    if (model.Password != model.PasswordAgain)
                    {
                        modelState.AddModelError("PasswordMatch", "Пароли не совпадают");
                        return;
                    }

                    model.Email = model.Email.ToLowerInvariant().Trim();

                    if (model.BusinessName != null)
                        model.BusinessName = model.BusinessName.ToTitleCase().Trim();

                    if (model.FIO != null)
                        model.FIO = model.FIO.ToTitleCase().Trim();

                    var phone = PhoneConverter.ImportPhoneNumber(model.Phone);
                    if (phone == null)
                    {
                        modelState.AddModelError("Phone", "Некорректный номер телефона");
                    }

                    //todo can check email is unique
                    if (!EmailValidator.IsValidEmail(model.Email)) //todo move to validator with DI?
                    {
                        modelState.AddModelError("Email", "Некорректный адрес электронной почты");
                    }

                    if (_db.Users.Any(x => x.Email == model.Email && !x.IsDeleted))
                    {
                        modelState.AddModelError("Email", "Адрес электронной почты уже зарегистрирован");
                    }

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    this.Error = $"{e}";
                    modelState.AddModelError("Fatal", e.Message);
                }
            });

            if (isValid)
            {
                Error = string.Empty;

                try
                {
                    var phone = PhoneConverter.ImportPhoneNumber(Item.Phone);

                    //create account in core db
                    #region CORE DB

                    var trial = FinanceService.GetDefaultTrialSubscription();

                    var domain = new UserDomainGame
                    {
                        Name = GetAvailableDomainName(Item.BusinessName),
                        Subscriptions = new List<UserSubscription>()
                        {
                            trial
                        },
                    };

                    var user = new UserGame()
                    {
                        BusinessName = Item.BusinessName,
                        Phone = phone,
                        Email = Item.Email,
                        FIO = Item.FIO,
                        Password = Item.Password,
                        Domains = new()
                        {
                            domain
                        }
                    };

                    _db.Users.Add(user);

                    await _db.SaveChangesAsync();

                    await _manager.PropagateSeatsAsync(domain.Name, trial.UsersCount);

                    #endregion

                    //create gamification admin profile
                    #region GAMIFICATION DB

                    var gamification = _gamifications.CreateGamification(domain.Name, false);

                    //var store = gamification.Stores.FirstOrDefault();

                    var userModel = new EditingUserPermissionsModel
                    {
                        AdminTabCRMUserId = user.Id,
                        Email = user.Email,
                        Name = user.FIO,
                        Surname = "",
                        Password = user.Password,
                        AvatarPath = "",
                        HasAccessToGamificationAdminApp = true,
                        HasAccessToGamificationManagerApp = true,
                        Role = GamificationUserRole.Director,
                        UserAccessType = UserAccessType.Full,
                        UserType = UserType.Owner,
                        PositionId = gamification.Positions.First().Id,
                        Stores = new()
                    };

                    var settingsService = new SettingsService(null, _gamifications, _cache, null)
                    {
                        Domain = domain.Name,
                    };

                    settingsService.UpdatePermissions(userModel);

                    await MakeAuth(user);

                    #endregion

                    //UpdateComponent<MenuComponent>();

                    Close();

                    string url = Url.Action("Index", "Cabinet");

                    try
                    {
                        var model = new RegisteredForDomainInfo
                        {
                            Name = user.FIO,
                            Domain = domain.Name,
                            Login = user.Email,
                            Password = user.Password,
                            Url = $"https://mibugame.ru/cabinet?Login=user.Email"
                        };

                        //MAIL for registration
                        await _emails.SendEmailTemplateAsync("Registered", user.Email, model);

                        //MAIL FOR ADMIN
                        model.Url = $"https://{domain.Name}.mibugame24.ru?login={user.Email}";
                        if (await _emails.SendEmailTemplateAsync("AddedAdmin", user.Email, model))
                        {
                            var existing = await _gamifications.Users
                                .FirstOrDefaultAsync(u => u.Email == user.Email && !u.IsDeleted);

                            existing.EmailSentForAdmin = DateTime.UtcNow;
                            _gamifications.Entry(existing).State = EntityState.Modified;
                            await _gamifications.SaveChangesAsync();
                        }

                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }

                    Redirect(url);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    this.Error = $"{e}";
                }
            }
            else
            {
                var errors = ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();

                Error = errors.ToTags(", ");
            }

        }

        public string Error { get; set; }

        private string GetAvailableDomainName(string businessName)
        {
            var domainName = EncodeHelper.GetLatynSlug(businessName).Trim()
                .Replace("(",string.Empty)
                .Replace(")", string.Empty)
                .ToLower();
            var freeDomainName = domainName;

            var hasDomain = _db.UserDomains.Any(o => o.Name == domainName);
            int counter = 2;
            while (hasDomain)
            {
                freeDomainName = $"{domainName}{counter}";
                hasDomain = _db.UserDomains.Any(o => o.Name == freeDomainName);
                counter++;
            }

            return freeDomainName;
        }

    }

}
