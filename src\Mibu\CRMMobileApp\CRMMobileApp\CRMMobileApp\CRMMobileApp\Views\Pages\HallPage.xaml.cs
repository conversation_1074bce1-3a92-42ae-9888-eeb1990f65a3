﻿using CRM.Models.Enums.Settings;
using CRM.Models.Reports.Mobile.Models.Halls;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Tables;
using CRMMobileApp.Models.Reports;
using CRMMobileApp.Views.Pages.Tables;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Internals;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class HallPage : ContentPage
    {
        private bool _isInit = false;
        public HallPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        //public HallPage(HallWithOrders hall)
        //{
        //    InitializeComponent();
        //    NavigationPage.SetHasNavigationBar(this, false);
        //    CurrentHall = hall;

        //    Load();
        //}

        protected override void OnAppearing()
        {
            base.OnAppearing();

            Load();
        }


        private HallWithOrders currentHall;
        public HallWithOrders CurrentHall
        {
            get => currentHall;
            set { currentHall = value; OnPropertyChanged(nameof(CurrentHall)); }
        }


        private ObservableCollection<HallWithOrders> halls = new ObservableCollection<HallWithOrders>();
        public ObservableCollection<HallWithOrders> Halls
        {
            get => halls;
            set { halls = value; OnPropertyChanged(nameof(Halls)); }
        }

        #region Начальная инициализация зала

      

        private void Load()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                var rooms = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetHallsWithOrders(ApplicationState.CurrentDomain, 
                                                                                                   ApplicationState.CurrentStore.Id,
                                                                                                   ApplicationState.CurrentDuty.Id);
                Halls = new ObservableCollection<HallWithOrders>(rooms);

                if (CurrentHall is null)
                {
                    CurrentHall = Halls.FirstOrDefault();
                }
                else
                {
                    CurrentHall = Halls.FirstOrDefault(o => o.Hall.Id == CurrentHall.Hall.Id);
                }

                hallsComboBox.SelectedIndex = Halls.IndexOf(Halls.FirstOrDefault(o => o.Hall.Id == CurrentHall.Hall.Id));

                ArrangeTables();


                await Task.Delay(333);
                _isInit = true;
            });
        }




        private void ArrangeTables()
        {
            tablesLayout.Children.Clear();
            if (CurrentHall is null) return;

            foreach (var table in CurrentHall.Tables)
            {
                var control = MakeTable(table);
                tablesLayout.Children.Add(control);
            }
        }
        private AbsTable MakeTable(TableWithOrders table)
        {
            AbsTable control = null;
            switch (table.Table.Form)
            {
                case TableForm.Square:
                    control = new SquareTable(table);
                    break;
                case TableForm.Circle:
                    control = new RoundTable(table);
                    break;
                default:
                    throw new NotImplementedException();
            }
            control.TableTapped += Control_TableTapped;
            return control;
        }

        #endregion

        private void Control_TableTapped(object sender, Table e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                var table = sender as AbsTable;
                if (table.TableOrders.Any())
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new TableCurrentOrdersPopup(table));
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new GuestsCountPopup(table));
                }
            });
        }
        private void onHallSelected(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                var selected = hallsComboBox.SelectedItem as HallWithOrders;
                if (selected == null || CurrentHall == null)
                    return;

                if (CurrentHall.Hall.Id != selected.Hall.Id && _isInit)
                {

                    CurrentHall = selected;
                    ArrangeTables();
                }
            });
        }
    }
}