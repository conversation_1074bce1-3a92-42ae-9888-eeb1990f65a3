﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview"
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             x:Name="this"
             Background="White"
             BackgroundColor="White"
             NavigationPage.HasNavigationBar="False"
             x:Class="CRMAdminMoblieApp.MainPage">

    <ContentPage.Resources>
        <ResourceDictionary Source="Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid>
            <Image 
                Aspect="Fill"
                Source="main_decor.png"/>


            <ImageButton 
                Command="{Binding Source={x:Reference this},Path=GoBack}"
                Source="arrowBack.png"
                WidthRequest="12"
                HeightRequest="28"
                Margin="30,40,0,0"
                BackgroundColor="Transparent"
                HorizontalOptions="Start"
                VerticalOptions="Start"/>

            <StackLayout
                WidthRequest="380"
                HorizontalOptions="Center"
                Margin="0,150,0,0">

                <Image 
                    WidthRequest="240"
                    HeightRequest="140"
                    Source="logo.png"/>

                <StackLayout
                    HorizontalOptions="Center">

                    <StackLayout
                         Margin="30,0,30,0"
                        HorizontalOptions="Center"
                        Spacing="20">

                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            Text="{Binding Source={x:Reference this},Path=Domain,Mode=TwoWay}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="Домен"/>

                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            Text="{Binding Source={x:Reference this},Path=Username,Mode=TwoWay}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="Логин"/>

                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            IsPassword="True"
                            Text="{Binding Source={x:Reference this},Path=Password,Mode=TwoWay}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="Пароль"/>

                        <Grid HeightRequest="40">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>

                         

                            <!--<StackLayout
                                Orientation="Horizontal"
                                Spacing="1"
                                Grid.Column="0">
                                <CheckBox
                                    IsChecked="{Binding Source={x:Reference this},Path=RememberMe,Mode=TwoWay}"
                                    VerticalOptions="Center" />
                                <Label 
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    VerticalOptions="Center"
                                    TextColor="{x:StaticResource dark_purple}"
                                    HorizontalOptions="Center"
                                    Text="Запомнить меня"/>
                            </StackLayout>

                            <Button 
                                FontFamily="TTFirsNeue-Regular"
                                Grid.Column="1"
                                HorizontalOptions="End"
                                FontSize="14"
                                Text="Забыли пароль?"
                                TextColor="{x:StaticResource text_gray}"
                                Style="{x:StaticResource transparent_btn}"
                                HeightRequest="40"/>-->

                        </Grid>
                    </StackLayout>


                    <StackLayout>

                        <Button 
                            Command="{Binding Source={x:Reference this},Path=Auth}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Войти"
                            Style="{x:StaticResource purple_gradient_btn}"
                            WidthRequest="240"
                            HorizontalOptions="Center"
                            HeightRequest="40"
                            Margin="0,0,0,0"/>
                        
                        <Label 
                            x:Name="authErrorText"
                            TextColor="Red"
                            FontSize="14"
                            HorizontalTextAlignment="Center"
                            HorizontalOptions="Center"/>

                    </StackLayout> 
                    
                  

                    
                </StackLayout>

            </StackLayout>

        </Grid>
    </ContentPage.Content>

</ContentPage>
