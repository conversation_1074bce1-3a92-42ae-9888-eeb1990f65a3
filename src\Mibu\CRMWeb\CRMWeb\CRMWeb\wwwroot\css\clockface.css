.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.clockface {
  width: 160px;
  padding: 3px;
  text-align: center;
  /*
    .l3 .center span {
    	vertical-align: middle;
		display: inline-block;
		.ie7-inline-block();  
		padding: 0 2px;  	
    }
    */

  /*
    input {
    	width: 20px;
    	margin: 0;
    	vertical-align: top; 
     }	

    a {
    	text-decoration: none;
    	padding: 0 3px;
    	vertical-align: top;
    	font-size: 0.85em;
    	.border-radius(3px);

    	&.am {margin-right: 8px;}	
    	
    	&.active,
    	&.active:hover {
    		.buttonBackground(@btnSuccessBackground, spin(@btnSuccessBackground, 20));
    	}
    }
    */

}
.clockface > div {
  clear: both;
  overflow: auto;
}
.clockface .outer,
.clockface .inner {
  width: 22px;
  height: 22px;
  line-height: 22px;
  cursor: default;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.clockface .outer.active,
.clockface .inner.active,
.clockface .outer.active:hover,
.clockface .inner.active:hover {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.clockface .outer:hover,
.clockface .inner:hover {
  background-color: #dcdcdc;
}
.clockface .outer {
  color: gray;
  font-size: 0.8em;
}
.clockface .outer.active,
.clockface .outer.active:hover {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #5bb75b;
  background-image: -moz-linear-gradient(top, #62c462, #51a351);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#62c462), to(#51a351));
  background-image: -webkit-linear-gradient(top, #62c462, #51a351);
  background-image: -o-linear-gradient(top, #62c462, #51a351);
  background-image: linear-gradient(to bottom, #62c462, #51a351);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff62c462', endColorstr='#ff51a351', GradientType=0);
  border-color: #51a351 #51a351 #387038;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #51a351;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */

  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.clockface .outer.active:hover,
.clockface .outer.active:hover:hover,
.clockface .outer.active:active,
.clockface .outer.active:hover:active,
.clockface .outer.active.active,
.clockface .outer.active:hover.active,
.clockface .outer.active.disabled,
.clockface .outer.active:hover.disabled,
.clockface .outer.active[disabled],
.clockface .outer.active:hover[disabled] {
  color: #ffffff;
  background-color: #51a351;
  *background-color: #499249;
}
.clockface .outer.active:active,
.clockface .outer.active:hover:active,
.clockface .outer.active.active,
.clockface .outer.active:hover.active {
  background-color: #408140 \9;
}
.clockface .inner.active,
.clockface .inner.active:hover {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(to bottom, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #0044cc;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */

  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.clockface .inner.active:hover,
.clockface .inner.active:hover:hover,
.clockface .inner.active:active,
.clockface .inner.active:hover:active,
.clockface .inner.active.active,
.clockface .inner.active:hover.active,
.clockface .inner.active.disabled,
.clockface .inner.active:hover.disabled,
.clockface .inner.active[disabled],
.clockface .inner.active:hover[disabled] {
  color: #ffffff;
  background-color: #0044cc;
  *background-color: #003bb3;
}
.clockface .inner.active:active,
.clockface .inner.active:hover:active,
.clockface .inner.active.active,
.clockface .inner.active:hover.active {
  background-color: #003399 \9;
}
.clockface .l1 .cell,
.clockface .l5 .cell {
  width: 22px;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
}
.clockface .l1 .outer {
  margin-bottom: 3px;
}
.clockface .l5 .outer {
  margin-top: 3px;
}
.clockface .l2 .outer,
.clockface .l3 .outer,
.clockface .l4 .outer,
.clockface .l2 .inner,
.clockface .l3 .inner,
.clockface .l4 .inner {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
  vertical-align: middle;
}
.clockface .l2 .left,
.clockface .l3 .left,
.clockface .l4 .left {
  float: left;
}
.clockface .l2 .left .outer,
.clockface .l3 .left .outer,
.clockface .l4 .left .outer {
  margin-right: 3px;
}
.clockface .l2 .right,
.clockface .l3 .right,
.clockface .l4 .right {
  float: right;
}
.clockface .l2 .right .outer,
.clockface .l3 .right .outer,
.clockface .l4 .right .outer {
  margin-left: 3px;
}
.clockface .ampm {
  font-size: 0.8em;
  text-decoration: none;
  border-bottom: dashed 1px;
}
.clockface .ampm:focus {
  outline: 0;
  outline: thin dotted \9;
  /* IE6-9 */
}
