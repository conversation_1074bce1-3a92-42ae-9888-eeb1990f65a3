﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.TradeNetworkExpandableCard">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame CornerRadius="5"
               BorderColor="{x:StaticResource bg_purple}"
               BackgroundColor="{x:StaticResource bg_purple}"
               HasShadow="False"
               Padding="0">

            <Grid RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="70" />
                    <RowDefinition Height="180" />
                </Grid.RowDefinitions>

                <Grid
                    Grid.Row="0"
                    Padding="8,6">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="HeaderContent_OnTapped" />
                    </Grid.GestureRecognizers>



                    <StackLayout 
                        Margin="20,10,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Orientation="Horizontal"> 
                        <Frame
                            BackgroundColor="Gray"
                            HasShadow="False"
                            Padding="0"
                            CornerRadius="20"
                            WidthRequest="40"
                            HeightRequest="40"
                            HorizontalOptions="Start"
                            VerticalOptions="Center">
                            <Image/>
                        </Frame>

                        <StackLayout>
                            <Label 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="{x:StaticResource dark_purple}"
                                Text="{Binding Source={x:Reference this},Path=Model.NetworkSettings.Title}"/>
                            <StackLayout 
                                
                                Orientation="Horizontal">
                                <Label 
                                    VerticalOptions="Center"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    TextColor="{x:StaticResource text_gray}"
                                    Text="Торговых точек"/>
                                <Frame 
                                    BackgroundColor="{x:StaticResource purple}"
                                    Margin="23,0,0,0"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    WidthRequest="30"
                                    HeightRequest="20"
                                    CornerRadius="5"
                                    Padding="0"
                                    HasShadow="False">
                                    <Label 
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        TextColor="White"
                                        Text="{Binding Source={x:Reference this},Path=Model.Stores.Count}"/>
                                </Frame>
                            </StackLayout>
                    
                        </StackLayout>
                    </StackLayout>
                    
                    <Image
                        Margin="0,17,20,0"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        WidthRequest="14"
                        HeightRequest="14">
                         <Image.Source>
                            <FileImageSource File="{Binding ExpandIcon}" />
                        </Image.Source>
                    </Image>

                  

                </Grid>

                <!-- Header Bottom Border -->
                <!--<BoxView Grid.Row="0"
                 Color="#EBEBEB"
                 HeightRequest="1"
                 Opacity="0.8"
                 VerticalOptions="End"
                 HorizontalOptions="FillAndExpand"
                 IsVisible="{Binding IsExpanded}" />-->

                <!-- Content View -->
                <Grid 
                    Margin="20,0,20,0"
                    Grid.Row="1"
                    x:Name="BodyContentView"
                    IsVisible="False" >

                    <Grid.RowDefinitions>
                        <RowDefinition Height="100"/>
                        <!--<RowDefinition Height="70"/>-->
                    </Grid.RowDefinitions>

                    <Grid
                        Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">
                            <StackLayout>
                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     TextColor="{x:StaticResource text_gray}"
                                     Text="Выручка"/>
                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="20"
                                     TextColor="{x:StaticResource dark_purple}">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=BriefReport.Revenue}"/>
                                                <Span Text="₽"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <Button 
                                    Command="{Binding Source={x:Reference this},Path=SelectNetwork}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    Text="Выбрать"
                                    Style="{x:StaticResource purple_gradient_btn}"
                                    HeightRequest="40"
                                    Margin="0,10,0,0"/>

                            </StackLayout>
                        </Grid>

                        <Grid Grid.Column="1">
                            <StackLayout >
                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     TextColor="{x:StaticResource text_gray}"
                                     Text="Прибыль"/>
                                <Label 
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="20"
                                     TextColor="{x:StaticResource dark_purple}">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=BriefReport.Profit}"/>
                                                <Span Text="₽"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <!--<Button 
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    Text="Подробнее"
                                    Style="{x:StaticResource green_gradient_btn}"
                                    HeightRequest="40"
                                    Margin="0,10,0,0"/>-->

                                <Button 
                                     Command="{Binding Source={x:Reference this},Path=DeleteNetwork}"
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     Text="Удалить сеть"
                                     BackgroundColor="White"
                                     Style="{x:StaticResource bg_purple_btn}"
                                     VerticalOptions="Center"
                                     Margin="0,10,0,0"
                                     HeightRequest="40"/>
                                
                            </StackLayout>
                        </Grid>
                        
                    </Grid>

                    <!--<Grid Grid.Row="1">
                        <Button 
                             Command="{Binding Source={x:Reference this},Path=DeleteNetwork}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             Text="Удалить сеть"
                             BackgroundColor="White"
                             Style="{x:StaticResource bg_purple_btn}"
                             HorizontalOptions="FillAndExpand"
                             VerticalOptions="Center"
                             HeightRequest="40"/>
                    </Grid>-->

                </Grid>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>