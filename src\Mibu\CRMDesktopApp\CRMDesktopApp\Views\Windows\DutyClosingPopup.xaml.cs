﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для DutyClosingPopup.xaml
    /// </summary>
    public partial class DutyClosingPopup : BaseWindow
    {
        public DutyClosingPopup()
        {
            InitializeComponent();
        }

        private string comment;
        public string Comment
        {
            get => comment;
            set { comment = value; OnPropertyChanged(nameof(Comment)); }
        }

        private double sum;
        public double Sum
        {
            get => sum;
            set { sum = value; OnPropertyChanged(nameof(Sum)); }
        }

        private ICommand closeDuty;
        public ICommand CloseDuty
        {
            get => closeDuty ??= new RelayCommand(async obj =>
            {
                await OrdersHelper.GetActiveOrders();
                if (OrdersHelper.ActiveOrders.Count == 0)
                {
                    await ApplicationState.CloseDuty(Sum, Comment);
                    this.Close();
                    await Auth.LogoutUser();
                }
                else
                {
                    this.Close();
                    new OneButtonedPopup("Невозможно закрыть смену, пока есть открытые заказы", "Ошибка").ShowDialog();
                }
            });
        }
    }
}
