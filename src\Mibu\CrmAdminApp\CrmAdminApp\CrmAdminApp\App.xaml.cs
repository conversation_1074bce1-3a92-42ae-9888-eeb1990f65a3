﻿using CrmAdminApp;
using CrmAdminApp.Helpers;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMAdminMoblieApp.Views.Pages.Settings;
using CRMGamificationAPIWrapper;
using CRMMoblieApiWrapper;
using MobPhone;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace CRMAdminMoblieApp
{
    public partial class App : Application
    {

        public App()
        {
            Sharpnado.CollectionView.Initializer.Initialize(true, false);
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzE5MDk1N0AzMjM1MmUzMDJlMzBJVTErUzQwNDNOdCs0YnV1dTFWcm0zd2NhcXBqSGh6MXUwTm01aDZRemFjPQ==");
            InitializeComponent();

            DevExpress.XamarinForms.Editors.Initializer.Init();
            DevExpress.XamarinForms.DataForm.Initializer.Init();
            DevExpress.XamarinForms.Core.Initializer.Init();

            MainPage = new NavigationPage(new SplashPage());
        }


   


        protected async override void OnStart()
        {
            await ApplicationState.LoadData();
            await Device.InvokeOnMainThreadAsync(async() =>
            {
                if (Auth.User == null)
                {
                    MainPage = new NavigationPage(new WelcomePage());
                }
                else if (ApplicationState.Subscription.IsExpired)
                {
                    MainPage = new NavigationPage(new MainPage(true));
                }
                else if(ApplicationState.CurrentTradeNetwork == null)
                {
                    MainPage = new NavigationPage(new TradeNetworks());
                }
                else if (ApplicationState.CurrentStore == null)
                {
                    MainPage = new NavigationPage(new TradeNetworkStores(ApplicationState.CurrentTradeNetwork));
                }
                else
                {
                    await ApplicationState.GetDuty();
                    await PagesHelper.BuildDashboardPages();
                    MainPage = new NavigationPage(PagesHelper.GetRealTimePage());
                }
        
            });

            StartCheckSessionLoop();
        }  

        protected override void OnSleep()
        {
        }

        protected override void OnResume()
        {
        }



        private static async Task StartCheckSessionLoop()
        {
            Device.StartTimer(TimeSpan.FromSeconds(60), () =>
            {
                CheckSessionLoop();
                return true;
            });
        }

        private static async Task CheckSessionLoop()
        {
            if (Auth.User != null)
            {
                if (await MobileAPI.AdminMethods.AdminProfileMethods.HasProfileBySessionToken(ApplicationState.CurrentDomain, Auth.User.SessionToken) == false)
                {
                    Auth.User = null;
                    await Device.InvokeOnMainThreadAsync(async () =>
                    {
                        App.Current.MainPage = new NavigationPage(new MainPage("Пароль пользователя был изменен."));
                    });
                }
                else
                {
                    if (await MobileAPI.MainMethods.DoesDomainExist(ApplicationState.CurrentDomain) == false)
                    {
                        Auth.User = null;
                        ApplicationState.Subscription = null;

                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            App.Current.MainPage = new NavigationPage(new MainPage("Домен не существует или был удален"));
                        });
                    }
                    else if (await MobileAPI.MainMethods.IsSubscriptionExpired(ApplicationState.CurrentDomain) == true)
                    {
                        Auth.User = null;
                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            App.Current.MainPage = new NavigationPage(new MainPage("Необходимо оплатить подписку"));
                        });
                    }
                }
            }
        }
    }
}
