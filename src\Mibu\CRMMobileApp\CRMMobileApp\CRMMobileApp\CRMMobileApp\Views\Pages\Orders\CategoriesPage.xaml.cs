﻿using CRM.Models.Network;
using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using Tech = CRM.Models.Network.ReferenceBooks.Menu.TechnicalCard;
using TechnicalCard = CRMMobileApp.Controls.Templates.TechnicalCard;

namespace CRMMobileApp.Views.Pages.Orders
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CategoriesPage : OrdersPage
    {
      


       
        public CategoriesPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        //      public CategoriesPage(MenuCategoriesTreeItem currentCategory)
        //      {
        //          InitializeComponent();
        //          NavigationPage.SetHasNavigationBar(this, false);
        //}

        protected override void OnAppearing()
        {
            base.OnAppearing();

            leftOrderPanel.Init();
        }





























    }
}