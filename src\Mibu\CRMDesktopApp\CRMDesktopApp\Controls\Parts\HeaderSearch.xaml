﻿<UserControl x:Class="CRMDesktopApp.Controls.Parts.HeaderSearch"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Parts"
             xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="60">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid 
        Background="{StaticResource bg_purple}"
        Grid.Row="0">

        <StackPanel 
            VerticalAlignment="Center"
            Orientation="Horizontal">

            <customcontrols:ImageButton 
                Command="{Binding ElementName=this,Path=GoBack}"
                Margin="30,0,0,0"
                VerticalAlignment="Center"
                Width="10"
                Height="20"
                Source="pack://application:,,,/Resources/Images/arrowBack.png"/>

            <Image
                Margin="30,5,0,0"
                VerticalAlignment="Center"
                Stretch="Fill"
                Source="pack://application:,,,/Resources/Images/logo.png"
                Height="50"
                Width="60"/>


        </StackPanel>

        <TextBlock
            VerticalAlignment="Center"
            HorizontalAlignment="Center"
            Foreground="{StaticResource dark_purple}"
            FontFamily="TTFirsNeue-Regular"
            FontSize="20"
            Text="{Binding ElementName=this,Path=Title}"/>


        <Grid 
            VerticalAlignment="Center"
            Margin="0,0,30,0"
            HorizontalAlignment="Right">

            <customcontrols:EntryOutlined
                IsEnabled="False"
                Width="220"
                Height="30"
                CornerRadius="10"
                PlaceholderMargin="40,0,0,0"
                TextMargin="40,0,0,0"
                EntryBackground="White"
                BorderColor="White"
                PlaceholderColor="{StaticResource text_gray}"
                Placeholder="Поиск"/>

            <Image
                Margin="12,7,7,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Width="16"
                Height="16"
                Source="pack://application:,,,/Resources/Images/search.png"/>

        </Grid>

    </Grid>
</UserControl>
