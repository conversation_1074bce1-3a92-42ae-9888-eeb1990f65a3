﻿using CRM.Database.Core;
using CRM.Models;
using Microsoft.AspNetCore.Mvc;

namespace CRMWeb.MobileAPI.Abstractions
{
    //[ApiController]
    //[Route("[controller]")]
    public abstract class AbsController : Controller
    {
        public DatabaseContext DB { get; set; }
        public IWebHostEnvironment AppEnvironment { get; set; }
        public AbsController(DatabaseContext db, IWebHostEnvironment appEnv)
        {
            DB = db;
            AppEnvironment = appEnv;
        }
        [NonAction]
        public CRMInstance GetCRM(string domain)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.CRMs.FirstOrDefault();
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }

        //[NonAction]
        //public async Task<string> SetAttachmentIfHas(string domain, string oldFilePath, int index = 0)
        //{
        //	var dateStr = DateTime.UtcNow.ToString("dd-MM-yyyy");

        //	var attachment = Request.Form.Files.Skip(index).FirstOrDefault();
        //	var filePath = $"/uploads/{domain}/{dateStr}/" + Guid.NewGuid().ToString();

        //	if (attachment != null)
        //	{
        //		string path = filePath + attachment.FileName;
        //		string fullPath = AppEnvironment.WebRootPath + path;

        //		var dirName = Path.GetDirectoryName(fullPath);
        //		if (!Directory.Exists(dirName))
        //		{
        //			Directory.CreateDirectory(dirName);
        //		}

        //		using (var fileStream = new FileStream(fullPath, FileMode.Create))
        //		{
        //			await attachment.CopyToAsync(fileStream);
        //		}
        //		return path;
        //	}
        //	return oldFilePath;
        //}

        //[NonAction]
        //public async Task<string> SetAttachmentIfHasWithBasePath(string domain, string oldFilePath, string basePathTo, int index = 0)
        //{
        //	var dateStr = DateTime.UtcNow.ToString("dd-MM-yyyy");

        //	var attachment = Request.Form.Files.Skip(index).FirstOrDefault();
        //	var filePath = $"/uploads/{domain}/{dateStr}/" + Guid.NewGuid().ToString();

        //	if (attachment != null)
        //	{
        //		string path = filePath + attachment.FileName;
        //		string fullPath = basePathTo + path;

        //		var dirName = Path.GetDirectoryName(fullPath);
        //		if (!Directory.Exists(dirName))
        //		{
        //			Directory.CreateDirectory(dirName);
        //		}

        //		using (var fileStream = new FileStream(fullPath, FileMode.Create))
        //		{
        //			await attachment.CopyToAsync(fileStream);
        //		}
        //		return path;
        //	}
        //	return oldFilePath;
        //}

        [NonAction]
		public async Task<string> SetAttachmentIfHasWithBasePath(string domain, string basePathTo, string filename, byte[] fileBytes)
		{
			var dateStr = DateTime.UtcNow.ToString("dd-MM-yyyy");

			string dirRelativePath = $"/uploads/{domain}/{dateStr}/";
			var filePath = dirRelativePath + Guid.NewGuid().ToString() + filename;

			string fullDirPath = basePathTo + dirRelativePath;
			fullDirPath = fullDirPath.Replace("/", "\\");
			if (!Directory.Exists(fullDirPath))
			{
				Directory.CreateDirectory(fullDirPath);
			}


			string fullPath = basePathTo + filePath;


			if (fileBytes != null && fileBytes.Length > 0)
			{			
				System.IO.File.WriteAllBytes(fullPath.Replace("/","\\"), fileBytes);			
			}

			return filePath;
		}

		[NonAction]
		public string GenerateFileName(string extension)
		{
			if (extension.StartsWith('.'))
				extension = extension.Substring(1);

			return Guid.NewGuid().ToString() + "." + extension;
		}
	}   
}
