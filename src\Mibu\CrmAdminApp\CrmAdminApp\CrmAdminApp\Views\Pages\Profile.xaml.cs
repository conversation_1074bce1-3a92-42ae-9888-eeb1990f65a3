﻿using CRM.Models.Network;
using CrmAdminApp.Helpers;
using CRMAdminApp.Popups;
using CRMAdminMoblieApp.Helpers;
using CRMGamificationAPIWrapper;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Essentials;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Profile : ContentPage
    {
		private bool isAvatarChanged;
		public string selectedAvatarFilename;
		public byte[] selectedAvatarBytes;
		public Profile()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            User = Auth.User.Clone();


            Init();


            userAvatar.Source = MobileAPI.GetFullFilePath(User.AvatarPath);

			dateEdit.Date = User.BirthDate;
        }
        private async Task Init()
        {
            await Device.InvokeOnMainThreadAsync(async () =>
            {
                await Auth.AuthorizeInGamification();
                notificationsLayout.IsVisible = Auth.GamificationUser != null && ApplicationState.Subscription.IsGamificationAvailable();

                shouldSentPushNotificationsToggledShitcher.IsToggled = Auth.GamificationUser.ShouldSentPushNotifications;
            });
        }




        private NetworkAdminTabUser user;
        public NetworkAdminTabUser User
        {
            get => user;
            set { user = value; OnPropertyChanged(nameof(User)); }
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }


        private ICommand saveChanges;
        public ICommand SaveChanges
        {
            get => saveChanges ??= new RelayCommand(async obj =>
            {
				await Device.InvokeOnMainThreadAsync(async () =>
				{
					try
					{
						await AnimateButton(saveChangesBtn);

						User.BirthDate = (DateTime)dateEdit.Date;
						await Auth.UpdateUser(User);


						if (isAvatarChanged)
						{
							await Auth.UpdateUserAvatar(ApplicationState.CurrentDomain, selectedAvatarFilename, selectedAvatarBytes);
							PagesHelper.RefreshApplicationHeader();
						}

						await App.Current.MainPage.Navigation.PopAsync();
					}
					catch (Exception ex)
					{
						await App.Current.MainPage.DisplayAlert("error", ex.Message, "ok");
					}
				});
			});
        }

        #region Password
        private string oldPassword = "";
        public string OldPassword
        {
            get => oldPassword;
            set { oldPassword = value; OnPropertyChanged(nameof(OldPassword)); }
        }
        private string newPassword = "";
        public string NewPassword
        {
            get => newPassword;
            set { newPassword = value; OnPropertyChanged(nameof(NewPassword)); }
        }
        private ICommand changePassword;
        public ICommand ChangePassword
        {
            get => changePassword ??= new RelayCommand(async obj =>
            {
				await AnimateButton(savePasswordBtn);

				if (User.Password != OldPassword)
                {
                    changePwdErrorText.Text = "Старый пароль неверно указан";
                }
                else if (NewPassword.Length < 4) 
                {
                    changePwdErrorText.Text = "Пароль должен иметь как минимум четыре символа";
                }
                else
                {
                    User = Auth.User.Clone();
                    User.Password = NewPassword;

                    await Auth.UpdateUser(User);
                    await App.Current.MainPage.Navigation.PopAsync();
                }
            });
        }



        private void showOrHideOldPassword(object sender, EventArgs e)
        {
            oldPwdEntry.IsPassword = !oldPwdEntry.IsPassword;
            if (oldPwdEntry.IsPassword)
            {
                hideOldPwdBtn.Source = ImageSource.FromFile("hidePassword.png");
            }
            else
            {
                hideOldPwdBtn.Source = ImageSource.FromFile("showPassword.png");
            }
        }
        private void showOrHideNewPassword(object sender, EventArgs e)
        {
            newPwdEntry.IsPassword = !newPwdEntry.IsPassword;
            if (newPwdEntry.IsPassword)
            {
                hideNewPwdBtn.Source = ImageSource.FromFile("hidePassword.png");
            }
            else
            {
                hideNewPwdBtn.Source = ImageSource.FromFile("showPassword.png");
            }
        }

		#endregion

		#region Avatar

		private ICommand openGallery;
		public ICommand OpenGallery
		{
			get => openGallery ??= new RelayCommand(async obj =>
			{
                await Device.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        var photo = await MediaPicker.PickPhotoAsync();
                        if (photo != null)
                        {
                            var cropperPopup = new PopupImageCrop(photo.FullPath);
                            cropperPopup.Dismissed += (o, e) =>
                            {
                                var stream = e as Stream;
                                if (stream != null)
                                {
                                    using (MemoryStream ms = new MemoryStream())
                                    {
                                        stream.CopyTo(ms);

                                        isAvatarChanged = true;
                                        selectedAvatarFilename = photo.FileName;
                                        selectedAvatarBytes = ms.ToArray();



                                        userAvatar.Source = ImageSource.FromStream(() =>
                                        {
                                             return new MemoryStream(selectedAvatarBytes);
                                        });
                                    }
                                }
                            };
                            await App.Current.MainPage.Navigation.PushPopupAsync(cropperPopup);
                        }

                    }
                    catch (FeatureNotSupportedException fnsEx)
                    {
                        await DisplayAlert("Ошибка", "Нет камеры на телефоне", "Купить новый");
                    }
                    catch (PermissionException pEx)
                    {
                        await DisplayAlert("Ошибка", "Нет доступа к галерее", "Ок");
                    }
                    catch (Exception ex)
                    {
                        await DisplayAlert("Ошибка", ex.Message, "Ок");
                    }
                });

			});
		}

		#endregion

		private async Task AnimateButton(Xamarin.Forms.Button button)
		{
			await Device.InvokeOnMainThreadAsync(async () =>
			{
				await button.ScaleTo(1.06, 100);
				button.ScaleTo(1, 100);
			});
		}


        private void onShouldSentPushNotificationsToggled(object sender, ToggledEventArgs e)
        {
            if(Auth.GamificationUser != null)
            {
                GamificationAPI.Barista.Profile.SetShouldSendPushNotifications(Auth.GamificationUser.Id, e.Value);
            }
        }
    }
}