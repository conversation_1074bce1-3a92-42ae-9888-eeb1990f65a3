﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.Editors
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CustomCrossRadioButton : ContentView
    {
        public CustomCrossRadioButton()
        {
            InitializeComponent();
        }

        protected override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            var a = this.ControlTemplate;
        }


        public static readonly BindableProperty TextProperty = BindableProperty.Create(nameof(Text), typeof(string), typeof(CustomCrossRadioButton));
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }
        public static readonly BindableProperty GroupNameProperty = BindableProperty.Create(nameof(GroupName), typeof(string), typeof(CustomCrossRadioButton));
        public string GroupName
        {
            get { return (string)GetValue(GroupNameProperty); }
            set { SetValue(GroupNameProperty, value); }
        }
        public static readonly BindableProperty IsCheckedProperty = BindableProperty.Create(nameof(IsChecked), typeof(bool), typeof(CustomCrossRadioButton), defaultBindingMode: BindingMode.TwoWay, propertyChanged: (a, b, c) =>
        {
            var control = a as CustomCrossRadioButton;
            if (control.IsChecked)
            {
                control.ControlTemplate = control.CheckedStateTemplate;
            }
            else
            {
                control.ControlTemplate = control.UncheckedStateTemplate;
            }
        });
        public bool IsChecked
        {
            get { return (bool)GetValue(IsCheckedProperty); }
            set { SetValue(IsCheckedProperty, value); }
        }




        public static readonly BindableProperty UncheckedStateTemplateProperty = BindableProperty.Create(nameof(UncheckedStateTemplate), typeof(ControlTemplate), typeof(CustomCrossRadioButton), propertyChanged: (a, b, c) =>
        {
            var control = a as CustomCrossRadioButton;

            var ct = control.ControlTemplate;

            if ((control.ControlTemplate == null || control.ControlTemplate != control.UncheckedStateTemplate) && !control.IsChecked)
            {
                control.ControlTemplate = control.UncheckedStateTemplate;
            }
        });
        public ControlTemplate UncheckedStateTemplate
        {
            get { return (ControlTemplate)GetValue(UncheckedStateTemplateProperty); }
            set { SetValue(UncheckedStateTemplateProperty, value); }
        }
        public static readonly BindableProperty CheckedStateTemplateProperty = BindableProperty.Create(nameof(CheckedStateTemplate), typeof(ControlTemplate), typeof(CustomCrossRadioButton), propertyChanged: (a, b, c) =>
        {
            var control = a as CustomCrossRadioButton;

            var ct = control.ControlTemplate;

            if ((control.ControlTemplate == null || control.ControlTemplate != control.CheckedStateTemplate) && control.IsChecked)
            {
                control.ControlTemplate = control.CheckedStateTemplate;
            }
        });
        public ControlTemplate CheckedStateTemplate
        {
            get { return (ControlTemplate)GetValue(CheckedStateTemplateProperty); }
            set { SetValue(CheckedStateTemplateProperty, value); }
        }


        public event EventHandler<EventArgs> CheckedChanged;
        private void onTapped(object sender, EventArgs e)
        {
            IsChecked = true;
            CheckedChanged?.Invoke(this, e);
        }
    }
}