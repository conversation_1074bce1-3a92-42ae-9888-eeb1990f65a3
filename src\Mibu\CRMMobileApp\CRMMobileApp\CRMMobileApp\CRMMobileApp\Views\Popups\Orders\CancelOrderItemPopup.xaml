﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.CancelOrderItemPopup">
    <Grid>


        <Frame
             HasShadow="{OnPlatform WPF=True, Default=False}"
             BackgroundColor="White"
             Background="White"
             WidthRequest="300"
             HeightRequest="240"
             Padding="0"
             HorizontalOptions="Center"
             VerticalOptions="Start"
             Margin="0,70,0,0"
             CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <Grid 
                     Grid.Row="0"
                     Margin="0,20,0,0">
                    <StackLayout Orientation="Horizontal">
                        <Image 
                             WidthRequest="40"
                             HeightRequest="40"/>
                        <Label
                             TextColor="{StaticResource dark_purple}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="18"
                             FontAttributes="Bold"
                             Text="Возврат заказа"/>
                    </StackLayout>


                    <ImageButton 
                         Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                         Command="{Binding Source={x:Reference this},Path=Close}"
                         BackgroundColor="Transparent"
                         VerticalOptions="Start"
                         HorizontalOptions="End"
                         Padding="10"
                         WidthRequest="36"
                         HeightRequest="36"
                         Margin="0,5,10,0"/>
                </Grid>


                <Grid Grid.Row="1">

                    <StackLayout
                         VerticalOptions="Fill"
                         HorizontalOptions="Fill"
                         Margin="30,0,30,0"
                         Spacing="12">

                        <Button 
                             Command="{Binding Source={x:Reference this},Path=CancelPosition}"
                             CommandParameter="Ошибка официанта"
                             Style="{StaticResource purple_gradient_btn}"
                             VerticalOptions="Start"
                             Text="Ошибка официанта"
                             HeightRequest="40"/>

                        <Button 
                             Command="{Binding Source={x:Reference this},Path=CancelPosition}"
                             CommandParameter="Отказ клиента"
                             Style="{StaticResource purple_gradient_btn}"
                             VerticalOptions="Start"
                             Text="Отказ клиента"
                             HeightRequest="40"/>

                        <Button 
                             Command="{Binding Source={x:Reference this},Path=CancelPosition}"
                             CommandParameter="Нет в наличии"
                             Style="{StaticResource purple_gradient_btn}"
                             VerticalOptions="Start"
                             Text="Нет в наличии"
                             HeightRequest="40"/>

                    </StackLayout>

                </Grid>


            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>