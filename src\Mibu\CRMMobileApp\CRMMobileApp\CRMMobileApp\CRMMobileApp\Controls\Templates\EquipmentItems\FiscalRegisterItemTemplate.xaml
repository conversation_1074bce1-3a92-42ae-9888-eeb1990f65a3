﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.FiscalRegisterItemTemplate">
    <ContentView.Content>
        <Grid BackgroundColor="Transparent">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onItemTapped"/>
            </Grid.GestureRecognizers>
            <StackLayout VerticalOptions="Center">
                <Label 
                    x:Name="providerLabel"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Start"
                    FontSize="14"
                    Text="{Binding Source={x:Reference this},Path=Registrator.Name}"
                    TextColor="{StaticResource dark_purple}"/>
                <Label 
                    Margin="0,0,0,0"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Start"
                    FontSize="14"
                    Text="{Binding Source={x:Reference this},Path=Registrator.ConnectionDescription}"
                    TextColor="{StaticResource text_gray}"/>
            </StackLayout>

        </Grid>
    </ContentView.Content>
</ContentView>