﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.TabView
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CustomTabViewItemInternalHeader : ContentView
    {
        private CustomTabViewItem _parent;
        public CustomTabViewItemInternalHeader(CustomTabViewItem parent, string guid)
        {
            _parent = parent;

            InitializeComponent();

            hiddenRb.GroupName = guid;

            unselectedStateFrame.Content = parent.TabHeaderUnselectedStateContent;
            selectedStateFrame.Content = parent.TabHeaderSelectedStateContent;
        }


        public static readonly BindableProperty IsSelectedProperty = BindableProperty.Create(nameof(IsSelected), typeof(bool), typeof(CustomTabViewItemInternalHeader), false);
        public bool IsSelected
        {
            get { return (bool)GetValue(IsSelectedProperty); }
            set { SetValue(IsSelectedProperty, value); }
        }


        //public static readonly BindableProperty TabHeaderUnselectedStateContentProperty = BindableProperty.Create(nameof(TabHeaderUnselectedStateContent), typeof(CustomTabViewItemInternalHeader), typeof(CustomTabViewItem), null);
        //public View TabHeaderUnselectedStateContent
        //{
        //    get { return (View)GetValue(TabHeaderUnselectedStateContentProperty); }
        //    set { SetValue(TabHeaderUnselectedStateContentProperty, value); }
        //}

        //public static readonly BindableProperty TabHeaderSelectedStateContentProperty = BindableProperty.Create(nameof(TabHeaderSelectedStateContent), typeof(View), typeof(CustomTabViewItemInternalHeader), null);
        //public View TabHeaderSelectedStateContent
        //{
        //    get { return (View)GetValue(TabHeaderSelectedStateContentProperty); }
        //    set { SetValue(TabHeaderSelectedStateContentProperty, value); }
        //}


        public void SetAsTapped()
        {
            IsSelected = true;
            TabSelected?.Invoke(this, _parent);
        }


        public event EventHandler<CustomTabViewItem> TabSelected;
        private void onTapped(object sender, EventArgs e)
        {
            SetAsTapped();
        }
    }
}