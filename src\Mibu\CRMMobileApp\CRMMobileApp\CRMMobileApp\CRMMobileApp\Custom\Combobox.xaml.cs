﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Custom
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Combobox : ContentView
    {
        private static readonly string ArrowDown = "arrowDown.png";
        private static readonly string ArrowUp = "arrowDown.png";
        public Combobox()
        {
            InitializeComponent();
            Init();
        }
        public static readonly BindableProperty IsExpandedProperty =
            BindableProperty.Create(nameof(IsExpanded), typeof(bool), typeof(Combobox), false, BindingMode.TwoWay);
        public bool IsExpanded
        {
            get => (bool)GetValue(IsExpandedProperty);
            set => SetValue(IsExpandedProperty, value);
        }
        #region Размеры
        public static readonly BindableProperty ExpandedContentMarginProperty =
         BindableProperty.Create(nameof(ExpandedContentMargin), typeof(Thickness), typeof(Combobox),new Thickness(0), BindingMode.TwoWay);
        public Thickness ExpandedContentMargin
        {
            get => (Thickness)GetValue(ExpandedContentMarginProperty);
            set => SetValue(ExpandedContentMarginProperty, value);
        }
        public static readonly BindableProperty ExpandedHeightRequestProperty =
           BindableProperty.Create(nameof(ExpandedHeightRequest), typeof(double), typeof(Combobox), 280d, BindingMode.TwoWay);
        public double ExpandedHeightRequest
        {
            get => (double)GetValue(ExpandedHeightRequestProperty);
            set => SetValue(ExpandedHeightRequestProperty, value);
        }
        public static readonly BindableProperty CollapsedHeightRequestProperty =
         BindableProperty.Create(nameof(CollapsedHeightRequest), typeof(double), typeof(Combobox), 70d, BindingMode.TwoWay);
        public double CollapsedHeightRequest
        {
            get => (double)GetValue(CollapsedHeightRequestProperty);
            set => SetValue(CollapsedHeightRequestProperty, value);
        }
        public static readonly BindableProperty RowHeightProperty =
        BindableProperty.Create(nameof(RowHeight), typeof(double), typeof(Combobox), 280d, BindingMode.TwoWay);
        public double RowHeight
        {
            get => (double)GetValue(RowHeightProperty);
            set => SetValue(RowHeightProperty, value);
        }

        #endregion

        #region Свойства
        public static readonly BindableProperty CornerRadiusProperty =
          BindableProperty.Create(nameof(CornerRadius), typeof(float), typeof(Combobox), 5f, BindingMode.TwoWay);
        public float CornerRadius
        {
            get => (float)GetValue(CornerRadiusProperty);
            set => SetValue(CornerRadiusProperty, value);
        }
        #endregion

        #region Цвета
        public static readonly BindableProperty HeaderBackgroundColorProperty =
           BindableProperty.Create(nameof(HeaderBackgroundColor), typeof(Color), typeof(Combobox), Color.Gray, BindingMode.TwoWay);
        public Color HeaderBackgroundColor
        {
            get => (Color)GetValue(HeaderBackgroundColorProperty);
            set => SetValue(HeaderBackgroundColorProperty, value);
        }
        public static readonly BindableProperty PopupBackgroundColorProperty =
            BindableProperty.Create(nameof(HeaderBackgroundColor), typeof(Color), typeof(Combobox), Color.White, BindingMode.TwoWay);
        public Color PopupBackgroundColor
        {
            get => (Color)GetValue(PopupBackgroundColorProperty);
            set => SetValue(PopupBackgroundColorProperty, value);
        }
        #endregion

        #region Нижнее подчеркривание
        public static readonly BindableProperty UnderLineColorProperty =
        BindableProperty.Create(nameof(UnderLineColor), typeof(Color), typeof(Combobox), Color.Gray, BindingMode.TwoWay);
        public Color UnderLineColor
        {
            get => (Color)GetValue(UnderLineColorProperty);
            set => SetValue(UnderLineColorProperty, value);
        }
        public static readonly BindableProperty HasUnderLineProperty =
          BindableProperty.Create(nameof(HasUnderLine), typeof(bool), typeof(Combobox), false, BindingMode.TwoWay);
        public bool HasUnderLine
        {
            get => (bool)GetValue(HasUnderLineProperty);
            set => SetValue(HasUnderLineProperty, value);
        }
        #endregion

        #region Датабайндинг
        public static readonly BindableProperty ItemsSourceProperty =
           BindableProperty.Create(nameof(ItemsSource), typeof(IList), typeof(Combobox), null, BindingMode.TwoWay);
        public IList ItemsSource
        {
            get => (IList)GetValue(ItemsSourceProperty);
            set => SetValue(ItemsSourceProperty, value);
        }
        public static readonly BindableProperty SelectedItemProperty =
           BindableProperty.Create(nameof(SelectedItem), typeof(object), typeof(Combobox), null, BindingMode.TwoWay);
        public object SelectedItem
        {
            get => (object)GetValue(SelectedItemProperty);
            set => SetValue(SelectedItemProperty, value);
        }
        public static readonly BindableProperty ItemTemplateProperty =
         BindableProperty.Create(nameof(ItemTemplate), typeof(DataTemplate), typeof(Combobox), null, BindingMode.TwoWay);
        public DataTemplate ItemTemplate
        {
            get => (DataTemplate)GetValue(ItemTemplateProperty);
            set => SetValue(ItemTemplateProperty, value);
        }

        #endregion


        private string _expandIcon;
        public string ExpandIcon
        {
            get => _expandIcon;
            set
            {
                _expandIcon = value;
                OnPropertyChanged(nameof(ExpandIcon));
            }
        }




        public event EventHandler<object> SelectionChanged;





      
        private void Init()
        {
            ExpandIcon = ArrowDown;
            BodyContentView.IsVisible = false;
        }



        private void HeaderContent_OnTapped(object sender, EventArgs e)
        {
            if (!IsExpanded)
            {
                BodyContentView.Opacity = 0;
            }

            IsExpanded = !IsExpanded;
            ExpandIcon = IsExpanded ? ArrowUp : ArrowDown;

            BodyContentView.IsVisible = IsExpanded;
            BodyContentView.FadeTo(1, 400, Easing.SpringOut);


            if (IsExpanded)
                this.HeightRequest = ExpandedHeightRequest;
            else this.HeightRequest = CollapsedHeightRequest;
        }
    }
}