﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Name="this"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    CloseWhenBackgroundIsClicked="False"
    x:Class="CRMMobileApp.Views.Popups.SendReceiptTo">
    <Grid>

        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            WidthRequest="400"
            HeightRequest="250"
            Padding="0"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,70,0,0"
            CornerRadius="25">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <StackLayout
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Spacing="0">
                        <Label 
                            TextColor="{StaticResource dark_purple}"
                            FontSize="17"
                            HorizontalOptions="Center"
                            Text="Отправить чек на телефон"/>
                        <Label 
                            TextColor="{StaticResource dark_purple}"
                            FontSize="17"
                            HorizontalOptions="Center"
                            Text=" или электронную почту"/>
                        <Label 
                            TextColor="{StaticResource dark_purple}"
                            FontSize="10"
                            HorizontalOptions="Center"
                            Text="Заполнение обоих полей не обязательно"/>
                    </StackLayout>
                </Grid>


                <Grid Grid.Row="1">
                    <StackLayout 
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Spacing="10">
                        <controls:EntryOutlined   
                            Margin="20,0,20,0"
                            PlaceholderMargin="20,0,0,0"
                            TextMargin="20,0,0,0"
                            Placeholder="E-mail"
                            HorizontalOptions="Center"
                            Style="{StaticResource white_cornered_entry}"/>
                        <controls:EntryOutlined    
                            Margin="20,0,20,0"
                            PlaceholderMargin="20,0,0,0"
                            TextMargin="20,0,0,0"
                            Placeholder="Телефон"
                            HorizontalOptions="Center"
                            Style="{StaticResource white_cornered_entry}"/>
                    </StackLayout>
                </Grid>

                <Grid Grid.Row="2">
                    <StackLayout
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Margin="0,0,0,30"
                        Spacing="13"
                        Orientation="Horizontal">

                        <Button 
                            Style="{StaticResource bg_purple_btn}"
                            VerticalOptions="Center"
                            Text="Назад"
                            WidthRequest="170"
                            HeightRequest="40"/>
                        <Button 
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalOptions="Center"
                            Text="Отправить"
                            WidthRequest="170"
                            HeightRequest="40"/>

                    </StackLayout>
                </Grid>
            </Grid>



        </Frame>


    </Grid>
</animations:PopupPage>