﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

namespace TriggeroV2.Extensions
{
    public static class DoubleExtensions
    {
        public static bool TryParseSafely(string strVal, out double result)
        {
            var ret = 0.0;
            try
            {
                ret = double.Parse(strVal.Replace(",", "."), NumberStyles.Any, CultureInfo.InvariantCulture);
            }
            catch
            {
                result = ret;
                return false;
            }

            result = ret;
            return true;
        }
    }
}
