﻿using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.Text;
using System.Windows;
using System.Windows.Data;

namespace MarkupCreator.Converters
{
    public class EnumListToStringsConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return new ObservableCollection<string>();
            ObservableCollection<string> strings = new ObservableCollection<string>
                (EnumToListConverter.GetEnumStringValuesList(value.GetType().GenericTypeArguments[0]));
        
            return strings;
        } 

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return null;
        }
    }
}
