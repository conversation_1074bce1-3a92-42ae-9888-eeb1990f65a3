﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Parts.ListViews.CustomCrossCollectionView">

    <ContentView.Content>

        <Grid>

            <collectionview:DXCollectionView
                 x:Name="forAndroidIOSlistView"
                 IsVisible="{OnPlatform Android=True, iOS=True, Default=False}"
                 SelectionMode="{Binding Source={x:Reference this},Path=SelectionMode}"
                 Background="{Binding Source={x:Reference this},Path=Background}"
                 BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor}"
                 ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource, Mode=TwoWay}"
                 ItemTemplate="{Binding Source={x:Reference this},Path=ItemTemplateForMobile}"
                 ItemSpacing="{Binding Source={x:Reference this},Path=ItemSpacing}"
                 MinItemSize="{Binding Source={x:Reference this},Path=MinItemSize}">
            </collectionview:DXCollectionView>


            <ListView
                 x:Name="forWPFlistView"
                 IsVisible="{OnPlatform WPF=True, Default=False}"
                 HorizontalScrollBarVisibility="Never"
                 VerticalScrollBarVisibility="Never"
                 ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource, Mode=TwoWay}"
                 ItemTemplate="{Binding Source={x:Reference this},Path=ItemTemplateForWPF}"
                 RowHeight="{Binding Source={x:Reference this},Path=MinItemSize}"
                 Background="{Binding Source={x:Reference this},Path=Background}"
                 BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor}">

            </ListView>

        </Grid>
    </ContentView.Content>
</ContentView>