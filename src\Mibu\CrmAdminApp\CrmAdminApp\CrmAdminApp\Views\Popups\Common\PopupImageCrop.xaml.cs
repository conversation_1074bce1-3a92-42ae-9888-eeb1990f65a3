﻿using CRM.Models.Gamification.Abstractions;
using CRM.Models.Gamification.General;
using CRM.Models.Gamification.General.Levels;
using CRMAdminMoblieApp.Helpers;
using CRMGamificationAPIWrapper;
using MibuAdminApp.Abstractions;
using Syncfusion.SfImageEditor.XForms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminApp.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PopupImageCrop : ReturnPopupPage
    {
		public PopupImageCrop(ImageSource img)
		{
			InitializeComponent();

			cropper.Source = img;

			cropper.ImageLoaded += CropEditor_ImageLoaded;
			cropper.ImageSaving += editor_ImageSaving;
		}
		private void CropEditor_ImageLoaded(object sender, ImageLoadedEventArgs args)
		{
			Device.InvokeOnMainThreadAsync(async () =>
			{
				cropper.Margin = new Thickness(0, 0, 0, -60);
				cropper.ToolbarSettings.FooterToolbarHeight = 0.001;


				await Task.Delay(150);
				cropper.ToggleCropping(true, 0);
				await Task.Delay(10);
				cropper.ToggleCropping(true, 3);


				cropper.Opacity = 1;
			});
		}
		private void editor_ImageSaving(object sender, ImageSavingEventArgs args)
		{
			Device.InvokeOnMainThreadAsync(() =>
			{
				try
				{
					var source = ImageSource.FromStream(() =>
					{
						return args.Stream;
					});
					this.Dismiss(args.Stream);
				}
				catch (Exception ex)
				{

				}
			});
		}

		private void ClosePopup(object sender, EventArgs e)
		{
			this.Dismiss(null);
		}
		private void ConfirmCrop(object sender, EventArgs e)
		{
			Device.InvokeOnMainThreadAsync(() =>
			{
				try
				{
					cropper.Crop();
					cropper.Save();
				}
				catch (Exception ex)
				{

				}
			});
		}
	}
}