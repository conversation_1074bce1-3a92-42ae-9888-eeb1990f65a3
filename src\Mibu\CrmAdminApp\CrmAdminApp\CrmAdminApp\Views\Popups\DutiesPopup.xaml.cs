﻿using CRM.Models;
using CRM.Models.Network;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Models.Wrappers;
using CRMAdminMoblieApp.Views.Pages;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMMobileApp.Core;
using MarkupCreator.Helpers.Converters;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using System.Linq;

namespace CRMAdminMoblieApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DutiesPopup : PopupPage
    {
        public DutiesPopup()
        {
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                foreach (var duty in ApplicationState.Duties.OrderByDescending(o => o.Id))
                {
                    Duties.Add(new DutyWrapper { Duty = duty });
                }

                noItemsLabel.IsVisible = !Duties.Any();
                if (noItemsLabel.IsVisible)
                {
                    selectDutyBtn.InputTransparent = true;
                    selectDutyBtn.Opacity = 0.6;
                }
            });
		}

        private List<DutyWrapper> duties = new List<DutyWrapper>();
        public List<DutyWrapper> Duties
        {
            get => duties;
            set { duties = value; OnPropertyChanged(nameof(Duties)); }
        }

        private ICommand itemSelected;
        public ICommand ItemSelected
        {
            get => itemSelected ??= new RelayCommand(obj =>
            {
                SelectedDuty = obj as DutyWrapper;
                if (obj is null) return;

                SelectedDuty.IsSelected = true;

                var otherDuties = Duties.Where(o => o != obj);
                foreach (var otherDuty in otherDuties)
                    otherDuty.IsSelected = false;

            });
        }

        private DutyWrapper selectedDuty;
        public DutyWrapper SelectedDuty
        {
            get => selectedDuty;
            set { selectedDuty = value; OnPropertyChanged(nameof(SelectedDuty)); }
        }






        private ICommand selectDuty;
        public ICommand SelectDuty
        {
            get => selectDuty ??= new RelayCommand(async obj =>
            {
                if(SelectedDuty == null)
                {
                    await DisplayAlert("Уведомление", "Сначала выберите смену","Ок");
                    return;
                }
				await App.Current.MainPage.Navigation.PopPopupAsync();
			    ApplicationState.SetDuty(SelectedDuty.Duty);
            
            });
        }


    }
}