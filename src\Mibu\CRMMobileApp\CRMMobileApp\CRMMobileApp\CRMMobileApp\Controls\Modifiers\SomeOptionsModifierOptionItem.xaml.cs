﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using CRM.Models.Stores.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Views.Popups;
using Rg.Plugins.Popup.Extensions;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Modifiers
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SomeOptionsModifierOptionItem : ContentView
    {
        public SomeOptionsModifierOptionItem(OrderItemModifierOption option)
        {
            InitializeComponent();
            Model = option;
        }

        public static readonly BindableProperty ModelProperty =
          BindableProperty.Create(nameof(Model), typeof(OrderItemModifierOption), typeof(SomeOptionsModifierOptionItem));
        public OrderItemModifierOption Model
        {
            get { return (OrderItemModifierOption)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        private ICommand incrementAmount;
        public ICommand IncrementAmount
        {
            get => incrementAmount ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (!Model.ModifierOption.MaxCount.IsLimited)
                    {
                        Model.Amount++;
                        amountLabel.Text = $"{Model.Amount}";
                    }
                    else if (Model.ModifierOption.MaxCount.Limit >= Model.Amount + 1)
                    {
                        Model.Amount++;
                        amountLabel.Text = $"{Model.Amount}";
                    }
                    else
                    {
                        App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Достигнут лимит по количеству модификатора", "Уведомление"));
                    }
                });
            });
        }
        private ICommand decrementAmount;
        public ICommand DecrementAmount
        {
            get => decrementAmount ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (Model.Amount > 0)
                        Model.Amount--;
                    amountLabel.Text = $"{Model.Amount}";
                });
            });
        }
    }
}