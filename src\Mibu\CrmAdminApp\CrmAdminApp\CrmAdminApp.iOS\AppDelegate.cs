﻿using System;
using System.Collections.Generic;
using System.Linq;
using CrmAdminApp.iOS;
using Foundation;
using Plugin.FirebasePushNotification;
using UIKit;

namespace CRMAdminMoblieApp.iOS
{
    // The UIApplicationDelegate for the application. This class is responsible for launching the 
    // User Interface of the application, as well as listening (and optionally responding) to 
    // application events from iOS.
    [Register("AppDelegate")]
    public partial class AppDelegate : global::Xamarin.Forms.Platform.iOS.FormsApplicationDelegate
    {
        //
        // This method is invoked when the application has loaded and is ready to run. In this 
        // method you should instantiate the window, load the UI into it and then make the window
        // visible.
        //
        // You have 17 seconds to return from this method, or iOS will terminate your application.
        //
        public override bool FinishedLaunching(UIApplication app, NSDictionary options)
        {
            Sharpnado.CollectionView.iOS.Initializer.Initialize();

            global::Xamarin.Forms.Forms.Init();

            Sharpnado.MaterialFrame.iOS.iOSMaterialFrameRenderer.Init();


            CrmAdminApp.iOS.CustomEntryRenderer.Load();
            CrmAdminApp.iOS.CustomEditorRenderer.Load();
            CrmAdminApp.iOS.TouchableFrameRenderer.Load();

            Syncfusion.SfPicker.XForms.iOS.SfPickerRenderer.Init();
			Syncfusion.SfImageEditor.XForms.iOS.SfImageEditorRenderer.Init();


            FirebasePushNotificationManager.Initialize(options, true);


            LoadApplication(new App());


            DevExpress.XamarinForms.Editors.iOS.Initializer.Init();
            DevExpress.XamarinForms.DataForm.iOS.Initializer.Init();

            return base.FinishedLaunching(app, options);
        }

        public override void RegisteredForRemoteNotifications(UIApplication application, NSData deviceToken)
        {
            FirebasePushNotificationManager.DidRegisterRemoteNotifications(deviceToken);
        }

        public override void FailedToRegisterForRemoteNotifications(UIApplication application, NSError error)
        {
            FirebasePushNotificationManager.RemoteNotificationRegistrationFailed(error);

        }
        // To receive notifications in foregroung on iOS 9 and below.
        // To receive notifications in background in any iOS version
        public override void DidReceiveRemoteNotification(UIApplication application, NSDictionary userInfo, Action<UIBackgroundFetchResult> completionHandler)
        {
            // If you are receiving a notification message while your app is in the background,
            // this callback will not be fired 'till the user taps on the notification launching the application.

            // If you disable method swizzling, you'll need to call this method. 
            // This lets FCM track message delivery and analytics, which is performed
            // automatically with method swizzling enabled.
            FirebasePushNotificationManager.DidReceiveMessage(userInfo);
            // Do your magic to handle the notification data
            System.Console.WriteLine(userInfo);

            completionHandler(UIBackgroundFetchResult.NewData);
        }
    }
}
