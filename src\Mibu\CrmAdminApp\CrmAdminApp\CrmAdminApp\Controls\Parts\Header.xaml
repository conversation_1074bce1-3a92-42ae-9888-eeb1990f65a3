﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Controls.Parts.Header"
             x:Name="this"
             HeightRequest="60">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
  <ContentView.Content>
        <Grid>


            <Grid 
                 HeightRequest="60"
                 VerticalOptions="End">

                <ImageButton 
                     Source="burger.png"
                     Command="{Binding Source={x:Reference this},Path=OpenMenu}"
                     HeightRequest="18"
                     WidthRequest="30"
                     CornerRadius="0"
                     BackgroundColor="Transparent"
                     Margin="30,0,0,0"
                     VerticalOptions="Center"
                     HorizontalOptions="Start"/>


                <StackLayout 
                     Margin="0,0,30,0"
                     VerticalOptions="Center"
                     HorizontalOptions="End"
                     Spacing="10"
                     Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenUserMenu}"/>
                    </StackLayout.GestureRecognizers>
                    <Frame 
                         HasShadow="False"
                         VerticalOptions="Center"
                         BackgroundColor="{x:StaticResource text_gray}"
                         Padding="0"
                         IsClippedToBounds="True"
                         CornerRadius="20"
                         HeightRequest="40"
                         WidthRequest="40">
                        <Grid>
                            <Image
                                x:Name="avatarImg"
                                Margin="0"
                                Aspect="Fill"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill"
                                Source="userAvatar.png" />
                        </Grid>
                    </Frame>

                    <ImageButton 
                         Source="arrowDown.png"
                         HeightRequest="5"
                         WidthRequest="10"
                         BackgroundColor="Transparent"
                         VerticalOptions="Center"/>
                </StackLayout>


                <Image 
                     Margin="0,0,0,-6"
                     VerticalOptions="End"
                     HeightRequest="6"
                     Aspect="Fill"
                     Source="shadowLine.png"></Image>


            </Grid>

           
            
        </Grid>
    </ContentView.Content>
</ContentView>