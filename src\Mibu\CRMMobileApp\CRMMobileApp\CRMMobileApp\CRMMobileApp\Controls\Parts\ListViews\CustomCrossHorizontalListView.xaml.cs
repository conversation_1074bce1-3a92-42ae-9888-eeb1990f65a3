﻿using DevExpress.XamarinForms.CollectionView;
using Sharpnado.CollectionView.RenderedViews;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CarouselView = Xamarin.Forms.CarouselView;

namespace CRMMobileApp.Controls.Parts.ListViews
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CustomCrossHorizontalListView : ContentView
    {
        public CustomCrossHorizontalListView()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ItemWidthProperty = BindableProperty.Create(nameof(ItemWidth), typeof(double), typeof(CustomCrossHorizontalListView), defaultBindingMode: BindingMode.TwoWay);
        public double ItemWidth
        {
            get { return (double)GetValue(ItemWidthProperty); }
            set { SetValue(ItemWidthProperty, value); }
        }
        public static readonly BindableProperty ItemHeightProperty = BindableProperty.Create(nameof(ItemHeight), typeof(double), typeof(CustomCrossHorizontalListView), defaultBindingMode: BindingMode.TwoWay);
        public double ItemHeight
        {
            get { return (double)GetValue(ItemHeightProperty); }
            set { SetValue(ItemHeightProperty, value); }
        }

        public static readonly BindableProperty ItemSpacingProperty = BindableProperty.Create(nameof(ItemSpacing), typeof(int), typeof(CustomCrossHorizontalListView), defaultBindingMode: BindingMode.TwoWay);
        public int ItemSpacing
        {
            get { return (int)GetValue(ItemSpacingProperty); }
            set { SetValue(ItemSpacingProperty, value); }
        }

        public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(IEnumerable), typeof(CustomCrossHorizontalListView), propertyChanged: delegate (BindableObject x, object oldValue, object newValue)
        {

        });
        public IEnumerable ItemsSource
        {
            get { return (IEnumerable)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }

        public static readonly BindableProperty TapCommandProperty = BindableProperty.Create(nameof(TapCommand), typeof(ICommand), typeof(CustomCrossHorizontalListView));
        public ICommand TapCommand
        {
            get { return (ICommand)GetValue(TapCommandProperty); }
            set { SetValue(TapCommandProperty, value); }
        }





        public static readonly BindableProperty ItemTemplateForMobileProperty = BindableProperty.Create(nameof(ItemTemplateForMobile), typeof(DataTemplate), typeof(CustomCrossHorizontalListView), new DataTemplate(() =>
        {
            return new Label();
        }));
        public DataTemplate ItemTemplateForMobile
        {
            get { return (DataTemplate)GetValue(ItemTemplateForMobileProperty); }
            set { SetValue(ItemTemplateForMobileProperty, value); }
        }
        public static readonly BindableProperty ItemTemplateForWPFProperty = BindableProperty.Create(nameof(ItemTemplateForWPF), typeof(DataTemplate), typeof(CustomCrossHorizontalListView), new DataTemplate(() =>
        {
            return new Label();
        }));
        public DataTemplate ItemTemplateForWPF
        {
            get { return (DataTemplate)GetValue(ItemTemplateForWPFProperty); }
            set { SetValue(ItemTemplateForWPFProperty, value); }
        }








        private void onWpfListViewItemSelected(object sender, SelectedItemChangedEventArgs e)
        {
            TapCommand?.Execute(e.SelectedItem);
        }
    }

    public class HorizontalListViewForWPF : ListView
    {
        /// <summary>
        /// В WPF проекте будет написан рендерер, где будет задаваться горизонтальная ориентация листа
        /// </summary>
        /// <exception cref="NotSupportedException"></exception>
        public HorizontalListViewForWPF()
        {
            
        }

        public static readonly BindableProperty ItemWidthProperty = BindableProperty.Create(nameof(ItemWidth), typeof(double), typeof(HorizontalListViewForWPF), defaultBindingMode: BindingMode.TwoWay);
        public double ItemWidth
        {
            get { return (double)GetValue(ItemWidthProperty); }
            set { SetValue(ItemWidthProperty, value); }
        }
        public static readonly BindableProperty ItemHeightProperty = BindableProperty.Create(nameof(ItemHeight), typeof(double), typeof(HorizontalListViewForWPF), defaultBindingMode: BindingMode.TwoWay);
        public double ItemHeight
        {
            get { return (double)GetValue(ItemHeightProperty); }
            set { SetValue(ItemHeightProperty, value); }
        }
    }

}