﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Enums;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMAdminMoblieApp.Views.Popups;
using CRMMobileApp.Core;
using Rg.Plugins.Popup.Extensions;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMAdminMoblieApp.Abstractions
{
    public abstract class BaseDashboardPage : ContentPage
    {
        public abstract DashboardPageType PageType { get; }
        public Label DutyLabel { get; set; }


        public BaseDashboardPage()
        {
            ApplicationState.OnDutyChanged += OnDutyChanged;
        }
        public virtual async void OnDutyChanged(object sender,Duty e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (DutyLabel != null)
                {
                    if (e == null || e.Id == 0)
                    {
                        DutyLabel.Text = "Смена не выбрана";
                    }
                    else
                    {
                        DutyLabel.Text = $"Смена #{e.DutyNumber}, {e.OpenedAt.ToString("ddd")}, {e.OpenedAt.ToString("dd MMMM")}";
                    }
                }

                 RefreshData();
            });
        }

         
        public virtual async Task RefreshData() 
        {

        } 


        private ICommand openMenuPopup;
        public ICommand OpenMenuPopup
        {
            get => openMenuPopup ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new MenuPopup());
            });
        }

        private ICommand openDutyPopup;
        public ICommand OpenDutyPopup
        {
            get => openDutyPopup ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new DutiesPopup());
            });
        }
    }
}
