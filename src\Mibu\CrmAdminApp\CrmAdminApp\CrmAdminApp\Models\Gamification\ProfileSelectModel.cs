﻿using CRM.Models.Gamification.General;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace CrmAdminApp.Models.Gamification
{
    public class ProfileSelectModel : BindableObject
    {
        public GamificationUser User { get; set; }

        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
        
    }
}
