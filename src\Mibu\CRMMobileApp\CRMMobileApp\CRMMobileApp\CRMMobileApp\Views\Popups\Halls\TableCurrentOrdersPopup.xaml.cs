﻿using CRM.Models.Stores.Settings.Tables;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TableCurrentOrdersPopup : PopupPage
    {
        private AbsTable table;
        public AbsTable Table
        {
            get => table;
            set { table = value; OnPropertyChanged(nameof(Table)); }
        }
        public TableCurrentOrdersPopup(AbsTable table)
        {
            InitializeComponent();
            Table = table;
            LoadData();
        }

        private void LoadData()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ordersStackLayout.Children.Clear();
                foreach (var order in Table.TableOrders)
                {
                    var control = new TableOrderTemplate(order)
                    {
                        HeightRequest = 44,
                        Margin = new Thickness(0, 4, 0, 0)
                    };
                    control.ItemTapped += Control_ItemTapped;
                    ordersStackLayout.Children.Add(control);
                }
            });
        }
        private void Control_ItemTapped(object sender, CRM.Models.Stores.Order e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();

                OrdersHelper.FromTable = Table;

                OrdersHelper.CurrentOrder = e;
                await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
            });
        }



        private ICommand createOrder;
        public ICommand CreateOrder
        {
            get => createOrder ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushPopupAsync(new GuestsCountPopup(Table));
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

    }



}