﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using Tech = CRM.Models.Network.ReferenceBooks.Menu.TechnicalCard;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TechnicalCard : ContentView
    {
        public TechnicalCard(Tech product)
        {
            Model = product;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                priceLabel.Text = $"{(int)product.Price}₽";

                if (string.IsNullOrEmpty(product.ImgPath) || product.ImgPath == MobileAPI.MAIN_HOST)
                {
                    try
                    {
                        var firstLetter = product.Title.Substring(0, 1).ToUpper();
                        var secondLetter = product.Title.Substring(1, 1).ToLower();
                        lettersLabel.Text = firstLetter + secondLetter;
                    }
                    catch { }
                }
            });
        }
        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(Tech), typeof(TechnicalCard));
        public Tech Model
        {
            get { return (Tech)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        public event EventHandler<Tech> CardTapped;
        private void onCardTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                CardTapped?.Invoke(this, Model);
            });
        }
    }
}