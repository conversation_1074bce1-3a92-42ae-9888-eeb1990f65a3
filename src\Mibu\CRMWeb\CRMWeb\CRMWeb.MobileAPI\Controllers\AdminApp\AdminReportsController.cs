﻿using CRM.Database.Core;
using CRM.Models.Enums;
using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Network;
using CRM.Models.Network.Finances;
using CRM.Models.Reports.Mobile.Admin;
using CRM.Models.Reports.Mobile.Admin.RealTime;
using CRM.Models.Reports.Mobile.Admin.Waiters;
using CRM.Models.Reports.Mobile.Admin.Warehouse;
using CRM.Models.Reports.Mobile.Admin.Workshops;
using CRM.Models.Reports.Mobile.AdminCarousel;
using CRM.Models.Stores;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;


namespace CRMWeb.MobileAPI.Controllers.AdminApp
{
    [ApiController]
    [Route("[controller]")]
    public class AdminReportsController : AbsController
    {
        public AdminReportsController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
            
        }

        [HttpGet, Route("GetAdminDashboardCarouselReport")]
        public AdminDashboardCarouselReport GetAdminDashboardCarouselReport(string domain, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var store = db.Stores.Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                                     .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                                     .Include(o => o.Orders).ThenInclude(o => o.OrderEnvironment)
                                     .FirstOrDefault(o => o.Id == storeId);

                var orders = store.Orders.Where(o => o.DutyId == dutyId && o.IsSuccessfullySold);

                double average = 0;

                if (orders.Any())
                {
                    average = orders.Average(o => o.PaidTotal);
                }

                var report = new AdminDashboardCarouselReport()
                {
                    ChecksCount = orders.Count(),
                    GuestsCount = orders.Sum(o => o.GuestsCount),
                    Sum = orders.Sum(o => o.PaidTotal),
                    AverageCheck = Math.Round(average, 1)
                };
                return report;
            }
        }
      
        [HttpGet, Route("GetRealTimeReport")]
        public RealTimeReport GetRealTimeReport(string domain, int networkId, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks.Include(o => o.Stores)
                                              .FirstOrDefault(o => o.Id == networkId);
                network.Stores = new ObservableCollection<CRM.Models.Store>(network.Stores.Where(o => !o.IsDeleted));


                var allOrders = db.Orders.Include(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                                         .Include(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                                         .Where(o => o.DutyId == dutyId)
                                         .ToList()
                                         .Where(o => network.Stores.Select(a => a.Id).Any(a => a == o.StoreId) && o.IsSuccessfullySold);

                var storeOrders = allOrders.Where(o => o.StoreId == storeId);


                var revenueTotal = allOrders.Sum(o => o.PaidTotal);
                var storeRevenue = storeOrders.Sum(o => o.PaidTotal);

                var report = new RealTimeReport()
                {
                    TotalPaymentsSum = storeOrders.Sum(o => o.PaidTotal),
                    Revenue = storeRevenue,
                    Profit = storeOrders.Sum(o => o.Profit),
                    ByBonuses = storeOrders.Sum(o => o.PaidByBonuses),
                    ByCard = storeOrders.Sum(o => o.PaidByCard),
                    ByCash = storeOrders.Sum(o => o.PaidByCash),
                    RevenuePercent = storeRevenue / (revenueTotal / 100)
                };
                return report;
            }
        }
      
        [HttpGet, Route("GetCashRegisterReport")]
        public CashRegisterReport GetCashRegisterReport(string domain, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                         .Include(o => o.Transactions)
                         .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                         .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                         .FirstOrDefault(o => o.Id == storeId);

                    var orders = store.Orders.Where(o => o.DutyId == dutyId && o.IsSuccessfullySold);
                  

                    var duty = db.Duties.FirstOrDefault(o => o.Id == dutyId);

                    IEnumerable<Transaction> transactions = store.Transactions;

                    if (orders == null || !orders.Any())
                    {
                        return new CashRegisterReport()
                        {
                            Transactions = transactions.ToList()
                        };
                    }

                    if (duty != null)
                    {
                        transactions = transactions.Where(o => o.TransactionDate >= duty.OpenedAt);

                        if (duty.ClosedAt != null)
                            transactions = transactions.Where(o => o.TransactionDate <= duty.ClosedAt.Value);
                    }
                    transactions = transactions.Where(o => o.Operation != FinanceOperation.Null);

                    var report = new CashRegisterReport()
                    {
                        Revenue = orders.Sum(o => o.PaidByMoney),
                        ByCard = orders.Sum(o => o.PaidByCard),
                        ByCash = orders.Sum(o => o.PaidByCash),
                        Transactions = transactions.ToList()
                    };
                    return report;
                }
                catch (Exception ex)
                {
                    return new CashRegisterReport();
                }
            }
        }
      
        [HttpGet, Route("GetWarehouseReport")]
        public WarehouseReport GetWarehouseReport(string domain, int networkId,int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks.Include(o => o.MenuCategories)
                                              .Include(o => o.Products)
                                              .FirstOrDefault(o => o.Id == networkId);

                var store = db.Stores.Include(o => o.Stock).ThenInclude(o => o.Ingridients)
                                     .Include(o => o.Stock).ThenInclude(o => o.Products)
                                     .FirstOrDefault(o => o.Id == storeId);


                var report = new WarehouseReport();
                foreach (var category in network.MenuCategories.Where(o => !o.IsDeleted))
                {
                    var catGroup = new WarehouseReportCategoryGroup
                    {
                        Title = category.Title,
                    };

                    foreach (var product in network.Products.Where(o => o.MenuCategoryId == category.Id && !o.IsDeleted))
                    {
                        catGroup.Rows.Add(new WarehouseReportRow
                        {
                            Title = product.Title,
                            Amount = store.Stock.GetBalance(product),
                            Price = product.Price,
                        });
                    }

                    report.CategoryGroups.Add(catGroup);
                }


                return report;
            }
        }



        [HttpGet, Route("GetWorkshopsReport")]
        public WorkshopsReport GetWorkshopsReport(string domain, int networkId, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks
                        .Include(o => o.Workshops)
                        .FirstOrDefault(o => o.Id == networkId);

                network.Workshops = network.Workshops.Where(o => !o.IsDeleted).ToList();

                network.Workshops.Add(null); //Чтобы сделать категорию "без цеха"



                var store = db.Stores
                     .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.Product).ThenInclude(o => o.MenuCategory)
                     .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.TechnicalCard).ThenInclude(o => o.MenuCategory)
                     .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.TechnicalCard).ThenInclude(o => o.MenuCategory)
                     .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                     .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.Modifier)
                     .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                     .FirstOrDefault(o => o.Id == storeId);



                var orderItems = store.Orders.Where(o => o.DutyId == dutyId && o.IsSuccessfullySold).SelectMany(o => o.Items);
                var report = new WorkshopsReport();

                foreach (var workshop in network.Workshops)
                {
                    var workshopProducts = orderItems.Where(o => o.Product != null && o.Product.MenuCategory.WorkshopId == workshop?.Id);
                    var workshopTechCards = orderItems.Where(o => o.TechnicalCard != null && o.TechnicalCard.MenuCategory.WorkshopId == workshop?.Id);

                    var workshopModifiers = orderItems.SelectMany(o => o.SelectedModifiers)
                                                      .Where(o => o.Modifier.WorkshopId == workshop?.Id).SelectMany(o => o.SelectedOptions);

                    string title = "Без цеха";
                    if (workshop != null)
                    {
                        title = workshop.Title;
                    }

                    report.Workshops.Add(new WorkshopsReportRow
                    {
                        Title = title,
                        Sum = workshopProducts.Sum(o => o.TotalProductPriceWithDiscount)
                              + workshopTechCards.Sum(o => o.TotalProductPriceWithDiscount)
                              + workshopModifiers.Sum(o => o.TotalWithDiscount)
                    });
                }

                return report;
            }
        }


        [HttpGet, Route("GetWaitersReport")]
        public WaitersReport GetWaitersReport(string domain, int networkId, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks
                       .Include(o => o.TerminalUsers)
                       .FirstOrDefault(o => o.Id == networkId);

                var store = db.Stores
                     .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                     .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                     .FirstOrDefault(o => o.Id == storeId);



                var orders = store.Orders.Where(o => o.DutyId == dutyId && o.IsSuccessfullySold);



                var report = new WaitersReport();
                foreach (var waiter in network.TerminalUsers.Where(o => !o.IsDeleted))
                {

                    //Чтобы JSON не циклился и не падал эндпоинт
                    foreach (var order in waiter.Store.Orders)
                    {
                        order.User = null;
                    }


                    var waiterOrders = orders.Where(o => o.UserId == waiter.Id);
                    report.Waiters.Add(new WaitersReportRow
                    {
                        Waiter = waiter,
                        Sum = waiterOrders.Sum(o => o.PaidTotal)
                    });
                }

                return report;
            }
        }


        [HttpGet, Route("GetNotifications")]
        public List<NetworkNotification> GetNotifications(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks.Include(o => o.NetworkNotifications)
                                              .FirstOrDefault(o => o.Id == networkId);

                return network.NetworkNotifications.Where(o => o.CreatedAt.Date == DateTime.UtcNow.Date)
                                                   .ToList();
            }
        }




        [HttpGet, Route("GetBriefTradeNetworkReport")]
        public BriefTradeNetworkReport GetBriefTradeNetworkReport(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks.Include(o => o.Stores).ThenInclude(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                                              .Include(o => o.Stores).ThenInclude(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                                              .FirstOrDefault(o => o.Id == networkId);


                var orders = network.Stores.SelectMany(o => o.Orders)
                                           .Where(o => o.IsSuccessfullySold);

                var report = new BriefTradeNetworkReport()
                {
                    Revenue = orders.Sum(o => o.SumWithDiscount),
                    Profit = orders.Sum(o => o.Profit),
                };
                return report;
            }
        }

        [HttpGet, Route("GetBriefStoreReport")]
        public BriefStoreReport GetBriefStoreReport(string domain, int networkId,int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                var network = db.TradeNetworks.Include(o => o.Stores).ThenInclude(o => o.Duties)
                                              .Include(o => o.Stores).ThenInclude(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions)
                                              .Include(o => o.Stores).ThenInclude(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                                              .FirstOrDefault(o => o.Id == networkId);

                var store = network.Stores.FirstOrDefault(o => o.Id == storeId);
                var orders = store.Orders.Where(o => o.IsSuccessfullySold);

                var networkReport = GetBriefTradeNetworkReport(domain, networkId);

                var report = new BriefStoreReport()
                {
                    Revenue = orders.Sum(o => o.SumWithDiscount),
                    Profit = orders.Sum(o => o.Profit),
                };

                if (store.Duties.Any())
                {
                    report.LastDuty = store.Duties.FirstOrDefault(o => o.DutyNumber == store.Duties.Max(o => o.DutyNumber));
                }


                if (networkReport.Revenue > 0)
                {
                    report.RevenuePercent = report.Revenue / (networkReport.Revenue / (double)100);
                }

                if (networkReport.Profit > 0)
                {
                    report.ProfitPercent = report.Profit / (networkReport.Profit / (double)100);
                }



                return report;
            }
        }
    }
}
