﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Delivery.DeliveriesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Delivery"
      xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
      xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts" 
      xmlns:templates="clr-namespace:CRMDesktopApp.Controls.Templates.Orders"
      mc:Ignorable="d" 
      d:DesignHeight="450"
      d:DesignWidth="800"
      x:Name="this"
      Title="DeliveriesPage">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid Background="#F6F6FB">

        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>


        <parts:Header Grid.Row="0"/>

        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="40"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <customcontrols:ImageButton
                        Command="{Binding ElementName=this,Path=FilterOrders}"
                        Background="Transparent"
                        Width="20"
                        Height="20"
                        Source="pack://application:,,,/Resources/Images/search.png"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"/>
                </Grid>

                <Grid Grid.Column="1">

                    <customcontrols:ImageButton
                        Command="{Binding ElementName=this,Path=DecrementDay}"
                        Background="Transparent"
                        Width="10"
                        Height="20"
                        Source="pack://application:,,,/Resources/Images/arrowBack.png"
                        Margin="15,0,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"/>


                    <TextBlock
                        x:Name="filterDate"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text=""/>

                    <customcontrols:ImageButton
                        Command="{Binding ElementName=this,Path=IncrementDay}"
                        Background="Transparent"
                        Width="10"
                        Height="20"
                        Source="pack://application:,,,/Resources/Images/arrowRight.png"
                        Margin="0,0,15,0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"/>

                </Grid>

                <Grid Grid.Column="2">
                    <editors:ComboBoxEdit   
                            IsTextEditable="False"
                            Margin="0,0,0,0"
                            Background="#F6F6FB"
                            Foreground="{StaticResource dark_purple}"
                            BorderBrush="#F6F6FB"
                            Height="40"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            SelectedIndex="0"
                            CornerRadius="0"
                            ItemsSource="{Binding ElementName=this,Path=DeliveryServices,Mode=TwoWay}"
                            SelectedItem="{Binding ElementName=this,Path=SelectedDeliveryService,Mode=TwoWay}">
                        <editors:ComboBoxEdit.ItemTemplate>
                            <DataTemplate>
                                <Grid Height="40">
                                    <TextBlock
                                        Margin="8,8,0,0"
                                        Foreground="{StaticResource dark_purple}" 
                                        Text="{Binding Title}"/>
                                </Grid>
                            </DataTemplate>
                        </editors:ComboBoxEdit.ItemTemplate>
                    </editors:ComboBoxEdit>
                </Grid>

                <Grid Grid.Column="3">
                    <editors:ComboBoxEdit   
                            IsTextEditable="False"
                            Margin="0,0,0,0"
                            Background="#F6F6FB"
                            Foreground="{StaticResource dark_purple}"
                            BorderBrush="#F6F6FB"
                            Height="40"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            SelectedIndex="0"
                            CornerRadius="0"
                            ItemsSource="{Binding ElementName=this,Path=DeliveryMen,Mode=TwoWay}"
                            SelectedItem="{Binding ElementName=this,Path=SelectedDeliveryMan,Mode=TwoWay}">
                        <editors:ComboBoxEdit.ItemTemplate>
                            <DataTemplate>
                                <Grid Height="40">
                                    <TextBlock
                                        Margin="8,8,0,0"
                                        Foreground="{StaticResource dark_purple}" Text="{Binding}"/>
                                </Grid>
                            </DataTemplate>
                        </editors:ComboBoxEdit.ItemTemplate>
                    </editors:ComboBoxEdit>
                </Grid>

                <Grid Grid.Column="4">
                    <Button 
                        BorderThickness="0"
                        Height="40"
                        Command="{Binding ElementName=this,Path=CreateOrderWithDelivery}"
                        Foreground="White"
                        Content="Новый заказ"
                        Padding="0"
                        FontSize="16"
                        Background="{StaticResource purple_gradient}"/>
                </Grid>

            </Grid>

            <Grid Grid.Row="1">
                <!--<Grid.ColumnDefinitions>
                    <ColumnDefinition Width="261*"/>
                    <ColumnDefinition Width="539*"/>
                </Grid.ColumnDefinitions>-->
                <ListBox
                    ScrollViewer.VerticalScrollBarVisibility="Hidden"
                    ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                    ItemContainerStyle="{StaticResource lbItemContainerStyle}"
                    x:Name="listView"
                    BorderThickness="0"
                    Background="Transparent"
                    Margin="30,10,30,0"
                    SelectionMode="Single"
                    SelectionChanged="onDeliverySelected"
                    ItemsSource="{Binding ElementName=this,Path=Orders}" Grid.ColumnSpan="2">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <templates:OrderWithDeliveryTemplate
                                Model="{Binding}"
                                Margin="0,0,0,0"
                                Width="{Binding ElementName=listView,Path=ActualWidth}"
                                Height="46"/>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <TextBlock
                    
                      x:Name="noItemsTextBlock"
                      Visibility="Hidden"
                      FontFamily="{StaticResource TTFirsNeue-Regular}"
                      TextAlignment="Center"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Center"
                      Foreground="{StaticResource text_gray}"
                      FontSize="15"
                      Width="300"
                      TextWrapping="Wrap"
                      Text="На данный момент нет закрытых заказов"/>

            </Grid>
        </Grid>

    </Grid>
</abstactions:BasePage>
