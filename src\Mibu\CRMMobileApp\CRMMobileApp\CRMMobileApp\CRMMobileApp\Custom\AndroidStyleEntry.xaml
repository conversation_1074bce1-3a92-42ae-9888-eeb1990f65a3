﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Custom.AndroidStyleEntry">
  <ContentView.Content>
        <StackLayout>
            <Grid>

                <Label x:Name="PlaceHolderLabel"
                       VerticalOptions="End"
                       HeightRequest="{Binding Source={x:Reference this},Path=HeightRequest}"
                       FontFamily="{Binding Source={x:Reference this},Path=FontFamily}"
                       FontSize="{Binding Source={x:Reference this},Path=PlaceholderFontSize}"
                       HorizontalOptions="{Binding Source={x:Reference this},Path=PlaceholderHorizontalOptions}"
                       Margin="{Binding PlaceholderMargin,Source={x:Reference this}}"      
                       TextColor="{Binding PlaceholderColor, Source={x:Reference this}}"
                       Text="{Binding Placeholder,Source={x:Reference this}}" />

                <controls:BorderlessEntry
                    VerticalOptions="End"
                    Background="{OnPlatform iOS=Transparent}"
                    BackgroundColor="{OnPlatform iOS=Transparent}"
                    HeightRequest="{Binding Source={x:Reference this},Path=HeightRequest}"
                    IsReadOnly="{Binding Source={x:Reference this},Path=IsReadOnly}"
                    FontFamily="{Binding Source={x:Reference this},Path=FontFamily}"
                    Keyboard="{Binding Source={x:Reference this},Path=Keyboard}"
                    IsPassword="{Binding Source={x:Reference this},Path=IsPassword}"
                    FontSize="{Binding Source={x:Reference this},Path=TextFontSize}"
                    HorizontalTextAlignment="{Binding Source={x:Reference this},Path=HorizontalTextAlignment}"
                    x:Name="TextBox"                         
                    Text="{Binding Text,Source={x:Reference this},Mode=TwoWay}"
                    TextColor="{Binding TextColor,Source={x:Reference this},Mode=TwoWay}"
                    Margin="{Binding TextMargin,Source={x:Reference this}}"                            
                    Focused="TextBox_Focused"                            
                    Unfocused="TextBox_Unfocused"
                    TextChanged="OnTextChanged" >
                </controls:BorderlessEntry>

                <BoxView
                    BackgroundColor="{Binding Source={x:Reference this},Path=BorderColor}"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="End"
                    HeightRequest="1"/>
            </Grid>
        </StackLayout>
    </ContentView.Content>
</ContentView>