using Xamarin.Forms;
using Xamarin.Forms.Xaml;

[assembly: XamlCompilation(XamlCompilationOptions.Compile)]

[assembly: ExportFont("TTFirsNeue-DemiBold.ttf", <PERSON><PERSON> = "TTFirsNeue-DemiBold")]
[assembly: ExportFont("TTFirsNeue-Light.ttf", <PERSON>as = "TTFirsNeue-Light")]
[assembly: ExportFont("TTFirsNeue-Medium.ttf", <PERSON><PERSON> = "TTFirsNeue-Medium")]
[assembly: ExportFont("TTFirsNeue-Regular.ttf", <PERSON>as = "TTFirsNeue-Regular")]

[assembly: ExportFont("neue_regular.ttf", <PERSON><PERSON> = "neue_regular")]
[assembly: ExportFont("noto_sans_bold.ttf", <PERSON><PERSON> = "noto_sans_bold")]
[assembly: ExportFont("noto_sans_light.ttf", <PERSON><PERSON> = "noto_sans_light")]
[assembly: ExportFont("noto_sans_medium.ttf", <PERSON>as = "noto_sans_medium")]
[assembly: ExportFont("noto_sans_regular.ttf", <PERSON><PERSON> = "noto_sans_regular")]