﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.OrderDivisionPopup">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="750"
            HeightRequest="550"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,40,0,0"
            CornerRadius="20">
            <Grid
                ColumnSpacing="0"
                RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">

                        <ImageButton
                            Aspect="Fill"
                            Command="{Binding Source={x:Reference this},Path=Close}"
                            Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"
                            Margin="10,0,0,0"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            WidthRequest="6"
                            HeightRequest="12"/>


                        <Label
                            Margin="30,0,0,0"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            TextColor="{StaticResource dark_purple}"
                            Text="Разделение заказа"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16"/>

                        <Label
                            Margin="0,0,10,0"
                            VerticalOptions="Center"
                            HorizontalOptions="End"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="Заказ №"/>
                                        <Span x:Name="orderNumberSpan" Text=""/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                    </Grid>


                    <Grid Grid.Column="1">
                        <Label
                            Margin="10,0,0,0"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            TextColor="{StaticResource dark_purple}"
                            Text="Новый заказ"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16"/>


                    </Grid>


                </Grid>

                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <ScrollView Margin="10,5,10,30">
                            <StackLayout 
                                x:Name="orderItemsStackLayout">



                            </StackLayout>
                        </ScrollView>

                        <Label
                            Margin="0,0,10,0"
                            VerticalOptions="End"
                            HorizontalOptions="End"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="Всего "/>
                                        <Span Text="{Binding Source={x:Reference this},Path=OrderItemsTotal}"/>
                                        <Span Text=" р"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                    </Grid>

                    <BoxView
                        VerticalOptions="Fill"
                        HorizontalOptions="End"
                        BackgroundColor="{StaticResource dark_purple}"
                        WidthRequest="1"/>

                    <Grid Grid.Column="1">


                        <ScrollView Margin="10,5,10,30">
                            <StackLayout 
                                x:Name="newOrderItemsStackLayout">



                            </StackLayout>
                        </ScrollView>

                        <Label
                            Margin="0,0,10,0"
                            VerticalOptions="End"
                            HorizontalOptions="End"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="Всего "/>
                                        <Span Text="{Binding Source={x:Reference this},Path=NewOrderItemsTotal}"/>
                                        <Span Text=" р"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>

                    </Grid>


                </Grid>


                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Button 
                        Grid.Column="0"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Style="{StaticResource bg_purple_btn}"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Margin="20,0,0,0"
                        Text="Отмена"
                        WidthRequest="150"
                        HeightRequest="40"/>


                    <Button 
                        Grid.Column="1"
                        Command="{Binding Source={x:Reference this},Path=DivideOrder}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalOptions="Center"
                        HorizontalOptions="End"
                        Margin="0,0,20,0"
                        Text="Разделить"
                        WidthRequest="150"
                        HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>