﻿using CRM.Models.Reports.Mobile.BalancesAtStock;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Tables
{
    /// <summary>
    /// Логика взаимодействия для BalanceAtStock.xaml
    /// </summary>
    public partial class BalanceAtStock : BasePage
    {
        public BalanceAtStock()
        {
            InitializeComponent();
            Loaded += BalanceAtStock_Loaded;

        }

        private async void BalanceAtStock_Loaded(object sender, RoutedEventArgs e)
        {
            Report = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetBalancesAtStock(ApplicationState.CurrentDomain,
                                                                                 ApplicationState.CurrentTradeNetwork.Id,
                                                                                 ApplicationState.CurrentStore.Id);
            if (!Report.Rows.Any())
            {
                noItemsTextBlock.Visibility = Visibility.Visible;
            }
        }



        private BalancesAtStockReport report;
        public BalancesAtStockReport Report
        {
            get => report;
            set { report = value; OnPropertyChanged(nameof(Report)); }
        }



      
    }
}
