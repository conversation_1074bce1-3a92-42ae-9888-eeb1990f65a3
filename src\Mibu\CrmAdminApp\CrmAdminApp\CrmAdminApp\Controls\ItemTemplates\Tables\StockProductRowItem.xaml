﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.StockProductRowItem">
    <ContentView.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Grid 
            Margin="0,20,0,0"
            WidthRequest="300"
            HorizontalOptions="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="4*"/>
            </Grid.ColumnDefinitions>

            <Label 
                 Grid.Column="0"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 LineBreakMode="TailTruncation"
                 Text="{Binding Source={x:Reference this},Path=Model.Title}"/>

            <Label 
                 Margin="13,0,0,0"
                 Grid.Column="1"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 Text="{Binding Source={x:Reference this},Path=Model.Amount}"/>

        </Grid>
    </ContentView.Content>
</ContentView>