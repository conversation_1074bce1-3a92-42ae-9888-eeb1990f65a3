﻿using CRM.Models;
using CRM.Models.Gamification.General.Levels;
using MobPhone.Model;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CRMMobileApp.Models.Gamification
{
    public class RatingSortModel
    {
        public bool ByPosition { get; set; }
        public bool ByWaiterPosition { get; set; }
        public bool ByBaristaPosition { get; set; }
        public bool ByManagerPosition { get; set; }
        public bool ByDirectorPosition { get; set; }


        public bool ByStore { get; set; }
        public List<RatingSortByStoreModel> Stores { get; set; } = new List<RatingSortByStoreModel>();



        public bool ByLevel { get; set; }
		public List<RatingSortByLevelModel> Levels { get; set; } = new List<RatingSortByLevelModel>();




		public bool ByCoins { get; set; }
        public double ByCoinsFrom { get; set; } 
        public double ByCoinsTo { get; set; } = 50000;


        public bool ByMonthKarma { get; set; }
        public double ByMonthKarmaFrom { get; set; } 
        public double ByMonthKarmaTo { get; set; } = 50000;


        public bool ByTotalKarma { get; set; }
        public double ByTotalKarmaFrom { get; set; } 
        public double ByTotalKarmaTo { get; set; } = 50000;


        public IEnumerable<ProfileRaiting> Sort(IEnumerable<ProfileRaiting> items)
        {
            IEnumerable<ProfileRaiting> sorted = new List<ProfileRaiting>(items);

            if (ByPosition)
            {
                sorted = items.OrderByDescending(o => o.Position);

                var sortedByPosition = new List<ProfileRaiting>();
                bool wasSet = false;

                if (ByWaiterPosition)
                {
                    sortedByPosition.AddRange(sorted.Where(o => o.Position == "Официант"));
                    wasSet = true;
                }
                if (ByBaristaPosition)
                {
                    sortedByPosition.AddRange(sorted.Where(o => o.Position == "Бариста"));
                    wasSet = true;
                }
                if (ByManagerPosition)
                {
                    sortedByPosition.AddRange(sorted.Where(o => o.Position == "Менеджер"));
                    wasSet = true;
                }
                if (ByDirectorPosition)
                {
                    sortedByPosition.AddRange(sorted.Where(o => o.Position == "Директор"));
                    wasSet = true;
                }

                if(wasSet)
                {
                    sorted = sortedByPosition;
                }
            }

            if (ByStore && Stores.Any(o => o.IsSelected))
            {
                var selected = Stores.Where(o => o.IsSelected);
                sorted = sorted.Where(o => selected.Any(a => a.Store.CommonStoreSettings.ToString().Contains(o.Address)));
            }
            if (ByLevel && Levels.Any(o => o.IsSelected))
            {
				var selected = Levels.Where(o => o.IsSelected);
				sorted = sorted.Where(o => selected.Any(a => a.Level.Number == o.level));
			}
            if (ByCoins)
            {
                sorted = sorted.Where(o => o.Coin >= ByCoinsFrom  && o.Coin <= ByCoinsTo);
            }

            if (ByMonthKarma)
            {
                sorted = sorted.Where(o => o.Karma >= ByMonthKarmaFrom && o.Karma <= ByMonthKarmaTo);
            }

            if (ByTotalKarma)
            {
                sorted = sorted.Where(o => o.Karma_Total >= ByTotalKarmaFrom && o.Karma <= ByTotalKarmaTo);
            }

            return sorted;
        }

       
    }

	public class RatingSortByStoreModel
	{
		public Store Store { get; set; }
		public bool IsSelected { get; set; }
	}

	public class RatingSortByLevelModel
	{
		public Level Level { get; set; }
		public bool IsSelected { get; set; }
	}
}
