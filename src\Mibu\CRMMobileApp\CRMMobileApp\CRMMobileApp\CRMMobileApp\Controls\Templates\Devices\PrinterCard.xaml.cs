﻿using CRM.Models.Stores.Settings.Equipment;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using TermoPrintingLib;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Devices
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PrinterCard : ContentView
    {

        protected double ExpandedHeightRequest = -1;
        protected double CollapsedHeightRequest = 60;
  
        public PrinterCard(Printer printer)
        {
            ExpandedHeightRequest = 95;
            Model = printer;
            InitializeComponent();
        }
        public static readonly BindableProperty ModelProperty =
        BindableProperty.Create(nameof(Model), typeof(Printer), typeof(PrinterCard));
        public Printer Model
        {
            get { return (Printer)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        #region Экспандер 
        private bool isExpanded;
        public bool IsExpanded
        {
            get => isExpanded;
            set
            {
                isExpanded = value;
                OnPropertyChanged(nameof(IsExpanded));

                menuLayout.IsVisible = IsExpanded;

                if (value)
                {
                    this.HeightRequest = ExpandedHeightRequest;
                }
                else
                {
                    this.HeightRequest = CollapsedHeightRequest;
                }
            }
        }

        private ICommand toggleExpand;
        public ICommand ToggleExpand
        {
            get => toggleExpand ??= new RelayCommand(obj =>
            {
                IsExpanded = !IsExpanded;
            });
        }
        #endregion

        #region Функции
        private ICommand checkConnection;
        public ICommand CheckConnection
        {
            get => checkConnection ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (new TermoPrinter(Model).CheckConnection())
                    {
                        connectionCircleFrame.BackgroundColor = Color.Green;
                    }
                    else
                    {
                        connectionCircleFrame.BackgroundColor = Color.Red;
                    }
                });
            });
        }
        #endregion
    }
}