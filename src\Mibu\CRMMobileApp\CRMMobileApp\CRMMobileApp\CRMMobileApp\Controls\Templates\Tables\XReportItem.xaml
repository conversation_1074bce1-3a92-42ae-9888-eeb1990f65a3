﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.Tables.XReportItem">
    <ContentView.Content>
      <Grid>
          <Grid.ColumnDefinitions>
               <ColumnDefinition Width="1*"/>
               <ColumnDefinition Width="1*"/>
          </Grid.ColumnDefinitions>

            <Label
                Grid.Column="0"
                Margin="20,0,0,0"
                VerticalOptions="Center"
                HorizontalOptions="Start"
                TextColor="{StaticResource text_gray}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="{Binding Source={x:Reference this},Path=TextFontSize}"     
                Text="{Binding Source={x:Reference this},Path=FirstColumn}"/>


            <Label
                Grid.Column="1"
                Margin="0,0,0,0"
                VerticalOptions="Center"
                HorizontalOptions="Start"
                TextColor="{StaticResource text_gray}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="{Binding Source={x:Reference this},Path=TextFontSize}"    
                Text="{Binding Source={x:Reference this},Path=SecondColumn}"/>

        </Grid>
  </ContentView.Content>
</ContentView>