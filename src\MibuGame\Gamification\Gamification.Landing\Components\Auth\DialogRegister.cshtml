@model Gamification.Landing.Components.Auth.DialogRegister

<div class="modal popup profile-popup dialog">

    <form method="post" hydro-on:submit="@(() => Model.Submit())" class='modal_dialog'>

        <button type='button' class='close' hydro-on:click="@(() => Model.Close())"><img src='/images/close.svg' /></button>

        <div class='modal_content'>
            <div class="start_df">

                <div class="start_left">
                    <div class="mob">
                        <div class="start_right_title">
                            Начни работу с MIBU <span>GAME</span>
                        </div>
                        <div class="start_right_subtitle">
                            Бесплатный доступ на 30 дней. На указанный e-mail будут направлены логин и пароль для начала работы.
                        </div>
                    </div>
                    <img src="/images/lp7.png">
                </div>

                <div class="start_right">
                    <div class="lp7_form">
                        <div class="desk">
                            <div class="start_right_title">
                                Начни работу с MIBU <span>GAME</span>
                            </div>
                            <div class="start_right_subtitle">
                                Бесплатный доступ на 30 дней. На указанный e-mail будут направлены логин и пароль для начала работы.
                            </div>
                        </div>
                        <div class="lp7_form_control">
                            <div class="lp7_lab">
                                Название вашей компании
                            </div>
                            
                            <input type="text" asp-for="@Model.Item.BusinessName">

                        </div>
                        <div class="lp7_form_control">
                            <div class="lp7_lab">
                                Ф.И.О.
                            </div>

                            <input type="text"    asp-for="Item.FIO" >

                        </div>
                        <div class="lp7_form_control">
                            <div class="lp7_lab">
                                Ваш email
                            </div>

                            <input id="registerFormEmail" type="email"     asp-for="Item.Email"  >

                        </div>
                        <div class="lp7_form_control">
                            <div class="lp7_lab">
                                Номер телефона
                            </div>

                            <input type="tel"   asp-for="Item.Phone"  placeholder="">

                        </div>



                        <div class="lp7_form_control">
                            <div class="lp7_lab">
                                Придумайте пароль
                            </div>
                            <div style="position: relative;">

                                <input class="passwordControl" asp-for="Item.Password" placeholder="">

              @*                   <div class="popup__password" id="registerFormPasswordControl">
                                    <a href="#" class="password-control password-hide"></a>
                                </div> *@

                                <div class="popup__password">
                                    <a class="password-control password-view" onclick="togglePassword()"></a>
                                </div>

                            </div>
                        </div>

                        <div class="lp7_form_control">
                            <div class="lp7_lab">
                                Повторите пароль
                            </div>
                            <div style="position: relative;">

                                <input class="passwordControl" placeholder="" asp-for="Item.PasswordAgain">

                                <div class="popup__password">
                                    <a class="password-control password-view" onclick="togglePassword()"></a>
                                </div>

                            </div>
                        </div>

                        <div id="registerFormErrorText" hidden class="group__status group__wrong" style="margin-bottom: 10px; color: red;"></div>

                        <div class="lp7_link">

                            <button type="button" data-mod='#loginPopup' class="btn js_open_close">
                                Уже зарегистрирован?
                            </button>

                        </div>

                        <button type="button" class="btn" id="hiddenSuccessRegLink" data-mod="#registrationSuccessPopup" hidden></button>

                        <div class="lp7_button">

                            <button class="btn btn_blue" submit>
                                <span>
                                    Начать бесплатно
                                </span>
                            </button>

                        </div>
                        
                        <h2 id="profileErrorText" 
                            style="text-align:start; margin: 16px; color:red; font-size: 14px; width: 100%;">@Model.Error</h2>


                        <div class="lp7_info">
                            Заполняя форму, вы даёте согласие на обработку персональных данных в соответствии с <a target="_blank" asp-action="Privacy" asp-controller="Home">Политикой конфиденциальности</a>.
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </form>
 
    <script>

     
        async function togglePassword(e) {
            if (event) {
                event.preventDefault();
            }

            let entry = document.querySelector('.passwordControl');
            let isHidden = entry.getAttribute('type') === "password";

            let entries = document.querySelectorAll('.passwordControl');
            entries.forEach(entry => {
                if (isHidden) {
                    entry.setAttribute('type', 'text');
                } else {
                    entry.setAttribute('type', 'password');
                }
            });

            let divs = document.querySelectorAll('.password-view, .password-hide');
            divs.forEach(div => {
                if (isHidden) {
                    div.classList.remove('password-hide');
                    div.classList.add('password-view');
                } else {
                    div.classList.remove('password-view');
                    div.classList.add('password-hide');
                }
            });
        }

        console.log("hiding passwords");

        togglePassword();

    </script>


</div>
