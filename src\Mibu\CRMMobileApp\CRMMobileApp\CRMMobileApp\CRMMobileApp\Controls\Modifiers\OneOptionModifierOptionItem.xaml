﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Modifiers.OneOptionModifierOptionItem">
    <ContentView.Content>
      <Grid>
           <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="2.5*"/>
                <ColumnDefinition Width="1.5*"/>
           </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">
                <Label 
                    Text="{Binding Source={x:Reference this},Path=Model.ModifierOption.Title}"
                    FontSize="14"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    Margin="15,0,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Center"/>
            </Grid>

            <Grid Grid.Column="1">
                <Label      
                    FontSize="14"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    Margin="5,0,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Center">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.ModifierOption.Price}"/>
                                <Span Text=" р"/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
            </Grid>


            <Grid Grid.Column="2">
                <RadioButton 
                    x:FieldModifier="Public"
                    x:Name="radioButton"
                    Margin="0,0,15,0"
                    BackgroundColor="{AppThemeBinding Dark={StaticResource dark_purple},Default=Transparent,Light=Transparent}"
                    Background="{AppThemeBinding Dark={StaticResource dark_purple},Default=Transparent,Light=Transparent}"
                    BorderColor="{StaticResource dark_purple}"
                    TextColor="{StaticResource dark_purple}"
                    CheckedChanged="onCheckedChanged"
                    VerticalOptions="Center"
                    HorizontalOptions="End"/>
            </Grid>

        </Grid>
  </ContentView.Content>
</ContentView>