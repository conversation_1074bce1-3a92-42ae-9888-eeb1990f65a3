﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Controls.Parts.Footer"
             x:Name="this"
             HeightRequest="80">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
      <Grid BackgroundColor="#ffffff">

          
          
            <Image 
                Margin="0,-6,0,0"
                VerticalOptions="Start"
                HeightRequest="6"
                Aspect="Fill"
                Source="shadowLine.png"></Image>
          
          
            <Grid
                RowSpacing="15"
                Margin="10,0,10,0"
                HorizontalOptions="Fill"
                VerticalOptions="Center">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>



                <Grid Grid.Column="0">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="ordersTapped"/>
                    </Grid.GestureRecognizers>
                    <RadioButton
                        InputTransparent="True"
                        IsChecked="{Binding Source={x:Reference this},Path=IsOrdersPage,Mode=TwoWay}">
                        <RadioButton.Style>
                            <Style TargetType="RadioButton">
                                <Style.Triggers>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="ordersSelected.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource purple}"
                                                            HorizontalOptions="Center"
                                                            Text="Заказы"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="orders.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource text_gray}"
                                                            HorizontalOptions="Center"
                                                            Text="Заказы"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </RadioButton.Style>
                    </RadioButton>
                </Grid>

                <Grid Grid.Column="1">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="salesTapped"/>
                    </Grid.GestureRecognizers>
                    <RadioButton 
                        InputTransparent="True"
                        IsChecked="{Binding Source={x:Reference this},Path=IsSalesPage,Mode=TwoWay}">
                        <RadioButton.Style>
                            <Style TargetType="RadioButton">
                                <Style.Triggers>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="salesSelected.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource purple}"
                                                            HorizontalOptions="Center"
                                                            Text="Продажи"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="sales.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource text_gray}"
                                                            HorizontalOptions="Center"
                                                            Text="Продажи"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </RadioButton.Style>
                    </RadioButton>
                </Grid>


                <Grid Grid.Column="2">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="realTimePageTapped"/>
                    </Grid.GestureRecognizers>
                    <RadioButton 
                      InputTransparent="True"
                      IsChecked="{Binding Source={x:Reference this},Path=IsRealTimePage,Mode=TwoWay}">
                        <RadioButton.Style>
                            <Style TargetType="RadioButton">
                                <Style.Triggers>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="realTimeSelected.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource purple}"
                                                            HorizontalOptions="Center"
                                                            Text="Сейчас"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="realTime.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource text_gray}"
                                                            HorizontalOptions="Center"
                                                            Text="Сейчас"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </RadioButton.Style>
                    </RadioButton>
                </Grid>


                <Grid Grid.Column="3">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="stockTapped"/>
                    </Grid.GestureRecognizers>
                    <RadioButton
                        InputTransparent="True"
                        IsChecked="{Binding Source={x:Reference this},Path=IsStocksPage,Mode=TwoWay}">
                        <RadioButton.Style>
                            <Style TargetType="RadioButton">
                                <Style.Triggers>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="stocksSelected.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource purple}"
                                                            HorizontalOptions="Center"
                                                            Text="Склад"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="stocks.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource text_gray}"
                                                            HorizontalOptions="Center"
                                                            Text="Склад"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </RadioButton.Style>
                    </RadioButton>
                </Grid>


                <Grid Grid.Column="4">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="cassaTapped"/>
                    </Grid.GestureRecognizers>
                    <RadioButton 
                        InputTransparent="True"
                        IsChecked="{Binding Source={x:Reference this},Path=IsCassaPage,Mode=TwoWay}">
                        <RadioButton.Style>
                            <Style TargetType="RadioButton">
                                <Style.Triggers>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="cashRegisterSelected.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource purple}"
                                                            HorizontalOptions="Center"
                                                            Text="Касса"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                                        <Setter Property="ControlTemplate">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <StackLayout Spacing="10">
                                                        <Image 
                                                            HeightRequest="20"
                                                            WidthRequest="20"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Center"
                                                            Source="cashRegister.png"/>
                                                        <Label 
                                                            FontFamily="TTFirsNeue-Regular"
                                                            FontSize="12"
                                                            TextColor="{x:StaticResource text_gray}"
                                                            HorizontalOptions="Center"
                                                            Text="Касса"/>
                                                    </StackLayout>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </RadioButton.Style>
                    </RadioButton>
                </Grid>

             

            </Grid>
          
          
      </Grid>
  </ContentView.Content>
</ContentView>