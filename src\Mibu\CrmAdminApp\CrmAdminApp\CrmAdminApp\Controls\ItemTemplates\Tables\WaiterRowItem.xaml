﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.WaiterRowItem">
    <ContentView.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Grid 
            Margin="0,20,0,0"
            HorizontalOptions="Fill"
            HeightRequest="30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="8*"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <Label 
                 Grid.Column="0"
                 Margin="10,0,0,0"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 LineBreakMode="TailTruncation"
                 VerticalOptions="Center">
                <Label.FormattedText>
                    <FormattedString>
                        <FormattedString.Spans>
                            <Span Text="{Binding Source={x:Reference this},Path=Model.Waiter.Name}"/>
                            <Span Text=" "/>
                            <Span Text="{Binding Source={x:Reference this},Path=Model.Waiter.Surname}"/>
                        </FormattedString.Spans>
                    </FormattedString>
                </Label.FormattedText>
            </Label>


            <Label 
                 Grid.Column="1"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 Text="{Binding Source={x:Reference this},Path=Model.Sum}"/>


        </Grid>
    </ContentView.Content>
</ContentView>