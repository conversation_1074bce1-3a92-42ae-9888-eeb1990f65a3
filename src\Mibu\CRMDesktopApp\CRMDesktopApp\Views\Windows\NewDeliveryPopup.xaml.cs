﻿using CRM.Models.Network.Delivery;
using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для NewDeliveryPopup.xaml
    /// </summary>
    public partial class NewDeliveryPopup : BaseWindow
    {
        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }
        public NewDeliveryPopup()
        {
            InitializeComponent();
            Order = new Order();
            Order.Delivery = new Delivery();

            Load();
        }


        protected async void Load()
        {

            DeliveryServices = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliveryServices(ApplicationState.CurrentDomain,
                                                                                                         ApplicationState.CurrentTradeNetwork.Id);
            SelectedDeliveryService = DeliveryServices.FirstOrDefault();


            DeliveryMen = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliverymen(ApplicationState.CurrentDomain,
                                                                                               ApplicationState.CurrentTradeNetwork.Id);
            SelectedDeliveryMan = DeliveryMen.FirstOrDefault();



            AvailableCustomers = await MobileAPI.BaristaMethods.BaristaClientsMethods.GetClients(ApplicationState.CurrentDomain,
                                                                                                 ApplicationState.CurrentTradeNetwork.Id);

            personsCountCB.SelectedIndex = 0;
            deliveryDateDateEdit.DateTime = DateTime.Now;
        }



        #region Главное
        private List<int> personsCount = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
        public List<int> PersonsCount
        {
            get => personsCount;
            set { personsCount = value; OnPropertyChanged(nameof(PersonsCount)); }
        }





        public List<Customer> AvailableCustomers { get; set; } = new List<Customer>();

        private Customer selectedCustomer;
        public Customer SelectedCustomer
        {
            get => selectedCustomer;
            set
            {
                selectedCustomer = value;
                OnPropertyChanged(nameof(SelectedCustomer));
                if (value != null)
                {
                    clientFIOEntry.Text = $"{value.Name} {value.Surname}";
                }
            }
        }

        private string phoneNumber;
        public string PhoneNumber
        {
            get => phoneNumber;
            set
            {
                phoneNumber = value;
                OnPropertyChanged(nameof(PhoneNumber));

                var found = AvailableCustomers.FirstOrDefault(o => o.Phone == value);
                if (found != null)
                {
                    SelectedCustomer = found;
                }
            }
        }

        private ICommand goToNewClient;
        public ICommand GoToNewClient
        {
            get => goToNewClient ??= new RelayCommand(async obj =>
            {
                var popup = new NewClientPopup();
                popup.OnClientCreated += Popup_OnClientCreated;
                popup.ShowDialog();
            });
        }
        private void Popup_OnClientCreated(object sender, Customer e)
        {
            AvailableCustomers.Add(e);
            SelectedCustomer = e;
        }


        #endregion

        #region Аванс

        public void SetPrepaymentSumText()
        {
            var sum = Order.OrderPayments.Where(o => o.IsPrepeyment).Sum(o => o.Sum);
            prepaymentSumSpan.Text = Math.Round(sum, 2).ToString();
        }


        private ICommand goToPrepayment;
        public ICommand GoToPrepayment
        {
            get => goToPrepayment ??= new RelayCommand(async obj =>
            {
                this.Hide();
                (App.Current.MainWindow as MainWindow).frame.Navigate(new OrderPrepaymentPage(Order, this));
            });
        }
        #endregion

        #region Исполнители

        private List<DeliveryService> deliveryServices = new List<DeliveryService>();
        public List<DeliveryService> DeliveryServices
        {
            get => deliveryServices;
            set { deliveryServices = value; OnPropertyChanged(nameof(DeliveryServices)); }
        }
        private List<DeliveryMan> deliveryMen = new List<DeliveryMan>();
        public List<DeliveryMan> DeliveryMen
        {
            get => deliveryMen;
            set { deliveryMen = value; OnPropertyChanged(nameof(DeliveryMen)); }
        }

        private DeliveryService selectedDeliveryService;
        public DeliveryService SelectedDeliveryService
        {
            get => selectedDeliveryService;
            set
            {
                selectedDeliveryService = value;
                OnPropertyChanged(nameof(SelectedDeliveryService));

                if (value is null) return;
                deliveryManComboBox.IsEnabled = value.OurDeliverymans;
            }
        }
        private DeliveryMan selectedDeliveryMan;
        public DeliveryMan SelectedDeliveryMan
        {
            get => selectedDeliveryMan;
            set { selectedDeliveryMan = value; OnPropertyChanged(nameof(SelectedDeliveryMan)); }
        }

        #endregion


        private ICommand createOrder;
        public ICommand CreateOrder
        {
            get => createOrder ??= new RelayCommand(async obj =>
            {
                Order.Customer = SelectedCustomer;

                Order.Delivery.Date = deliveryDateDateEdit.DateTime;
                Order.Delivery.Service = SelectedDeliveryService;
                Order.Delivery.DeliveryMan = SelectedDeliveryMan;
                Order.DutyId = ApplicationState.CurrentDuty.Id;
                Order.UserId = Auth.User.Id;
                

                Order = await MobileAPI.BaristaMethods.BaristaOrdersMethods.CreateOrder(ApplicationState.CurrentDomain,
                                                                                ApplicationState.CurrentStore.Id,
                                                                                Order);
                OrdersHelper.CurrentOrder = Order;

                this.Close();
                (App.Current.MainWindow as MainWindow).frame.NavigationService.Navigate(new CategoriesPage());

            });
        }

        #region Табы
        private void MainTabSelected(object sender, RoutedEventArgs e)
        {
            if (tabControl != null)
                tabControl.SelectedIndex = 0;
        }
        private void PrepaymentTabSelected(object sender, RoutedEventArgs e)
        {
            if (tabControl != null)
                tabControl.SelectedIndex = 1;
        }
        private void DeliveryTabSelected(object sender, RoutedEventArgs e)
        {
            if (tabControl != null)
                tabControl.SelectedIndex = 2;
        }
        #endregion


    }
}
