﻿
using CRMAdminMoblieApp.Models;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SaleListItem : ContentView
    {
        public SaleListItem()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(SaleReportRowModel), typeof(SaleListItem));
        public SaleReportRowModel Model
        {
            get { return (SaleReportRowModel)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}