﻿using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Header : ContentView
    {
        public Header()
        {
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                var waiter = Auth.User;
                baristaFIOLabel.Text = $"{waiter.Name} {waiter.Surname}";
                baristaRoleLabel.Text = EnumDescriptionHelper.GetDescription(waiter.Role);

                //userAvatar.Source = MobileAPI.GetFullFilePath(waiter.Pat)
            });
        }
        public static readonly BindableProperty TitleProperty = BindableProperty.Create(nameof(Title), typeof(string), typeof(Header));
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {
                    var currentPage = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault();
                    if (currentPage is CategoriesPage catsPage)
                    {
                        if (!catsPage.CategoriesView.fromRoot)
                        {
                            catsPage.CategoriesView.GoToRoot();
                        }
                        else
                        {
                            await App.Current.MainPage.Navigation.PopAsync();
                        }
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PopAsync();
                    }
                });            
            });
        }

        private ICommand goMainMenu;
        public ICommand GoMainMenu
        {
            get => goMainMenu ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PushAsync(new Views.Pages.Main.MainMenu());
                });
            });
        }
    }
}