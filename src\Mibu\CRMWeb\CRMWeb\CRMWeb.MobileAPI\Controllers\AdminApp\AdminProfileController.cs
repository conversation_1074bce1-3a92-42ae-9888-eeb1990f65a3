﻿using CRM.Database.Core;
using CRM.Models.Gamification;
using CRM.Models.Gamification.General;
using CRM.Models.Network;
using CRMGamification.Services.Сommon;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.AdminApp
{
    [ApiController]
    [Route("[controller]")]
    public class AdminProfileController : AbsController
    {
        public AdminProfileController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
            
        }


        [HttpGet, Route("GetProfileById")]
        public NetworkAdminTabUser GetProfileById(string domain, int profileId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.NetworkAdminTabUsers.FirstOrDefault(o => o.Id == profileId && !o.IsDeleted);
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }

        [HttpGet, Route("GetProfileBySessionToken")]
        public NetworkAdminTabUser GetProfileBySessionToken(string domain, string token)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.NetworkAdminTabUsers.FirstOrDefault(o => o.SessionToken == token && !o.IsDeleted);
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }

        [HttpGet, Route("GetProfileBySessionToken")]
        public bool HasProfileBySessionToken(string domain, string token)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                return db.NetworkAdminTabUsers.FirstOrDefault(o => o.SessionToken == token && !o.IsDeleted) != null;
            }
        }

        [HttpGet, Route("GetProfile")]
        public NetworkAdminTabUser GetProfile(string domain, int networkId, int profileId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.AdminTabUsers)
                        .FirstOrDefault(o => o.Id == networkId);

                    return network.AdminTabUsers.FirstOrDefault(o => o.Id == profileId && !o.IsDeleted);
                }
                catch (Exception ex) 
                { return null; }
            }
        }


        [HttpPut, Route("UpdateProfile")]
        public async Task<NetworkAdminTabUser> UpdateProfile(string domain, NetworkAdminTabUser profile, string firebaseIdToSave = null)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var userBeforeChanges = DB.NetworkAdminTabUsers.AsNoTracking()
                                                                   .FirstOrDefault(o => o.Id == profile.Id);

                    if (userBeforeChanges.Password != profile.Password)
                    {
                        userBeforeChanges.SessionToken = Guid.NewGuid().ToString();
                    }
                    profile.SessionToken = userBeforeChanges.SessionToken;



                    db.NetworkAdminTabUsers.Update(profile);
                    db.SaveChanges();

                    using (GamificationContext gamificationDb = new GamificationContext())
                    {
                        var gamificationUser = gamificationDb.Users.AsNoTracking()
                                                                   .FirstOrDefault(o => o.TerminalCRMUserId == profile.Id
                                                                                     || o.AdminTabCRMUserId == profile.Id);
                        if(gamificationUser != null)
                        {
                            ProfilesService.RemoveFirebaseIds(gamificationDb, gamificationUser.Id, firebaseIdToSave);
                        }
                      
                    }

                    return profile;
                }
                catch (Exception ex)
                { return null; }
            }
        }

		[HttpPut, Route("UpdateProfileAvatar")]
		public async Task<NetworkAdminTabUser> UpdateProfileAvatar(string domain, int userId, string avatarFileName, [FromBody] string body)
		{
            try
            {
				var avatarBytes = Newtonsoft.Json.JsonConvert.DeserializeObject<byte[]>(body);
				using (DatabaseContext db = new DatabaseContext(domain))
				{
					var user = db.NetworkAdminTabUsers.FirstOrDefault(o => o.Id == userId && !o.IsDeleted);
					user.AvatarPath = await SetAttachmentIfHasWithBasePath(domain, "C:\\Users\\<USER>\\Desktop\\crm_web\\wwwroot\\", avatarFileName, avatarBytes);

					db.NetworkAdminTabUsers.Update(user);
					db.SaveChanges();

					return user;
				}
			}
            catch(Exception ex)
            {
                return new NetworkAdminTabUser { AvatarPath = ex.ToString() };
            }
		}


		[NonAction]
		public string GenerateFileName(string extension)
		{
			if (extension.StartsWith('.'))
				extension = extension.Substring(1);

			return Guid.NewGuid().ToString() + "." + extension;
		}
	}
}
