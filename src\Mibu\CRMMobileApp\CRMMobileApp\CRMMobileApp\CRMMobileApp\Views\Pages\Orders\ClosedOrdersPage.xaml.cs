﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Templates;
using CRMMoblieApiWrapper;
using Sharpnado.CollectionView.RenderedViews;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Pages.Orders
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ClosedOrdersPage : OrdersPage
    {
        public ClosedOrdersPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            leftOrdersPanel.SetClosedOrderMode();

            LoadOrders();
        }

        public ClosedOrdersPage(Order order)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            leftOrdersPanel.SetClosedOrderMode();
            leftOrdersPanel.CurrentOrder = order;

            LoadOrders();
        }

        #region Инициализация
        public async void LoadOrders()
        {
            List<Order> closedOrders = new List<Order>();

            await Task.Run(async () =>
            {
                closedOrders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersByDuty(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id, ApplicationState.CurrentDuty.Id);
                closedOrders = closedOrders.Where(o => o.ClosedAt != null && !o.IsCanceled).ToList();
            });

            Device.InvokeOnMainThreadAsync(async () =>
            {
                noItemsLabel.IsVisible = closedOrders.Count == 0;
                collectionView.ItemsSource = closedOrders;
            });
        }
        //private void RenderItems()
        //{
        //    collectionView.ItemsSource = Orders;

        //    //closedOrdersStackLayout.Children.Clear();
        //    //foreach(var order in Orders)
        //    //{
        //    //    var view = new ClosedOrderTemplate(order)
        //    //    {
        //    //        HeightRequest = 43
        //    //    };
        //    //    view.Tapped += View_Tapped;
        //    //    closedOrdersStackLayout.Children.Add(view);
        //    //}
        //}
        #endregion
        private void View_Tapped(object sender, Order e)
        {
            leftOrdersPanel.SetItems(e);
        }

    }
}