﻿using CRM.Models;
using CRM.Models.Network;
using CRM.Models.Reports.Mobile.Admin;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class StoreExpandableCard : ContentView
    {
        public StoreExpandableCard(Store store)
        {
            InitializeComponent();
            Model = store;

            Init();

        }

        private static readonly string ArrowDown = "arrowForward.png";
        private static readonly string ArrowUp = "arrowDown.png";

        private string _expandIcon;

        public static readonly BindableProperty IsExpandedProperty =
            BindableProperty.CreateAttached(nameof(IsExpanded),typeof(bool),  typeof(StoreExpandableCard), false, BindingMode.TwoWay);
        public bool IsExpanded
        {
            get => (bool)GetValue(IsExpandedProperty);
            set => SetValue(IsExpandedProperty, value);
        }
        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(Store), typeof(StoreExpandableCard));
        public Store Model
        {
            get { return (Store)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }



        public event EventHandler<Store> OnSelectBtnTapped;

        private ICommand selectStore;
        public ICommand SelectStore
        {
            get => selectStore ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    OnSelectBtnTapped?.Invoke(this, Model);
                });
            });
        }

		public event EventHandler<Store> OnDeleteBtnTapped;

		private ICommand deleteStore;
        public ICommand DeleteStore
        {
            get => deleteStore ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    OnDeleteBtnTapped?.Invoke(this, Model);
                });
            });
        }






        public string ExpandIcon
        {
            get => _expandIcon;
            set
            {
                _expandIcon = value;
                OnPropertyChanged(nameof(ExpandIcon));
            }
        }


        private async void Init()
        {
            await Task.Run(async () =>
            {
                BriefReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetBriefStoreReport(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id, Model.Id);
            });

            Device.InvokeOnMainThreadAsync(() =>
            {
                ExpandIcon = ArrowDown;
                BodyContentView.IsVisible = false;

              

                if (BriefReport.LastDuty != null)
                {
                    dutyNumberLabel.Text = $"№{BriefReport.LastDuty.DutyNumber}";
                    if (BriefReport.LastDuty.ClosedAt.HasValue)
                    {
                        dutyStatusLabel.Text = "Закрыта";
                        dutyTimeLabel.Text = BriefReport.LastDuty.ClosedAtLocalAuto.Value.ToString("HH:mm");
                    }
                    else
                    {
                        dutyStatusLabel.Text = "Открыта";
                        dutyTimeLabel.Text = BriefReport.LastDuty.OpenedAtLocalAuto.ToString("HH:mm");
                    }
                }
                else
                {
                    dutyNumberLabel.Text = $"XXXX";
                    dutyTimeLabel.Text = "XX:XX";
                }

               
                RevenueDonutChartSections = new ObservableCollection<Tuple<int, string>>()
                {
                    new Tuple<int, string>((int)Math.Round(BriefReport.RevenuePercent,0), "#7265FB"),
                };
                ProfitDonutChartSections = new ObservableCollection<Tuple<int, string>>()
                {
                    new Tuple<int, string>((int)Math.Round(BriefReport.ProfitPercent,0), "#26E27C"),
                };
            });
        }

        public static readonly BindableProperty BriefReportProperty =
            BindableProperty.Create(nameof(BriefReport), typeof(BriefStoreReport), typeof(StoreExpandableCard));
        public BriefStoreReport BriefReport
        {
            get { return (BriefStoreReport)GetValue(BriefReportProperty); }
            set { SetValue(BriefReportProperty, value); }
        }




        private void HeaderContent_OnTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!IsExpanded)
                {
                    BodyContentView.Opacity = 0;
                }

                IsExpanded = !IsExpanded;
                ExpandIcon = IsExpanded ? ArrowUp : ArrowDown;

                BodyContentView.IsVisible = IsExpanded;
                BodyContentView.FadeTo(1, 400, Easing.SpringOut);

                if (IsExpanded)
                    this.HeightRequest = 390;
                else this.HeightRequest = 85;
            });
        }







        private ObservableCollection<Tuple<int, string>> revenueDonutChartSections;
        public ObservableCollection<Tuple<int, string>> RevenueDonutChartSections
        {
            get => revenueDonutChartSections;
            set { revenueDonutChartSections = value; OnPropertyChanged(nameof(RevenueDonutChartSections)); }
        }
        private ObservableCollection<Tuple<int, string>> profitDonutChartSections;
        public ObservableCollection<Tuple<int, string>> ProfitDonutChartSections
        {
            get => profitDonutChartSections;
            set { profitDonutChartSections = value; OnPropertyChanged(nameof(ProfitDonutChartSections)); }
        }
    }
}