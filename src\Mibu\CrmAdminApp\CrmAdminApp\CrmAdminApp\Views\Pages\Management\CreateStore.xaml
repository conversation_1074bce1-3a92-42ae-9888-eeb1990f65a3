﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts" 
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             ios:Page.UseSafeArea="true"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             x:Class="CRMAdminMoblieApp.CreateStore">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">

            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <parts:Header x:Name="header" Grid.Row="0"/>

            
            <Grid Grid.Row="1">



                <StackLayout
                    Margin="30,20,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Start"
                    Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                    </StackLayout.GestureRecognizers>
                    <ImageButton 
                        Source="arrowBack.png"
                        HeightRequest="10"
                        WidthRequest="5"
                        BackgroundColor="Transparent"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"/>
                    <Label 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        VerticalOptions="Center"
                        Text="Создание точки продаж"/>
                </StackLayout>


                <StackLayout
                    HorizontalOptions="Center"
                    Margin="0,75,0,0">

                    <StackLayout
                        Margin="30,0,30,0"
                        HorizontalOptions="Center">

                        <StackLayout Spacing="3">

                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Название точки"/>
                            <controls:EntryOutlined
                                HeightRequest="45"
                                Margin="0,3,0,0"
                                Style="{x:StaticResource white_cornered_entry}"
                                Text="{Binding Source={x:Reference this},Path=NewStore.CommonStoreSettings.Title,Mode=TwoWay}"
                                FontFamily="TTFirsNeue-Regular"
                                Placeholder=""/>



                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Город"/>
                            <controls:EntryOutlined
                                HeightRequest="45"
                                Margin="0,3,0,0"
                                Style="{x:StaticResource white_cornered_entry}"
                                Text="{Binding Source={x:Reference this},Path=NewStore.CommonStoreSettings.City,Mode=TwoWay}"
                                FontFamily="TTFirsNeue-Regular"
                                Placeholder=""/>



                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Адрес"/>
                            <controls:EntryOutlined
                                HeightRequest="45"
                                Margin="0,3,0,0"
                                Style="{x:StaticResource white_cornered_entry}"
                                Text="{Binding Source={x:Reference this},Path=NewStore.CommonStoreSettings.Address,Mode=TwoWay}"
                                FontFamily="TTFirsNeue-Regular"
                                Placeholder=""/>


                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Пароль администратора"/>
                            <controls:EntryOutlined
                                HeightRequest="45"
                                Margin="0,3,0,0"
                                Style="{x:StaticResource white_cornered_entry}"
                                Text="{Binding Source={x:Reference this},Path=NewStore.CommonStoreSettings.AdminPassword,Mode=TwoWay}"
                                FontFamily="TTFirsNeue-Regular"
                                Placeholder=""/>


                        </StackLayout>


                        <StackLayout>

                            <Button 
                                Command="{Binding Source={x:Reference this},Path=CreateStoreCommand}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                Text="Создать"
                                Style="{x:StaticResource purple_gradient_btn}"
                                WidthRequest="240"
                                HeightRequest="40"
                                Margin="0,30,0,0"/>

                        </StackLayout>
                    </StackLayout>

                </StackLayout>
            </Grid>
        </Grid>
    </ContentPage.Content>
</ContentPage>
