﻿using CRM.Models.Network.Delivery;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.PancakeView;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Pages.Delivery
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DeliveriesPage : ContentPage
    {
        public DeliveriesPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Load();
        }

        #region Свойства
        public DateTime SelectedDate { get; set; } = DateTime.Now;

        private List<DeliveryService> deliveryServices = new List<DeliveryService>();
        public List<DeliveryService> DeliveryServices
        {
            get => deliveryServices;
            set { deliveryServices = value; OnPropertyChanged(nameof(DeliveryServices)); }
        }
        private List<DeliveryMan> deliveryMen = new List<DeliveryMan>();
        public List<DeliveryMan> DeliveryMen
        {
            get => deliveryMen;
            set { deliveryMen = value; OnPropertyChanged(nameof(DeliveryMen)); }
        }
        private List<Order> orders = new List<Order>();
        public List<Order> Orders
        {
            get => orders;
            set { orders = value; OnPropertyChanged(nameof(Orders)); }
        }




        private DeliveryService selectedDeliveryService;
        public DeliveryService SelectedDeliveryService
        {
            get => selectedDeliveryService;
            set { selectedDeliveryService = value; OnPropertyChanged(nameof(SelectedDeliveryService)); }
        }
        private DeliveryMan selectedDeliveryMan;
        public DeliveryMan SelectedDeliveryMan
        {
            get => selectedDeliveryMan;
            set { selectedDeliveryMan = value; OnPropertyChanged(nameof(SelectedDeliveryMan)); }
        }


        #endregion

        protected void Load()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                filterDate.Text = SelectedDate.ToString("dd.MM.yyyy");

                DeliveryServices = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliveryServices(ApplicationState.CurrentDomain,
                                                                                                             ApplicationState.CurrentTradeNetwork.Id);

                DeliveryServices.Insert(0, new DeliveryService { Title = "Все доставки" });
                SelectedDeliveryService = DeliveryServices.FirstOrDefault();




                DeliveryMen = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliverymen(ApplicationState.CurrentDomain,
                                                                                                   ApplicationState.CurrentTradeNetwork.Id);
                DeliveryMen.Insert(0, new DeliveryMan { Name = "Все сотрудники" });
                SelectedDeliveryMan = DeliveryMen.FirstOrDefault();




                Orders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersWithDelivery(ApplicationState.CurrentDomain,
                                                                                                   ApplicationState.CurrentTradeNetwork.Id,
                                                                                                   SelectedDate,
                                                                                                   0,
                                                                                                   0);
                noItemsLabel.IsVisible = Orders.Count == 0;
            });
        }


        #region Управление выбранной датой

        private ICommand decrementDay;
        public ICommand DecrementDay
        {
            get => decrementDay ??= new RelayCommand(async obj =>
            {
                SelectedDate = SelectedDate.AddDays(-1);
                filterDate.Text = SelectedDate.ToString("dd.MM.yyyy");
            });
        }

        private ICommand incrementDay;
        public ICommand IncrementDay
        {
            get => incrementDay ??= new RelayCommand(async obj =>
            {
                SelectedDate = SelectedDate.AddDays(1);
                filterDate.Text = SelectedDate.ToString("dd.MM.yyyy");
            });
        }
        #endregion

        #region Фильтрация
        private ICommand filterOrders;
        public ICommand FilterOrders
        {
            get => filterOrders ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {
                    Orders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersWithDelivery(ApplicationState.CurrentDomain,
                                                                                             ApplicationState.CurrentTradeNetwork.Id,
                                                                                             SelectedDate,
                                                                                             SelectedDeliveryService.Id,
                                                                                             SelectedDeliveryMan.Id);
                });
            });
        }
        #endregion

        #region Выбор доставки
        private void onItemTapped(object sender, Order e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (e != null)
                {
                    if (e.ClosedAtLocalAuto != null)
                    {
                        await App.Current.MainPage.Navigation.PushAsync(new ClosedOrdersPage(e));
                    }
                    else
                    {
                        OrdersHelper.CurrentOrder = e;
                        await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
                    }
                }
            });  
        }

        #endregion


        #region Создание доставки
        private ICommand createOrderWithDelivery;
        public ICommand CreateOrderWithDelivery
        {
            get => createOrderWithDelivery ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new NewDeliveryPopup());
            });
        }


        #endregion

      
    }
}