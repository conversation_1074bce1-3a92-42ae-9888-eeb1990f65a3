﻿using CRMMoblieApiWrapper;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для CategoryCard.xaml
    /// </summary>
    public partial class CategoryCard : UserControl
    {
        public CategoryCard()
        {
            InitializeComponent();
        }
        public CategoryCard(MenuCategoriesTreeItem category)
        {
            Model = category;
            InitializeComponent();

            if (string.IsNullOrEmpty(category.Category.ImgPath)
               || category.Category.ImgPath == MobileAPI.MAIN_HOST)
            {
                try
                {
                    var firstLetter = category.Category.Title.Substring(0, 1).ToUpper();
                    var secondLetter = category.Category.Title.Substring(1, 1).ToLower();
                    lettersLabel.Text = firstLetter + secondLetter;
                }
                catch { }
            }
        }


        public static readonly DependencyProperty ModelProperty =
              DependencyProperty.Register(nameof(Model), typeof(MenuCategoriesTreeItem), typeof(CategoryCard));
        public MenuCategoriesTreeItem Model
        {
            get { return (MenuCategoriesTreeItem)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


        public event EventHandler<MenuCategoriesTreeItem> CardTapped;
        private void onCardClicked(object sender, MouseButtonEventArgs e)
        {
            CardTapped?.Invoke(this, Model);
        }
    }
}
