﻿using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.Orders
{
    /// <summary>
    /// Логика взаимодействия для ActiveOrdersPopup.xaml
    /// </summary>
    public partial class CancelOrderItemPopup : BaseWindow
    {
        private OrderItem _item;
        public CancelOrderItemPopup(OrderItem item)
        {
            InitializeComponent();
            _item = item;
        }

        private ICommand cancelPosition;
        public ICommand CancelPosition
        {
            get => cancelPosition ??= new RelayCommand(async obj =>
            {
                var reason = obj as string;

                await OrdersHelper.CancelOrderItem(_item, reason);
                this.Close();
            });
        }
    }
}
