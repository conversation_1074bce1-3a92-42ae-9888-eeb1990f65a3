﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             x:Name="this"
             Background="Transparent"
             xmlns:editors="http://schemas.devexpress.com/xamarin/2014/forms/editors" 
             xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
             x:Class="CRMMobileApp.Controls.Parts.Editors.CustomCrossComboBoxEdit">
    <ContentView.Content>
      <Grid>



            <editors:ComboBoxEdit   
                   x:Name="forAndroidIOSdateEdit"
                   IsVisible="{OnPlatform Android=True, iOS=True, Default=False}"
                   BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor}"
                   TextColor="{Binding Source={x:Reference this},Path=TextColor}"
                   BorderColor="{Binding Source={x:Reference this},Path=BorderColor}"
                   LabelText="{Binding Source={x:Reference this},Path=LabelText}"
                   HorizontalOptions="Fill"
                   VerticalOptions="Fill"
                   SelectedIndex="{Binding Source={x:Reference this},Path=SelectedIndex,Mode=OneWay}"
                   TextFontFamily="{Binding Source={x:Reference this},Path=TextFontFamily}"
                   CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
                   PlaceholderText="{Binding Source={x:Reference this},Path=PlaceholderText}"
                   PlaceholderColor="{Binding Source={x:Reference this},Path=PlaceholderColor}"
                   CornerMode="Round"
                   IsLabelFloating="False"
                   SelectionChanged="onSelectionChanged"
                   SelectedItem="{Binding Source={x:Reference this},Path=SelectedItem,Mode=TwoWay}"
                   ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource,Mode=TwoWay}" >
                <editors:ComboBoxEdit.ItemTemplate>
                    <DataTemplate>
                        <Grid HeightRequest="40">
                            <Label
                               Margin="8,8,0,0"
                               TextColor="{Binding Source={x:Reference this},Path=TextColor}" 
                               Text="{Binding}"/>
                        </Grid>
                    </DataTemplate>
                </editors:ComboBoxEdit.ItemTemplate>
            </editors:ComboBoxEdit>

            <editors1:ComboBoxEditForWPF 
                x:Name="forWPFdateEdit"
                IsVisible="{OnPlatform WPF=True, Default=False}"
                BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor}"
                TextColor="{Binding Source={x:Reference this},Path=TextColor}"
                BorderColor="{Binding Source={x:Reference this},Path=BorderColor}"
                HorizontalOptions="Fill"
                VerticalOptions="Fill"
                SelectedIndex="{Binding Source={x:Reference this},Path=SelectedIndex}"
                SelectedIndexChanged="onSelectionChanged"
                CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
                PlaceholderText="{Binding Source={x:Reference this},Path=PlaceholderText}"
                PlaceholderColor="{Binding Source={x:Reference this},Path=PlaceholderColor}"
                SelectedItem="{Binding Source={x:Reference this},Path=SelectedItem,Mode=TwoWay}"
                ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource,Mode=TwoWay}" />


        </Grid>
  </ContentView.Content>
</ContentView>