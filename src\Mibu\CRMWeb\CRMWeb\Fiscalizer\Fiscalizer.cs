﻿using Atol.Drivers10.Fptr;
using CRM.Models.Enums.Equipment;
using CRM.Models.Enums.Info;
using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Network;
using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using System.Text;

namespace Fiscalization
{
    public class Fiscalizer
    {
        private FiscalRegistrator _registrator;
        private NetworkTerminalUser _cashier;
        private Customer _customer;
        private Fptr _fptr;
        public Fiscalizer(FiscalRegistrator registrator,NetworkTerminalUser cashier)
        {
            _registrator = registrator;
            _cashier = cashier;
        }
        public Fiscalizer(FiscalRegistrator registrator, NetworkTerminalUser cashier,Customer customer)
        {
            _registrator = registrator;
            _cashier = cashier;
            _customer = customer;
        }


        public bool IsInitialized { get; private set; }
        
        public bool InitFiscalizer()
        {
            try
            {
                _fptr = new Fptr();


                if (!NumberCheckHelper.IsINN(_cashier.ИНН))
                {
                    return false;
                }
                if (!NumberCheckHelper.IsINN(_registrator.LegalEntity.ИНН))
                {
                    return false;
                }

                switch (_registrator.Connection)
                {
                    case ConnectionType.COM:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_PORT, Constants.LIBFPTR_PORT_COM.ToString());
                        break;
                    case ConnectionType.USB:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_PORT, Constants.LIBFPTR_PORT_USB.ToString());
                        break;
                    case ConnectionType.TCP_IP:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_PORT, Constants.LIBFPTR_PORT_TCPIP.ToString());
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_IPADDRESS, _registrator.TCPIP_IPAddress);
                        break;
                }

                switch (_registrator.Model)
                {
                    case FiscalRegistratorModel.Atol11F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_11F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol150F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_AUTO.ToString());
                        break;
                    case FiscalRegistratorModel.Atol25F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_25F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol20F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_20F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol50F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_50F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol91F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_91F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol22PTK:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_22F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol30F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_30F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol77F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_77F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol92F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_92F.ToString());
                        break;
                    case FiscalRegistratorModel.Atol55F:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_MODEL, Constants.LIBFPTR_MODEL_ATOL_55F.ToString());
                        break;
                }

                switch (_registrator.ConnectionPortNumber)
                {
                    case ConnectionPortNumber.COM1:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM1");
                        break;
                    case ConnectionPortNumber.COM2:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM2");
                        break;
                    case ConnectionPortNumber.COM3:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM3");
                        break;
                    case ConnectionPortNumber.COM4:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM4");
                        break;
                    case ConnectionPortNumber.COM5:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM5");
                        break;
                    case ConnectionPortNumber.COM6:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM6");
                        break;
                    case ConnectionPortNumber.COM7:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM7");
                        break;
                    case ConnectionPortNumber.COM8:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM8");
                        break;
                    case ConnectionPortNumber.COM9:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM9");
                        break;
                    case ConnectionPortNumber.COM10:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM10");
                        break;
                    case ConnectionPortNumber.COM11:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM11");
                        break;
                    case ConnectionPortNumber.COM12:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM12");
                        break;
                    case ConnectionPortNumber.COM13:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM13");
                        break;
                    case ConnectionPortNumber.COM14:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM14");
                        break;
                    case ConnectionPortNumber.COM15:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "COM15");
                        break;
                    case ConnectionPortNumber.ttyMT1:
                        _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_COM_FILE, "ttyMT1");
                        break;
                }

                _fptr.setSingleSetting(Constants.LIBFPTR_SETTING_BAUDRATE, Constants.LIBFPTR_PORT_BR_115200.ToString());
                _fptr.applySingleSettings();

               
                var res = _fptr.open();
        


                IsInitialized = true;
                return IsInitialized;
            }
            catch (Exception ex)
            {
                IsInitialized = false;
                return IsInitialized;
            }
        }


        ~Fiscalizer()
        {
            _fptr?.close();
            _fptr?.destroy();
        }




        /// <summary>
        /// true - если удалось открыть смену
        /// false - если смена уже открыта
        /// </summary>
        /// <returns></returns>
        public bool OpenShift()
        {
            if (!IsInitialized) throw ThrowNotInit();

            //while (true)
            //{

            //}

            AuthCashier();
            var res = _fptr.openShift();
            return res == 0;        
        }


        /// <summary>
        /// true - если удалось закрыть смену
        /// false - если смена уже закрыта
        /// </summary>
        /// <returns></returns>
        public bool CloseShift()
        {
            if (!IsInitialized) throw ThrowNotInit();


            AuthCashier();
            _fptr.setParam(Constants.LIBFPTR_PARAM_REPORT_TYPE, Constants.LIBFPTR_RT_CLOSE_SHIFT);

            var res = _fptr.report();
            return res == 0;
        }



        public bool CheckConnection()
        {
            if (!IsInitialized) throw ThrowNotInit();

            return _fptr.isOpened();
        }

        public bool TryOpenConnection()
        {
            if(_fptr == null)
            {
                return false;
            }

            _fptr.open();
            return _fptr.isOpened();
        }


        /// <summary>
        /// Фасад
        /// </summary>
        /// <param name="order"></param>
        public void FiscalizeOrder(Order order)
        {
            if (!IsInitialized) throw ThrowNotInit();

            OpenReceipt();
            SetReceiptItems(order);
            RegisterPayments(order);
            CloseReceipt();
        }


        /// <summary>
        /// true - если удалось открыть чек
        /// false - если чек уже открыт
        /// </summary>
        /// <returns></returns>
        public bool OpenReceipt()
        {
            if (!IsInitialized) throw ThrowNotInit();

            //  CloseReceipt();

            AuthCashier();

            if (_customer != null)
            {
                _fptr.setParam(1227, $"Покупатель {_customer.Surname} {_customer.Name} {_customer.Patronymic}");
                //_fptr.setParam(1228, "112233445573"); // инн
                _fptr.utilFormTlv();
                byte[] clientInfo = _fptr.getParamByteArray(Constants.LIBFPTR_PARAM_TAG_VALUE);

                _fptr.setParam(Constants.LIBFPTR_PARAM_RECEIPT_TYPE, Constants.LIBFPTR_RT_SELL);
                _fptr.setParam(1256, clientInfo);
            }


            _fptr.setParam(Constants.LIBFPTR_PARAM_RECEIPT_TYPE, Constants.LIBFPTR_RT_SELL);
            var res = _fptr.openReceipt();

            var e = _fptr.errorDescription();

            return res == 0;
        }

        public void SetReceiptItems(Order order)
        {
            if (!IsInitialized) throw ThrowNotInit();

            foreach (var item in order.Items)
            {
                MenuCategory cat = null;
                if (item.Product != null)
                    cat = item.Product.MenuCategory;
                else if (item.TechnicalCard != null)
                    cat = item.TechnicalCard.MenuCategory;

                SetProductOrTechCard(item, cat);

                foreach (var modifier in item.SelectedModifiers)
                {
                    foreach(var option in modifier.SelectedOptions)
                    {
                        SetItem(cat, 
                                option.ModifierOption.Title, 
                                option.PriceWithDiscount, 
                                option.Amount,
                                option.TotalDiscount,
                                false,
                                option.ModifierOption.Barcode);
                    }
                }

            }
        }

        private void SetProductOrTechCard(OrderItem item,MenuCategory cat)
        {


            string commodityTitle = "";
            if (item.Product != null)
                commodityTitle = item.Product.Title;
            else if (item.TechnicalCard != null)
                commodityTitle = item.TechnicalCard.Title;


            bool? needMarking = item.Product?.IsMarkingRequired;
            string barcode = "";
            if (needMarking == true)
            {
                barcode = item.Product.Barcode;
            }

            SetItem(cat, 
                    commodityTitle, 
                    item.ProductPriceWithDiscount, 
                    item.Amount,
                    item.TotalProductPriceDiscountSum,
                    needMarking, 
                    barcode);
        }
        private void SetItem(MenuCategory cat,
                             string commodityTitle,
                             double price,
                             double amount,
                             double discountSum,
                             bool? needMarking = false,
                             string barcode = "")
        {
            AuthCashier();



            _fptr.setParam(Constants.LIBFPTR_PARAM_COMMODITY_NAME, commodityTitle);
            _fptr.setParam(Constants.LIBFPTR_PARAM_PRICE, price);
            _fptr.setParam(Constants.LIBFPTR_PARAM_QUANTITY, amount);
            _fptr.setParam(Constants.LIBFPTR_PARAM_INFO_DISCOUNT_SUM, discountSum);
          



            if (needMarking == true)
            {
                _fptr.setParam(Constants.LIBFPTR_PARAM_MARKING_CODE_TYPE, Constants.LIBFPTR_MCT_EGAIS_20);
                byte[] barcodeBytes = Encoding.ASCII.GetBytes(barcode);
                _fptr.setParam(Constants.LIBFPTR_PARAM_MARKING_CODE, barcodeBytes);
            }



            if (cat.Fiscalization != null)
            {
                switch (cat.Fiscalization.НДС)
                {
                    case НДС.НДС0:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT0);
                        break;
                    case НДС.НДС10:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT10);
                        break;
                    case НДС.NO_НДС:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_NO);
                        break;
                    case НДС.НДС20:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT20);
                        break;
                    case НДС.НДС18:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT18);
                        break;
                    case НДС.НДС_Рассчитанный10_110:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT110);
                        break;
                    case НДС.НДС_Рассчитанный18_118:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT118);
                        break;
                    case НДС.НДС_Рассчитанный20_120:
                        _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT120);
                        break;
                }
                //_fptr.setParam(Constants.LIBFPTR_PARAM_USE_ONLY_TAX_TYPE, true);

                switch (cat.Fiscalization.CalculationSubject)
                {
                    case CalculationSubject.ExciseProduct:
                        _fptr.setParam(1212, 2);
                        break;
                    case CalculationSubject.Product:
                        _fptr.setParam(1212, 1);
                        break;
                    case CalculationSubject.Service:
                        _fptr.setParam(1212, 4);
                        break;
                }


                switch (cat.Fiscalization.Taxation.Type)
                {
                    case TaxationType.Default:
                        _fptr.setParam(1055, Constants.LIBFPTR_TT_OSN);
                        break;
                    case TaxationType.Упрощенная_СНО_Доход_минус_Расход:
                        _fptr.setParam(1055, Constants.LIBFPTR_TT_USN_INCOME_OUTCOME);
                        break;
                    case TaxationType.Упрощенная_СНО_Доход:
                        _fptr.setParam(1055, Constants.LIBFPTR_TT_USN_INCOME);
                        break;
                    case TaxationType.Единый_сельскохозяйственный_налог:
                        _fptr.setParam(1055, Constants.LIBFPTR_TT_ESN);
                        break;
                    case TaxationType.Традиционная_СНО:
                        _fptr.setParam(1055, Constants.LIBFPTR_TT_OSN);
                        break;
                    case TaxationType.Патентная_система_налогообложения:
                        _fptr.setParam(1055, Constants.LIBFPTR_TT_PATENT);
                        break;

                }
            }
            else
            {
                _fptr.setParam(Constants.LIBFPTR_PARAM_TAX_TYPE, Constants.LIBFPTR_TAX_VAT0);
                _fptr.setParam(63, 1);
                _fptr.setParam(1055, Constants.LIBFPTR_TT_OSN);
            }



            var res = _fptr.registration();
            var e = _fptr.errorDescription();
        }


        public void RegisterPayments(Order order)
        {
            if (!IsInitialized) throw ThrowNotInit();

            foreach (var payment in order.OrderPayments.Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Card))
            {
                _fptr.setParam(Constants.LIBFPTR_PARAM_PAYMENT_TYPE, Constants.LIBFPTR_PT_ELECTRONICALLY);
                _fptr.setParam(Constants.LIBFPTR_PARAM_PAYMENT_SUM, payment.Sum);

                 var res = _fptr.payment();
                 var e = _fptr.errorDescription();
            }
            foreach (var payment in order.OrderPayments.Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Cash))
            {
                _fptr.setParam(Constants.LIBFPTR_PARAM_PAYMENT_TYPE, Constants.LIBFPTR_PT_CASH);
                _fptr.setParam(Constants.LIBFPTR_PARAM_PAYMENT_SUM, payment.Sum);

                var res = _fptr.payment();
                var e = _fptr.errorDescription();
            }
        }

        /// <summary>
        /// true - если удалось отменить чек
        /// false - если не удалось отменить чек (например, чек закрыт или не открыт)
        /// </summary>
        /// <returns></returns>
        public bool CancelReceipt()
        {
            if (!IsInitialized) throw ThrowNotInit();

            var res = _fptr.cancelReceipt();
            return res == 0;
        }


        /// <summary>
        /// true - если удалось закрыть чек
        /// false - если не удалось закрыть чек или чек уже закрыт или еще не открыт
        /// </summary>
        /// <returns></returns>
        public bool CloseReceipt()
        {
            if (!IsInitialized) throw ThrowNotInit();

            _fptr.setParam(Constants.LIBFPTR_PARAM_PAYMENT_TYPE, Constants.LIBFPTR_PT_ELECTRONICALLY);
            var res = _fptr.closeReceipt();

            var e = _fptr.errorDescription();



            if (_fptr.checkDocumentClosed() == Constants.LIBFPTR_PARAM_DOCUMENT_PRINTED)
            {
                _fptr.continuePrint();
            }

            var eHue = _fptr.errorDescription();
            return res == 0;
        }



     



















        private bool AuthCashier()
        {
            _fptr.setParam(1021, $"Кассир {_cashier.Surname} {_cashier.Name.Substring(0,1)} {_cashier.Patronymic?.Substring(0, 1)}");
            _fptr.setParam(1203, _cashier.ИНН);
            var res = _fptr.operatorLogin();

            return res == 0;
        }

        private Exception ThrowNotInit()
        {
            return new Exception("Need to call InitFiscalizer and check IsInitialized property"); 
        }

    }
}