﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DutyClosingPopup : PopupPage
    {
        public DutyClosingPopup()
        {
            InitializeComponent();
        }


        private string comment;
        public string Comment
        {
            get => comment;
            set { comment = value; OnPropertyChanged(nameof(Comment)); }
        }

        private double sum;
        public double Sum
        {
            get => sum;
            set { sum = value; OnPropertyChanged(nameof(Sum)); }
        }

        private ICommand closeDuty;
        public ICommand CloseDuty
        {
            get => closeDuty ??= new RelayCommand(async obj =>
            {
                try
                {
                    if (string.IsNullOrEmpty(Comment))
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Комментарий обязателен для заполнения", "Ошибка"));
                        return;
                    }

                    await OrdersHelper.GetActiveOrders();
                    if (OrdersHelper.ActiveOrders.Count == 0)
                    {

                        if (ApplicationState.IsFiscalizationAvailable() == Enums.FiscalizationStatus.CanBeDone)
                        {
                            try
                            {
                                var res = ApplicationState.FiscalizerInUse.CloseShift();
                                if (!res)
                                {
                                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Фискальная смена уже закрыта", "Ошибка", "Продолжить"));
                                }
                            }
                            catch (Exception ex)
                            {
                                App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нет подключения к фискальному аппарату. Продолжение операции невозможно", "Ошибка", "Хорошо"));
                                return;
                            }
                        }



                        await ApplicationState.CloseDuty(Sum, Comment);
                        await App.Current.MainPage.Navigation.PopPopupAsync();
                        await Auth.LogoutUser();
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PopPopupAsync();
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Невозможно закрыть смену, пока есть открытые заказы", "Ошибка"));
                    }
                }
                catch(Exception ex)
                {

                }
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}