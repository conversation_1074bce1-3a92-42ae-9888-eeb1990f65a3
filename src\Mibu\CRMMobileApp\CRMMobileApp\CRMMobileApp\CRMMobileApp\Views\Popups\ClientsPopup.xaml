﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls" 
    xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates" 
    xmlns:effects="http://sharpnado.com" xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.ClientsPopup"
    x:Name="this">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            Margin="0,0,0,0"
            Padding="0"
            CornerRadius="20"
            WidthRequest="770"
            HeightRequest="360">
            <Grid ColumnSpacing="0"
                RowSpacing="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="280"/>
                    <ColumnDefinition Width="490"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0"
                      Margin="40,34,0,0"
                      HorizontalOptions="Fill"
                      VerticalOptions="Fill">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="50"/>
                        <RowDefinition Height="50"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>


                    <StackLayout
                        Grid.Row="0"
                        Spacing="1"
                        Orientation="Horizontal">
                        <Image 
                             VerticalOptions="Center"
                             HorizontalOptions="Start"
                             Source="{OnPlatform Default=client.png, WPF='pack://application:,,,/Images/client.png'}"
                             WidthRequest="30"
                             HeightRequest="30"/>
                        <Label
                             Margin="15,0,0,0"
                             VerticalOptions="Center"
                             TextColor="{StaticResource dark_purple}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="20"
                             Text="Клиенты"/>
                    </StackLayout>

                    <controls:EntryOutlined
                        Grid.Row="1"
                        Text="{Binding Source={x:Reference this},Path=SearchQuery,Mode=TwoWay}"
                        WidthRequest="250"
                        Margin="0,0,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        HeightRequest="40"
                        CornerRadius="20"
                        PlaceholderMargin="20,0,0,0"
                        TextMargin="20,0,0,0"
                        EntryBackground="{StaticResource bg_purple}"
                        BorderColor="{StaticResource bg_purple}"
                        PlaceholderColor="{StaticResource text_gray}"
                        Placeholder="Поиск"/>


                    <listviews:CustomCrossCollectionView  
                        Grid.Row="2"
                        MinItemSize="40"
                        ItemSpacing="5"
                        VerticalOptions="Fill"
                        Padding="0,0,0,0"
                        ItemsSource="{Binding Source={x:Reference this},Path=Clients,Mode=TwoWay}">
                        <listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                            <DataTemplate>
                                <ViewCell>
                                    <templates:ClientListItem 
                                        Tapped="onClientTapped"
                                        Model="{Binding}"/>
                                </ViewCell>
                            </DataTemplate>
                        </listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                        <listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                            <DataTemplate>
                                <templates:ClientListItem 
                                    Tapped="onClientTapped"
                                    Model="{Binding}"/>
                            </DataTemplate>
                        </listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                    </listviews:CustomCrossCollectionView>

                </Grid>

                <Grid Grid.Column="1">

                    <ImageButton 
                        Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        BackgroundColor="Transparent"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Padding="10"
                        WidthRequest="35"
                        HeightRequest="35"
                        Margin="0,10,10,0"/>

                    <Frame 
                        HasShadow="False"
                        CornerRadius="10"
                        WidthRequest="410"
                        HeightRequest="180"
                        BackgroundColor="{StaticResource bg_purple}"
                        VerticalOptions="Center"
                        HorizontalOptions="Center">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">
                                <StackLayout
                                    HorizontalOptions="Start"
                                    VerticalOptions="Start"
                                    Spacing="20"
                                    Margin="50,20,0,0">
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource text_gray}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        Text="ФИО"/>
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource text_gray}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        Text="Телефон"/>
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource text_gray}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        Text="Дата рождения"/>
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource text_gray}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        Text="Скидка"/>
                                </StackLayout>
                            </Grid>

                            <Grid Grid.Column="1">
                                <StackLayout
                                    HorizontalOptions="Start"
                                    VerticalOptions="Start"
                                    Spacing="20"
                                    Margin="0,20,0,0">
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource dark_purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span Text="{Binding Source={x:Reference this},Path=SelectedClient.Model.Surname}"/>
                                                    <Span Text=" "/>
                                                    <Span Text="{Binding Source={x:Reference this},Path=SelectedClient.Model.Name}"/>
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource dark_purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        Text="{Binding Source={x:Reference this},Path=SelectedClient.Model.Phone}"/>
                                    <Label
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource dark_purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        Text="{Binding Source={x:Reference this},Path=SelectedClient.Model.BirthDate, StringFormat='{0:dd.MM.yyyy}'}"/>
                                    <StackLayout
                                        Orientation="Horizontal">
                                        <Label
                                            HorizontalOptions="Start"
                                            TextColor="{StaticResource dark_purple}"
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="14"
                                            LineBreakMode="TailTruncation"
                                            Text="{Binding Source={x:Reference this},Path=SelectedClient.Model.LoyaltyProgram.Title}"/>
                                        <Frame 
                                            BackgroundColor="{StaticResource green}"
                                            WidthRequest="40"
                                            HeightRequest="20"
                                            HasShadow="False"
                                            Padding="0">
                                            <Label
                                                HorizontalOptions="Center"
                                                VerticalOptions="Center"
                                                TextColor="White"
                                                FontFamily="TTFirsNeue-Regular"
                                                FontSize="14">
                                                <Label.FormattedText>
                                                    <FormattedString>
                                                        <FormattedString.Spans>
                                                            <Span Text="{Binding Source={x:Reference this},Path=SelectedClient.Model.CurrentLoyaltyProgramPercent}"/>
                                                            <Span Text="%"/>
                                                        </FormattedString.Spans>
                                                    </FormattedString>
                                                </Label.FormattedText>
                                            </Label>
                                        </Frame>
                                    </StackLayout>
                                </StackLayout>
                            </Grid>

                        </Grid>
                    </Frame>


                    <StackLayout
                        VerticalOptions="End"
                        HorizontalOptions="End"
                        Margin="0,0,30,15"
                        Spacing="20"
                        Orientation="Horizontal">

                        <Button 
                            IsVisible="{Binding Source={x:Reference this},Path=ClientSettings.AllowToAddClientsInMobileApp}"
                            Command="{Binding Source={x:Reference this},Path=GoToNewClient}"
                            Style="{StaticResource bg_purple_btn}"
                            VerticalOptions="Center"
                            Text="Создать нового"
                            WidthRequest="170"
                            HeightRequest="40"/>
                        <Button 
                            Command="{Binding Source={x:Reference this},Path=SelectClient}"
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalOptions="Center"
                            Text="Выбрать клиента"
                            WidthRequest="170"
                            HeightRequest="40"/>

                    </StackLayout>

                </Grid>



            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>