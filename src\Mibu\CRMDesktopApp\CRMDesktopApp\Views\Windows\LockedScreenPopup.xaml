﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.LockedScreenPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls"
        xmlns:controls1="clr-namespace:CRMDesktopApp.Controls"
        mc:Ignorable="d"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        x:Name="this"
        Title="" >
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Grid   
        Background="Transparent">

        <Image
            Stretch="Fill"
            Source="pack://application:,,,/Resources/Images/main_gradient.png"/>

        <StackPanel
            Margin="0,110,0,0">

            <TextBlock
                HorizontalAlignment="Center"
                Foreground="White"
                FontSize="24"
                Text="Экран заблокирован, введите Ваш пин код" />

            <Grid
                 Margin="0,50,0,0">
                <controls:EntryOutlined  
                    x:Name="entry"
                    HorizontalAlignment="Center"
                    TextMargin="45,0,0,0"
                    PlaceholderMargin="45,0,0,0"
                    PlaceholderColor="#e5e5e5"
                    BorderColor="White"
                    EntryBackground="Transparent"
                    IsEnabled="False"
                    Placeholder="Введите пин"
                    Text="{Binding ElementName=this,Path=Password,Mode=TwoWay}"
                    Width="225"
                    Style="{StaticResource gray_bordered_entry}"/>

                <Image 
                     Margin="-180,-4,0,0"
                     HorizontalAlignment="Center"
                     Height="30"
                     Width="30"
                     Source="pack://application:,,,/Resources/Images/login.png"/>
            </Grid>

            <controls1:AuthWhiteKeyboard
                Padding="0,0,0,0"
                HorizontalAlignment="Center"
                x:Name="keyboard" />

            <Button 
                FontSize="15"
                HorizontalAlignment="Center"
                Command="{Binding ElementName=this,Path=Logout}"
                Background="Transparent"
                BorderThickness="0"
                Foreground="White"
                Content="Выйти" />

        </StackPanel>


    </Grid>
</abstactions:BaseWindow>
