﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMMobileApp.Controls.Templates.ClientListItem"
             x:Name="this"
             HeightRequest="30"
             WidthRequest="250">
    <ContentView.Content>
      <Frame
          Padding="0"
          Background="Transparent"
          CornerRadius="10"
          HasShadow="False">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Frame.GestureRecognizers>
            <Frame.Style>
                <Style TargetType="Frame">
                    <Style.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="False">
                            <Setter Property="BorderColor" Value="Transparent"></Setter>
                        </DataTrigger>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="True">
                            <Setter Property="BorderColor" Value="{StaticResource purple}"></Setter>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Frame.Style>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Label
                    Grid.Column="0"
                    Margin="20,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    TextColor="{StaticResource text_gray}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="12">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.Model.Surname}"/>
                                <Span Text=" "/>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.Model.Name}"/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
                
                
                <Label
                    Grid.Column="1"
                    Margin="0,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    TextColor="{StaticResource text_gray}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="12"
                    Text="{Binding Source={x:Reference this},Path=Model.Model.Phone}"/>

            </Grid>
      </Frame>
  </ContentView.Content>
</ContentView>