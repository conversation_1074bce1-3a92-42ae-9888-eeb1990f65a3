﻿using CRM.Models.Gamification.Abstractions;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace CrmAdminApp.Abstractions
{
    public abstract class AbsChatMessageCard : ContentView
    {
        public ChatMessage Message { get; protected set; }
        public AbsChatMessageCard(ChatMessage message)
        {
            Message = message;
        }

        public abstract void Render();
        public abstract void Render(ChatMessage message);
    }
}
