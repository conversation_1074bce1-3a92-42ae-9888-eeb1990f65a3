/**
Core script to handle the entire theme and core functions
**/
$.fn.modal.Constructor.prototype.enforceFocus = function() {}; // Для правильной работы Select2 в модальных окнах bootstrap
var App = function() {

    // IE mode
    var isRTL = false;
    var isIE8 = false;
    var isIE9 = false;
    var isIE10 = false;

    var resizeHandlers = [];

    var assetsPath = '/themes/metronic4.6/assets/';

    var globalImgPath = 'img/';

    var globalPluginsPath = 'plugins/';

    var globalCssPath = 'css/';

    // theme layout color set

    var brandColors = {
        'blue': '#12b9f3',
        'red': '#F3565D',
        'green': '#1bbc9b',
        'purple': '#9b59b6',
        'grey': '#95a5a6',
        'yellow': '#F8CB00'
    };

    // initializes main settings
    var handleInit = function() {

        if ($('body').css('direction') === 'rtl') {
            isRTL = true;
        }

        isIE8 = !!navigator.userAgent.match(/MSIE 8.0/);
        isIE9 = !!navigator.userAgent.match(/MSIE 9.0/);
        isIE10 = !!navigator.userAgent.match(/MSIE 10.0/);

        if (isIE10) {
            $('html').addClass('ie10'); // detect IE10 version
        }

        if (isIE10 || isIE9 || isIE8) {
            $('html').addClass('ie'); // detect IE10 version
        }
    };

    // runs callback functions set by App.addResponsiveHandler().
    var _runResizeHandlers = function() {
        // reinitialize other subscribed elements
        for (var i = 0; i < resizeHandlers.length; i++) {
            var each = resizeHandlers[i];
            each.call();
        }
    };

    // handle the layout reinitialization on window resize
    var handleOnResize = function() {
        var resize;
        if (isIE8) {
            var currheight;
            $(window).resize(function() {
                if (currheight == document.documentElement.clientHeight) {
                    return; //quite event since only body resized not window.
                }
                if (resize) {
                    clearTimeout(resize);
                }
                resize = setTimeout(function() {
                    _runResizeHandlers();
                }, 50); // wait 50ms until window resize finishes.
                currheight = document.documentElement.clientHeight; // store last body client height
            });
        } else {
            $(window).resize(function() {
                if (resize) {
                    clearTimeout(resize);
                }
                resize = setTimeout(function() {
                    _runResizeHandlers();
                }, 50); // wait 50ms until window resize finishes.
            });
        }
    };

    var handlePortletToolsInited = false;
    var handlePortletTools = function() {
        if (handlePortletToolsInited) {
            return false;
        } else {
            handlePortletToolsInited = true;
        }
        // handle portlet remove
        $('body').on('click', '.portlet > .portlet-title > .tools > a.remove', function(e) {
            e.preventDefault();
            var portlet = $(this).closest(".portlet");

            if ($('body').hasClass('page-portlet-fullscreen')) {
                $('body').removeClass('page-portlet-fullscreen');
            }

            portlet.find('.portlet-title .fullscreen').tooltip('destroy');
            portlet.find('.portlet-title > .tools > .reload').tooltip('destroy');
            portlet.find('.portlet-title > .tools > .remove').tooltip('destroy');
            portlet.find('.portlet-title > .tools > .config').tooltip('destroy');
            portlet.find('.portlet-title > .tools > .collapse, .portlet > .portlet-title > .tools > .expand').tooltip('destroy');

            portlet.remove();
        });

        // handle portlet fullscreen
        $('body').on('click', '.portlet > .portlet-title .fullscreen', function(e) {
            e.preventDefault();
            var portlet = $(this).closest(".portlet");
            if (portlet.hasClass('portlet-fullscreen')) {
                $(this).removeClass('on');
                portlet.removeClass('portlet-fullscreen');
                $('body').removeClass('page-portlet-fullscreen');
                portlet.children('.portlet-body').css('height', 'auto');
            } else {
                var height = App.getViewPort().height -
                    portlet.children('.portlet-title').outerHeight() -
                    parseInt(portlet.children('.portlet-body').css('padding-top')) -
                    parseInt(portlet.children('.portlet-body').css('padding-bottom'));

                $(this).addClass('on');
                portlet.addClass('portlet-fullscreen');
                $('body').addClass('page-portlet-fullscreen');
                portlet.children('.portlet-body').css('height', height);
            }
        });

        $('body').on('click', '.portlet > .portlet-title > .tools > a.reload', function(e) {
            e.preventDefault();
            var el = $(this).closest(".portlet").children(".portlet-body");
            var url = $(this).attr("data-url");
            var error = $(this).attr("data-error-display");
            if (url) {
                App.blockUI({
                    target: el,
                    animate: true,
                    overlayColor: 'none'
                });
                $.ajax({
                    type: "GET",
                    cache: false,
                    url: url,
                    dataType: "html",
                    success: function(res) {
                        App.unblockUI(el);
                        el.html(res);
                        App.initAjax() // reinitialize elements & plugins for newly loaded content
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        App.unblockUI(el);
                        var msg = 'Error on reloading the content. Please check your connection and try again.';
                        if (error == "toastr" && toastr) {
                            toastr.error(msg);
                        } else if (error == "notific8" && $.notific8) {
                            $.notific8('zindex', 11500);
                            $.notific8(msg, {
                                theme: 'ruby',
                                life: 3000
                            });
                        } else {
                            alert(msg);
                        }
                    }
                });
            } else {
                // for demo purpose
                App.blockUI({
                    target: el,
                    animate: true,
                    overlayColor: 'none'
                });
                window.setTimeout(function() {
                    App.unblockUI(el);
                }, 1000);
            }
        });

        // load ajax data on page init
        $('.portlet .portlet-title a.reload[data-load="true"]').click();

        $('body').on('click', '.portlet > .portlet-title > .tools > .collapse, .portlet .portlet-title > .tools > .expand', function(e) {
            e.preventDefault();
            var el = $(this).closest(".portlet").children(".portlet-body");
            if ($(this).hasClass("collapse")) {
                $(this).removeClass("collapse").addClass("expand").trigger('collapse');
                el.slideUp(200);
            } else {
                $(this).removeClass("expand").addClass("collapse").trigger('expand');
                el.slideDown(200);
            }
        });
    };

    // Handle full screen mode toggle
    var handleFullScreenMode = function() {
        // mozfullscreenerror event handler

        // toggle full screen
        function toggleFullScreen() {
            if (!document.fullscreenElement &&    // alternative standard method
                    !document.mozFullScreenElement && !document.webkitFullscreenElement) {  // current working methods
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.mozRequestFullScreen) {
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
                }
            } else {
                if (document.cancelFullScreen) {
                    document.cancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                }
            }
        }

        $('#trigger_fullscreen').unbind().click(function() {
            toggleFullScreen();
        });
    }

    // Handlesmaterial design checkboxes
    var handleMaterialDesign = function() {

        // Material design ckeckbox and radio effects
        $('body').on('click', '.md-checkbox > label, .md-radio > label', function() {
            var the = $(this);
            // find the first span which is our circle/bubble
            var el = $(this).children('span:first-child');

            // add the bubble class (we do this so it doesnt show on page load)
            el.addClass('inc');

            // clone it
            var newone = el.clone(true);

            // add the cloned version before our original
            el.before(newone);

            // remove the original so that it is ready to run on next click
            $("." + el.attr("class") + ":last", the).remove();
        });

        if ($('body').hasClass('page-md')) {
            // Material design click effect
            // credit where credit's due; http://thecodeplayer.com/walkthrough/ripple-click-effect-google-material-design
            var element, circle, d, x, y;
            $('body').on('click', 'a.btn, button.btn, input.btn, label.btn', function(e) {
                element = $(this);

                if(element.find(".md-click-circle").length == 0) {
                    element.prepend("<span class='md-click-circle'></span>");
                }

                circle = element.find(".md-click-circle");
                circle.removeClass("md-click-animate");

                if(!circle.height() && !circle.width()) {
                    d = Math.max(element.outerWidth(), element.outerHeight());
                    circle.css({height: d, width: d});
                }

                x = e.pageX - element.offset().left - circle.width()/2;
                y = e.pageY - element.offset().top - circle.height()/2;

                circle.css({top: y+'px', left: x+'px'}).addClass("md-click-animate");

                setTimeout(function() {
                    circle.remove();
                }, 1000);
            });
        }

        // Floating labels
        var handleInput = function(el) {
            if (el.val() != "") {
                el.addClass('edited');
            } else {
                el.removeClass('edited');
            }
        }

        $('body').on('keydown', '.form-md-floating-label .form-control', function(e) {
            handleInput($(this));
        });
        $('body').on('blur', '.form-md-floating-label .form-control', function(e) {
            handleInput($(this));
        });

        $('.form-md-floating-label .form-control').each(function(){
            if ($(this).val().length > 0) {
                $(this).addClass('edited');
            }
        });
    }

    // Handles custom checkboxes & radios using jQuery iCheck plugin
    var handleiCheck = function() {
        if (!$().iCheck) {
            return;
        }

        $('.icheck').each(function() {
            var checkboxClass = $(this).attr('data-checkbox') ? $(this).attr('data-checkbox') : 'icheckbox_minimal-grey';
            var radioClass = $(this).attr('data-radio') ? $(this).attr('data-radio') : 'iradio_minimal-grey';

            if (checkboxClass.indexOf('_line') > -1 || radioClass.indexOf('_line') > -1) {
                $(this).iCheck({
                    checkboxClass: checkboxClass,
                    radioClass: radioClass,
                    insert: '<div class="icheck_line-icon"></div>' + $(this).attr("data-label")
                });
            } else {
                $(this).iCheck({
                    checkboxClass: checkboxClass,
                    radioClass: radioClass
                });
            }
        });
    };

    // Handles Bootstrap switches
    var handleBootstrapSwitch = function() {
        if (!$().bootstrapSwitch) {
            return;
        }
        $('.make-switch').bootstrapSwitch();
    };

    // Handles Bootstrap confirmations
    var handleBootstrapConfirmation = function() {
        if (!$().confirmation) {
            return;
        }
        $('[data-toggle=confirmation]').confirmation({ btnOkClass: 'btn btn-sm btn-success', btnCancelClass: 'btn btn-sm btn-danger'});
    }

    // Handles Bootstrap Accordions.
    var handleAccordions = function() {
        $('body').on('shown.bs.collapse', '.accordion.scrollable', function(e) {
            App.scrollTo($(e.target));
        });
    };

    // Handles Bootstrap Tabs.
    var handleTabs = function() {
        //activate tab if tab id provided in the URL
        if (encodeURI(location.hash)) {
            var tabid = encodeURI(location.hash.substr(1));
            $('a[href="#' + tabid + '"]').parents('.tab-pane:hidden').each(function() {
                var tabid = $(this).attr("id");
                $('a[href="#' + tabid + '"]').click();
            });
            $('a[href="#' + tabid + '"]').click();
        }

        if ($().tabdrop) {
            $('.tabbable-tabdrop .nav-pills, .tabbable-tabdrop .nav-tabs').tabdrop({
                text: '<i class="fa fa-ellipsis-v"></i>&nbsp;<i class="fa fa-angle-down"></i>'
            });
        }
    };

    // Handles Bootstrap Modals.
    var handleModals = function() {
        // fix stackable modal issue: when 2 or more modals opened, closing one of modal will remove .modal-open class.
        $('body').on('hide.bs.modal', function() {
            if ($('.modal:visible').size() > 1 && $('html').hasClass('modal-open') === false) {
                $('html').addClass('modal-open');
            } else if ($('.modal:visible').size() <= 1) {
                $('html').removeClass('modal-open');
            }
        });

        // fix page scrollbars issue
        $('body').on('show.bs.modal', '.modal', function() {
            if ($(this).hasClass("modal-scroll")) {
                $('body').addClass("modal-open-noscroll");
            }
        });

        // fix page scrollbars issue
        $('body').on('hidden.bs.modal', '.modal', function() {
            $('body').removeClass("modal-open-noscroll");
        });

        // remove ajax content and remove cache on modal closed
        $('body').on('hidden.bs.modal', '.modal:not(.modal-cached)', function () {
            $(this).removeData('bs.modal');
        });
    };

    // Handles Bootstrap Tooltips.
    var handleTooltips = function() {
        // global tooltips
        $('.tooltips').tooltip();

        // portlet tooltips
        $('.portlet > .portlet-title .fullscreen').tooltip({
            trigger: 'hover',
            container: 'body',
            title: 'Fullscreen'
        });
        $('.portlet > .portlet-title > .tools > .reload').tooltip({
            trigger: 'hover',
            container: 'body',
            title: 'Reload'
        });
        $('.portlet > .portlet-title > .tools > .remove').tooltip({
            trigger: 'hover',
            container: 'body',
            title: 'Remove'
        });
        $('.portlet > .portlet-title > .tools > .config').tooltip({
            trigger: 'hover',
            container: 'body',
            title: 'Settings'
        });
        $('.portlet > .portlet-title > .tools > .collapse, .portlet > .portlet-title > .tools > .expand').tooltip({
            trigger: 'hover',
            container: 'body',
            title: 'Collapse/Expand'
        });
    };

    // Handles Bootstrap Dropdowns
    var handleDropdowns = function() {
        /*
          Hold dropdown on click
        */
        $('body').on('click', '.dropdown-menu.hold-on-click', function(e) {
            e.stopPropagation();
        });
    };

    var handleAlerts = function() {
        $('body').on('click', '[data-close="alert"]', function(e) {
            $(this).parent('.alert').hide();
            $(this).closest('.note').hide();
            e.preventDefault();
        });

        $('body').on('click', '[data-close="note"]', function(e) {
            $(this).closest('.note').hide();
            e.preventDefault();
        });

        $('body').on('click', '[data-remove="note"]', function(e) {
            $(this).closest('.note').remove();
            e.preventDefault();
        });
    };

    // Handle Hower Dropdowns
    var handleDropdownHover = function() {
        $('[data-hover="dropdown"]').not('.hover-initialized').each(function() {
            $(this).dropdownHover();
            $(this).addClass('hover-initialized');
        });
    };

    // Handle textarea autosize
    var handleTextareaAutosize = function() {
        if (typeof(autosize) == "function") {
            autosize(document.querySelector('textarea.autosizeme'));
        }
    }

    // Handles Bootstrap Popovers

    // last popep popover
    var lastPopedPopover;

    var handlePopovers = function() {
        $('.popovers').popover();

        // close last displayed popover

        $(document).on('click.bs.popover.data-api', function(e) {
            if (lastPopedPopover) {
                lastPopedPopover.popover('hide');
            }
        });
    };

    // Handles scrollable contents using jQuery SlimScroll plugin.
    var handleScrollers = function() {
        App.initSlimScroll('.scroller');
    };

    // Handles Image Preview using jQuery Fancybox plugin
    var handleFancybox = function() {
        if (!jQuery.fancybox) {
            return;
        }

        if ($(".fancybox-button").size() > 0) {
            $(".fancybox-button").fancybox({
                groupAttr: 'data-rel',
                prevEffect: 'none',
                nextEffect: 'none',
                closeBtn: true,
                helpers: {
                    title: {
                        type: 'inside'
                    }
                }
            });
        }
    };

    // Handles counterup plugin wrapper
    var handleCounterup = function() {
        if (!$().counterUp) {
            return;
        }

        $("[data-counter='counterup']").counterUp({
            delay: 10,
            time: 1000
        });
    };

    // Fix input placeholder issue for IE8 and IE9
    var handleFixInputPlaceholderForIE = function() {
        //fix html5 placeholder attribute for ie7 & ie8
        if (isIE8 || isIE9) { // ie8 & ie9
            // this is html5 placeholder fix for inputs, inputs with placeholder-no-fix class will be skipped(e.g: we need this for password fields)
            $('input[placeholder]:not(.placeholder-no-fix), textarea[placeholder]:not(.placeholder-no-fix)').each(function() {
                var input = $(this);

                if (input.val() === '' && input.attr("placeholder") !== '') {
                    input.addClass("placeholder").val(input.attr('placeholder'));
                }

                input.focus(function() {
                    if (input.val() == input.attr('placeholder')) {
                        input.val('');
                    }
                });

                input.blur(function() {
                    if (input.val() === '' || input.val() == input.attr('placeholder')) {
                        input.val(input.attr('placeholder'));
                    }
                });
            });
        }
    };

    // Handle Select2 Dropdowns
    var handleSelect2 = function() {
        if ($().select2) {
            //$.fn.select2.defaults.set("theme", "bootstrap");
            $('.select2me').select2({
                placeholder: "Select",
                width: 'auto',
                allowClear: true
            });
        }
    };

    // handle group element heights
    var handleHeight = function() {
        $('[data-auto-height]').each(function() {
            var parent = $(this);
            var items = $('[data-height]', parent);
            var height = 0;
            var mode = parent.attr('data-mode');
            var offset = parseInt(parent.attr('data-offset') ? parent.attr('data-offset') : 0);

            items.each(function() {
                if ($(this).attr('data-height') == "height") {
                    $(this).css('height', '');
                } else {
                    $(this).css('min-height', '');
                }

                var height_ = (mode == 'base-height' ? $(this).outerHeight() : $(this).outerHeight(true));
                if (height_ > height) {
                    height = height_;
                }
            });

            height = height + offset;

            items.each(function() {
                if ($(this).attr('data-height') == "height") {
                    $(this).css('height', height);
                } else {
                    $(this).css('min-height', height);
                }
            });

            if(parent.attr('data-related')) {
                $(parent.attr('data-related')).css('height', parent.height());
            }
        });
    }

    var floatAddBtnInited = false;
    var floatAddBtnInit = function () {
        if (floatAddBtnInited) { return; }
        floatAddBtnInited = true;
        var $addBtn =$('span[data-act="add"]').filter(function () {
            return !$(this).closest('.modal').length;
        }).filter(function () {
            return !$(this).closest('.noFloatAddBtn').length;
        });
        if ($addBtn.length!==1) { return; }
        var $floatAddBtn = $('<a class="btn-floating btn-no-showed"><i class="fa fa-plus" aria-hidden="true"></i></a>').appendTo('body').click(function () {
            $addBtn.trigger('click');
        });
        window.onscroll = function() {
            var scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
            if ($addBtn.offset().top < scrollTop+15) {
                $floatAddBtn.removeClass('btn-no-showed');
            } else {
                $floatAddBtn.addClass('btn-no-showed');
            }
        }
    };

    //* END:CORE HANDLERS *//
    // Initialized flag
    var AppInitialized = false;

    // Ajax requests pool
    var _ajaxRequests = [];

    $(window).unload(function () {
        $(_ajaxRequests).each(function (i, xhr) {
            xhr.abort();
        });
        _ajaxRequests = [];
    });

    return {
        //main function to initiate the theme
        init: function() {
            // Запрещаем повторную инициализацию
            if (AppInitialized) {
                return;
            }
            //IMPORTANT!!!: Do not modify the core handlers call order.

            //Core handlers
            handleInit(); // initialize core variables
            handleOnResize(); // set and handle responsive

            //UI Component handlers
            handleMaterialDesign(); // handle material design
            handleiCheck(); // handles custom icheck radio and checkboxes
            handleBootstrapSwitch(); // handle bootstrap switch plugin
            handleScrollers(); // handles slim scrolling contents
            handleFancybox(); // handle fancy box
            handleSelect2(); // handle custom Select2 dropdowns
            handlePortletTools(); // handles portlet action bar functionality(refresh, configure, toggle, remove)
            handleAlerts(); //handle closabled alerts
            handleDropdowns(); // handle dropdowns
            handleTabs(); // handle tabs
            handleTooltips(); // handle bootstrap tooltips
            handlePopovers(); // handles bootstrap popovers
            handleAccordions(); //handles accordions
            handleModals(); // handle modals
            handleBootstrapConfirmation(); // handle bootstrap confirmations
            handleTextareaAutosize(); // handle autosize textareas
            handleCounterup(); // handle counterup instances
            handleFullScreenMode(); // handles full screen

            //Handle group element heights
            this.addResizeHandler(handleHeight); // handle auto calculating height on window resize

            // Hacks
            handleFixInputPlaceholderForIE(); //IE8 & IE9 input placeholder issue fix
            floatAddBtnInit();
            window.addEventListener("message", function(event) {
                if (event.source != window || !event.data) {return;}
                if (event.data.type && (event.data.type == "FPOSExtensionExist")) {$(document).trigger('extension.exist');}
            }, false);
            window.postMessage({ type: "FPOSExtensionCheck"}, "*");
            AppInitialized = true;
            $(document).trigger('app.init');
            setTimeout(_=> {$(document).trigger('extension.exist');}, 200);
        },

        //main function to initiate core javascript after ajax complete
        initAjax: function() {
            //handleUniform(); // handles custom radio & checkboxes
            handleiCheck(); // handles custom icheck radio and checkboxes
            handleBootstrapSwitch(); // handle bootstrap switch plugin
            handleDropdownHover(); // handles dropdown hover
            handleScrollers(); // handles slim scrolling contents
            handleSelect2(); // handle custom Select2 dropdowns
            handleFancybox(); // handle fancy box
            handleDropdowns(); // handle dropdowns
            handleTooltips(); // handle bootstrap tooltips
            handlePopovers(); // handles bootstrap popovers
            handleAccordions(); //handles accordions
            handleBootstrapConfirmation(); // handle bootstrap confirmations
        },

        //init main components
        initComponents: function() {
            this.initAjax();
        },

        //public function to remember last opened popover that needs to be closed on click
        setLastPopedPopover: function(el) {
            lastPopedPopover = el;
        },

        //public function to add callback a function which will be called on window resize
        addResizeHandler: function(func) {
            resizeHandlers.push(func);
        },

        //public functon to call _runresizeHandlers
        runResizeHandlers: function() {
            _runResizeHandlers();
        },

        // wrApper function to scroll(focus) to an element
        scrollTo: function(el, offeset) {
            var pos = (el && el.size() > 0) ? el.offset().top : 0;

            if (el) {
                if ($('body').hasClass('page-header-fixed')) {
                    pos = pos - $('.page-header').height();
                } else if ($('body').hasClass('page-header-top-fixed')) {
                    pos = pos - $('.page-header-top').height();
                } else if ($('body').hasClass('page-header-menu-fixed')) {
                    pos = pos - $('.page-header-menu').height();
                }
                pos = pos + (offeset ? offeset : -1 * el.height());
            }

            $('html,body').animate({
                scrollTop: pos
            }, 'slow');
        },

        initSlimScroll: function(el) {
            if (!$().slimScroll) {
                return;
            }

            $(el).each(function() {
                if ($(this).attr("data-initialized")) {
                    return; // exit
                }

                var height;

                if ($(this).attr("data-height")) {
                    height = $(this).attr("data-height");
                } else {
                    height = $(this).css('height');
                }

                $(this).slimScroll({
                    allowPageScroll: true, // allow page scroll when the element scroll is ended
                    size: '7px',
                    color: ($(this).attr("data-handle-color") ? $(this).attr("data-handle-color") : '#bbb'),
                    wrapperClass: ($(this).attr("data-wrapper-class") ? $(this).attr("data-wrapper-class") : 'slimScrollDiv'),
                    railColor: ($(this).attr("data-rail-color") ? $(this).attr("data-rail-color") : '#eaeaea'),
                    position: isRTL ? 'left' : 'right',
                    height: height,
                    alwaysVisible: ($(this).attr("data-always-visible") == "1" ? true : false),
                    railVisible: ($(this).attr("data-rail-visible") == "1" ? true : false),
                    disableFadeOut: true
                });

                $(this).attr("data-initialized", "1");
            });
        },

        destroySlimScroll: function(el) {
            if (!$().slimScroll) {
                return;
            }

            $(el).each(function() {
                if ($(this).attr("data-initialized") === "1") { // destroy existing instance before updating the height
                    $(this).removeAttr("data-initialized");
                    $(this).removeAttr("style");

                    var attrList = {};

                    // store the custom attribures so later we will reassign.
                    if ($(this).attr("data-handle-color")) {
                        attrList["data-handle-color"] = $(this).attr("data-handle-color");
                    }
                    if ($(this).attr("data-wrapper-class")) {
                        attrList["data-wrapper-class"] = $(this).attr("data-wrapper-class");
                    }
                    if ($(this).attr("data-rail-color")) {
                        attrList["data-rail-color"] = $(this).attr("data-rail-color");
                    }
                    if ($(this).attr("data-always-visible")) {
                        attrList["data-always-visible"] = $(this).attr("data-always-visible");
                    }
                    if ($(this).attr("data-rail-visible")) {
                        attrList["data-rail-visible"] = $(this).attr("data-rail-visible");
                    }

                    $(this).slimScroll({
                        wrapperClass: ($(this).attr("data-wrapper-class") ? $(this).attr("data-wrapper-class") : 'slimScrollDiv'),
                        destroy: true
                    });

                    var the = $(this);

                    // reassign custom attributes
                    $.each(attrList, function(key, value) {
                        the.attr(key, value);
                    });

                }
            });
        },

        // function to scroll to the top
        scrollTop: function() {
            App.scrollTo();
        },

        // wrApper function to  block element(indicate loading)
        blockUI: function(options) {
            options = $.extend(true, {}, options);
            var html = '';
            if (options.animate) {
                html = '<div class="loading-message ' + (options.boxed ? 'loading-message-boxed' : '') + '">' + '<div class="block-spinner-bar"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div>' + '</div>';
            } else if (options.iconOnly) {
                html = '<div class="loading-message ' + (options.boxed ? 'loading-message-boxed' : '') + '"><img src="' + this.getGlobalImgPath() + 'loading-spinner-grey.gif" align=""></div>';
            } else if (options.textOnly) {
                html = '<div class="loading-message ' + (options.boxed ? 'loading-message-boxed' : '') + '"><span>&nbsp;&nbsp;' + (options.message ? options.message : 'LOADING...') + '</span></div>';
            } else {
                html = '<div class="loading-message ' + (options.boxed ? 'loading-message-boxed' : '') + '"><img src="' + this.getGlobalImgPath() + 'loading-spinner-grey.gif" align=""><span>&nbsp;&nbsp;' + (options.message ? options.message : 'LOADING...') + '</span></div>';
            }

            if (options.target) { // element blocking
                var el = $(options.target);
                if (el.height() <= ($(window).height())) {
                    options.cenrerY = true;
                }
                el.block({
                    message: html,
                    baseZ: options.zIndex ? options.zIndex : 1000,
                    centerY: options.cenrerY !== undefined ? options.cenrerY : false,
                    css: {
                        top: '10%',
                        border: '0',
                        padding: '0',
                        backgroundColor: 'none'
                    },
                    overlayCSS: {
                        backgroundColor: options.overlayColor ? options.overlayColor : '#555',
                        opacity: options.boxed ? 0.05 : 0.1,
                        cursor: 'wait'
                    }
                });
            } else { // page blocking
                $.blockUI({
                    message: html,
                    baseZ: options.zIndex ? options.zIndex : 1000,
                    css: {
                        border: '0',
                        padding: '0',
                        backgroundColor: 'none'
                    },
                    overlayCSS: {
                        backgroundColor: options.overlayColor ? options.overlayColor : '#555',
                        opacity: options.boxed ? 0.05 : 0.1,
                        cursor: 'wait'
                    }
                });
            }
        },

        // wrApper function to  un-block element(finish loading)
        unblockUI: function(target) {
            if (target) {
                $(target).unblock({
                    onUnblock: function() {
                        $(target).css('position', '');
                        $(target).css('zoom', '');
                    }
                });
            } else {
                $.unblockUI();
            }
        },

        startPageLoading: function(options) {
            if (options && options.animate) {
                $('.page-spinner-bar').remove();
                $('body').append('<div class="page-spinner-bar"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div>');
            } else {
                $('.page-loading').remove();
                $('body').append('<div class="page-loading"><img src="' + this.getGlobalImgPath() + 'loading-spinner-grey.gif"/>&nbsp;&nbsp;<span>' + (options && options.message ? options.message : 'Loading...') + '</span></div>');
            }
        },

        stopPageLoading: function() {
            $('.page-loading, .page-spinner-bar').remove();
        },

        alert: function(options) {

            options = $.extend(true, {
                container: "", // alerts parent container(by default placed after the page breadcrumbs)
                place: "append", // "append" or "prepend" in container
                type: 'success', // alert's type
                message: "", // alert's message
                close: true, // make alert closable
                reset: true, // close all previouse alerts first
                focus: true, // auto scroll to the alert after shown
                closeInSeconds: 0, // auto close after defined seconds
                icon: "" // put icon before the message
            }, options);

            var id = App.getUniqueID("App_alert");

            var html = '<div id="' + id + '" class="custom-alerts alert alert-' + options.type + ' fade in">' + (options.close ? '<button type="button" class="close" data-dismiss="alert" aria-hidden="true"></button>' : '') + (options.icon !== "" ? '<i class="fa-lg fa fa-' + options.icon + '"></i>  ' : '') + options.message + '</div>';

            if (options.reset) {
                $('.custom-alerts').remove();
            }

            if (!options.container) {
                if ($('.page-fixed-main-content').size() === 1) {
                    $('.page-fixed-main-content').prepend(html);
                } else if (($('body').hasClass("page-container-bg-solid") || $('body').hasClass("page-content-white")) && $('.page-head').size() === 0) {
                    $('.page-title').after(html);
                } else {
                    if ($('.page-bar').size() > 0) {
                        $('.page-bar').after(html);
                    } else {
                        $('.page-breadcrumb, .breadcrumbs').after(html);
                    }
                }
            } else {
                if (options.place == "append") {
                    $(options.container).append(html);
                } else {
                    $(options.container).prepend(html);
                }
            }

            if (options.focus) {
                App.scrollTo($('#' + id));
            }

            if (options.closeInSeconds > 0) {
                setTimeout(function() {
                    $('#' + id).remove();
                }, options.closeInSeconds * 1000);
            }

            return id;
        },

        //public function to initialize the fancybox plugin
        initFancybox: function() {
            handleFancybox();
        },

        //public helper function to get actual input value(used in IE9 and IE8 due to placeholder attribute not supported)
        getActualVal: function(el) {
            el = $(el);
            if (el.val() === el.attr("placeholder")) {
                return "";
            }
            return el.val();
        },

        //public function to get a paremeter by name from URL
        getURLParameter: function(paramName) {
            var searchString = window.location.search.substring(1),
                i, val, params = searchString.split("&");

            for (i = 0; i < params.length; i++) {
                val = params[i].split("=");
                if (val[0] == paramName) {
                    return unescape(val[1]);
                }
            }
            return null;
        },

        // check for device touch support
        isTouchDevice: function() {
            try {
                document.createEvent("TouchEvent");
                return true;
            } catch (e) {
                return false;
            }
        },

        // To get the correct viewport width based on  http://andylangton.co.uk/articles/javascript/get-viewport-size-javascript/
        getViewPort: function() {
            var e = window,
                a = 'inner';
            if (!('innerWidth' in window)) {
                a = 'client';
                e = document.documentElement || document.body;
            }

            return {
                width: e[a + 'Width'],
                height: e[a + 'Height']
            };
        },

        getUniqueID: function(prefix) {
            return 'prefix_' + Math.floor(Math.random() * (new Date()).getTime());
        },

        // check IE8 mode
        isIE8: function() {
            return isIE8;
        },

        // check IE9 mode
        isIE9: function() {
            return isIE9;
        },

        //check RTL mode
        isRTL: function() {
            return isRTL;
        },

        // check IE8 mode
        isAngularJsApp: function() {
            return (typeof angular == 'undefined') ? false : true;
        },

        getAssetsPath: function() {
            return assetsPath;
        },

        setAssetsPath: function(path) {
            assetsPath = path;
        },

        setGlobalImgPath: function(path) {
            globalImgPath = path;
        },

        getGlobalImgPath: function() {
            return assetsPath + globalImgPath;
        },

        setGlobalPluginsPath: function(path) {
            globalPluginsPath = path;
        },

        getGlobalPluginsPath: function() {
            return assetsPath + globalPluginsPath;
        },

        getGlobalCssPath: function() {
            return assetsPath + globalCssPath;
        },

        // get layout color code by color name
        getBrandColor: function(name) {
            if (brandColors[name]) {
                return brandColors[name];
            } else {
                return '';
            }
        },

        getResponsiveBreakpoint: function(size) {
            // bootstrap responsive breakpoints
            var sizes = {
                'xs' : 480,     // extra small
                'sm' : 768,     // small
                'md' : 992,     // medium
                'lg' : 1200     // large
            };

            return sizes[size] ? sizes[size] : 0;
        },

        guid: function() {
            function s4() {
                return Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1);
            }
            return s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4();
        },

        EGAIS: {
            SETTINGS: null, // EGAIS SETTINGS
            CONNECT: false, // EGAIS CONNECT
            UTM_VER: 3, // EGAIS CONNECT
            findBadUTMInfo: function (html) {
                if ( !html || !html.length ) { return true; }
                html = html.split('<h4>Сертификаты</h4>')[0];
                var allAlerts = html.split('glyphicon-remove').length - 1;
                var licAlert = html.split('Лицензия на вид деятельности не действует').length - 1;
                return (allAlerts - licAlert) > 0;
            },
            testConnection: function (successCallback, failureCallback) {
                $.ajax({
                    url: App.EGAIS.SETTINGS.url+'/home',
                    // url: App.EGAIS.SETTINGS.url+'/api/rsa/orginfo',
                    success: function(data) {
                        App.EGAIS.UTM_VER = 4;
                        if (!App.EGAIS.findBadUTMInfo(data)) {
                            App.EGAIS.CONNECT = true;
                            typeof(successCallback) == 'function' && successCallback();
                            return;
                        }
                        typeof(failureCallback) == 'function' && failureCallback();
                    },
                    error: function () {
                        App.EGAIS.request('GET', App.EGAIS.SETTINGS.url, null, function(r) {
                            if (!App.EGAIS.findBadUTMInfo(r)) {
                                App.EGAIS.CONNECT = true;
                                typeof(successCallback) == 'function' && successCallback();
                                return;
                            }
                            typeof(failureCallback) == 'function' && failureCallback();
                        });
                    },
                    timeout: 10000
                });

            },
            initConnection: function (successCallback, failureCallback) {
                $.post('/egais/getsettings', {}, function (data) {
                    if(data.status == 'ok') {
                        App.EGAIS.SETTINGS = { url: 'http://'+data.ip_address+':'+data.port, FSRAR_ID: data.FSRAR_ID };
                        App.EGAIS.testConnection(successCallback, failureCallback);
                    } else if(data.status != 'ok' && location.pathname == '/egais/settings') {
                        App.EGAIS.SETTINGS = { url: 'http://localhost:8080', FSRAR_ID: null };
                        App.EGAIS.testConnection(successCallback, failureCallback);
                    } else {
                        typeof(failureCallback) == 'function' && failureCallback();
                    }
                }, 'json');
            },
            getFSRAR_ID: function (callback) {
                App.EGAIS.requestUTM('GET', App.EGAIS.SETTINGS.url + '/api/rsa/orginfo', null, r => {
                    callback && callback(r.cn || null);
                }, 5000);
            },
            requestUTM: function (method, url, data, callback, timeout) {
                const prepareHeaders = h => {
                    if (!h || !h.length) { return null; }
                    let r = {};
                    h.map(h_ => { r[h_.name] = h_.value; });
                    return r;
                };
                let dfd = $.Deferred();
                data = data ? data : {body: null, headers: null};
                const headers = prepareHeaders(data.headers);
                data = data.body;
                timeout = timeout ? timeout : 5000;
                $.ajax({
                    url,
                    data,
                    method,
                    timeout,
                    headers,
                    success: function(data) {
                        callback && callback(data);
                        dfd.resolve(data.result);
                    },
                    error: function (e) {
                        /* Исправление косяка в ТТН */
                        if (e.readyState == 4 && e.responseText.length > 0 && e.status == 200) {
                            const result = e.responseText.replace(/\-[\n\r ]+instance/, '-instance');
                            callback && callback(result);
                            dfd.resolve(result);
                        } else {
                            dfd.reject(e);
                        }
                    }
                });
                return dfd.promise();
            },
            request: function (method, url, data, callback, timeout) {
                if (App.EGAIS.UTM_VER == 4) {
                    return App.EGAIS.requestUTM(method, url, data, callback, timeout);
                }
                var dfd = $.Deferred();
                var uuid = App.guid();
                var cbf = function(event) {
                    if (event.source != window || !event.data) {
                        dfd.reject("error");
                        return;
                    }
                    if (event.data.type && event.data.type == "xdomainResponse" && event.data.uuid == uuid) {
                        callback && callback(event.data.result);
                        dfd.resolve(event.data.result);
                        window.removeEventListener("message", cbf, false);
                    }
                };
                window.addEventListener("message", cbf, false);
                data = data ? data : {body: null, headers: null};
                timeout = timeout ? timeout : 5000;
                var message = JSON.parse(JSON.stringify({ type: "xdomainRequest", method: method, url: url, data: data, uuid: uuid, timeout: timeout}));
                window.postMessage(message, "*");
                return dfd.promise();
            },
            getForm: function (filename, filecontent) {
                var boundary = '---------------------------' + (new Date()).getTime();
                var bodyParts = [
                    '--' +boundary,
                    'Content-Disposition: form-data; name="xml_file"; filename="' + filename + '"',
                    'Content-Type: application/xml',
                    '',
                    filecontent,
                    '--'+ boundary + '--'
                ];
                var body = bodyParts.join('\r\n');
                var headers = [{'name': 'Content-Type', 'value': 'multipart/form-data; boundary='+boundary}]
                return {body: body, headers: headers};
            },
            sendForm: function (url, form, callback) {
                App.EGAIS.request('POST', url, form, function (r) {
                    callback && callback(r);
                }, 20000);
            },
            sendXml: function (name, text, url, callback) {
                var dfd = $.Deferred();
                var form = App.EGAIS.getForm(name, text);
                App.EGAIS.sendForm(url, form, function (r) {
                    dfd.resolve(r);
                    callback && callback(r);
                });
                return dfd.promise();
            },
            getNATTN: function (url, callback) {
                let dfd = $.Deferred();
                App.EGAIS.request('GET', url+"/opt/out/ReplyNATTN", null, r => {
                    let json = $.xml2json(r);
                    if (json['#document']) { json = json['#document']; }
                    let deferreds = [];
                    let infos = {count: 0};
                    if (json.A && json.A.url && (json.A.url.length || json.A.url._)) {
                        if (json.A.url._) {
                            json.A.url = [json.A.url];
                        }
                        json.A.url.map(function (v) {
                            deferreds.push(App.EGAIS.request('GET', v._, null, xml => {
                                let json = $.xml2json(xml);
                                if (json['#document']) { json = json['#document']; }
                                const ReplyDate = json["ns:Documents"]["ns:Document"]["ns:ReplyNoAnswerTTN"]["ttn:ReplyDate"];
                                infos[ReplyDate] = {url: v._, xml: json};
                                infos.count++;
                            }));
                        });
                    }
                    $.when.apply($, deferreds).then(function() {
                        callback && callback(infos);
                        dfd.resolve();
                    });
                });
                return dfd.promise();
            },
            getWayBill: function (url, ver, callback) {
                App.EGAIS.request('GET', url+"/opt/out/WayBill_v3", null, function(xmlV3) {
                    App.EGAIS.request('GET', url+"/opt/out/WayBill_v4", null, function(xmlV4) {
                        var jsonV4 = $.xml2json(xmlV4);
                        if (jsonV4['#document']) { jsonV4 = jsonV4['#document']; }
                        var jsonV3 = $.xml2json(xmlV3);
                        if (jsonV3['#document']) { jsonV3 = jsonV3['#document']; }
                        var deferreds = [];
                        var ttns = {count: 0};
                        if (jsonV4.A && jsonV4.A.url && (jsonV4.A.url.length || jsonV4.A.url._)) {
                            if (jsonV4.A.url._) { jsonV4.A.url = [jsonV4.A.url]; }
                            jsonV4.A.url.map(function (v) {
                                deferreds.push(App.EGAIS.request('GET', v._, null, function (ttn) {
                                    console.log(ttn);
                                    ttns[v._] = $.xml2json(ttn);
                                    if (ttns[v._]['#document']) { ttns[v._] = ttns[v._]['#document']; }
                                    ttns.count++;
                                }));
                            });
                        }
                        if (jsonV3.A && jsonV3.A.url && (jsonV3.A.url.length || jsonV3.A.url._)) {
                            if (jsonV3.A.url._) { jsonV3.A.url = [jsonV3.A.url]; }
                            jsonV3.A.url.map(function (v) {
                                deferreds.push(App.EGAIS.request('GET', v._, null, function (ttn) {
                                    ttns[v._] = $.xml2json(ttn);
                                    if (ttns[v._]['#document']) { ttns[v._] = ttns[v._]['#document']; }
                                    ttns.count++;
                                }));
                            });
                        }
                        $.when.apply($, deferreds).then(function() {
                            console.log(ttns);
                            callback && callback(ttns);
                        });
                    });
                });
            },
            getF2Reg: function (url, type, callback) {
                var dfd = $.Deferred();
                App.EGAIS.request('GET', url+"/opt/out/"+type, null, function(r) {
                    var json = $.xml2json(r);
                    if (json['#document']) { json = json['#document']; }
                    var deferreds = [];
                    var infos = {count: 0};
                    if (json.A && json.A.url && (json.A.url.length || json.A.url._)) {
                        if (json.A.url._) {
                            json.A.url = [json.A.url];
                        }
                        json.A.url.map(function (v) {
                            deferreds.push(App.EGAIS.request('GET', v._, null, function (xml) {
                                var json = $.xml2json(xml);
                                if (json['#document']) { json = json['#document']; }
                                if (type == 'FORM2REGINFO') {
                                    var Identity = json["ns:Documents"]["ns:Document"]['ns:TTNInformF2Reg']["wbr:Header"]["wbr:Identity"];
                                    var WBRegId = json["ns:Documents"]["ns:Document"]['ns:TTNInformF2Reg']["wbr:Header"]["wbr:WBRegId"];
                                    infos[Identity] = {url: v._, WBRegId: WBRegId, xml: json};
                                } else {
                                    var WBRegId = json["ns:Documents"]["ns:Document"]['ns:TTNHistoryF2Reg']["wbr:Header"]["wbr:WBRegId"];
                                    infos[WBRegId] = {url: v._, xml: json};
                                }
                                infos.count++;
                            }));
                        });
                    }
                    $.when.apply($, deferreds).then(function() {
                        callback && callback(infos);
                        dfd.resolve();
                    });
                });
                return dfd.promise();
            },
            getWBTicket: function (url, callback) {
                var dfd = $.Deferred();
                App.EGAIS.request('GET', url+"/opt/out/WayBillTicket", null, function(r) {
                    var json = $.xml2json(r);
                    if (json['#document']) { json = json['#document']; }
                    var deferreds = [];
                    var tickets = {count: 0};
                    if (json.A && json.A.url && (json.A.url.length || json.A.url._)) {
                        if (json.A.url._) {
                            json.A.url = [json.A.url];
                        }
                        json.A.url.map(function (v) {
                            deferreds.push(App.EGAIS.request('GET', v._, null, function (xml) {
                                var json = $.xml2json(xml);
                                if (json['#document']) { json = json['#document']; }
                                tickets[json["ns:Documents"]["ns:Document"]["ns:ConfirmTicket"]["wt:Header"]["wt:WBRegId"]] = {url: v._, xml: json};
                                tickets.count++;
                            }));
                        });
                    }
                    $.when.apply($, deferreds).then(function() {
                        callback && callback(tickets);
                        dfd.resolve();
                    });
                });
                return dfd.promise();
            },
            getTickets: function (callback) {
                var dfd = $.Deferred();
                App.EGAIS.request('GET', App.EGAIS.SETTINGS.url+"/opt/out/Ticket", null, function(r) {
                    var json = $.xml2json(r);
                    if (json['#document']) { json = json['#document']; }
                    var deferreds = [];
                    var tickets = {count: 0};
                    if (json.A && json.A.url && (json.A.url.length || json.A.url._)) {
                        if (json.A.url._) {
                            json.A.url = [json.A.url];
                        }
                        json.A.url.map(function (v) {
                            deferreds.push(App.EGAIS.request('GET', v._, null, function (xml) {
                                var json = $.xml2json(xml);
                                if (json['#document']) { json = json['#document']; }
                                tickets[v._] = {url: v._, transportId: json["ns:Documents"]["ns:Document"]["ns:Ticket"]["tc:TransportId"], xml: json};
                                tickets.count++;
                            }));
                        });
                    }
                    $.when.apply($, deferreds).then(function() {
                        callback && callback(tickets);
                        dfd.resolve();
                    });
                });
                return dfd.promise();
            },
            getWayBillActs: function (callback) {
                var dfd = $.Deferred();
                App.EGAIS.request('GET', App.EGAIS.SETTINGS.url+"/opt/out/WayBillAct_v3", null, function(r) {
                    var json = $.xml2json(r);
                    if (json['#document']) { json = json['#document']; }
                    var deferreds = [];
                    var wayBillActs = {count: 0};
                    if (json.A && json.A.url && (json.A.url.length || json.A.url._)) {
                        if (json.A.url._) {
                            json.A.url = [json.A.url];
                        }
                        json.A.url.map(function (v) {
                            deferreds.push(App.EGAIS.request('GET', v._, null, function (xml) {
                                var json = $.xml2json(xml);
                                if (json['#document']) { json = json['#document']; }
                                wayBillActs[v._] = {url: v._, xml: json};
                                wayBillActs.count++;
                            }));
                        });
                    }
                    $.when.apply($, deferreds).then(function() {
                        callback && callback(wayBillActs);
                        dfd.resolve();
                    });
                });
                return dfd.promise();
            },
            getTTN: function (url, ver, callback) {
                App.EGAIS.getWayBill(url, ver, function (ttns) {
                    let tickets = false, nattn = false;
                    if (ttns.count) {
                        let f2rinfo = {}, f2rhist = {};
                        $.when.apply($, [
                            App.EGAIS.getF2Reg(url, 'FORM2REGINFO', r => f2rinfo = r ),
                            App.EGAIS.getF2Reg(url, 'TTNHISTORYF2REG', r => f2rhist = r ),
                            App.EGAIS.getWBTicket(url, r => tickets = r ),
                            App.EGAIS.getNATTN(url, r => nattn = r)
                        ]).then(function() {
                            callback && callback(ttns, f2rinfo, f2rhist, tickets, nattn);
                        });
                    } else {
                        $.when.apply($, [
                            App.EGAIS.getWBTicket(url, r => tickets = r ),
                            App.EGAIS.getNATTN(url, r => nattn = r)
                        ]).then(function() {
                            callback && callback(false, false, false, tickets, nattn);
                        });
                    }
                })
            },
            getRests: function (type, callback, pn) {
                pn = pn ? pn : {offset: false, limit: false};
                pn = (pn.offset === false?'':'?offset='+pn.offset) + (pn.limit === false?'':'&limit='+pn.limit);
                var dfd = $.Deferred();
                App.EGAIS.request('GET', App.EGAIS.SETTINGS.url+"/opt/out/"+type+pn, null, function(r) {
                    var json = $.xml2json(r);
                    if (json['#document']) { json = json['#document']; }
                    var deferreds = [];
                    var remains = {count: 0};
                    if (json.A && json.A.url && (json.A.url.length || json.A.url._)) {
                        if (json.A.url._) {
                            json.A.url = [json.A.url];
                        }
                        json.A.url.map(function (v) {
                            deferreds.push(App.EGAIS.request('GET', v._, null, function (xml) {
                                remains[v._] = $.xml2json(xml);
                                if (remains[v._]['#document']) { remains[v._] = remains[v._]['#document']; }
                                remains.count++;
                            }));
                        });
                    }
                    $.when.apply($, deferreds).then(function() {
                        callback && callback(remains);
                        dfd.resolve();
                    });
                });
                return dfd.promise();
            },
            getReplyRests: function (callback) {
                var ReplyRests = {}, ReplyRestsShop = {};
                $.when.apply($, [
                    App.EGAIS.getRests('ReplyRests_v2', function (r) { ReplyRests = r; }),
                    App.EGAIS.getRests('ReplyRestsShop_v2', function (r) { ReplyRestsShop = r; }),
                ]).then(function() {
                    callback && callback(ReplyRests, ReplyRestsShop);
                });
            },
            getReplyAP: function (callback) {
                var results = {};
                $.when.apply($, [
                    App.EGAIS.getRests('ReplyAP', function (r) { results = r; }, {offset: 0, limit: 200}),
                ]).then(function() {
                    callback && callback(results);
                });
            },
            renewTickets: function() {
                App.blockUI({boxed: true, message: 'Получение решений...'});
                App.EGAIS.getTickets(function(tickets) {
                    $.post('/egais/newtickets', {tickets: tickets}, function (data) {
                        if (data.status == 'ok') {
                            var deferreds = [];
                            $.each(tickets, function(k, v) {
                                if (k != 'count') {
                                    deferreds.push(App.EGAIS.request('DELETE', v.url));
                                }
                            });
                            $.when.apply($, deferreds).then(function() {
                                $('[data-act="refresh"]').trigger('click');
                                App.unblockUI();
                            });
                        } else {
                            App.unblockUI();
                        }
                    }, 'json');
                });
            },
            __sanitizeLetters: {'Q':'Й','W':'Ц','E':'У','R':'К','T':'Е','Y':'Н','U':'Г','I':'Ш','O':'Щ','P':'З','A':'Ф','S':'Ы','D':'В','F':'А','G':'П','H':'Р','J':'О','K':'Л','L':'Д','Z':'Я','X':'Ч','C':'С','V':'М','B':'И','N':'Т','M':'Ь',},
            sanitizeMarkInput: $input => {
                let val = $input.val().toUpperCase();
                $.each(App.EGAIS.__sanitizeLetters, (en, ru) => {
                    val = val.replace(new RegExp(ru, 'ig'), en);
                });
                $input.val(val)
            }
        },
        AJAX: {
            send: function (url, data, callback) {
                let dfd = $.Deferred();
                _ajaxRequests.push($.post(url, data, function (response) {
                    let data = null;
                    if (response.status == 'success') {
                        data = response.data;
                    } else {
                        swal('Ошибка', response.error, 'error');
                    }
                    if (typeof(callback) === 'function') {
                        callback(data);
                    }
                    dfd.resolve(data);
                }, 'json'));
                return dfd.promise();
            }
        },
        getFormObject: function (form) {
            var object = $(form).serializeArray().reduce(function(obj, item) {
                var name = item.name.replace("[]", "");
                if ( typeof obj[name] !== "undefined" ) {
                    if ( !Array.isArray(obj[name]) ) {
                        obj[name] = [ obj[name], item.value ];
                    } else {
                        obj[name].push(item.value);
                    }
                } else {
                    obj[name] = item.value;
                }
                return obj;
            }, {});
            return object;
        },
        bsModal: (title, content, successBtnText, callback) => {
            const modalId = 'dynamic--modal';
            const scsBtnId = 'dynamic--modal--scs--btn';
            const modalHtml = `<div class="modal fade bs-modal-md modal-scroll" id="${modalId}">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                            <h4 class="modal-title">${title}</h4>
                        </div>
                        <div class="modal-body">${content}</div>
                        <div class="modal-footer form-actions">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-success" id="${scsBtnId}">${successBtnText}</button>
                        </div>
                    </div>
                </div>
            </div>`;
            $('body').append(modalHtml);
            $('#'+modalId).modal().on('hidden.bs.modal', _=> {
                $('#'+modalId).remove();
                $('body').removeClass('modal-open-noscroll');
            });
            $('#'+scsBtnId).off('click').on('click', _=> {
                let data = {};
                $('input, select, textarea', $('#'+modalId)).each(function () {
                    const $this = $(this);
                    let val = $this.val();
                    if ($this.is('input') && $this.attr('type') == 'checkbox') {
                        val = $this.prop('checked');
                    }
                    data[$this.attr('name')] = val;
                });
                callback(data);
                $('#'+modalId).modal('hide');
            });
        },
        toQueryString: object => Object.keys(object).map(key => `${key}=${object[key].toString()}`).join('&'),
        exportParams: function (e) {
            e.stopPropagation();
            e.preventDefault();
            const $this = $(this);
            let content = `<div class="form-horizontal">
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-6 col-md-6 control-label">Количество</label>
                        <div class="col-xs-12 col-sm-6 col-md-3">
                            <div class="input-icon">
                                <i class="fa fa-balance-scale font-dark"></i>
                                <input class="form-control" type="number" value="1" name="count"  min="1" max="10000000000" step="0.01" maxlength="11">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-6 col-md-6 control-label" for="expand-sg">Раскрывать полуфабрикаты</label>
                        <div class="col-xs-12 col-sm-6 col-md-6 control-label">
                            <div class="md-checkbox">
                                <input type="checkbox" id="expand-sg" name="expand_sg" class="md-check">
                                <label for="expand-sg">
                                    <span class="inc"></span>
                                    <span class="check"></span>
                                    <span class="box"></span>
                                </label>
                            </div>
                        </div>
                    </div> 
                </div> 
            </div>`;
            const href = $this.attr('href');
            App.bsModal('Параметры экспорта', content, 'Скачать', data => {
                data.count = parseFloat(data.count) || 1;
                data.expand_sg = !!data.expand_sg;
                location.href = href + (href.includes('?') ? '&' : '?') + App.toQueryString(data);
            });
            return false;
        }
    };

}();

jQuery(document).ready(function() {
    $('.close-pay-alert-message').on('click', function () {
        $('body').removeClass('pay-alert');
    });
    $('.getBillAuthLink').on('click', function () {
        App.AJAX.send('/ajax/getbillauthlink', {}, function (r) {
            var win = window.open(r.link, '_blank');
            win.focus();
        })
    });
});
