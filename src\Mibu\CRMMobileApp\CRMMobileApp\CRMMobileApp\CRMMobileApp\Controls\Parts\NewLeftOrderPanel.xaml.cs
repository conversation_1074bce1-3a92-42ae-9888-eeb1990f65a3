﻿using CRM.Models.Enums.General;
using CRM.Models.Network.ReferenceBooks;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages;
using CRMMobileApp.Views.Pages.Delivery;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using TermoPrintingLib;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NewLeftOrderPanel : ContentView
    {
        public NewLeftOrderPanel()
        {
            InitializeComponent();
            //OrdersHelper.OnOrderItemsCountChanged += OrdersHelper_OnOrderItemsCountChanged;

            CurrentOrder = OrdersHelper.CurrentOrder;
            RenderItems();
            SetOrderCommentFrameVisibility();
            SetSendToWorkBtnVisibility();
            SetPriceText();

            splitOrderStackLayout.IsVisible = ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe;
        }


        private void OrdersHelper_OnOrderItemsCountChanged(object sender, Order e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                CurrentOrder = OrdersHelper.CurrentOrder;
                if (OrdersHelper.CurrentOrder is null)
                {
                    orderItemsCollectionView.ItemsSource = null;

                    orderTotalLabel.Text = $"0.00₽";
                    orderTotalWithDiscountLabel.Text = $"0.00₽";

                    orderCreateCloseText.Text = "Открыть заказ";
                    return;
                }

                SetPriceText();
            });

            RenderItems();
        }



        #region Для закрытого заказа

        /// <summary>
        /// Метод для показа закрытого заказа
        /// </summary>
        /// <param name="order"></param>
        public void SetItems(Order order)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (order is null) return;

                CurrentOrder = order;


                orderIdSpan.Text = order.OrderNumber.ToString();
                returnOrderBtn.IsVisible = !order.WasReturned;
                activeOrderFooter.IsVisible = false;
                activeOrderBottomLayout.IsVisible = false;

                orderItemsCollectionView.ItemsSource = CurrentOrder.Items;
            });
        }
        public void SetClosedOrderMode()
        {
            orderIdFrame.InputTransparent = true;

            orderIdSpan.Text = "XX";
            createCancelOrderFrame.IsVisible = false;
            activeOrderFooter.IsVisible = false;
            activeOrderBottomLayout.IsVisible = false;

            orderCommentFrame.IsVisible = false;
            sendItemsToWork.IsVisible = false;

            closedOrderFooter.IsVisible = true;

        }

        #region Печать чека закрытого заказа
        private ICommand printClosedOrderCheque;
        public ICommand PrintClosedOrderCheque
        {
            get => printClosedOrderCheque ??= new RelayCommand(async obj =>
            {
                if(ApplicationState.StorePrinters.Count == 0)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Печать невозможна, нет ни одного принтера для печати чеков", "Ошибка"));
                }
                else
                {     

                    if (ApplicationState.StorePrinters.Count == 1)
                    {
                        var printer = ApplicationState.StorePrinters[0];
                        PrintClosedOrder(printer);
                    }
                    else
                    {
                        var popup = new AvailablePrintersPopup(CRM.Models.Enums.Equipment.PrinterType.OrdersForWorkshops);
                        popup.ItemSelected += (o, e) =>
                        {
                            PrintClosedOrder(e);
                        };
                        await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                    }
                }
            });
        }

        private async void PrintClosedOrder(Printer printer)
        {
            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);

            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintOrderCheque(CurrentOrder);
            if (!result)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Ошибка печати", "Ошибка"));
            }
        }
        #endregion


        private ICommand returnClosedOrder;
        public ICommand ReturnClosedOrder
        {
            get => returnClosedOrder ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.OrderReturning))
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new ReturnOrder(CurrentOrder));
                }
            });
        }

        #endregion


        #region Рендер и т.д.
        private void RenderItems()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                SelectedOrderItem = null;
                

                if (CurrentOrder is null) return;

                orderItemsCollectionView.ItemsSource = CurrentOrder.Items;
            });
        }

  

        public void SetPriceText()
        {
            if (CurrentOrder is null) return;

            orderTotalLabel.Text = $"Итого: {Math.Round(CurrentOrder.Sum, 2)}₽";

            if (CurrentOrder.Discount != null)
                orderDiscountLabel.Text = $"Скидка: {CurrentOrder.Discount.Percent}%";
            else
                orderDiscountLabel.Text = "Скидка: 0%";

            orderTotalWithDiscountLabel.Text = $"{Math.Round(CurrentOrder.SumWithDiscount, 2)}₽";
        }
        private async void View_AmountChanged(object sender, OrderItem e)
        {
            SetPriceText();
          //  OrdersHelper.SaveOrder();
        }


        public void SetOrderCommentFrameVisibility()
        {
            orderCommentFrame.IsVisible = !string.IsNullOrEmpty(CurrentOrder?.Comment);
        }
        public void SetSendToWorkBtnVisibility()
        {
            if(CurrentOrder is null || ApplicationState.CurrentStore.StoreMode == StoreMode.Point)
            {
                sendItemsToWork.IsVisible = false;
            }
            else
            {
                sendItemsToWork.IsVisible = CurrentOrder.Items.Any(o => !o.IsInWork);
            }
        }

        #endregion

        private Order currentOrder;
        public Order CurrentOrder
        {
            get => currentOrder;
            set 
            { 
                if(value != null)
                {
                    if (value.ClosedAtLocal.HasValue)
                    {
                        orderIdSpan.Text = "XX";
                        orderCreateCloseText.Text = "Открыть заказ";
                    }
                    else
                    {
                        orderCreateCloseText.Text = "Отменить заказ";
                        orderIdSpan.Text = value.OrderNumber.ToString();
                    }       
                }
                else
                {
                    orderIdSpan.Text = "XX";
                    orderCreateCloseText.Text = "Открыть заказ";
                }
                SetOrderCommentFrameVisibility();
                SetSendToWorkBtnVisibility();

                currentOrder = value;
                OnPropertyChanged(nameof(CurrentOrder));
            }
        }

        #region Верхние кнопки

        private ICommand showActiveOrders;
        public ICommand ShowActiveOrders
        {
            get => showActiveOrders ??= new RelayCommand(async obj =>
            {
                await OrdersHelper.GetActiveOrders();
                if(OrdersHelper.ActiveOrders.Count == 0)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("На данный момент нет открытых заказов","Уведомление"));
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new ActiveOrdersPopup());
                }  
            });
        }


        private ICommand openOrCloseOrder;
        public ICommand OpenOrCloseOrder
        {
            get => openOrCloseOrder ??= new RelayCommand(async obj =>
            {
                if (CurrentOrder != null)
                {
                    if (CurrentOrder.ClosedAtLocal.HasValue)
                    {
                        await OrdersHelper.OpenOrder();
                        CurrentOrder = OrdersHelper.CurrentOrder;
                    }
                    else
                    {
                        await NavigationHelper.GoBackIfCafe(false);

                        await OrdersHelper.CancelOrder();
                        CurrentOrder = OrdersHelper.CurrentOrder;
                    }
                }
                else
                {
                    await OrdersHelper.OpenOrder();
                    CurrentOrder = OrdersHelper.CurrentOrder;
                }
            });
        }

        #endregion

        #region Меню внизу
        private ICommand openMenu;
        public ICommand OpenMenu
        {
            get => openMenu ??= new RelayCommand(async obj =>
            {
                menuFrame.IsVisible = true;
            });
        }
        private ICommand closeMenu;
        public ICommand CloseMenu
        {
            get => closeMenu ??= new RelayCommand(async obj =>
            {
                menuFrame.IsVisible = false;
            });
        }


        private ICommand splitOrder;
        public ICommand SplitOrder
        {
            get => splitOrder ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OrderDivisionPopup());
            });
        }


        private ICommand setClient;
        public ICommand SetClient
        {
            get => setClient ??= new RelayCommand(async obj =>
            {
                if(OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                await App.Current.MainPage.Navigation.PushPopupAsync(new ClientsPopup(CurrentOrder));
                menuFrame.IsVisible = false;
            });
        }

        private ICommand setDiscount;
        public ICommand SetDiscount
        {
            get => setDiscount ??= new RelayCommand(async obj =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                await App.Current.MainPage.Navigation.PushPopupAsync(new DiscountPopup(CurrentOrder));
                menuFrame.IsVisible = false;
            });
        }

        private ICommand setOrderComment;
        public ICommand SetOrderComment
        {
            get => setOrderComment ??= new RelayCommand(async obj =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                await App.Current.MainPage.Navigation.PushPopupAsync(new OrderCommentPopup(CurrentOrder));
                menuFrame.IsVisible = false;
            });
        }
        private ICommand clearOrderItems;
        public ICommand ClearOrderItems
        {
            get => clearOrderItems ??= new RelayCommand(async obj =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                OrdersHelper.ClearOrderItems();
                menuFrame.IsVisible = false;
            });
        }



        #endregion

        #region Работа с выбранной позицией заказа

        private OrderItem selectedOrderItem = null;
        private OrderItem SelectedOrderItem
        {
            get => selectedOrderItem;
            set
            {
                editAmountStackLayout.IsVisible = value != null;
                editModifiersStackLayout.IsVisible = value != null;
                orderItemCommentStackLayout.IsVisible = value != null;

                selectedOrderItem = value;
                OnPropertyChanged(nameof(SelectedOrderItem));
            }
        }

        private void View_OrderItemTapped(object sender, OrderItem e)
        {
            //foreach (var item in orderItemsLayout.Children.Cast<OrderItemTemplate>())
            //{
            //    item.IsSelected = false;
            //}
            //(sender as OrderItemTemplate).IsSelected = true;
            //SelectedOrderItem = e;
        }

        private async void View_DeleteBtnTapped(object sender, OrderItem e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.CheckPositionRemoving))
            {
                if (!e.IsInWork)
                {
                    SelectedOrderItem = null;
                    OrdersHelper.DeleteOrderItem(e);

                    //var view = sender as OrderItemTemplate;
                    //orderItemsLayout.Children.Remove(view);
				}
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new CancelOrderItemPopup(e));
                }
            }
        }

        private ICommand editAmount;
        public ICommand EditAmount
        {
            get => editAmount ??= new RelayCommand(async obj =>
            {
                if (SelectedOrderItem.TechnicalCard != null)
                {
                    if (SelectedOrderItem.TechnicalCard.IsWeightProduct)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new WeightTechCardOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new TechCardOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                }
                else if (SelectedOrderItem.Product != null)
                {
                    if (SelectedOrderItem.Product.IsWeightProduct)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new WeightProductOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new ProductOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                }
            });
        }


        private ICommand editModifiers;
        public ICommand EditModifiers
        {
            get => editModifiers ??= new RelayCommand(async obj =>
            {
                List<Modifier> availableModifiers = new List<Modifier>();
                if(SelectedOrderItem.TechnicalCard != null)
                {
                    availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForTechCard(ApplicationState.CurrentDomain,
                                                                                                                              ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                              SelectedOrderItem.TechnicalCard.Id);
                }
                else if (SelectedOrderItem.Product != null)
                {
                    availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForProduct(ApplicationState.CurrentDomain,
                                                                                                                             ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                             SelectedOrderItem.Product.Id);
                }


                if (availableModifiers.Any())
                {
                    var popup = new ModifiersPopup(SelectedOrderItem, availableModifiers, true);
                    await App.Current.MainPage.Navigation.PushPopupAsync(popup);

                    while (!popup.IsCompleted)
                        await Task.Delay(200);

                    if (!popup.IsSuccessfully)
                    {
                        return;
                    }

                    await OrdersHelper.SetOrderItemModifiers(SelectedOrderItem);
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Для данной позиции нет доступных модификаторов","Уведомление"));
                }
            });
        }


        private ICommand goToOrderItemCommentPopup;
        public ICommand GoToOrderItemCommentPopup
        {
            get => goToOrderItemCommentPopup ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OrderItemCommentPopup(SelectedOrderItem));
            });
        }

        #endregion


        private ICommand sendItemsToWorkCommand;
        public ICommand SendItemsToWorkCommand
        {
            get => sendItemsToWorkCommand ??= new RelayCommand(async obj =>
            {
                OrdersHelper.SendItemsToWork();
            });
        }


        private ICommand lockScreen;
        public ICommand LockScreen
        {
            get => lockScreen ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new MainPage());
            });
        }
        private ICommand payOrder;
        public ICommand PayOrder
        {
            get => payOrder ??= new RelayCommand(async obj =>
            {

                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нельзя оплатить заказ, не открыв его", "Ошибка"));
                }
                else if (CurrentOrder.ClosedAtLocal.HasValue)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нельзя оплатить уже закрытый заказ", "Ошибка"));
                }
                else if (await Auth.CheckAllowance(rights.CheckPayment))
                {
                    if (CurrentOrder.Items.Count == 0)
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нельзя оплатить пустой заказ", "Ошибка"));
                    else
                        await App.Current.MainPage.Navigation.PushAsync(new CreateOrder());
                }
            });
        }
    }
}