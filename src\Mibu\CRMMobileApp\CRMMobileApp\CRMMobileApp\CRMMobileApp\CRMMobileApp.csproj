﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>
    <ProduceReferenceAssemblyInOutDir>true</ProduceReferenceAssemblyInOutDir>
    <LangVersion>latest</LangVersion>
</PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="SignalR\**" />
    <EmbeddedResource Remove="SignalR\**" />
    <None Remove="SignalR\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Controls\Parts\NewLeftOrderPanel.xaml.cs" />
    <Compile Remove="Views\Pages\Orders\ProductsPage.xaml.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="Controls\Parts\NewLeftOrderPanel.xaml" />
    <EmbeddedResource Remove="Views\Pages\Orders\ProductsPage.xaml" />
  </ItemGroup>

   <ItemGroup>
    <PackageReference Include="Alfateam.Plugins.Popup" Version="2.1.2" />
    <PackageReference Include="DevExpress.XamarinForms.Core" Version="22.1.7" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.21" />
    <PackageReference Include="Plugin.FirebasePushNotification" Version="3.4.35" />
    <PackageReference Include="RestSharp" Version="106.12.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DevExpress.XamarinForms.Charts" Version="22.1.7" />
    <PackageReference Include="DevExpress.XamarinForms.CollectionView" Version="22.1.7" />
    <PackageReference Include="DevExpress.XamarinForms.Editors" Version="22.1.7" />
    <PackageReference Include="DevExpress.XamarinForms.Grid" Version="22.1.7" />
    <PackageReference Include="Sharpnado.CollectionView" Version="2.1.0" />
    <PackageReference Include="Sharpnado.MaterialFrame" Version="1.3.0" />
    <PackageReference Include="Syncfusion.Xamarin.SfProgressBar" Version="22.2.8" />
    <PackageReference Include="Xam.Plugins.Forms.ProgressRing" Version="0.1.2" />
    <PackageReference Include="Xamarin.CommunityToolkit" Version="2.0.1" />
    <PackageReference Include="Xamarin.CommunityToolkit.Markup" Version="2.0.1" />
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2662" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.1" />
    <PackageReference Include="Xamarin.Forms.DataGrid" Version="4.8.0" />
    <PackageReference Include="Xam.DataGrid" Version="1.0.2" />
    <PackageReference Include="Xamarin.Forms.PancakeView" Version="2.3.0.759" />
    <PackageReference Include="System.Buffers">
		<Version>4.5.1</Version>
		<IncludeAssets>none</IncludeAssets>
	</PackageReference>
	<PackageReference Include="System.Memory">
		<Version>4.5.4</Version>
		<IncludeAssets>none</IncludeAssets>
	</PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Controls\AuthWhiteKeyboard.xaml.cs">
      <DependentUpon>AuthWhiteKeyboard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\KeyboardBlackLight.xaml.cs">
      <DependentUpon>KeyboardBlackLight.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\KeyboardWhiteLight.xaml.cs">
      <DependentUpon>KeyboardWhiteLight.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Modifiers\SomeOptionsModifierView.xaml.cs">
      <DependentUpon>SomeOptionsModifierView.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Modifiers\SomeOptionsModifierOptionItem.xaml.cs">
      <DependentUpon>SomeOptionsModifierOptionItem.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Parts\HeaderSearch.xaml.cs">
      <DependentUpon>HeaderSearch.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Tables\RoundTable.xaml.cs">
      <DependentUpon>RoundTable.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Devices\Acquiring\IngenicoAcquringCard.xaml.cs">
      <DependentUpon>IngenicoAcquringCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Devices\Acquiring\UCSCardsAcquringCard.xaml.cs">
      <DependentUpon>UCSCardsAcquringCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Devices\Acquiring\SberAcquringCard.xaml.cs">
      <DependentUpon>SberAcquringCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\EquipmentItems\AcquiringItemTemplate.xaml.cs">
      <DependentUpon>AcquiringItemTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\EquipmentItems\FiscalRegisterItemTemplate.xaml.cs">
      <SubType>Code</SubType>
      <DependentUpon>FiscalRegisterItemTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Orders\OrderDivision\OrderDivisionItemTemplate.xaml.cs">
      <DependentUpon>OrderDivisionItemTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Orders\OrderItemModifierOptionTemplate.xaml.cs">
      <DependentUpon>OrderItemModifierOptionTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Orders\OrderWithDeliveryTemplate.xaml.cs">
      <SubType>Code</SubType>
      <DependentUpon>OrderWithDeliveryTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Orders\TableOrderTemplate.xaml.cs">
      <DependentUpon>TableOrderTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Orders\OrderItemModifierTemplate.xaml.cs">
      <DependentUpon>OrderItemModifierTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Orders\OrderItemTemplate.xaml.cs">
      <DependentUpon>OrderItemTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Devices\CashMachineCard.xaml.cs">
      <DependentUpon>CashMachineCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Devices\PosTerminalCard.xaml.cs">
      <DependentUpon>PosTerminalCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\DiscountCard.xaml.cs">
      <DependentUpon>DiscountCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Reports\SalesByIngridientsReportRow.xaml.cs">
      <DependentUpon>SalesByIngridientsReportRow.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Reports\BalanceAtStockReportRow.xaml.cs">
      <DependentUpon>BalanceAtStockReportRow.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\Reports\SalesOnDutyReportRow.xaml.cs">
      <DependentUpon>SalesOnDutyReportRow.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\EquipmentItems\PrinterItemTemplate.xaml.cs">
      <DependentUpon>PrinterItemTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\TechnicalCard.xaml.cs">
      <DependentUpon>TechnicalCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="Controls\Templates\ProductCard.xaml.cs">
      <DependentUpon>ProductCard.xaml</DependentUpon>
    </Compile>
    <Compile Update="LoginPage.xaml.cs">
      <DependentUpon>LoginPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Pages\Orders\ClosedOrdersPage.xaml.cs">
      <DependentUpon>ClosedOrdersPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Pages\Orders\OrderPrepaymentPage.xaml.cs">
      <DependentUpon>OrderPrepaymentPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Pages\Orders\ProductsPage.xaml.cs">
      <DependentUpon>ProductsPage.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Acquiring\AcquringReturnCancelationPopup.xaml.cs">
      <DependentUpon>AcquringReturnCancelationPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Acquiring\AcquringWaitingPopup.xaml.cs">
      <DependentUpon>AcquringWaitingPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Acquiring\AcquringReturnSumPopup.xaml.cs">
      <DependentUpon>AcquringReturnSumPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Acquiring\AvailableAcquiringPopup.xaml.cs">
      <DependentUpon>AvailableAcquiringPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\CountSelections\TechCardOptionsPopup.xaml.cs">
      <DependentUpon>TechCardOptionsPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\CountSelections\WeightTechCardOptionsPopup.xaml.cs">
      <DependentUpon>WeightTechCardOptionsPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\CreateTransactionPopup.xaml.cs">
      <DependentUpon>CreateTransactionPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Equipment\AvailableFiscalRegistratiorsPopup.xaml.cs">
      <DependentUpon>AvailableFiscalRegistratiorsPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Equipment\AvailablePrintersPopup.xaml.cs">
      <DependentUpon>AvailablePrintersPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Halls\TableCurrentOrdersPopup.xaml.cs">
      <DependentUpon>TableCurrentOrdersPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Halls\GuestsCountPopup.xaml.cs">
      <DependentUpon>GuestsCountPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\ModifiersPopup.xaml.cs">
      <DependentUpon>ModifiersPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\DiscountPopup.xaml.cs">
      <DependentUpon>DiscountPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\DutyClosingPopup.xaml.cs">
      <DependentUpon>DutyClosingPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\DutyOpeningPopup.xaml.cs">
      <DependentUpon>DutyOpeningPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\CountSelections\WeightProductOptionsPopup.xaml.cs">
      <DependentUpon>WeightProductOptionsPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\AdminPasswordPopup.xaml.cs">
      <DependentUpon>AdminPasswordPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\NewDeliveryPopup.xaml.cs">
      <DependentUpon>NewDeliveryPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Orders\OrderItemCommentPopup.xaml.cs">
      <DependentUpon>OrderItemCommentPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Orders\OrderDivisionPopup.xaml.cs">
      <DependentUpon>OrderDivisionPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Orders\ActiveOrdersPopup.xaml.cs">
      <DependentUpon>ActiveOrdersPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Orders\CancelOrderItemPopup.xaml.cs">
      <DependentUpon>CancelOrderItemPopup.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\XCashierReportPopup.xaml.cs">
      <DependentUpon>XCashierReportPopup.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Controls\AuthKeyboard.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Modifiers\OneOptionModifierOptionItem.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Modifiers\OneOptionModifierView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\Editors\CustomCrossComboBoxEdit.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\Editors\CustomCrossDateEdit.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\Editors\CustomCrossRadioButton.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\Header.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\LeftOrderPanel.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\ListViews\CustomCrossCollectionView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\ListViews\CustomCrossHorizontalListView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\TabView\CustomTabView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\TabView\CustomTabViewItem.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\TabView\CustomTabViewItemInternalHeader.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Tables\SquareTable.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\CategoryCard.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\ClientListItem.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\EquipmentItems\FiscalRegisterItemTemplate.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\Orders\OrderItemModifierOptionTemplate.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\Orders\OrderWithDeliveryTemplate.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\Orders\ClosedOrderTemplate.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\Devices\PrinterCard.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\Other\PaymentTypeView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Templates\Tables\XReportItem.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Custom\EntryOutlined.xaml">
      <Generator>MSBuild:Compile</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="SplashPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Delivery\DeliveriesPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\HallPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Main\MainMenu.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Orders\CategoriesPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Orders\CreateOrder.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Tables\BalanceAtStock.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Tables\SalesByIngridients.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Pages\Tables\SalesOnDuty.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\AboutSoftwarePopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\ClientsPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\ErrorPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\LockedScreenPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\NewClientPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\OneButtonedPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\Orders\OrderCommentPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\Orders\OrderPaidPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\CountSelections\ProductAmountKeyboardPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\CountSelections\ProductOptionsPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\Orders\ReturnOrder.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\SendReceiptTo.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\SupportPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Popups\XReportPopup.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>
  
  <ItemGroup>
    <None Remove="Fonts\neue_regular.ttf" />
    <None Remove="Fonts\noto_sans_bold.ttf" />
    <None Remove="Fonts\noto_sans_light.ttf" />
    <None Remove="Fonts\noto_sans_medium.ttf" />
    <None Remove="Fonts\noto_sans_regular.ttf" />
    <None Remove="Fonts\TTFirsNeue-DemiBold.ttf" />
    <None Remove="Fonts\TTFirsNeue-Light.ttf" />
    <None Remove="Fonts\TTFirsNeue-Medium.ttf" />
    <None Remove="Fonts\TTFirsNeue-Regular.ttf" />
    <None Remove="NoFrills.Xamarin.Forms" />
    <None Remove="C1.Xamarin.Forms.Grid" />
    <None Remove="Resources\Fonts\neue_regular.ttf" />
    <None Remove="Resources\Fonts\noto_sans_bold.ttf" />
    <None Remove="Resources\Fonts\noto_sans_light.ttf" />
    <None Remove="Resources\Fonts\noto_sans_medium.ttf" />
    <None Remove="Resources\Fonts\noto_sans_regular.ttf" />
    <None Remove="Xam.DataGrid" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Fonts\neue_regular.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_bold.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_light.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_medium.ttf" />
    <EmbeddedResource Include="Fonts\noto_sans_regular.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-DemiBold.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-Light.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-Medium.ttf" />
    <EmbeddedResource Include="Fonts\TTFirsNeue-Regular.ttf" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\MibuGame\Gamification\CRM.Models.GamificationMobileAPI\CRM.Models.GamificationMobileAPI.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuGame\Gamification\CRM.Models.Gamification\CRM.Models.Gamification.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuGame\Gamification\CRMGamificationAPIWrapper\CRMGamificationAPIWrapper.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuGame\Gamification\Gamification.Landing.Models\Gamification.Landing.Models.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuGame\SharedGamificationBarista\SharedGamificationBarista\SharedGamificationBarista\SharedGamificationBarista.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuShared\CRM.Models.Core\CRM.Models.Core.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuShared\CRM.Models.Reports.Mobile\CRM.Models.Reports.Mobile.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuShared\CRM.Models\CRM.Models.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuShared\CRMMoblieApiWrapper\CRMMoblieApiWrapper.csproj" />
    <ProjectReference Include="..\..\..\..\..\MibuShared\Mibu.Landing.Models\Mibu.Landing.Models.csproj" />

    <ProjectReference Include="..\..\..\..\CRMWeb\CRMWeb\AcquiringProviders\AcquiringProviders.csproj" />
    
    <ProjectReference Include="..\..\..\..\CRMWeb\CRMWeb\Fiscalizer\Fiscalizer.csproj" /> 
    
    <!--<ProjectReference Include="..\..\..\..\CRMWeb\CRMWeb\ScalesRecognizer\ScalesRecognizer.csproj" />--> 
    
    <ProjectReference Include="..\..\..\..\CRMWeb\CRMWeb\TermoPrintingLib\TermoPrintingLib.csproj" />

  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\Fonts\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Controls\Templates\EquipmentItems\ScalesItemTemplate.xaml.cs">
      <DependentUpon>ScalesItemTemplate.xaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Popups\Equipment\AvailableScalesPopup.xaml.cs">
      <DependentUpon>AvailableScalesPopup.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Controls\Parts\Breadcrumps\BreadcrumpPart.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Controls\Parts\Breadcrumps\BreadcrumpsView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Views\Views\CategoriesView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>
</Project>