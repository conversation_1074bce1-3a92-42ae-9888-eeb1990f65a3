﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32630.192
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CRMDesktopApp", "CRMDesktopApp\CRMDesktopApp.csproj", "{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AcquiringProviders", "..\CRMWeb\CRMWeb\AcquiringProviders\AcquiringProviders.csproj", "{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TermoPrintingLib", "..\CRMWeb\CRMWeb\TermoPrintingLib\TermoPrintingLib.csproj", "{9DD942D1-A519-4360-92C0-F0860676EE83}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ScalesRecognizer", "..\CRMWeb\CRMWeb\ScalesRecognizer\ScalesRecognizer.csproj", "{B2ACA572-70E7-4BF8-A711-E314FB6FB002}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MassaKDriver", "..\CRMWeb\CRMWeb\MassaKDriver\MassaKDriver.csproj", "{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AtolMartaDriver", "..\CRMWeb\CRMWeb\AtolMartaDriver\AtolMartaDriver.csproj", "{0A2A258D-DC44-4C91-8428-91A09E92C354}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fiscalizer", "..\CRMWeb\CRMWeb\Fiscalizer\Fiscalizer.csproj", "{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TestHui", "TestHui\TestHui.csproj", "{00531027-2FF6-4BD7-9748-80E46965ABBC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CRM.Models", "..\..\MibuShared\CRM.Models\CRM.Models.csproj", "{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CRM.Models.Reports.Mobile", "..\..\MibuShared\CRM.Models.Reports.Mobile\CRM.Models.Reports.Mobile.csproj", "{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CRMMoblieApiWrapper", "..\..\MibuShared\CRMMoblieApiWrapper\CRMMoblieApiWrapper.csproj", "{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Debug|x86.ActiveCfg = Debug|x86
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Debug|x86.Build.0 = Debug|x86
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Release|x86.ActiveCfg = Release|x86
		{0B47C1A1-A9BA-4180-B9E2-EEC7CB71E2E8}.Release|x86.Build.0 = Release|x86
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Debug|x86.ActiveCfg = Debug|x86
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Debug|x86.Build.0 = Debug|x86
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Release|Any CPU.Build.0 = Release|Any CPU
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Release|x86.ActiveCfg = Release|x86
		{7086F58E-1025-4D22-AA1C-F8CC4FAAF860}.Release|x86.Build.0 = Release|x86
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Debug|x86.ActiveCfg = Debug|x86
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Debug|x86.Build.0 = Debug|x86
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Release|x86.ActiveCfg = Release|x86
		{9DD942D1-A519-4360-92C0-F0860676EE83}.Release|x86.Build.0 = Release|x86
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Debug|x86.ActiveCfg = Debug|x86
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Debug|x86.Build.0 = Debug|x86
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Release|x86.ActiveCfg = Release|x86
		{B2ACA572-70E7-4BF8-A711-E314FB6FB002}.Release|x86.Build.0 = Release|x86
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Debug|x86.ActiveCfg = Debug|x86
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Debug|x86.Build.0 = Debug|x86
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Release|x86.ActiveCfg = Release|x86
		{CE6B0F80-8954-4292-90FD-EA48CDDE6F74}.Release|x86.Build.0 = Release|x86
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Debug|x86.ActiveCfg = Debug|x86
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Debug|x86.Build.0 = Debug|x86
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Release|Any CPU.Build.0 = Release|Any CPU
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Release|x86.ActiveCfg = Release|x86
		{0A2A258D-DC44-4C91-8428-91A09E92C354}.Release|x86.Build.0 = Release|x86
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Debug|x86.ActiveCfg = Debug|x86
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Debug|x86.Build.0 = Debug|x86
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Release|x86.ActiveCfg = Release|x86
		{AEF0A481-8C87-4310-BA4E-9B3B8042C0E8}.Release|x86.Build.0 = Release|x86
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Debug|x86.ActiveCfg = Debug|x86
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Debug|x86.Build.0 = Debug|x86
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Release|Any CPU.Build.0 = Release|Any CPU
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Release|x86.ActiveCfg = Release|x86
		{00531027-2FF6-4BD7-9748-80E46965ABBC}.Release|x86.Build.0 = Release|x86
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Debug|x86.Build.0 = Debug|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Release|x86.ActiveCfg = Release|Any CPU
		{E6BBE93C-B4C2-4F84-BC1A-3FC89162596D}.Release|x86.Build.0 = Release|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Debug|x86.Build.0 = Debug|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Release|x86.ActiveCfg = Release|Any CPU
		{3BCFAF9A-101C-43EF-A63B-2347AFB5FEC7}.Release|x86.Build.0 = Release|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Debug|x86.Build.0 = Debug|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Release|x86.ActiveCfg = Release|Any CPU
		{1BDB5557-E01D-4CEE-BB91-3DEFBDD00CF1}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B49A9B2D-0E30-467A-A8FB-0C6037A73F77}
	EndGlobalSection
EndGlobal
