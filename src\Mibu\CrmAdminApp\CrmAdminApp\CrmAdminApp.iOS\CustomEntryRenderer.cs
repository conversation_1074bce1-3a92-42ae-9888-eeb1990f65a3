﻿using CrmAdminApp.iOS;
using Foundation;
using MobPhone.Custom;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UIKit;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;

[assembly: ExportRenderer(typeof(CustomEntry), typeof(CustomEntryRenderer))]
namespace CrmAdminApp.iOS
{
    [Foundation.Preserve]
    public class CustomEntryRenderer : EntryRenderer
    {

        //public static void Init() { }
        public static void Load() { }

        protected override void OnElementChanged(ElementChangedEventArgs<Entry> e)
        {
            base.OnElementChanged(e);

            //Configure Native control (UITextField)
            if (Control != null)
            {
                Control.BorderStyle = UITextBorderStyle.None;
                Control.Layer.BorderColor = null;
                Control.Layer.BorderWidth = 0;
                Control.BackgroundColor = UIColor.Clear;
            }
        }
    }
}