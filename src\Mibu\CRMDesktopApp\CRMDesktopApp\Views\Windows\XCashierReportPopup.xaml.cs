﻿using CRM.Models.Enums.Equipment;
using CRM.Models.Reports.Mobile.X_CashierReport;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates.Tables;
using CRMDesktopApp.Views.Windows.CountSelections;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TermoPrintingLib;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для XCashierReportPopup.xaml
    /// </summary>
    public partial class XCashierReportPopup : BaseWindow
    {
        public XCashierReportPopup()
        {
            InitializeComponent();
            Loaded += XCashierReportPopup_Loaded;
        }

        private async void XCashierReportPopup_Loaded(object sender, RoutedEventArgs e)
        {
            XReport = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetCashierXReport(ApplicationState.CurrentDomain,
                                                                               ApplicationState.CurrentTradeNetwork.Id,
                                                                               ApplicationState.CurrentStore.Id,
                                                                               ApplicationState.CurrentDuty.Id);
            FillTable();
        }


        private void FillTable()
        {
            var dutyRow = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"№{ApplicationState.CurrentDuty.Id} от {ApplicationState.CurrentDuty.OpenedAt.ToString("dd.MM.yyyy")}",
                SecondColumn = ApplicationState.CurrentDuty.OpenedAt.ToString("HH:mm")
            };
            reportStackLayout.Children.Add(dutyRow);
            var createdBy = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"Отчет сформировал",
                SecondColumn = $"{Auth.User.Name} {Auth.User.Surname}"
            };
            reportStackLayout.Children.Add(createdBy);
            var checksCount = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"Количество чеков",
                SecondColumn = $"{XReport.ChecksCount}"
            };
            reportStackLayout.Children.Add(checksCount);
            foreach (var workshop in XReport.WorkshopRows)
            {
                var workshopRevenue = new XReportItem()
                {
                    Height = 30,
                    FirstColumn = $"Выручка {workshop.Workshop.Title}",
                    SecondColumn = $"{workshop.Revenue}"
                };
                reportStackLayout.Children.Add(workshopRevenue);
            }
            var cashRevenue = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"Выручка наличными",
                SecondColumn = $"{XReport.ByCash}"
            };
            reportStackLayout.Children.Add(cashRevenue);
            var cashlessRevenue = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"Выручка безналичными",
                SecondColumn = $"{XReport.ByCashless}"
            };
            reportStackLayout.Children.Add(cashlessRevenue);
            var paidByBonuses = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"Оплачено бонусами",
                SecondColumn = $"{XReport.PaidByBonuses}"
            };
            reportStackLayout.Children.Add(paidByBonuses);
            var totalRevenue = new XReportItem()
            {
                Height = 30,
                FirstColumn = $"Итого выручка",
                SecondColumn = $"{XReport.TotalRevenue}"
            };
            reportStackLayout.Children.Add(totalRevenue);
        }


        private XCashierReport xReport;
        public XCashierReport XReport
        {
            get => xReport;
            set { xReport = value; OnPropertyChanged(nameof(XReport)); }
        }

        #region Печать отчета
        private ICommand printReport;
        public ICommand PrintReport
        {
            get => printReport ??= new RelayCommand(async obj =>
            {
                var printers = ApplicationState.StorePrinters.Where(o => o.PrinterType == PrinterType.ForChecksAndReports);
                if (printers.Count() == 0)
                {
                    new OneButtonedPopup("Печать невозможна, нет ни одного принтера для печати чеков", "Ошибка").ShowDialog();
                }
                else
                {
                    if (printers.Count() == 1)
                    {
                        var printer = ApplicationState.StorePrinters[0];
                        Print(printer);
                    }
                    else
                    {
                        var popup = new AvailablePrintersPopup(PrinterType.ForChecksAndReports);
                        popup.ItemSelected += (o, e) =>
                        {
                            Print(e);
                        };
                        popup.ShowDialog();
                    }
                }
            });
        }
        private async void Print(Printer printer)
        {
            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);
            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintXCashierReport(XReport, Auth.User);
            if (!result)
            {
                new OneButtonedPopup("Ошибка печати", "Ошибка").ShowDialog();
            }
        }
        #endregion

    }
}
