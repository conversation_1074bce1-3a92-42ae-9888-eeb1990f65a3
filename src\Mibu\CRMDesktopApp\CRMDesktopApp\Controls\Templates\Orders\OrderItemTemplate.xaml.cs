﻿using CRM.Models.Stores;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для OrderItemTemplate.xaml
    /// </summary>
    public partial class OrderItemTemplate : UserControl,INotifyPropertyChanged
    {
        private OrderItem model;
        public OrderItem Model
        {
            get => model;
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }


        public OrderItemTemplate(OrderItem item)
        {
            Model = item;
            InitializeComponent();

            amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
            itemCommentSign.Visibility = !string.IsNullOrEmpty(item.Comment) ? Visibility.Visible: Visibility.Hidden;

            RenderModifiers();

            Amount = item.Amount;
        }


        private void RenderModifiers()
        {
            foreach (var modifier in Model.SelectedModifiers)
            {
                var item = new OrderItemModifierTemplate(modifier);
                //item.Height = 23;
                modifiersLayout.Children.Add(item);
            }
            double height = 30;
            foreach (var modifier in Model.SelectedModifiers)
            {
                height += modifier.SelectedOptions.Count * 30;
            }
            //this.Height = height;
        }




        private double amount;
        public double Amount
        {
            get => amount;
            set
            {
                amount = value;
                OnPropertyChanged(nameof(Amount));

                Model.Amount = amount;
                Sum = amount * Model.Price;
            }
        }
        private double sum;
        public double Sum
        {
            get => sum;
            set
            {
                sum = value;
                OnPropertyChanged(nameof(Sum)); 
            }
        }


        private string itemTitle;
        public string ItemTitle
        {
            get
            {
                if (Model.TechnicalCard != null)
                    return Model.TechnicalCard.Title;
                if (Model.Product != null)
                    return Model.Product.Title;
                return "";
            }
            set
            {
                itemTitle = value;
                OnPropertyChanged(nameof(ItemTitle));
            }
        }



        public event EventHandler<OrderItem> DeleteBtnTapped;
        private void onDeleteBtnTapped(object sender, MouseButtonEventArgs e)
        {
            DeleteBtnTapped?.Invoke(this, Model);
        }


        #region Выбор позиции
        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }

        public event EventHandler<OrderItem> OrderItemTapped;
        private void onOrderItemTapped(object sender, MouseButtonEventArgs e)
        {
            OrderItemTapped?.Invoke(this, Model);
        }
        #endregion

        #region Редактирование кол-ва через кнопки
        public event EventHandler<OrderItem> AmountChanged;

        private void IncrementAmount(object sender, MouseButtonEventArgs e)
        {
            Amount++;
            amountLabel.Text = Math.Round(Model.Amount, 3).ToString();

            AmountChanged?.Invoke(this, Model);
        }
        private void DecrementAmount(object sender, MouseButtonEventArgs e)
        {
            if (Amount > 1)
                Amount--;

            amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
            AmountChanged?.Invoke(this, Model);
        }
        #endregion



        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }

        
    }
}
