﻿using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ProductAmountKeyboardPopup : PopupPage
    {
        private double _priceForUnit;
        public ProductAmountKeyboardPopup(double priceForUnit)
        {
            try
            {
                _priceForUnit = priceForUnit;
                InitializeComponent();

                Device.InvokeOnMainThreadAsync(() =>
                {
                    priceForValueLabel.Text = $"0 руб.";
                    keyboard.ButtonTapped += Keyboard_ButtonTapped;

                    weightBtn.IsVisible = Device.RuntimePlatform == Device.WPF;
                });
            }
            catch (Exception ex)
            {

            }
        }

        private string amount = "";
        public string Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (e == "backspace" && Amount.Length > 0)
                {
                    Amount = Amount.Remove(Amount.Length - 1, 1);
                }
                else if (e != "backspace")
                {
                    if (e == "." && Amount.Contains(".")) return;
                    Amount += e;
                }

                try
                {
                    if (_priceForUnit != 0 && Amount.Length > 0)
                    {
                        double sum = Convert.ToDouble(Amount) * _priceForUnit;
                        priceForValueLabel.Text = $"{Math.Round(sum, 2)} руб.";
                    }
                    else
                    {
                        priceForValueLabel.Text = $"0 руб.";
                    }
                }
                catch { }
            });
        }




        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }


        public event EventHandler<double> onApplyingValue;
        private ICommand applyValue;
        public ICommand ApplyValue
        {
            get => applyValue ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {
                    await App.Current.MainPage.Navigation.PopPopupAsync();
                    try
                    {
                        onApplyingValue?.Invoke(this, Convert.ToDouble(Amount));
                    }
                    catch
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Значение имеет неверный формат", "Ошибка"));
                    }
                });
            });
        }



        private ICommand getWeight;
        public ICommand GetWeight
        {
            get => getWeight ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.StoreScales.Count == 0)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Взвешивание невозможно, т.к. не добавлены весы", "Ошибка"));
                }
                else
                {
                    if (ApplicationState.StoreScales.Count == 1)
                    {
                        var scales = ApplicationState.StoreScales.FirstOrDefault();
                        GetWeightFromScales(scales);
                    }
                    else
                    {
                        var popup = new AvailableScalesPopup();
                        popup.ItemSelected += (o, e) =>
                        {
                            GetWeightFromScales(e);
                        };
                        await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                    }
                }

            });
        }

        private void GetWeightFromScales(Scales scales)
        {
#if WINDOWS
            try
            {
                var driver = new ScalesRecognizer.ScalesRecognizer(scales);
                var weight = driver.GetWeight();

                Amount = weight.ToString();
            }
            catch (Exception ex) 
            {
                  App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Не удалось получить вес с весов. Проверьте настройки и подключение весов", "Ошибка"));
            }
#endif
        }
    }
}