﻿<?xml version="1.0" encoding="utf-8" ?>
<Application xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             x:Class="CRMMobileApp.App">
    <Application.Resources>

        <!--<ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="{OnPlatform Default='Resources/ButtonStyles.xaml'}" />
                <ResourceDictionary Source="{OnPlatform Default='Resources/EntryStyles.xaml'}" />
                <ResourceDictionary Source="{OnPlatform Default='Resources/MainResources.xaml'}" />
                <ResourceDictionary Source="{OnPlatform Default='Resources/RadioButtonStyles.xaml'}" />
                <ResourceDictionary Source="{OnPlatform Default='Resources/SwitchButtonStyles.xaml'}" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>-->


        <ResourceDictionary Source="Resources/ButtonStyles.xaml" />
        <ResourceDictionary Source="Resources/EntryStyles.xaml" />
        <ResourceDictionary Source="Resources/MainResources.xaml" />
        <ResourceDictionary Source="Resources/RadioButtonStyles.xaml" />
        <ResourceDictionary Source="Resources/SwitchButtonStyles.xaml" />




    </Application.Resources>
</Application>