﻿using CRM.Models.Network.Delivery;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages.Orders;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Delivery
{
    /// <summary>
    /// Логика взаимодействия для DeliveriesPage.xaml
    /// </summary>
    public partial class DeliveriesPage : BasePage
    {
        public DeliveriesPage()
        {
            InitializeComponent();
            Loaded += DeliveriesPage_Loaded;
        }

    
        #region Свойства
        public DateTime SelectedDate { get; set; } = DateTime.Now;

        private ObservableCollection<DeliveryService> deliveryServices = new ObservableCollection<DeliveryService>();
        public ObservableCollection<DeliveryService> DeliveryServices
        {
            get => deliveryServices;
            set { deliveryServices = value; OnPropertyChanged(nameof(DeliveryServices)); }
        }
        private ObservableCollection<DeliveryMan> deliveryMen = new ObservableCollection<DeliveryMan>();
        public ObservableCollection<DeliveryMan> DeliveryMen
        {
            get => deliveryMen;
            set { deliveryMen = value; OnPropertyChanged(nameof(DeliveryMen)); }
        }
        private ObservableCollection<Order> orders = new ObservableCollection<Order>();
        public ObservableCollection<Order> Orders
        {
            get => orders;
            set { orders = value; OnPropertyChanged(nameof(Orders)); }
        }




        private DeliveryService selectedDeliveryService;
        public DeliveryService SelectedDeliveryService
        {
            get => selectedDeliveryService;
            set { selectedDeliveryService = value; OnPropertyChanged(nameof(SelectedDeliveryService)); }
        }
        private DeliveryMan selectedDeliveryMan;
        public DeliveryMan SelectedDeliveryMan
        {
            get => selectedDeliveryMan;
            set { selectedDeliveryMan = value; OnPropertyChanged(nameof(SelectedDeliveryMan)); }
        }


        #endregion
        private async void DeliveriesPage_Loaded(object sender, RoutedEventArgs e)
        {
            filterDate.Text = SelectedDate.ToString("dd.MM.yyyy");

            var services = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliveryServices(ApplicationState.CurrentDomain,
                                                                                                     ApplicationState.CurrentTradeNetwork.Id);
            DeliveryServices = new ObservableCollection<DeliveryService>(services);

            DeliveryServices.Insert(0, new DeliveryService { Title = "Все доставки" });
            SelectedDeliveryService = DeliveryServices.FirstOrDefault();




            var deliveryMens = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliverymen(ApplicationState.CurrentDomain,
                                                                                               ApplicationState.CurrentTradeNetwork.Id);
            DeliveryMen = new ObservableCollection<DeliveryMan>(deliveryMens);
            DeliveryMen.Insert(0, new DeliveryMan { Name = "Все сотрудники" });
            SelectedDeliveryMan = DeliveryMen.FirstOrDefault();



            var ordersList= await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersWithDelivery(ApplicationState.CurrentDomain,
                                                                                                      ApplicationState.CurrentTradeNetwork.Id,
                                                                                                      SelectedDate,
                                                                                                      0,
                                                                                                      0);
            Orders = new ObservableCollection<Order>(ordersList);
            if (!Orders.Any())
            {
                noItemsTextBlock.Visibility = Visibility.Visible;
            }
        }




        #region Управление выбранной датой

        private ICommand decrementDay;
        public ICommand DecrementDay
        {
            get => decrementDay ??= new RelayCommand(async obj =>
            {
                SelectedDate = SelectedDate.AddDays(-1);
                filterDate.Text = SelectedDate.ToString("dd.MM.yyyy");
            });
        }

        private ICommand incrementDay;
        public ICommand IncrementDay
        {
            get => incrementDay ??= new RelayCommand(async obj =>
            {
                SelectedDate = SelectedDate.AddDays(1);
                filterDate.Text = SelectedDate.ToString("dd.MM.yyyy");
            });
        }
        #endregion

        #region Фильтрация
        private ICommand filterOrders;
        public ICommand FilterOrders
        {
            get => filterOrders ??= new RelayCommand(async obj =>
            {
                var ordersList = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersWithDelivery(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentTradeNetwork.Id,
                                                                                                           SelectedDate,
                                                                                                           SelectedDeliveryService.Id,
                                                                                                           SelectedDeliveryMan.Id);
                Orders = new ObservableCollection<Order>(ordersList);
                if (!Orders.Any())
                {
                    noItemsTextBlock.Visibility = Visibility.Visible;
                }
            });
        }
        #endregion

        #region Выбор доставки
        private async void onDeliverySelected(object sender, SelectionChangedEventArgs e)
        {
            var lb = sender as ListBox;
            var item = lb.SelectedItem;

            if (item != null)
            {
                OrdersHelper.CurrentOrder = item as Order;
                NavigationService.Navigate(new CategoriesPage());
            }
        }
        #endregion


        #region Создание доставки
        private ICommand createOrderWithDelivery;
        public ICommand CreateOrderWithDelivery
        {
            get => createOrderWithDelivery ??= new RelayCommand(async obj =>
            {
                new NewDeliveryPopup().ShowDialog();
            });
        }
        #endregion

     
    }
}
