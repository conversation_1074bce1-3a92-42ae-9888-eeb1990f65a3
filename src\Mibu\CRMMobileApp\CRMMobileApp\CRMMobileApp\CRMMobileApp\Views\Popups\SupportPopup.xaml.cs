﻿using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SupportPopup : PopupPage
    {
        public SupportPopup()
        {
            InitializeComponent();
            Load();
        }

        private void Load()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!string.IsNullOrEmpty(ApplicationState.CurrentDomain))
                {
                    domainLabel.Text = ApplicationState.CurrentDomain;
                }
                if (ApplicationState.CurrentStore != null)
                {
                    storeLabel.Text = ApplicationState.CurrentStore.CommonStoreSettings?.Title;
                    addressLabel.Text = $"{ApplicationState.CurrentStore.CommonStoreSettings?.City}, {ApplicationState.CurrentStore.CommonStoreSettings?.Address}";
                }
            });
        }


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}