﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для ProductCard.xaml
    /// </summary>
    public partial class ProductCard : UserControl
    {
        public ProductCard(Product product)
        {
            Model = product;
            InitializeComponent();
            priceLabel.Text = $"{(int)product.Price}₽";

            if (string.IsNullOrEmpty(product.ImgPath) || product.ImgPath == MobileAPI.MAIN_HOST)
            {
                try
                {
                    var firstLetter = product.Title.Substring(0, 1).ToUpper();
                    var secondLetter = product.Title.Substring(1, 1).ToLower();
                    lettersLabel.Text = firstLetter + secondLetter;
                }
                catch { }
            }
        }
        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(Product), typeof(ProductCard));
        public Product Model
        {
            get { return (Product)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


        public event EventHandler<Product> CardTapped;
        private void onCardClicked(object sender, MouseButtonEventArgs e)
        {
            CardTapped?.Invoke(this, Model);
        }
    }
}
