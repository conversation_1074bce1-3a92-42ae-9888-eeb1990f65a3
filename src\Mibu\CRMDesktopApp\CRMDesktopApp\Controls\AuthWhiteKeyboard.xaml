﻿<UserControl x:Class="CRMDesktopApp.Controls.AuthWhiteKeyboard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls" 
             xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450" 
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="80"/>
            <RowDefinition Height="80"/>
            <RowDefinition Height="80"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="87"/>
            <ColumnDefinition Width="87"/>
            <ColumnDefinition Width="87"/>
        </Grid.ColumnDefinitions>
        <Button 
            Content="1" 
            Grid.Row="0"
            Grid.Column="0"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="1"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="2" 
            Grid.Row="0"
            Grid.Column="1"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="2"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="3" 
            Grid.Row="0"
            Grid.Column="2"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="3"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="4" 
            Grid.Row="1"
            Grid.Column="0"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="4"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="5" 
            Grid.Row="1"
            Grid.Column="1"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="5"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="6" 
            Grid.Row="1"
            Grid.Column="2"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="6"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="7" 
            Grid.Row="2"
            Grid.Column="0"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="7"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="8" 
            Grid.Row="2"
            Grid.Column="1"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="8"
            Style="{StaticResource auth_white_btn}"/>
        <Button 
            Content="9" 
            Grid.Row="2"
            Grid.Column="2"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="9"
            Style="{StaticResource auth_white_btn}"/>
        <customcontrols:ImageButton 
            Background="Transparent"
            Grid.Row="3"
            Grid.Column="0"
            Width="60"
            Height="60"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Source="pack://application:,,,/Resources/Images/close_main.png"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="close"/>
        <Button 
            Content="0" 
            Grid.Row="3"
            Grid.Column="1"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="0"
            Style="{StaticResource auth_white_btn}"/>
        <customcontrols:ImageButton 
            Background="Transparent"
            Grid.Row="3"
            Grid.Column="2"
            Width="60"
            Height="60"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Source="pack://application:,,,/Resources/Images/backspace_main.png"
            Command="{Binding ElementName=this,Path=TapButton}"
            CommandParameter="backspace"/>
    </Grid>
</UserControl>
