﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:OrdersPage  
             xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CRMMobileApp.Controls;assembly=CRMMobileApp" 
             xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates;assembly=CRMMobileApp" 
             xmlns:controls1="clr-namespace:CRMMobileApp.Controls.Parts" 
             xmlns:controls11="clr-namespace:XamarinSamples.Views.Controls"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Orders.ProductsPage">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid        
            ColumnSpacing="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <controls1:LeftOrderPanel
                Grid.Column="0"/>

            <Grid Grid.Column="1"
                  Background="#ffffff">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="8*"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <controls1:Header 
                        VerticalOptions="Fill"
                        HorizontalOptions="Fill" />
                </Grid>

                <Grid Grid.Row="1">


                    <StackLayout
                        Spacing="12"
                        Margin="20,0,0,0"
                        VerticalOptions="Center"
                        Orientation="Horizontal">
                        <Label
                            VerticalOptions="Center"
                            TextColor="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16"
                            Text="Все"/>
                        <Image
                            WidthRequest="4"
                            HeightRequest="8"
                            Aspect="Fill"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            Source="arrowRight.png"/>
                        <Label
                            x:Name="categoryLabel"
                            VerticalOptions="Center"
                            TextColor="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16"/>
                    </StackLayout>

                    <Grid 
                        VerticalOptions="Center"
                        Margin="0,0,30,0"
                        HorizontalOptions="End">

                        <controls11:EntryOutlined
                            Text="{Binding Source={x:Reference this},Path=SearchQuery,Mode=TwoWay}"
                            WidthRequest="290"
                            HeightRequest="45"
                            CornerRadius="10"
                            PlaceholderMargin="40,0,0,0"
                            TextMargin="40,0,0,0"
                            EntryBackground="{StaticResource bg_purple}"
                            BorderColor="{StaticResource bg_purple}"
                            PlaceholderColor="{StaticResource text_gray}"
                            Placeholder="Поиск"/>

                        <Image
                            Margin="12,-5,7,0"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            WidthRequest="20"
                            HeightRequest="20"
                            Source="search.png"/>

                    </Grid>

                </Grid>

                <FlexLayout
                    AlignContent="Start"
                    Direction="Row"
                    AlignItems="Start"
                    x:Name="multiList"
                    Grid.Row="2">
                    
                </FlexLayout>
            </Grid>
        </Grid>
    </ContentPage.Content>
</abstractions:OrdersPage>