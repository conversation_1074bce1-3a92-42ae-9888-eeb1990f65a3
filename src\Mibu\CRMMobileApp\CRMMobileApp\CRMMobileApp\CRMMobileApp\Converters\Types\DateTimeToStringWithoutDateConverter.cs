﻿using MarkupCreator.EnumToTupleConverters;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Text;
using System.Windows;
using Xamarin.Forms;

namespace MarkupCreator.Converters
{
    public class DateTimeToStringWithoutDateConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var val = (DateTime)value;
            return val.ToString("HH:mm");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return null;
            //return System.Convert.ToDateTime(value);
        }
    }
}
