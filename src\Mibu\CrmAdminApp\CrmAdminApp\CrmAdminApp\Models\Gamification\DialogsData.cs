﻿using CRM.Models.Gamification.Abstractions;
using CRM.Models.Gamification.Enums;
using CRM.Models.Gamification.Messenger.Chats;
using CRMAdminMoblieApp.Helpers;
using CRMGamificationAPIWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace MobPhone.Model
{
    class DialogsData
    {
        public string ImgPath
        {
            get
            {
                if (Chat is PrivateChat chat)
                {
                    if (Auth.GamificationUser.Id == chat.CreatedById) 
                    {
                        return GamificationAPI.Main.GetImage(chat.ReceivedBy.DisplayAvatarPath);
                    }
                    else
                    {
                        return GamificationAPI.Main.GetImage(chat.CreatedBy.DisplayAvatarPath);
                    }
                }
                else if (Chat is GroupChat groupChat)
                {
                    return "";
                }
                return "";
            }
        }
        public string FIO
        {
            get
            {
                if (Chat is PrivateChat chat)
                {
                    if (Auth.GamificationUser.Id == chat.CreatedById)
                    {
                        return $"{chat.ReceivedBy.Name} {chat.ReceivedBy.Surname}";
                    }
                    else
                    {
                        return $"{chat.CreatedBy.Name} {chat.CreatedBy.Surname}";
                    }
                }
                else if (Chat is GroupChat groupChat)
                {
                    return groupChat.Title;
                }
                return "";
            }
        }
        public bool isIdea
        {
            get
            {
                var lastMsg = Chat.Messages.FirstOrDefault();
                if (lastMsg != null)
                {
                    return lastMsg.Mark == ChatMessageMark.Idea;
                }
                return false;
            }
        }



        public bool isUnread
        {
            get
            {
                var lastMsg = Chat.Messages.FirstOrDefault();
                if (lastMsg != null)
                {
                    return !lastMsg.IsRead && lastMsg.SendById != Auth.GamificationUser.Id;
                }
                return false;
            }
        }
        public int unreadCount
        {
            get
            {
                return Chat.Messages.Count(o => !o.IsRead && o.SendById != Auth.GamificationUser.Id);
            }
        }

        public string Message
        {
            get
            {
                var lastMsg = Chat.Messages.FirstOrDefault();
                if (lastMsg != null)
                {
                    return lastMsg.Message;
                }
                return "Напишите первое сообщение";
            }
        }
        public string DateLabel
        {
            get
            {
                var lastMsg = Chat.Messages.FirstOrDefault();
                if (lastMsg != null)
                {
                    if (DateTime.Now.Date == lastMsg.CreatedAtLocal.Date)
                    {
                        return lastMsg.CreatedAtLocal.ToString("HH:mm");
                    }
                    else if (DateTime.Now.Date.AddDays(-1) == lastMsg.CreatedAtLocal.Date)
                    {
                        return "Вчера";
                    }
                    else
                    {
                        return lastMsg.CreatedAtLocal.ToString("dd.MM.yyyy");
                    }
                }
                return "";
            }
        }




        public Chat Chat { get; set; }


        public event EventHandler ChatUpdated;
        public void OnChatUpdated()
        {
            ChatUpdated?.Invoke(this, EventArgs.Empty);
        }
    }
}
