﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.AdminPasswordPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Пароль администратора"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="450"
        Height="350">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <customcontrols:ClippingBorder
        Background="#FFFFFF"
        Width="440"
        Height="345"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        BorderThickness="1"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        CornerRadius="20">
        <customcontrols:ClippingBorder.Effect>
            <DropShadowEffect Color="LightGray"/>
        </customcontrols:ClippingBorder.Effect>
        <Grid ClipToBounds="True">
            <Grid.RowDefinitions>
                <RowDefinition Height="2.5*"/>
                <RowDefinition Height="4*"/>
                <RowDefinition Height="2*"/>
                <RowDefinition Height="1.5*"/>
            </Grid.RowDefinitions>
            <TextBlock 
                Grid.Row="0"
                FontSize="20"
                FontWeight="Bold"
                Foreground="{StaticResource dark_purple}"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Text="Введите пароль, чтобы продолжить"/>
            <StackPanel
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" >
                <customcontrols:EntryOutlined
                    FontSize="14"
                    PlaceholderFontSize="14"
                    Width="250"
                    Height="40"
                    Text="{Binding ElementName=this,Path=Password,Mode=TwoWay}"
                    HorizontalAlignment="Stretch"
                    Style="{StaticResource white_cornered_entry}"
                    Placeholder="Пароль"/>
            </StackPanel>
            <Button 
                Grid.Row="3"
                BorderThickness="0"
                Command="{Binding ElementName=this,Path=CheckPassword}"
                FontSize="16"
                FontWeight="Bold"
                Foreground="White"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Content="Подтвердить"
                Background="{DynamicResource purple}"/>
        </Grid>
    </customcontrols:ClippingBorder>
</abstactions:BaseWindow>
