﻿using CRMMobileApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Other
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PaymentTypeView : ContentView
    {
        public PaymentTypeView()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ModelProperty =
          BindableProperty.Create(nameof(Model), typeof(PaymentTypeWrapper), typeof(PaymentTypeView));
        public PaymentTypeWrapper Model
        {
            get { return (PaymentTypeWrapper)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}