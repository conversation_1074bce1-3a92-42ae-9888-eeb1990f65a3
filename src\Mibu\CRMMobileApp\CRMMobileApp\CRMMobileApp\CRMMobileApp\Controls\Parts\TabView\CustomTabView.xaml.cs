﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.TabView
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    [ContentProperty("TabItems")]
    public partial class CustomTabView : ContentView
    {

        private string _guid = Guid.NewGuid().ToString();

        public CustomTabView()
        {
            InitializeComponent();
            Init();
        }


        private void Init()
        {
            if(TabItems == null)
            {
                TabItems = new ObservableCollection<CustomTabViewItem>();
            }

            TabItems.CollectionChanged += TabItems_CollectionChanged;
        }


        public static readonly BindableProperty TabItemsProperty = BindableProperty.Create(nameof(TabItemsProperty), typeof(ObservableCollection<CustomTabViewItem>), typeof(CustomTabView));
        public ObservableCollection<CustomTabViewItem> TabItems
        {
            get { return (ObservableCollection<CustomTabViewItem>)GetValue(TabItemsProperty); }
            set { SetValue(TabItemsProperty, value); }
        }


        public int SelectedIndex { get; private set; } = -1;

        private void TabItems_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case System.Collections.Specialized.NotifyCollectionChangedAction.Add:
                    foreach(var item in e.NewItems)
                    {
                        var header = RenderTabHeader(e.NewStartingIndex + e.NewItems.IndexOf(item), item as CustomTabViewItem);
                        if(SelectedIndex == -1)
                        {
                            header.SetAsTapped();
                        }
                    }
                    break;
                case System.Collections.Specialized.NotifyCollectionChangedAction.Remove:
                    throw new NotImplementedException();
            }
        }


        private CustomTabViewItemInternalHeader RenderTabHeader(int index, CustomTabViewItem item)
        {
            var internalHeader = new CustomTabViewItemInternalHeader(item, _guid);
            internalHeader.TabSelected += InternalHeader_TabSelected;
            tabsLayout.Children.Insert(index, internalHeader);

            RemeasureHeaderItems();

            return internalHeader;
        }

        private void RemeasureHeaderItems()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {

                while (this.Width == -1)
                {
                    await Task.Delay(50);
                }

                foreach (var child in tabsLayout.Children)
                {
                    child.WidthRequest = this.Width / tabsLayout.Children.Count;
                }
            });
        }

        private void InternalHeader_TabSelected(object sender, CustomTabViewItem e)
        {
            SelectedIndex = TabItems.IndexOf(e);
            contentLayout.Content = e.TabContent;
        }
    }
}