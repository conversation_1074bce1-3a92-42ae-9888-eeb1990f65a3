﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для ProductAmountKeyboardPopup.xaml
    /// </summary>
    public partial class ProductAmountKeyboardPopup : BaseWindow
    {
        private double _priceForUnit;
        public ProductAmountKeyboardPopup(double priceForUnit)
        {
            _priceForUnit = priceForUnit;
            InitializeComponent();
            priceForValueLabel.Text = $"0 руб.";
            keyboard.ButtonTapped += Keyboard_ButtonTapped;
        }

        private string amount = "";
        public string Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (e == "backspace" && Amount.Length > 0)
            {
                Amount = Amount.Remove(Amount.Length - 1, 1);
            }
            else if (e != "backspace")
            {
                if (e == "." && Amount.Contains(".")) return;
                Amount += e;
            }

            try
            {
                if (_priceForUnit != 0 && Amount.Length > 0)
                {
                    double sum = Convert.ToDouble(Amount) * _priceForUnit;
                    priceForValueLabel.Text = $"{Math.Round(sum, 2)} руб.";
                }
                else
                {
                    priceForValueLabel.Text = $"0 руб.";
                }
            }
            catch { }
        }




        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                this.Close();
            });
        }


        public event EventHandler<double> onApplyingValue;
        private ICommand applyValue;
        public ICommand ApplyValue
        {
            get => applyValue ??= new RelayCommand(async obj =>
            {
                try
                {
                    onApplyingValue?.Invoke(this, Convert.ToDouble(Amount));
                    this.Close();
                }
                catch
                {
                    new OneButtonedPopup("Значение имеет неверный формат", "Ошибка").ShowDialog();
                }
            });
        }


        private ICommand getWeight;
        public ICommand GetWeight
        {
            get => getWeight ??= new RelayCommand(async obj =>
            {
                var scales = ApplicationState.StoreScales.FirstOrDefault();
                if(scales != null)
                {
                    var driver = new ScalesRecognizer.ScalesRecognizer(scales);
                    var weight = driver.GetWeight();

                    Amount = weight.ToString();
                }
            });
        }
    }
}
