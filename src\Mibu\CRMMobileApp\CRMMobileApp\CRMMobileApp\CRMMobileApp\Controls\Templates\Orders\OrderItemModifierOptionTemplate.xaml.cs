﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Orders
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderItemModifierOptionTemplate : ContentView
    {


        public OrderItemModifierOptionTemplate(OrderItemModifierOption option)
        {
            InitializeComponent();
            ModifierOption = option;
        }
        public OrderItemModifierOptionTemplate()
        {
            InitializeComponent();
        }



        public static readonly BindableProperty ModifierOptionProperty =
          BindableProperty.Create(nameof(ModifierOption), typeof(OrderItemModifierOption), typeof(OrderItemModifierOptionTemplate));
        public OrderItemModifierOption ModifierOption
        {
            get { return (OrderItemModifierOption)GetValue(ModifierOptionProperty); }
            set { SetValue(ModifierOptionProperty, value); }
        }
    }
}