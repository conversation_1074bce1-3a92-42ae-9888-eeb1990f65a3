﻿<UserControl x:Class="CRMDesktopApp.Controls.Modifiers.SomeOptionsModifierOptionItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Modifiers"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="6*"/>
            <ColumnDefinition Width="1.5*"/>
            <ColumnDefinition Width="2.5*"/>
        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0">
            <TextBlock 
                Text="{Binding ElementName=this,Path=Model.ModifierOption.Title}"
                FontSize="16"
                Foreground="{StaticResource dark_purple}"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                Margin="15,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"/>
        </Grid>

        <Grid Grid.Column="1">
            <TextBlock      
                FontSize="16"
                Foreground="{StaticResource dark_purple}"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                Margin="5,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center">
                <Run Text="{Binding ElementName=this,Path=Model.ModifierOption.Price}"/>
                <Run Text=" р"/>
            </TextBlock>
        </Grid>


        <Grid Grid.Column="2">
            <StackPanel
                Margin="0,0,15,0"
                Orientation="Horizontal"
                VerticalAlignment="Center"
                HorizontalAlignment="Right">

                <Border
                    Margin="30,0,0,0"
                    Background="#F6F6FB"
                    Padding="0"
                    CornerRadius="15"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    MouseDown="decrementAmount"
                    Height="30"
                    Width="30">
                    <Image 
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Source="pack://application:,,,/Resources/Images/minus.png"
                        Width="20"     
                        Height="20"/>
                </Border>

                <TextBlock
                    Margin="12,0,0,0"
                    x:Name="amountLabel"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    FontSize="18"
                    FontWeight="Bold"
                    Text="{Binding ElementName=this,Path=Model.Amount}"/>

                <Border
                    Margin="12,0,0,0"
                    Background="#7265FB"
                    Padding="0"
                    CornerRadius="15"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    MouseDown="incrementAmount"
                    Height="30"
                    Width="30">
                    <Image 
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Source="pack://application:,,,/Resources/Images/plus.png"
                        Width="20"
                        Height="20"/>
                </Border>

            </StackPanel>
        </Grid>

    </Grid>
</UserControl>
