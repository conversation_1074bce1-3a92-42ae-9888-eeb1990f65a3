﻿using CRM.Models.Enums.General;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Mibu.Landing.Models.Enums;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DutyOpeningPopup : PopupPage
    {
        public DutyOpeningPopup()
        {
            InitializeComponent();
        }




        private double cashInCassa;
        public double CashInCassa
        {
            get => cashInCassa;
            set { cashInCassa = value; OnPropertyChanged(nameof(CashInCassa)); }
        }

        private ICommand openDuty;
        public ICommand OpenDuty
        {
            get => openDuty ??= new RelayCommand(async obj =>
            {
                if(ApplicationState.IsFiscalizationAvailable() == Enums.FiscalizationStatus.CanBeDone)
                {
                    try
                    {
                        var res = ApplicationState.FiscalizerInUse.OpenShift();
                        if (!res)
                        {
                            App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Фискальная смена уже открыта", "Ошибка", "Продолжить"));
                        }
                    }
                    catch(Exception ex)
                    {
                        App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нет подключения к фискальному аппарату. Продолжение операции невозможно", "Ошибка", "Хорошо"));
                        return;
                    }
                }


                await ApplicationState.OpenDuty(CashInCassa);
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await OrdersHelper.GetCurrentOrder();

                if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe
                   && ApplicationState.Subscription.Subscription.Type != SubscriptionType.Start)
                {
                    await App.Current.MainPage.Navigation.PushAsync(new HallPage());
                }
                else
                {
                    //await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
                }

            });
        }



        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                GoBack();
            });
        }

        private void GoBack()
        {
            App.Current.MainPage.Navigation.RemovePopupPageAsync(this);
            App.Current.MainPage.Navigation.PopAsync();
        }
    }
}