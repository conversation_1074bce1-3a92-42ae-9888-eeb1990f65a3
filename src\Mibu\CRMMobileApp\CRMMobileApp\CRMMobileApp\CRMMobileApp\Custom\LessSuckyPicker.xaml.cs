﻿using System;
using System.Threading.Tasks;
using PovodogMobileApp.Custom;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace XamarinSamples.Views.Controls
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class LessSuckyPicker : ContentView
    {
        public LessSuckyPicker()
        {
            InitializeComponent();

        }
        public static readonly BindableProperty TitleProperty =
               BindableProperty.Create(nameof(Title), typeof(string), typeof(LessSuckyPicker));
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public static readonly BindableProperty ItemsSourceProperty =
                BindableProperty.Create(nameof(ItemsSource), typeof(System.Collections.IList), typeof(LessSuckyPicker));
        public System.Collections.IList ItemsSource
        {
            get { return (System.Collections.IList)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }
        public static readonly BindableProperty SelectedItemProperty =
           BindableProperty.Create(nameof(SelectedItem), typeof(object), typeof(LessSuckyPicker));
        public object SelectedItem
        {
            get { return (object)GetValue(SelectedItemProperty); }
            set { SetValue(SelectedItemProperty, value); }
        }
        #region Кастомизация
        public static readonly BindableProperty TextColorProperty =
            BindableProperty.Create(nameof(TextColor), typeof(Color), typeof(LessSuckyPicker), Color.Black);
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }
        public static readonly BindableProperty FontSizeProperty =
             BindableProperty.Create(nameof(FontSize), typeof(double), typeof(LessSuckyPicker), 16.0);
        public double FontSize
        {
            get { return (double)GetValue(FontSizeProperty); }
            set { SetValue(FontSizeProperty, value); }
        }
        public static readonly BindableProperty TextAlignmentProperty =
           BindableProperty.Create(nameof(TextAlignment), typeof(TextAlignment), typeof(LessSuckyPicker), TextAlignment.Center);
        public TextAlignment TextAlignment
        {
            get { return (TextAlignment)GetValue(TextAlignmentProperty); }
            set { SetValue(TextAlignmentProperty, value); }
        }




        public static readonly BindableProperty ControlBackgroundProperty =
              BindableProperty.Create(nameof(ControlBackground), typeof(Color), typeof(LessSuckyPicker), Color.White);
        public Color ControlBackground
        {
            get { return (Color)GetValue(ControlBackgroundProperty); }
            set { SetValue(ControlBackgroundProperty, value); }
        }
        public static readonly BindableProperty BorderColorProperty =
              BindableProperty.Create(nameof(BorderColor), typeof(Color), typeof(LessSuckyPicker),Color.Gray);
        public Color BorderColor
        {
            get { return (Color)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }
        public static readonly BindableProperty CornerRadiusProperty =
            BindableProperty.Create(nameof(CornerRadius), typeof(float), typeof(LessSuckyPicker), 5f);
        public float CornerRadius
        {
            get { return (float)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }
        #endregion


        public BorderlessPicker Picker
        {
            get => borderlessPicker1;
            set => borderlessPicker1 = value;
        }


       

    }
}