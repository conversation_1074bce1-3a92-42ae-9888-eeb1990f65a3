﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.OrderDivisionItemTemplate">
    <ContentView.Content>
        <Frame
            BackgroundColor="{StaticResource dark_purple}"
            CornerRadius="6"
            HasShadow="False"
            Padding="0,5,0,5">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onOrderItemTapped"/>
            </Frame.GestureRecognizers>
            <StackLayout>
                <Grid
                    VerticalOptions="Start">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1"/>
                        <ColumnDefinition Width="130"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="60"/>
                    </Grid.ColumnDefinitions>
                    

                    <Frame
                        IsVisible="{Binding Source={x:Reference this},Path=IsSelected}"
                        BackgroundColor="Transparent"
                        HasShadow="False"
                        Grid.Column="0"
                        Padding="0">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Tapped="onDeleteBtnTapped"/>
                        </Frame.GestureRecognizers>
                        <Image                    
                            WidthRequest="15"
                            HeightRequest="15"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            Source="{OnPlatform Default=close_white.png, WPF='pack://application:,,,/Images/close_white.png'}"/>
                    </Frame>
                    
                    
                    <Label 
                        Grid.Column="1"
                        Text="{Binding Source={x:Reference this},Path=ItemTitle}"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        FontSize="14"
                        TextColor="White"/>



                    <Grid
                        Margin="-20,0,0,0"
                        HorizontalOptions="Start"
                        Grid.Column="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                        </Grid.ColumnDefinitions>

                        <Frame
                            Grid.Column="0"
                            IsVisible="{Binding Source={x:Reference this},Path=IsSelected}"
                            Margin="0,0,0,0"
                            BackgroundColor="#F6F6FB"
                            HasShadow="False"
                            Padding="0"
                            CornerRadius="7"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            HeightRequest="15"
                            WidthRequest="15">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=DecrementAmount}"/>
                            </Frame.GestureRecognizers>
                            <Image 
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="{OnPlatform Default=minus.png, WPF='pack://application:,,,/Images/minus.png'}"
                                WidthRequest="10"     
                                HeightRequest="10"/>
                        </Frame>

                        <Label 
                            x:Name="amountLabel"
                            Grid.Column="1"
                            FontSize="14"
                            TextColor="White"
                            Text=""
                            VerticalOptions="Center"
                            HorizontalOptions="Center"/>

                        <Frame
                            Grid.Column="2"
                            IsVisible="{Binding Source={x:Reference this},Path=IsSelected}"
                            Margin="0,0,0,0"
                            BackgroundColor="#7265FB"
                            HasShadow="False"
                            Padding="0"
                            CornerRadius="7"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            HeightRequest="15"
                            WidthRequest="15">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=IncrementAmount}"/>
                            </Frame.GestureRecognizers>
                            <Image 
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="{OnPlatform Default=plus.png, WPF='pack://application:,,,/Images/plus.png'}"
                                WidthRequest="10"
                                HeightRequest="10"/>
                        </Frame>

                    </Grid>
                  
                    <Label 
                        Grid.Column="3"
                        FontSize="14"
                        TextColor="White"
                        Text="{Binding Source={x:Reference this},Path=Model.Price}"
                        Margin="-8,0,0,0"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"/>
                    <Label 
                        Grid.Column="4"
                        FontSize="14"
                        TextColor="White"
                        Text="{Binding Source={x:Reference this},Path=Model.Total}"
                        VerticalOptions="Center"
                        Margin="-8,0,0,0"
                        HorizontalOptions="Start"/>
                </Grid>

                <StackLayout 
                    Margin="30,0,0,0"
                    x:Name="modifiersLayout">
                    
                    
                    
                </StackLayout>
                
                
            </StackLayout>
        </Frame>
  </ContentView.Content>
</ContentView>