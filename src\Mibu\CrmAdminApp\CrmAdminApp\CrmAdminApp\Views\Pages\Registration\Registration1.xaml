﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Registration1">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid  Background="#ffffff">
            <Image 
                Aspect="Fill"
                Source="main_decor.png"/>

            <ImageButton 
                Source="arrowBack.png"
                WidthRequest="12"
                HeightRequest="28"
                Margin="30,40,0,0"
                BackgroundColor="Transparent"
                HorizontalOptions="Start"
                VerticalOptions="Start"/>

            <StackLayout
                HorizontalOptions="Center"
                Margin="0,150,0,0">

                <Image 
                    WidthRequest="240"
                    HeightRequest="140"
                    Source="logo.png"/>

                <StackLayout
                    HorizontalOptions="Center">

                    <StackLayout Spacing="20">
                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="E-mail или номер телефона"/>
                        <Grid HeightRequest="40">
                            <custom:AndroidStyleEntry  
                                FontFamily="TTFirsNeue-Regular"
                                TextFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Создайте пароль"/>
                            <ImageButton 
                                Source="showPassword.png"
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,10,0"
                                WidthRequest="20"
                                HeightRequest="15"
                                BackgroundColor="Transparent"/>
                        </Grid>
                        <Grid HeightRequest="40">
                            <custom:AndroidStyleEntry  
                                FontFamily="TTFirsNeue-Regular"
                                TextFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Повторите пароль"/>
                            <ImageButton 
                                Source="showPassword.png"
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,10,0"
                                WidthRequest="20"
                                HeightRequest="15"
                                BackgroundColor="Transparent"/>
                        </Grid>
                    </StackLayout>


                    <StackLayout>

                        <Button 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Далее"
                            Style="{x:StaticResource purple_gradient_btn}"
                            WidthRequest="240"
                            HeightRequest="40"
                            Margin="0,30,0,0"/>

                    </StackLayout> 
                    
                  

                    
                </StackLayout>

            </StackLayout>

        </Grid>
    </ContentPage.Content>

</ContentPage>
