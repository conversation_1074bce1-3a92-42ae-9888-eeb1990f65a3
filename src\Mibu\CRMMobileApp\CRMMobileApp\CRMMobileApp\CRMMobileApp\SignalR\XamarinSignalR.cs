﻿using CRMMobileApp;
using Microsoft.AspNetCore.SignalR.Client;
using SH.Forms.UI.Navigation;
using SH.Forms.UI.Navigation.ViewModels;
using System.Threading.Tasks;
using Xamarin.Forms.Internals;

namespace SH.Forms.Services
{


    //version with handshake 
    [Preserve(AllMembers = true)]
    public class XamarinSignalR : SignalRListener
    {
        public XamarinSignalR(IInAppMessager notify)
        {
            Notify = notify;
        }

        protected readonly IInAppMessager Notify;

        public NavBarViewModel NavbarModel => App.Instance.GetHostService<NavBarViewModel>();

        
        protected string BearerToken
        
        {
            get
            {
                var token = App.Instance.BearerToken;
                if (!string.IsNullOrEmpty(token))
                    token = token.Replace("Bearer ", "");
                return token;
            }
        }

        private bool lockConnection;

        /// <summary>
        /// Use once at startup
        /// </summary>
        //-----------------------------------------------------------------
        public async Task InitSignalRAsync(string tenantId)
        //-----------------------------------------------------------------
        {
            if (lockConnection)
                return;

            lockConnection = true;

            try
            {

                var url = App.Configuration.Endpoints.Hub.Replace("localhost", "********");

                NavbarModel.SignalsInfo = $"Connecting to {url}..";

                if (Connected)
                {
                    if (_tenantId == tenantId
                        && _token == BearerToken
                        && _url == url
                        && _profile == App.InProfileId)
                        return; //avoid multiple spam calls

                    await Disconnect();
                }

                abortWatchingConnect = true;

                await InitAndStart(url, "appHub", tenantId,
                    App.Instance.DeviceId, BearerToken,
                    App.Instance.SelectedLang,
                    App.InProfileId);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                lockConnection = false;
            }

        }





        public bool ExecuteCallbackOnMainThread { get; set; } = true;



        //private void OnNotificationRecieved(NotificationType notificationType, UserModelDto fromUser, int id, int count)
        //{
        //    NavbarModel.NewNotificationsCount = count; //todo from server
        //    if (count > 0)
        //        NavbarModel.HasNewNotifications = true;
        //    else
        //        NavbarModel.HasNewNotifications = false;

        //    //App.Alert($"Пришло уведомление! \r\n{notificationType} {}");
        //    var stop = true;

        //}

        protected override void OnHubCreated()
        {
            //Subscribe
            handlerSystemMessages = Hub.On<string, string>("Server", (name, param) =>
            {
                Debug.WriteLine($"[SignalR] {name} {param}");

                if (!string.IsNullOrEmpty(name))
                {
                    //if (name == "Content")
                    //{
                    //    try
                    //    {
                    //        var signal = JsonConvert.DeserializeObject<ContentSignal>(param);
                    //        //pass over
                    //        Notify.All("Content", signal);
                    //    }
                    //    catch (Exception e)
                    //    {
                    //        Console.WriteLine(e);
                    //    }
                    //}

                    Notify.All($"Signal_{name.ToTitleCase()}", param);
                }


            });

            handlerOnlineAll = Hub.On<int>("OnlineAll",
                (s) =>
                {
                    Debug.WriteLine($"[SignalR] Online - {s} ");
                });




        }


        protected IDisposable handlerSystemMessages;
        protected IDisposable handlerOnlineAll;

        protected override void OnHubDisposing()
        {
            //Unsubscribe
            handlerSystemMessages?.Dispose();
            handlerOnlineAll?.Dispose();
        }

        public bool isWatchingConnect;
        public bool abortWatchingConnect;
        public void WatchCanConnect(Action onConnected)
        {
            if (isWatchingConnect || lockReconnecting)
                return;

            if (App.IsConnected)
            {
                onConnected?.Invoke();
                return;
            }

            isWatchingConnect = true;

            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Internet unavailable, watching..");

            NavbarModel.SignalsInfo = "Internet unavailable, watching..";

            abortWatchingConnect = false;

            Device.StartTimer(TimeSpan.FromMilliseconds(2000), () =>
            {
                if (abortWatchingConnect)
                {
                    abortWatchingConnect = false;
                    Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                                    $"Watcher abort ordered");

                    return false;
                }

                if (!App.IsConnected)
                {
                    //Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                    //                $"Internet still unavailable, watching..");

                    return true;
                }

                isWatchingConnect = false;
                Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                                $"Internet available!");
                onConnected?.Invoke();
                return false;
            });

            return;
        }

        private bool incStartColdown;
        int delayFailedStartSecs = 2;

        protected override void OnFailedStartingHub()
        {

            //try reconnect
            Notify.All("Signals", "Offline");

            if (incStartColdown)
            {
                delayFailedStartSecs += 2;
                if (delayFailedStartSecs > 20)
                    delayFailedStartSecs = 20;
            }
            else
            {
                delayFailedStartSecs = 2;
                incStartColdown = true;
            }

            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Retrying initial conect in {delayFailedStartSecs} secs...");

            NavbarModel.SignalsInfo = $"Retrying initial conect in {delayFailedStartSecs} secs...";

            Device.StartTimer(TimeSpan.FromSeconds(delayFailedStartSecs), () =>
            {
                Task.Run(async () =>
                {
                    WatchCanConnect(TryConnect);
                }).ConfigureAwait(false);

                return false;
            });

        }

        void TryConnect()
        {
            InitAgainAndStart();
        }

        protected override void OnConnected()
        {
            incStartColdown = false;
            App.Instance.IsServerConnected = true;
            NavbarModel.IsOnline = true;
            Debug.WriteLine($"[SignalR] {String.Format("{0:mm:ss.ff}", DateTime.Now)} " +
                            $"Connected.");

            NavbarModel.SignalsInfo = $"Connected as {this.Hub.ConnectionId}, profile: {App.InProfileId}";

            Notify.All("Signals", "Online");

            needReconnect = true;

            base.OnConnected();

            SendPingAsync().ConfigureAwait(false);

            ProcessOutcomingQueue();
        }

        protected override void OnDisconnected()
        {
            incStartColdown = false;

            App.Instance.IsServerConnected = false;
            NavbarModel.IsOnline = false;

            NavbarModel.SignalsInfo = $"Disconnected";

            Notify.All("Signals", "Offline");

            base.OnDisconnected();
        }


        public async Task Disconnect()
        {
            needReconnect = false;
            if (Hub != null)
                await KillHubAsync();
        }

        public async Task SendPingAsync()
        {
            //await Hub.InvokeAsync("Test1", "Ping");

            //await Hub.InvokeAsync("Test2", "Ping");

            EnqueueSendToServer("Handshake", App.Instance.DeviceId, $"{Hub.ConnectionId}");
        }

        public async Task DeleteNotificationAsync(string group, string sub, string key)
        {
            //await Hub.InvokeAsync("Test1", "Ping");

            //await Hub.InvokeAsync("Test2", "Ping");

            EnqueueSendToServer("DelNotification", group, sub, key, "");
        }

        public class OutcomingMethod
        {
            public string Method { get; set; }

            public object[] Args { get; set; }
        }

        /// <summary>
        /// Safe
        /// </summary>
        /// <param name="method"></param>
        /// <param name="args"></param>
        public void EnqueueSendToServer(string method, params object[] args)
        {
            // Reminder: The client has SendAsync which is fire-and-forget
            // and InvokeAsync which waits for a completion message from the server.
            //
            // So if you wonna use InvokeAsync to get a responce do not use queue,
            // manually check if we are connected etc... mess..

            OutcomingQueue.Enqueue(new OutcomingMethod
            {
                Method = method,
                Args = args
            });

            ProcessOutcomingQueue();
        }

        public void ProcessOutcomingQueue()
        {
            if (!Connected)
                return;

            try
            {
                while (true)
                {
                    var send = OutcomingQueue.Dequeue();
                    Exception error = null;
                    Task.Run(new Action(async () =>
                    {
                        try
                        {
                            Debug.WriteLine($"[SignalR] Sending queued to server: {send.Method}({send.Args});");
                            await Hub.SendCoreAsync(send.Method, send.Args);
                        }
                        catch (Exception e)
                        {
                            Debug.WriteLine(e);
                            error = e;
                        }
                    }));
                    if (error != null)
                        throw error;
                }
            }
            catch
            {
                // queue is empty or hub error
            }
        }

        public Queue<OutcomingMethod> OutcomingQueue { get; } = new Queue<OutcomingMethod>();

    }
}
