﻿using CRM.Models.Enums;
using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.PancakeView;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ClosedOrderTemplate : ContentView
    {
        public ClosedOrderTemplate(Order order)
        {
            InitializeComponent();
            Model = order;

            Render();
        }
        public ClosedOrderTemplate()
        {
            InitializeComponent();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if(BindingContext is Order order)
            {
                Model = order;
                Render();
            }
        }

        private async Task Render()
        {
            await Device.InvokeOnMainThreadAsync(() =>
            {
                returnedItemFrame.IsVisible = Model.WasReturned;
                closedAtSpan.Text = Model.ClosedAtLocalAuto?.ToString("dd.MM.yyyy HH:mm");

                switch (Model.PaymentType)
                {
                    case PaymentTypeEnum.Cash:
                        cashIcon.IsVisible = true;
                        break;
                    case PaymentTypeEnum.Card:
                        cardIcon.IsVisible = true;
                        break;
                    case PaymentTypeEnum.Mixed:
                        mixedIcon.IsVisible = true;
                        break;
                    case PaymentTypeEnum.Bonuses:
                        bonusesIcon.IsVisible = true;
                        break;
                }
            });
        }


        public static readonly BindableProperty ModelProperty = BindableProperty.Create(nameof(Model), typeof(Order), typeof(ClosedOrderTemplate));
        public Order Model
        {
            get { return (Order)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        public event EventHandler<Order> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                Tapped?.Invoke(sender, Model);
            });
        }
    }
}