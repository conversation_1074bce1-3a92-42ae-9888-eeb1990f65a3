﻿using AcquiringProviders;
using CRM.Models.Network;
using CRM.Models.Stores.Settings.Equipment;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Core;
using CRMMobileApp.Views.Popups;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Devices
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class UCSCardsAcquringCard : AbsAcquiringCard
    {
        private UCSCardsProvider _provider;
        public UCSCardsAcquringCard(Acquiring acquiringDevice) : base(acquiringDevice)
        {
            _provider = new UCSCardsProvider(acquiringDevice);

            this.ExpandedHeightRequest = 250;
            InitializeComponent();
        }

        #region Функции
        private ICommand checkConnection;
        public ICommand CheckConnection
        {
            get => checkConnection ??= new RelayCommand(async obj =>
            {
                if (_provider.CheckConnection())
                {
                    connectionCircleFrame.BackgroundColor = Color.Green;
                }
                else
                {
                    connectionCircleFrame.BackgroundColor = Color.Red;
                }
            });
        }
        private ICommand showShortReport;
        public ICommand ShowShortReport
        {
            get => showShortReport ??= new RelayCommand(async obj =>
            {
                _provider.ShowBriefReport();
            });
        }
        private ICommand showDetailReport;
        public ICommand ShowDetailReport
        {
            get => showDetailReport ??= new RelayCommand(async obj =>
            {
                _provider.ShowDetailReport();
            });
        }
        private ICommand returnMoney;
        public ICommand ReturnMoney
        {
            get => returnMoney ??= new RelayCommand(async obj =>
            {
                 await App.Current.MainPage.Navigation.PushPopupAsync(new AcquringReturnSumPopup(_provider));
            });
        }
        private ICommand cancelReturn;
        public ICommand CancelReturn
        {
            get => cancelReturn ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new AcquringReturnCancelationPopup(_provider));
            });
        }
        private ICommand incassation;
        public ICommand Incassation
        {
            get => incassation ??= new RelayCommand(async obj =>
            {
                _provider.Encashment();
            });
        }
        #endregion
    }
}