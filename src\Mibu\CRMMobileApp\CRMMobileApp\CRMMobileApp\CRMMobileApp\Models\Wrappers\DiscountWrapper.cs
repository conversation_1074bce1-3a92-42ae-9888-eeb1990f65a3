﻿using CRM.Models.Network.LoyaltyProgram;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace CRMMobileApp.Models.Wrappers
{
    public class DiscountWrapper : BindableObject
    {
        public Discount Discount { get; set; }

        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
    }
}
