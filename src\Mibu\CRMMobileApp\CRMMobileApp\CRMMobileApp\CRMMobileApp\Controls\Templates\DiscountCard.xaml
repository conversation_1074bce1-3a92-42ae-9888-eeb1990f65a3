﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.DiscountCard">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            Padding="0"
            CornerRadius="10">
            <Frame.Style>
                <Style TargetType="Frame">
                    <Style.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="True">
                            <Setter Property="BackgroundColor" Value="{StaticResource purple}"></Setter>
                            <Setter Property="BorderColor" Value="Transparent"></Setter>
                        </DataTrigger>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="False">
                            <Setter Property="BackgroundColor" Value="{StaticResource grayBg}"></Setter>
                            <Setter Property="BorderColor" Value="{StaticResource purple}"></Setter>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Frame.Style>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>

                <Label 
                    Grid.Row="0"
                    FontSize="30"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Center"
                    VerticalOptions="Center">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.Discount.Percent}"/>
                                <Span Text="%"/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                    <Label.Style>
                        <Style TargetType="Label">
                            <Style.Triggers>
                                <DataTrigger TargetType="Label" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="False">
                                    <Setter Property="TextColor" Value="{StaticResource purple}"></Setter>
                                </DataTrigger>
                                <DataTrigger TargetType="Label" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="True">
                                    <Setter Property="TextColor" Value="White"></Setter>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Label.Style>
                </Label>

                <Label
                    Grid.Row="1"
                    Text="{Binding Source={x:Reference this},Path=Model.Discount.Title}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    VerticalOptions="Start">
                    <Label.Style>
                        <Style TargetType="Label">
                            <Style.Triggers>
                                <DataTrigger TargetType="Label" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="False">
                                    <Setter Property="TextColor" Value="{StaticResource purple}"></Setter>
                                </DataTrigger>
                                <DataTrigger TargetType="Label" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="True">
                                    <Setter Property="TextColor" Value="White"></Setter>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Label.Style>
                </Label>
            </Grid>
        </Frame>
  </ContentView.Content>
</ContentView>