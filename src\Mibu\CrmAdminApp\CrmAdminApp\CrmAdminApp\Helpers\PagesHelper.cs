﻿using CRMAdminMoblieApp;
using CRMAdminMoblieApp.Controls.Parts;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using MobPhone;
using MobPhone.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace CrmAdminApp.Helpers
{
    internal class PagesHelper
    {
		private static Orders OrdersPage;
		private static Sales SalesPage;
		private static RealTimePage RealTimePage;
		private static Stock StockPage;
        private static Cassa CassaPage;

        public static Orders GetOrdersPage()
        {
            OrdersPage.Load();
            return OrdersPage;
		}
		public static Sales GetSalesPage()
		{
			SalesPage.Load();
			return SalesPage;
		}
		public static RealTimePage GetRealTimePage()
		{
			RealTimePage.Load();
			return RealTimePage;
		}
		public static Stock GetStockPage()
		{
			StockPage.Load();
			return StockPage;
		}
		public static Cassa GetCassaPage()
		{
			CassaPage.Load();
			return CassaPage;
		}


		public static async Task BuildDashboardPages()
        {
            OrdersPage = new Orders();
            SalesPage = new Sales();
            RealTimePage = new RealTimePage();
            StockPage = new Stock();
            CassaPage = new Cassa();
        }

        public static void RefreshApplicationHeader()
        {
            foreach(var page in App.Current.MainPage.Navigation.NavigationStack)
            {
                try
                {
                    var header = page.FindByName("header");
                    if(header is CRMAdminMoblieApp.Controls.Parts.Header h)
                    {
                        h.Render();
                    }
                }
                catch (Exception ex) 
                { 
                }
			}
     
        }

    }
}
