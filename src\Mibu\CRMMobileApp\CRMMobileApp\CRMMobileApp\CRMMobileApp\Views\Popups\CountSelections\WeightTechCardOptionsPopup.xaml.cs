﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WeightTechCardOptionsPopup : PopupPage
    {
        private OrderItem technicalCard;
        public OrderItem TechnicalCard
        {
            get => technicalCard;
            set { technicalCard = value; OnPropertyChanged(nameof(TechnicalCard)); }
        }
        public WeightTechCardOptionsPopup(OrderItem product)
        {
            InitializeComponent();
            TechnicalCard = product;
        }
        public WeightTechCardOptionsPopup(OrderItem product,double amount)
        {
            InitializeComponent();
            TechnicalCard = product;
            Amount = amount;

            _isNewItem = false;
        }
        private bool _isNewItem = true;

        #region Количество товара
        private ICommand openAmountKeyboard;
        public ICommand OpenAmountKeyboard
        {
            get => openAmountKeyboard ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    var popup = new ProductAmountKeyboardPopup(TechnicalCard.TechnicalCard.Price);
                    popup.onApplyingValue += Popup_onApplyingValue;
                    App.Current.MainPage.Navigation.PushPopupAsync(popup);
                });
            });
        }
        private void Popup_onApplyingValue(object sender, double e)
        {
            Amount = e;
        }

        private double amount = 1.000;
        public double Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }
        #endregion







        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
        private ICommand addToOrder;
        public ICommand AddToOrder
        {
            get => addToOrder ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    TechnicalCard.CreatedAt = DateTime.Now;
                    TechnicalCard.Amount = Amount;

                    if (_isNewItem)
                    {
                        OrdersHelper.AddOrderItem(TechnicalCard);
                    }
                    else
                    {
                        OrdersHelper.UpdateOrderItem(TechnicalCard);
                    }

                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
    }
}