﻿using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Parts.Editors;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class GuestsCountPopup : PopupPage
    {
        public AbsTable Table { get; set; }
        public GuestsCountPopup(AbsTable table)
        {
            InitializeComponent();
            Table = table;
        }

        private int amount;
        public int Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }


        private ICommand apply;
        public ICommand Apply
        {
            get => apply ??= new RelayCommand(async obj =>
            {
                var newOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.OpenTableOrder(ApplicationState.CurrentDomain,
                                                                                                  ApplicationState.CurrentStore.Id,
                                                                                                  ApplicationState.CurrentDuty.Id,
                                                                                                  Table.Table.Id,
                                                                                                  Amount,
                                                                                                  Auth.User.Id);
                Table.AddOrder(newOrder);
                await App.Current.MainPage.Navigation.PopPopupAsync();

                OrdersHelper.FromTable = Table;

                OrdersHelper.CurrentOrder = newOrder;
                await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }


        private void rbCheckedChanged(object sender, CheckedChangedEventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                var rb = sender as CustomCrossRadioButton;
                if (rb != null)
                {
                    if (!rb.IsChecked) return;
                    Amount = Convert.ToInt32(rb.Text);
                }

            });
        }
    }



}