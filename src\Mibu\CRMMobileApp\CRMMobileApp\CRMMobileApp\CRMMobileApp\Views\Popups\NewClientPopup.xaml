﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls" 
    xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" 
    xmlns:converters="clr-namespace:MarkupCreator.Converters" 
    xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
    x:Name="this"
    BindingContext="{Binding Source={x:Reference this}}"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    CloseWhenBackgroundIsClicked="False"
    x:Class="CRMMobileApp.Views.Popups.NewClientPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary>
            <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
            <converters:EnumListToStringsConverter x:Key="EnumListToStringsConverter" />
        </ResourceDictionary>
    </animations:PopupPage.Resources>
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,70,0,0"
            Padding="0"
            CornerRadius="20"
            WidthRequest="770"
            HeightRequest="260">
            <Grid 
                 RowSpacing="0">

                <Grid.RowDefinitions>
                    <RowDefinition Height="80"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">

                    <StackLayout
                        Margin="30,38,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Spacing="0"
                        Orientation="Horizontal">

                        <ImageButton
                            Aspect="Fill"
                            Command="{Binding Source={x:Reference this},Path=Close}"
                            Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            WidthRequest="6"
                            HeightRequest="12"/>

                        <Image 
                            Margin="38,-2,0,0"
                            Aspect="Fill"
                            Source="{OnPlatform Default=client.png, WPF='pack://application:,,,/Images/client.png'}"
                            WidthRequest="20"
                            HeightRequest="20"
                            VerticalOptions="Center"/>

                        <Label
                            Margin="15,-2,0,0"
                            VerticalOptions="Center"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text="Новый клиент"/>

                    </StackLayout>


                    <ImageButton 
                        Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        BackgroundColor="Transparent"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Padding="10"
                        WidthRequest="36"
                        HeightRequest="36"
                        Margin="0,10,10,0"/>


                </Grid>


                <Grid Grid.Row="1">

                    <StackLayout
                        Margin="30,0,40,0"
                        Spacing="5">
                        <StackLayout 
                            Spacing="20"
                            Orientation="Horizontal">

                            <controls:EntryOutlined 
                                Style="{StaticResource white_cornered_entry}"
                                CornerRadius="10"
                                Text="{Binding Source={x:Reference this},Path=NewClient.Surname,Mode=TwoWay}"
                                Placeholder="Фамилия"
                                WidthRequest="220"
                                HeightRequest="50"/>
                            <controls:EntryOutlined 
                                CornerRadius="10"
                                Style="{StaticResource white_cornered_entry}"
                                Text="{Binding Source={x:Reference this},Path=NewClient.Name,Mode=TwoWay}"
                                Placeholder="Имя"
                                WidthRequest="210"
                                HeightRequest="50"/>
                            <controls:EntryOutlined 
                                Style="{StaticResource white_cornered_entry}"
                                CornerRadius="10"
                                Text="{Binding Source={x:Reference this},Path=NewClient.Patronymic,Mode=TwoWay}"
                                Placeholder="Отчество"
                                WidthRequest="210"
                                HeightRequest="50"/>

                        </StackLayout>

                        <StackLayout 
                            Spacing="20"
                            Orientation="Horizontal">

                            <controls:EntryOutlined 
                                Style="{StaticResource white_cornered_entry}"
                                CornerRadius="10"
                                Text="{Binding Source={x:Reference this},Path=NewClient.Phone,Mode=TwoWay}"
                                Placeholder="Номер телефона"
                                WidthRequest="220"
                                HeightRequest="50"/>

                            <editors1:CustomCrossDateEdit
                                x:Name="dateEdit"
                                Margin="{OnPlatform Default='0,3,0,0',WPF='0,1,0,0'}"
                                WidthRequest="250"
                                HeightRequest="{OnPlatform Default=40, WPF=49}"
                                HorizontalOptions="Start"
                                VerticalOptions="Start"
                                DisplayFormat="dd.MM.yyyy"
                                PlaceholderText="Дата рождения"
                                CornerRadius="10"
                                PlaceholderColor="#9795B1"
                                BackgroundColor="#F6F6FB"
                                TextColor="{StaticResource dark_purple}"     
                                BorderColor="#F6F6FB"/>

                            <editors1:CustomCrossComboBoxEdit  
                                x:Name="genderComboBox"
                                Margin="{OnPlatform Default='0,3,0,0',WPF='0,1,0,0'}"
                                BackgroundColor="#F6F6FB"
                                TextColor="{StaticResource dark_purple}"
                                BorderColor="#F6F6FB"
                                WidthRequest="180"
                                HeightRequest="{OnPlatform Default=40, WPF=49}"
                                HorizontalOptions="Start"
                                VerticalOptions="Start"
                                CornerRadius="10"
                                SelectedIndex="0"
                                PlaceholderText="Пол"
                                PlaceholderColor="#9795B1"
                                SelectedItem="{Binding Source={x:Reference this},Path=SelectedGenderString,Mode=TwoWay}"
                                ItemsSource="{Binding Source={x:Reference this},Path=GenderStrings,Mode=TwoWay}" />

                        </StackLayout>

                    </StackLayout>

                </Grid>

                <Grid Grid.Row="2">

                    <Button 
                         Command="{Binding Source={x:Reference this},Path=CreateClient}"
                         Style="{StaticResource bg_purple_btn}"
                         VerticalOptions="End"
                         HorizontalOptions="End"
                         Margin="0,0,40,20"
                         Text="Создать"
                         WidthRequest="140"
                         HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>

    </Grid>
    
  
</animations:PopupPage>