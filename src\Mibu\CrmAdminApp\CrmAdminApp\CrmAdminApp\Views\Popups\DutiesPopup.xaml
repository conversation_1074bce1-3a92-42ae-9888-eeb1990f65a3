﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage 
             xmlns:animations="http://rotorgames.com"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             xmlns:b="clr-namespace:Corcav.Behaviors;assembly=Corcav.Behaviors" 
             xmlns:effects="http://sharpnado.com"
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates"
             x:Class="CRMAdminMoblieApp.Views.Popups.DutiesPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </animations:PopupPage.Resources>

    <Grid
        HorizontalOptions="Center"
        VerticalOptions="Start"
        Padding="0"
        WidthRequest="300"
        HeightRequest="220"
        Margin="30,135,30,0">
        <Grid>

            <Frame 
                Margin="10"
                Padding="0"
                CornerRadius="10"
                BackgroundColor="#ffffff">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="65"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <effects:CollectionView  
                            ItemHeight="40"
                            TapCommand="{Binding Source={x:Reference this},Path=ItemSelected}"
                            ItemsSource="{Binding Source={x:Reference this},Path=Duties}">
                            <effects:CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ViewCell>
                                        <itemtemplates:DutyItem Model="{Binding}"/>
                                    </ViewCell>
                                </DataTemplate>
                            </effects:CollectionView.ItemTemplate>
                        </effects:CollectionView>

                        <Label 
                             x:Name="noItemsLabel"
                             IsVisible="False"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             TextColor="{x:StaticResource dark_purple}"
                             VerticalOptions="Center"
                             HorizontalOptions="Center"
                             HorizontalTextAlignment="Center"
                             WidthRequest="200"
                             Text="На данный момент еще нет смен"/>

                    </Grid>

                    <Grid
                        Grid.Row="1">

                        <Button 
                            x:Name="selectDutyBtn"
                            Command="{Binding Source={x:Reference this},Path=SelectDuty}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Выбрать смену"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            Style="{x:StaticResource purple_gradient_btn}"
                            WidthRequest="260"
                            HeightRequest="40"/>
                    </Grid>


                </Grid>



            </Frame>

        </Grid>
    </Grid>
  
</animations:PopupPage>