﻿using BottomSheet.Core;
using CRM.Models.Gamification.General;
using CrmAdminApp.Models.Gamification;
using CRMAdminMoblieApp.Helpers;
using CRMGamificationAPIWrapper;
using CRMMobileApp.Controls.Gamification;
using MobPhone.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using XamForms.Controls;

namespace MobPhone.Bottom
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class SelectProfileWithIcons : BaseBottomSheet
    {

        private bool isMultipleSelection = true;
        private List<ProfileSelectModel> SelectModels = new List<ProfileSelectModel>();
        public SelectProfileWithIcons()
		{
			InitializeComponent();
            Render();
        }

        private async Task Render()
        {
            await Device.InvokeOnMainThreadAsync(async () =>
            {
                var users = await GamificationAPI.Admin.EmployeeTasks.GetEmployeesByStores(ApplicationState.CurrentDomain, ApplicationState.SelectedStoresInGamificationIds);
                itemsCollectionView.ItemsSource = users;

                foreach(var user in users)
                {
                    SelectModels.Add(new ProfileSelectModel
                    {
                        User = user,
                    });
                }
                itemsCollectionView.ItemsSource = SelectModels;
            });
        }



        public void SetSelectionMode(bool isMultiple)
        {
            isMultipleSelection = isMultiple;

            allCheckboxLayout.IsVisible = isMultiple;
        }
        



        #region Выбор элементов
        private void OnCardSelectionChanged(object sender, ProfileSelectModel e)
        {
            allCheckBox.IsChecked = SelectModels.All(o => o.IsSelected);

            if (!isMultipleSelection)
            {
                var other = SelectModels.Where(o => o != e);
                other.ForEach(o => o.IsSelected = false);
            }
        }
        private void ToggleAllSelected(object sender, CheckedChangedEventArgs e)
        {
            

            if ((!allCheckBox.IsChecked && SelectModels.All(o => o.IsSelected))
                    || allCheckBox.IsChecked)
            {
                foreach (var profile in SelectModels)
                {
                    profile.IsSelected = allCheckBox.IsChecked;
                }
            }
        }
        #endregion

        #region Поиск по тексту
        private void onEntryTextChanged(object sender, TextChangedEventArgs e)
        {
            placeholderLayout.IsVisible = string.IsNullOrEmpty(taskText.Text);
            if (!string.IsNullOrEmpty(taskText.Text))
            {
                itemsCollectionView.ItemsSource = SelectModels.Where(o => o.User.FIO.ToLower().Contains(taskText.Text.ToLower()));
            }
            else
            {
                itemsCollectionView.ItemsSource = SelectModels;
            }
        }
        private void onEntryFocused(object sender, FocusEventArgs e)
        {
            placeholderLayout.IsVisible = false;
        }
        private void onEntryUnfocused(object sender, FocusEventArgs e)
        {
            if (string.IsNullOrEmpty(taskText.Text))
            {
                placeholderLayout.IsVisible = true;
            }
        }

        #endregion

        private void ClosePopup(object sender, System.EventArgs e)
        {
            this.IsOpen = false;
        }

        public event EventHandler<IEnumerable<GamificationUser>> SelectionFinished;
        private void SaveChanges(object sender, System.EventArgs e)
        {
            if(SelectModels.Any(o => o.IsSelected))
            {
                this.IsOpen = false;
                SelectionFinished?.Invoke(this, SelectModels.Where(o => o.IsSelected).Select(o => o.User));

            }     
        }

        
    }
}