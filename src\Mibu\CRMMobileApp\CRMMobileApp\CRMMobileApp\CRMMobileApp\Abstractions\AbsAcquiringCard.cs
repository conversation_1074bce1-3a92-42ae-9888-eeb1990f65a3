﻿using CRM.Models.Stores.Settings.Equipment;
using CRM.Models.Stores.Settings.Tables;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMMobileApp.Abstractions
{
    public abstract class AbsAcquiringCard : ContentView
    {
        protected double ExpandedHeightRequest = -1;
        protected double CollapsedHeightRequest = 60;

        public AbsAcquiringCard(Acquiring acquiringDevice)
        {
            Acquiring = acquiringDevice;
        }

        private Acquiring acquiring;
        public Acquiring Acquiring
        {
            get => acquiring;
            set { acquiring = value; OnPropertyChanged(nameof(Acquiring)); }
        }

        private bool isExpanded;
        public bool IsExpanded
        {
            get => isExpanded;
            set
            { 
                isExpanded = value; 
                OnPropertyChanged(nameof(IsExpanded));

                if (value)
                {
                    this.HeightRequest = ExpandedHeightRequest;
                }
                else
                {
                    this.HeightRequest = CollapsedHeightRequest;
                }
            }
        }

        private ICommand toggleExpand;
        public ICommand ToggleExpand
        {
            get => toggleExpand ??= new RelayCommand(async obj =>
            {
                IsExpanded = !IsExpanded;
            });
        }
    }
}
