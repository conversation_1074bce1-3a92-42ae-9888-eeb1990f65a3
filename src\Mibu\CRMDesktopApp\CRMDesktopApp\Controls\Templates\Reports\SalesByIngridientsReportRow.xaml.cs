﻿using CRM.Models.Reports.Mobile.ByIngridientSales;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Reports
{
    /// <summary>
    /// Логика взаимодействия для SalesByIngridientsReportRow.xaml
    /// </summary>
    public partial class SalesByIngridientsReportRow : UserControl
    {
        public SalesByIngridientsReportRow()
        {
            InitializeComponent();
        }
        public static readonly DependencyProperty ModelProperty =
         DependencyProperty.Register(nameof(Model), typeof(ByIngridientSalesReportRow), typeof(SalesByIngridientsReportRow));
        public ByIngridientSalesReportRow Model
        {
            get { return (ByIngridientSalesReportRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
