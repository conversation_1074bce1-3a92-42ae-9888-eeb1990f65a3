﻿using AcquiringProviders;
using CRMDesktopApp.Views.Windows.CountSelections;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using AcquiringTerminal = CRM.Models.Stores.Settings.Equipment.Acquiring;

namespace CRMDesktopApp.Controls.Templates.Devices.Acquiring
{
    /// <summary>
    /// Логика взаимодействия для IngenicoAcquringCard.xaml
    /// </summary>
    public partial class SberAcquringCard : AbsAcquiringCard
    {
        private SberbankProvider _provider;
        public SberAcquringCard(AcquiringTerminal acquiringDevice) : base(acquiringDevice)
        {
            _provider = new SberbankProvider(acquiringDevice);

            this.ExpandedHeightRequest = 250;
            InitializeComponent();
        }
        public SberAcquringCard() : base()
        {
            InitializeComponent();
        }


        #region Функции
        private ICommand checkConnection;
        public ICommand CheckConnection
        {
            get => checkConnection ??= new RelayCommand(async obj =>
            {
                if (_provider.CheckConnection())
                {
                    connectionCircleFrame.Background = Brushes.Green;
                }
                else
                {
                    connectionCircleFrame.Background = Brushes.Red;
                }
            });
        }
        private ICommand showShortReport;
        public ICommand ShowShortReport
        {
            get => showShortReport ??= new RelayCommand(async obj =>
            {
                _provider.ShowBriefReport();
            });
        }
        private ICommand showDetailReport;
        public ICommand ShowDetailReport
        {
            get => showDetailReport ??= new RelayCommand(async obj =>
            {
                _provider.ShowDetailReport();
            });
        }
        private ICommand returnMoney;
        public ICommand ReturnMoney
        {
            get => returnMoney ??= new RelayCommand(async obj =>
            {
                new AcquringReturnSumPopup(_provider).ShowDialog();
            });
        }
        private ICommand cancelReturn;
        public ICommand CancelReturn
        {
            get => cancelReturn ??= new RelayCommand(async obj =>
            {
                new AcquringReturnCancelationPopup(_provider).ShowDialog();
            });
        }
        private ICommand incassation;
        public ICommand Incassation
        {
            get => incassation ??= new RelayCommand(async obj =>
            {
                _provider.Encashment();
            });
        }
        #endregion

    }
}
