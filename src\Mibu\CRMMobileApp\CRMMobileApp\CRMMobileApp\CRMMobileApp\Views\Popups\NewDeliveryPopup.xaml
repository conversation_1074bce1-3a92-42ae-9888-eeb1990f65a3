﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls" 
    xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" 
    xmlns:converters="clr-namespace:MarkupCreator.Converters" 
    xmlns:extensions="http://xamarin.com/schemas/2020/toolkit" 
    xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors" 
    xmlns:tabview="clr-namespace:CRMMobileApp.Controls.Parts.TabView"
    x:Name="this"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    CloseWhenBackgroundIsClicked="False"
    x:Class="CRMMobileApp.Views.Popups.NewDeliveryPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary>
            <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
            <converters:EnumListToStringsConverter x:Key="EnumListToStringsConverter" />
        </ResourceDictionary>
    </animations:PopupPage.Resources>
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,10,0,0"
            Padding="0"
            CornerRadius="20"
            WidthRequest="1000"
            HeightRequest="350">
            <Grid RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="60"/>
                </Grid.RowDefinitions>


                <tabview:CustomTabView
                    Grid.Row="0"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    Margin="0,0,0,0">

                    <tabview:CustomTabViewItem>
                        <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                            <Grid>
                                <Label 
                                     FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                     FontFamily="TTFirsNeue-Regular"
                                     VerticalOptions="Center"
                                     HorizontalOptions="Center"
                                     TextColor="{StaticResource dark_purple}"
                                     Text="Главное">
                                </Label>
                                <BoxView 
                                   VerticalOptions="End"
                                   HorizontalOptions="Fill"
                                   HeightRequest="2"
                                   BackgroundColor="{StaticResource dark_purple}"/>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                        <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                            <Grid>
                                <Label 
                                   FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                   FontFamily="TTFirsNeue-Regular"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   TextColor="{StaticResource text_gray}"
                                   Text="Главное">
                                </Label>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                        <tabview:CustomTabViewItem.TabContent>
                            <Grid Margin="0,5,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="50"/>
                                    <RowDefinition Height="50"/>
                                    <RowDefinition Height="50"/>
                                    <RowDefinition Height="50"/>
                                    <RowDefinition Height="50"/>
                                </Grid.RowDefinitions>

                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="4*"/>
                                        <ColumnDefinition Width="4*"/>
                                        <ColumnDefinition Width="2*"/>
                                    </Grid.ColumnDefinitions>

                                    <Grid Grid.Column="0">

                                        <controls:EntryOutlined 
                                            Margin="7,0,7,0"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            Text="{Binding Source={x:Reference this},Path=PhoneNumber,Mode=TwoWay}"
                                            Style="{StaticResource white_cornered_entry}"
                                            Placeholder="Введите номер телефона"
                                            CornerRadius="10"
                                            HeightRequest="40"/>

                                        <Button 
                                             Command="{Binding Source={x:Reference this},Path=GoToNewClient}"
                                             Style="{StaticResource bg_purple_btn}"
                                             VerticalOptions="Center"
                                             HorizontalOptions="End"
                                             BackgroundColor="Transparent"
                                             Margin="{OnPlatform Default='0,0,3,0',WPF='0,0,3,0'}"
                                             Text="Клиент"
                                             WidthRequest="80"
                                             HeightRequest="35"/>

                                    </Grid>

                                    <Grid Grid.Column="1">
                                        <editors1:CustomCrossDateEdit
                                            x:Name="deliveryDateDateEdit"
                                            LabelText="Дата доставки"
                                            Margin="{OnPlatform Default='7,-6,7,0', WPF='7,0,7,0'}"
                                            HeightRequest="{OnPlatform Default=44, WPF=41}"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            DisplayFormat="dd.MM.yyyy"
                                            PlaceholderText="Дата доставки"
                                            CornerRadius="10"
                                            PlaceholderColor="#9795B1"
                                            BackgroundColor="#F6F6FB"
                                            TextColor="{StaticResource dark_purple}"
                                            BorderColor="#F6F6FB"/>
                                    </Grid>

                                    <Grid Grid.Column="2">
                                        <editors1:CustomCrossComboBoxEdit  
                                            x:Name="personsCountCB"
                                            LabelText="Кол-во персон"
                                            Margin="{OnPlatform Default='7,-6,7,0', WPF='7,0,7,0'}"
                                            HeightRequest="{OnPlatform Default=44, WPF=41}"
                                            BackgroundColor="#F6F6FB"
                                            TextColor="{StaticResource dark_purple}"
                                            BorderColor="#F6F6FB"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            PlaceholderColor="#9795B1"
                                            ItemsSource="{Binding Source={x:Reference this},Path=PersonsCount,Mode=TwoWay}"
                                            SelectedItem="{Binding Source={x:Reference this},Path=Order.Delivery.PersonsCount,Mode=TwoWay}"/>
                                    </Grid>

                                </Grid>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="4*"/>
                                        <ColumnDefinition Width="6*"/>
                                    </Grid.ColumnDefinitions>

                                    <Grid Grid.Column="0" Grid.ColumnSpan="2">

                                        <controls:EntryOutlined 
                                            x:Name="clientFIOEntry"
                                            Margin="7,0,7,0"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            EntryBackground="#EFF9F3"
                                            IsEnabled="False"
                                            CornerRadius="10"
                                            Style="{StaticResource white_cornered_entry}"
                                            Placeholder="Фамилия Имя"
                                            HeightRequest="45"/>

                                    </Grid>

                                    <Grid Grid.Column="1">

                                    </Grid>

                                </Grid>

                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="4*"/>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="2*"/>
                                    </Grid.ColumnDefinitions>

                                    <Grid Grid.Column="0">
                                        <controls:EntryOutlined 
                                            Margin="7,0,7,0"
                                            Text="{Binding Source={x:Reference this},Path=Order.Delivery.City}"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            Style="{StaticResource white_cornered_entry}"
                                            Placeholder="Город"
                                            HeightRequest="45"/>
                                    </Grid>

                                    <Grid Grid.Column="1">
                                        <controls:EntryOutlined 
                                            Margin="7,0,7,0"
                                            Text="{Binding Source={x:Reference this},Path=Order.Delivery.Street}"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            Style="{StaticResource white_cornered_entry}"
                                            Placeholder="Улица"
                                            HeightRequest="45"/>
                                    </Grid>

                                    <Grid Grid.Column="2">
                                        <controls:EntryOutlined 
                                            Margin="7,0,7,0"
                                            Text="{Binding Source={x:Reference this},Path=Order.Delivery.House}"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            Style="{StaticResource white_cornered_entry}"
                                            Placeholder="Дом"
                                            HeightRequest="45"/>
                                    </Grid>

                                    <Grid Grid.Column="3">
                                        <controls:EntryOutlined 
                                            Margin="7,0,7,0"
                                            Text="{Binding Source={x:Reference this},Path=Order.Delivery.Apartment}"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            Style="{StaticResource white_cornered_entry}"
                                            Placeholder="Квартира"
                                            HeightRequest="45"/>
                                    </Grid>

                                </Grid>

                                <Grid Grid.Row="3">

                                    <controls:EntryOutlined 
                                        Margin="7,0,7,0"
                                        Text="{Binding Source={x:Reference this},Path=Order.Delivery.Comment}"
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Center"
                                        CornerRadius="10"
                                        Style="{StaticResource white_cornered_entry}"
                                        Placeholder="Комментарий"
                                        HeightRequest="45"/>

                                </Grid>

                            </Grid>
                        </tabview:CustomTabViewItem.TabContent>
                    </tabview:CustomTabViewItem>

                    <tabview:CustomTabViewItem>
                        <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                            <Grid>
                                <Label 
                                     FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                     FontFamily="TTFirsNeue-Regular"
                                     VerticalOptions="Center"
                                     HorizontalOptions="Center"
                                     TextColor="{StaticResource dark_purple}"
                                     Text="Аванс">
                                </Label>
                                <BoxView 
                                   VerticalOptions="End"
                                   HorizontalOptions="Fill"
                                   HeightRequest="2"
                                   BackgroundColor="{StaticResource dark_purple}"/>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                        <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                            <Grid>
                                <Label 
                                   FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                   FontFamily="TTFirsNeue-Regular"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   TextColor="{StaticResource text_gray}"
                                   Text="Аванс">
                                </Label>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                        <tabview:CustomTabViewItem.TabContent>
                            <Grid>

                                <Label
                                     Margin="15,5,0,0"
                                     VerticalOptions="Start"
                                     HorizontalOptions="Start"
                                     TextColor="{StaticResource dark_purple}"
                                     FontFamily="TTFirsNeue-Regular">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span 
                                                     FontSize="14"
                                                     Text="Всего предоплат: "/>
                                                <Span 
                                                     x:Name="prepaymentSumSpan"
                                                     FontSize="20"
                                                     Text="0"/>
                                                <Span 
                                                     FontSize="20"
                                                     Text=" руб."/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>


                                <Button 
                                      Command="{Binding Source={x:Reference this},Path=GoToPrepayment}"
                                      Style="{StaticResource bg_purple_btn}"
                                      VerticalOptions="End"
                                      HorizontalOptions="End"
                                      Margin="0,0,20,20"
                                      Text="Внести"
                                      WidthRequest="140"
                                      HeightRequest="40"/>

                            </Grid>
                        </tabview:CustomTabViewItem.TabContent>
                    </tabview:CustomTabViewItem>

                    <tabview:CustomTabViewItem>
                        <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                            <Grid>
                                <Label 
                                    FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                    FontFamily="TTFirsNeue-Regular"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    TextColor="{StaticResource dark_purple}"
                                    Text="Исполнители">
                                </Label>
                                <BoxView 
                                  VerticalOptions="End"
                                  HorizontalOptions="Fill"
                                  HeightRequest="2"
                                  BackgroundColor="{StaticResource dark_purple}"/>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                        <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                            <Grid>
                                <Label 
                                  FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                  FontFamily="TTFirsNeue-Regular"
                                  VerticalOptions="Center"
                                  HorizontalOptions="Center"
                                  TextColor="{StaticResource text_gray}"
                                  Text="Исполнители">
                                </Label>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                        <tabview:CustomTabViewItem.TabContent>
                            <Grid Margin="0,5,0,0">

                                <StackLayout Margin="20,0,0,0">

                                    <StackLayout Orientation="Horizontal">
                                        <Label
                                            WidthRequest="250"
                                            VerticalOptions="Center"
                                            TextColor="{StaticResource dark_purple}"
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="20"
                                            Text="Сервис доставки: "/>

                                        <editors1:CustomCrossComboBoxEdit   
                                            BackgroundColor="#F6F6FB"
                                            TextColor="{StaticResource dark_purple}"
                                            BorderColor="#F6F6FB"
                                            HeightRequest="40"
                                            WidthRequest="200"
                                            HorizontalOptions="Start"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            PlaceholderColor="#9795B1"
                                            ItemsSource="{Binding Source={x:Reference this},Path=DeliveryServices,Mode=TwoWay}"
                                            SelectedItem="{Binding Source={x:Reference this},Path=SelectedDeliveryService,Mode=TwoWay}"/>
                                    </StackLayout>

                                    <StackLayout Orientation="Horizontal">
                                        <Label
                                            WidthRequest="250"
                                            VerticalOptions="Center"
                                            TextColor="{StaticResource dark_purple}"
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="20"
                                            Text="Доставщик: "/>

                                        <editors1:CustomCrossComboBoxEdit   
                                            x:Name="deliveryManComboBox"
                                            BackgroundColor="#F6F6FB"
                                            TextColor="{StaticResource dark_purple}"
                                            BorderColor="#F6F6FB"
                                            HeightRequest="40"
                                            WidthRequest="200"
                                            HorizontalOptions="Start"
                                            VerticalOptions="Center"
                                            CornerRadius="10"
                                            PlaceholderColor="#9795B1"
                                            ItemsSource="{Binding Source={x:Reference this},Path=DeliveryMen,Mode=TwoWay}"
                                            SelectedItem="{Binding Source={x:Reference this},Path=SelectedDeliveryMan,Mode=TwoWay}"/>
                                    </StackLayout>

                                </StackLayout>

                            </Grid>
                        </tabview:CustomTabViewItem.TabContent>
                    </tabview:CustomTabViewItem>

                </tabview:CustomTabView>



                <Grid
                    Grid.Row="1"
                    BackgroundColor="{StaticResource grayBg}">

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Style="{StaticResource gray_cornered_filled_btn}"
                        Margin="25,0,0,0"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="Отмена"
                        WidthRequest="170"
                        HeightRequest="40"/>

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=CreateOrder}"
                        Style="{StaticResource purple_gradient_btn}"
                        Margin="0,0,25,0"
                        VerticalOptions="Center"
                        HorizontalOptions="End"
                        Text="Создать заказ"
                        WidthRequest="170"
                        HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>


    </Grid>
    
  
</animations:PopupPage>