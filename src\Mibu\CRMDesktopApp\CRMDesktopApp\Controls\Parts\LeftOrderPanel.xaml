﻿<UserControl x:Class="CRMDesktopApp.Controls.Parts.LeftOrderPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Parts" 
             xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="700" 
             d:DesignWidth="380">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid
        Background="{StaticResource icon_gray}">
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="140"/>
        </Grid.RowDefinitions>

        <Grid Grid.Row="0">
            <Border 
                Margin="30,0,0,0"
                Padding="0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                CornerRadius="10"
                Height="30"
                Width="100"
                Background="{StaticResource bg_purple}"
                MouseDown="ShowActiveOrders">
                <Grid>
                    <TextBlock
                        FontSize="14"
                        FontFamily="TTFirsNeue-Regular"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center">
                        <Run Foreground="#7265FB" Text="#"/>
                        <Run Foreground="{StaticResource dark_purple}" x:Name="orderIdSpan"/>
                    </TextBlock>
                </Grid>
            </Border>


            <Border 
                x:Name="createCancelOrderFrame"
                Margin="0,0,20,0"
                Padding="0"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                CornerRadius="10"
                Height="30"
                Width="180"
                Background="{StaticResource bg_purple}"
                MouseDown="OpenOrCloseOrder">
                <Grid>
                    <StackPanel 
                        Orientation="Horizontal">
                        <Image 
                            Margin="23,0,0,0"
                            Height="14"
                            Width="14"
                            Source="pack://application:,,,/Resources/Images/close_duty.png"/>
                        <TextBlock
                            x:Name="orderCreateCloseText"
                            Margin="5,0,0,0"
                            FontSize="14"
                            Foreground="{StaticResource dark_purple}"
                            Text="Отменить заказ"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"/>
                    </StackPanel>

                </Grid>
            </Border>
        </Grid>

        <Grid 
            Grid.Row="1">
            <StackPanel>
                <Grid
                    HorizontalAlignment="Center"
                    Margin="30,0,40,0"
                    Height="22">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="60"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        Grid.Column="0"
                        FontSize="13"
                        Margin="9,0,0,0"
                        Foreground="White"
                        Text="Наименование"
                        FontFamily="TTFirsNeue-Regular"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"/>
                    <TextBlock
                        Grid.Column="1"
                        FontSize="13"
                        Foreground="White"
                        Text="Кол-во"
                        FontFamily="TTFirsNeue-Regular"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"/>
                    <TextBlock
                        Grid.Column="2"
                        FontSize="13"
                        Foreground="White"
                        Text="Цена"
                        FontFamily="TTFirsNeue-Regular"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"/>
                    <TextBlock
                        Grid.Column="3"
                        FontSize="13"
                        Foreground="White"
                        Text="Итого"
                        FontFamily="TTFirsNeue-Regular"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"/>

                    <Rectangle 
                        Grid.ColumnSpan="4"
                        VerticalAlignment="Bottom"
                        HorizontalAlignment="Stretch"
                        Fill="White"
                        Height="1"/>

                </Grid>
                <ScrollViewer
                    VerticalScrollBarVisibility="Hidden"
                    Margin="0,0,20,0">
                    <StackPanel x:Name="orderItemsLayout">

                    </StackPanel>
                </ScrollViewer>
            </StackPanel>


            <StackPanel
                    x:Name="activeOrderBottomLayout"
                    Margin="5,0,5,-20"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Bottom">

                <Border
                    x:Name="orderCommentFrame"
                    Background="{StaticResource bg_purple}"
                    CornerRadius="10"
                    Height="70"
                    HorizontalAlignment="Stretch"
                    Padding="5">
                    <TextBlock
                        Grid.Column="1"
                        FontSize="14"
                        Foreground="{StaticResource dark_purple}"
                        Text="{Binding ElementName=this,Path=CurrentOrder.Comment}"
                        FontFamily="TTFirsNeue-Regular"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"/>
                </Border>

                <Button 
                    Margin="0,5,0,0"
                    x:Name="sendItemsToWork"
                    Command="{Binding ElementName=this,Path=SendItemsToWorkCommand}"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Stretch"
                    Foreground="{StaticResource dark_purple}"
                    Content="Отправить в работу"
                    FontSize="14"
                    Background="{StaticResource bg_purple}"
                    Style="{StaticResource bg_purple_btn}"
                    Height="40"/>
            </StackPanel>


        </Grid>

        <Grid Grid.Row="2">


            <Border 
                x:Name="activeOrderFooter"
                Background="{StaticResource bg_purple}"
                VerticalAlignment="Bottom"
                Margin="0,0,0,-10"
                Padding="0"
                CornerRadius="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*"/>
                        <RowDefinition Height="1*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">

                        <customcontrols:ImageButton 
                            Command="{Binding ElementName=this,Path=OpenMenu}"
                            Margin="31,33,0,0"
                            Width="20"
                            Height="20"
                            Source="pack://application:,,,/Resources/Images/burger.png"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            Background="Transparent"/>

                        <Button 
                            Command="{Binding ElementName=this,Path=PayOrder}"
                            Margin="0,20,20,0"
                            VerticalAlignment="Top"
                            HorizontalAlignment="Right"
                            Foreground="White"
                            Content="Оплатить"
                            FontSize="14"
                            Style="{StaticResource purple_gradient_btn}"
                            Width="140"
                            Height="40"/>

                    </Grid>

                    <Grid Grid.Row="1">

                        <StackPanel Margin="30,0,0,0">
                            <TextBlock
                                x:Name="orderTotalLabel"
                                FontSize="14"
                                Foreground="{StaticResource text_gray}"
                                Text="Итого: 0.00₽"
                                FontFamily="TTFirsNeue-Regular"
                                HorizontalAlignment="Left"/>
                            <TextBlock
                                x:Name="orderDiscountLabel"
                                FontSize="14"
                                Foreground="{StaticResource text_gray}"
                                Text="Скидка: 0%"
                                FontFamily="TTFirsNeue-Regular"
                                HorizontalAlignment="Left"/>
                        </StackPanel>

                        <TextBlock
                            x:Name="orderTotalWithDiscountLabel"
                            Margin="0,3,25,20"
                            FontSize="30"
                            Foreground="{StaticResource dark_purple}"
                            Text="0.00₽"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Bottom"
                            HorizontalAlignment="Right"/>


                    </Grid>

                </Grid>
            </Border>

            <Border 
                x:Name="closedOrderFooter"
                Visibility="Collapsed"
                Background="{StaticResource bg_purple}"
                VerticalAlignment="Bottom"
                Margin="0,0,0,-10"
                Padding="0"
                CornerRadius="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">

                        <Button 
                            Command="{Binding ElementName=this,Path=PrintClosedOrderCheque}"
                            Margin="0,20,20,20"
                            VerticalAlignment="Top"
                            HorizontalAlignment="Right"
                            Foreground="White"
                            Content="Печать"
                            FontSize="14"
                            Style="{StaticResource bg_purple_btn}"
                            Width="140"
                            Height="40"/>

                    </Grid>

                    <Grid Grid.Column="1">

                        <Button 
                            x:Name="returnOrderBtn"
                            Command="{Binding ElementName=this,Path=ReturnClosedOrder}"
                            Margin="0,20,20,20"
                            VerticalAlignment="Top"
                            HorizontalAlignment="Right"
                            Foreground="White"
                            Content="Возврат"
                            FontSize="14"
                            Style="{StaticResource purple_gradient_btn}"
                            Width="140"
                            Height="40"/>


                    </Grid>

                </Grid>
            </Border>

        </Grid>



        <Border
            x:Name="menuFrame"
            Visibility="Collapsed"
            Grid.Row="1"
            Grid.RowSpan="2"
            Margin="20,0,20,63"
            VerticalAlignment="Bottom"
            HorizontalAlignment="Stretch"
            Width="300"
            CornerRadius="5"
            Background="#FFFFFF"
            Padding="0">
            <Grid>

                <customcontrols:ImageButton 
                    Command="{Binding ElementName=this,Path=CloseMenu}"
                    Margin="0,25,25,0"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Source="pack://application:,,,/Resources/Images/close.png"/>

                <StackPanel
                    Margin="20,20,0,20"
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Left">

                    <StackPanel 
                        Margin="0,12,0,0"
                        Orientation="Horizontal"
                        MouseDown="SetClient">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/client.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Выбрать клиента"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        Orientation="Horizontal"
                        MouseDown="SetDiscount">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/discount.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Скидка"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        Orientation="Horizontal"
                        MouseDown="SetOrderComment">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/comment.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Комментарии к чеку"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        Orientation="Horizontal"
                        MouseDown="ClearOrderItems">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/deleteAll.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Удалить все позиции"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        x:Name="editAmountStackLayout"
                        Visibility="Collapsed"
                        MouseDown="EditAmount"
                        Orientation="Horizontal">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/setAmount.png"/>
                        <TextBlock
                            FontSize="16"
                            Margin="13,0,0,0"
                            Foreground="{StaticResource dark_purple}"
                            Text="Количество"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        x:Name="editModifiersStackLayout"
                        Visibility="Collapsed"
                        Orientation="Horizontal"
                        MouseDown="EditModifiers">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/editModifiers.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Модификаторы"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        x:Name="orderItemCommentStackLayout"
                        Visibility="Collapsed"
                        Orientation="Horizontal"
                        MouseDown="GoToOrderItemCommentPopup">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/orderComment.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Комментарий к позиции"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel 
                        Margin="0,12,0,0"
                        x:Name="splitOrderStackLayout"
                        Visibility="Collapsed"
                        MouseDown="SplitOrder"
                        Orientation="Horizontal">
                        <Image
                            Height="13"
                            Width="13"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            Source="pack://application:,,,/Resources/Images/divideOrder.png"/>
                        <TextBlock
                            Margin="13,0,0,0"
                            FontSize="16"
                            Foreground="{StaticResource dark_purple}"
                            Text="Разделить заказ"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalAlignment="Center"/>
                    </StackPanel>

                </StackPanel>


            </Grid>
        </Border>

    </Grid>
</UserControl>
