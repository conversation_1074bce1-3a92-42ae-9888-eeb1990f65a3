﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CRMMobileApp.Controls" 
             xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates;assembly=CRMMobileApp" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts"
             xmlns:controls1="clr-namespace:XamarinSamples.Views.Controls" 
             xmlns:breadcrumps="clr-namespace:CRMMobileApp.Controls.Parts.Breadcrumps"
             x:Name="this"
             x:Class="CRMMobileApp.Views.Views.CategoriesView">
    <ContentView.Content>
        <Grid
          Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <parts:Header 
                   VerticalOptions="Fill"
                   HorizontalOptions="Fill" />
            </Grid>

            <Grid Grid.Row="1">


                <breadcrumps:BreadcrumpsView 
                    Margin="20,0,0,0"
                    VerticalOptions="Center"
                    CategoryTapped="breadCrumpsCategoryTapped"
                    x:Name="breadCrumps" />


                <Grid 
                   VerticalOptions="Center"
                   Margin="0,0,30,0"
                   HorizontalOptions="End">

                    <controls1:EntryOutlined
                       Text="{Binding Source={x:Reference this},Path=SearchQuery,Mode=TwoWay}"
                       WidthRequest="290"
                       HeightRequest="45"
                       CornerRadius="10"
                       PlaceholderMargin="40,0,0,0"
                       TextMargin="40,0,0,0"
                       EntryBackground="{StaticResource bg_purple}"
                       BorderColor="{StaticResource bg_purple}"
                       PlaceholderColor="{StaticResource text_gray}"
                       Placeholder="Поиск"/>

                    <Image
                       Margin="{OnPlatform Default='12,-5,7,0',WPF='17,5,7,0'}"
                       HorizontalOptions="Start"
                       VerticalOptions="Center"
                       WidthRequest="{OnPlatform Default=20, WPF=14}"
                       HeightRequest="{OnPlatform Default=20, WPF=14}"
                       Source="{OnPlatform Default=search.png, WPF='pack://application:,,,/Images/search.png'}"/>

                </Grid>

            </Grid>




            <Grid Grid.Row="2">




                <ScrollView
                   VerticalScrollBarVisibility="{OnPlatform Default=Default, WPF=Never}"
                   HorizontalOptions="Fill"
                   VerticalOptions="Fill">
                    <FlexLayout
                         AlignContent="Start"
                         Direction="Row"
                         Wrap="Wrap"
                         AlignItems="Start"
                         x:Name="multiList"
                         Padding="0,0,0,40">

                    </FlexLayout>
                </ScrollView>

                <Label
                      x:Name="noItemsLabel"
                      IsVisible="False"
                      VerticalOptions="Center"
                      HorizontalOptions="Center"
                      HorizontalTextAlignment="Center"
                      WidthRequest="300"
                      TextColor="{StaticResource dark_purple}"
                      FontFamily="TTFirsNeue-Regular"
                      Text="На данный момент здесь нет ничего"
                      FontSize="14" />

            </Grid>


        </Grid>
    </ContentView.Content>
</ContentView>