﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Orders.CreateOrder"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Orders" 
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts"
      xmlns:other="clr-namespace:CRMDesktopApp.Controls.Templates.Other" 
      xmlns:flexboxlayout="clr-namespace:FlexboxLayout"
      xmlns:controls="clr-namespace:CRMDesktopApp.Controls"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="CreateOrder">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/OtherStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    
    <Grid Background="#ffffff">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="130"/>
        </Grid.RowDefinitions>


        <parts:Header Grid.Row="0"/>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="4*"/>
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">

                <StackPanel 
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center">

                    <TextBlock
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Оплата"/>


                    <StackPanel
                          Orientation="Horizontal">

                        <TextBlock
                            VerticalAlignment="Center"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14">
                            <Run 
                                Foreground="{StaticResource text_gray}"
                                Text="Заказ "/>
                            <Run 
                                Foreground="{StaticResource purple}"
                                Text="№"/>
                            <Run 
                                x:Name="orderNumberSpan"
                                Foreground="{StaticResource dark_purple}"
                                Text="1234"/>
                        </TextBlock>

                        <Border 
                            Margin="24,0,0,0"
                            Padding="0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            CornerRadius="10"
                            Height="30"
                            Width="180"
                            MouseDown="CancelOrder"
                            Background="{StaticResource bg_purple}">

                            <Grid>
                                <StackPanel 
                                    Orientation="Horizontal">
                                    <Image 
                                        Margin="23,0,0,0"
                                        Height="14"
                                        Width="14"
                                        Source="pack://application:,,,/Resources/Images/close_duty.png"/>
                                    <TextBlock
                                        Margin="13,0,0,0"
                                        FontSize="14"
                                        Foreground="{StaticResource dark_purple}"
                                        Text="Отменить заказ"
                                        FontFamily="TTFirsNeue-Regular"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center"/>
                                </StackPanel>

                            </Grid>
                        </Border>

                    </StackPanel>

                    <StackPanel>
                        <TextBlock
                            Margin="0,10,0,0"
                            x:Name="prepaymentSumLabel"
                            Foreground="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"      
                            Text=""/>
                        <TextBlock
                            Margin="0,10,0,0"
                            x:Name="totalSumLabel"
                            Foreground="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"      
                            Text="Итого: 210.00₽"/>
                        <TextBlock
                            Margin="0,10,0,0"
                            x:Name="clientLabel"
                            Foreground="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Клиент: Гость"/>
                        <TextBlock
                            Margin="0,10,0,0"
                            x:Name="clientBonusesLabel"
                            Visibility="Collapsed"
                            Foreground="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14">
                             <Run Text="Бонусов: "/>
                             <Run x:Name="clientBonusesCountSpan"
                                  Foreground="{StaticResource purple}"
                                  Text="666"/>
                        </TextBlock>
                        <TextBlock
                            Margin="0,10,0,0"
                            x:Name="discountLabel"
                            Foreground="{StaticResource text_gray}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Скидка: 0%"/>
                    </StackPanel>

                    <ListView
                        Margin="0,20,0,0"
                        BorderThickness="0"
                        ItemContainerStyle="{StaticResource listViewItems}"
                        SelectedItem="{Binding ElementName=this,Path=SelectedPaymentType,Mode=TwoWay}"
                        ItemsSource="{Binding ElementName=this,Path=PaymentTypes}"
                        Width="580"
                        Height="220">
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel/>
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <other:PaymentTypeView 
                                    Margin="10,0,0,0"
                                    Width="160"
                                    Height="160"
                                    Model="{Binding}"/>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                    <TextBlock
                        x:Name="toPayLabel"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="К оплате: 210.00₽"/>
                    <TextBlock
                        x:Name="changeLabel"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Сдача: 210.00₽"/>
                    <TextBlock
                        x:Name="bonusesToAddLabel"
                        Visibility="Hidden"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20">
                        <Run Text="Бонусы: "/>
                        <Run Foreground="{StaticResource text_green}"
                             Text="+"/>
                        <Run x:Name="bonusesToAddCountSpan"
                             Foreground="{StaticResource text_green}"
                             Text="666"/>
                    </TextBlock>

                </StackPanel>

            </Grid>

            <Grid Grid.Column="1">

                <Border 
                    CornerRadius="10"
                    Background="{StaticResource bg_purple}"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Width="330"
                    Height="420">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="60"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">

                            <!--<Grid Margin="30,0,30,0">

                                    <TextBlock
                                        x:Name="keyboardText"
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Left"
                                        Foreground="{StaticResource dark_purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="26"
                                        Text="210"/>

                                    <TextBlock
                                        VerticalAlignment="Top"
                                        HorizontalAlignment="Right"
                                        Foreground="{StaticResource purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="26"
                                        Text="₽"/>

                                    <BoxView 
                                        Background="{StaticResource light_grey}"
                                        Height="1"
                                        VerticalAlignment="Bottom"
                                        HorizontalOptions="FillAndExpand" />
                                </Grid>-->


                        </Grid>

                        <Grid Grid.Row="0">

                            <controls:AuthKeyboard
                                x:Name="keyboard"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"/>


                        </Grid>

                        <Grid Grid.Row="1">

                            <flexboxlayout:Flexbox
                                Direction="Row"
                                AlignItems="Center"
                                JustifyContent="Center"
                                HorizontalAlignment="Stretch"
                                AlignContent="SpaceAround">
                                <Button 
                                    Command="{Binding ElementName=this,Path=AddSumBtnPressed}"
                                    CommandParameter="100"
                                    Foreground="{StaticResource dark_purple}"
                                    Background="Transparent"
                                    Content="100₽"
                                    Padding="0"
                                    FontSize="12"
                                    Width="70"
                                    Height="40"/>
                                <Button 
                                    Command="{Binding ElementName=this,Path=AddSumBtnPressed}"
                                    CommandParameter="500"
                                    Foreground="{StaticResource dark_purple}"
                                    Background="Transparent"
                                    Content="500₽"
                                    Padding="0"
                                    FontSize="12"
                                    Width="70"
                                    Height="40"/>
                                <Button 
                                    Command="{Binding ElementName=this,Path=AddSumBtnPressed}"
                                    CommandParameter="1000"
                                    Foreground="{StaticResource dark_purple}"
                                    Background="Transparent"
                                    Content="1000₽"
                                    Padding="0"
                                    FontSize="12"
                                    Width="70"
                                    Height="40"/>
                            </flexboxlayout:Flexbox>

                        </Grid>

                    </Grid>
                </Border>


            </Grid>


        </Grid>

        <Grid Grid.Row="2">
            <Button 
                Command="{Binding ElementName=this,Path=PayOrder}"
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Foreground="White"
                Content="Оплатить"
                FontSize="20"
                Style="{StaticResource purple_gradient_btn}"
                Width="240"
                Height="60"/>

        </Grid>


    </Grid>
</abstactions:BasePage>
