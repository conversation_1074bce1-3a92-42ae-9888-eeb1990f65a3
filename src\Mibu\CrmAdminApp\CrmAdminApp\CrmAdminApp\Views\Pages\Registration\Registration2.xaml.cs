﻿using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMAdminMoblieApp
{
    public partial class Registration2 : ContentPage
    {
        public Registration2()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        private string domain = string.Empty;
        public string Domain
        {
            get => domain;
            set { domain = value; OnPropertyChanged(nameof(Domain)); }
        }
        private string username = string.Empty;
        public string Username
        {
            get => username;
            set { username = value; OnPropertyChanged(nameof(Username)); }
        }
        private string password = string.Empty;
        public string Password
        {
            get => password;
            set { password = value; OnPropertyChanged(nameof(Password)); }
        }
        private ICommand auth;
        public ICommand Auth
        {
            get => auth ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new TradeNetworks());
            });
        }
    }
}
