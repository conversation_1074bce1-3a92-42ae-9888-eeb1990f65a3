﻿using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WelcomePage : ContentPage
    {
        public WelcomePage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        private ICommand goToAuth;
        public ICommand GoToAuth
        {
            get => goToAuth ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new MainPage());
            });
        }
    }
}