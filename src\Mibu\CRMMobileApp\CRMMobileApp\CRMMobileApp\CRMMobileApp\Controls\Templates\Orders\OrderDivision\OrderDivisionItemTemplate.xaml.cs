﻿using CRM.Models.Stores;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderDivisionItemTemplate : ContentView
    {
        private OrderItem model;
        public OrderItem Model
        {
            get => model;
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }


        public OrderDivisionItemTemplate(OrderItem item)
        {
            Model = item;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                amountLabel.Text = Math.Round(Model.Amount, 3).ToString();

                RenderModifiers();
            });
        }

       
        private void RenderModifiers()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                foreach (var modifier in Model.SelectedModifiers)
                {
                    var item = new OrderItemModifierTemplate(modifier);
                    modifiersLayout.Children.Add(item);
                }

                //double height = 37;
                //foreach (var modifier in Model.SelectedModifiers)
                //{
                //    //height += modifier.SelectedOptions.Count * 30;
                //}
                //this.HeightRequest = height;
            });
        }






        private string itemTitle;
        public string ItemTitle
        {
            get
            {
                if (Model.TechnicalCard != null)
                   return Model.TechnicalCard.Title;
                if (Model.Product != null)
                    return Model.Product.Title;
                return "";  
            }
            set
            {
                itemTitle = value;
                OnPropertyChanged(nameof(ItemTitle));
            }
        }



        public event EventHandler<OrderItem> DeleteBtnTapped;
        private void onDeleteBtnTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                DeleteBtnTapped?.Invoke(this, Model);
            });
        }



        #region Выбор позиции
        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }

        public event EventHandler<OrderItem> OrderItemTapped;
        private void onOrderItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                OrderItemTapped?.Invoke(this, Model);
            });
        }

        #endregion

        #region Редактирование кол-ва через кнопки
        public event EventHandler<OrderItem> AmountChanged;

        private ICommand incrementAmount;
        public ICommand IncrementAmount
        {
            get => incrementAmount ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    Model.Amount++;
                    amountLabel.Text = Math.Round(Model.Amount, 3).ToString();

                    AmountChanged?.Invoke(this, Model);
                });
            });
        }
        private ICommand decrementAmount;
        public ICommand DecrementAmount
        {
            get => decrementAmount ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (Model.Amount > 1)
                        Model.Amount--;

                    amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
                    AmountChanged?.Invoke(this, Model);
                });
            });
        }
        #endregion

    }
}