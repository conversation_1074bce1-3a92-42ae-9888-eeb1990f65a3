﻿using BottomSheet.Core;
using CRM.Models.Gamification.General;
using CRM.Models.Gamification.Messenger.Chats;
using CrmAdminApp.Controls.Gamification;
using CrmAdminApp.Helpers;
using CRMAdminMoblieApp.Helpers;
using CRMGamificationAPIWrapper;
using MobPhone.Model;
using MobPhone.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace MobPhone.Bottom
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class SelectProfile : BaseBottomSheet
    {
        public SelectProfile()
		{
			InitializeComponent();
            Render();
        }
        private async Task Render()
        {
            await Device.InvokeOnMainThreadAsync(async () =>
            {
                var users = await GamificationAPI.Admin.EmployeeTasks.GetEmployeesByStores(ApplicationState.CurrentDomain, ApplicationState.SelectedStoresInGamificationIds);
                itemsCollectionView.ItemsSource = users;
            });
        }



        public event EventHandler<GamificationUser> OnItemSelected;
        private void onUserTapped(object sender, EventArgs e)
        {
            if(sender is UserChatListCard card)
            {
                OnItemSelected?.Invoke(this, card.User);
                this.IsOpen = false;
            }
        }


        private void closeBtnClicked(object sender, EventArgs e)
        {
            this.IsOpen = false;
            //this.IsVisible = false;
        }
    }
}