﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Parts.Editors.CustomCrossRadioButton">
    <ContentView.GestureRecognizers>
        <TapGestureRecognizer Tapped="onTapped"/>
    </ContentView.GestureRecognizers>
    <!--<ContentView.ControlTemplate>
        <ControlTemplate>
            <Grid BackgroundColor="Red"></Grid>
        </ControlTemplate>
    </ContentView.ControlTemplate>-->
    <!--<ContentView.Style>
        <Style TargetType="ContentView">
            <Style.Triggers>
                <DataTrigger TargetType="ContentView" Binding="{Binding Source={x:Reference this},Path=IsChecked}" Value="True">
                    <Setter Property="ControlTemplate" Value="{Binding Source={x:Reference this},Path=CheckedStateTemplateProperty}"></Setter>
                </DataTrigger>
                <DataTrigger TargetType="ContentView" Binding="{Binding Source={x:Reference this},Path=IsChecked}" Value="False">
                    <Setter Property="ControlTemplate" Value="{Binding Source={x:Reference this},Path=UncheckedStateTemplateProperty}"></Setter>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </ContentView.Style>-->
    <ContentView.Content>
      <Grid>

            <RadioButton
                IsChecked="{Binding Source={x:Reference this},Path=IsChecked, Mode=TwoWay}"
                GroupName="{Binding Source={x:Reference this},Path=GroupName}"
                IsVisible="False"/>

        </Grid>
  </ContentView.Content>
</ContentView>