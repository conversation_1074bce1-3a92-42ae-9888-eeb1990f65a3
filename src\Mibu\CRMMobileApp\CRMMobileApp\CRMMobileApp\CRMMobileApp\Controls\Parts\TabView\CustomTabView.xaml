﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMMobileApp.Controls.Parts.TabView.CustomTabView">
  <ContentView.Content>
      <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <FlexLayout 
                AlignItems="Stretch"
                Direction="Row"
                AlignContent="Stretch"
                JustifyContent="SpaceAround"
                x:Name="tabsLayout"
                Grid.Row="0">


            </FlexLayout>


            <Frame 
                 Padding="0"
                 x:Name="contentLayout"
                 Grid.Row="1"
                 HasShadow="False"
                 IsClippedToBounds="False"
                 HorizontalOptions="Fill"
                 VerticalOptions="Fill"
                 BackgroundColor="Transparent"
                 Background="Transparent">
                
            </Frame>

        </Grid>
  </ContentView.Content>
</ContentView>