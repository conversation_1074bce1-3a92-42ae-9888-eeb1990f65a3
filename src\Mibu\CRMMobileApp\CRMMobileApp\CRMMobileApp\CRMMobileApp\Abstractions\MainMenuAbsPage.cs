﻿using CRMMobileApp.Core;
using CRMMobileApp.Views.Pages.Main;
using CRMMobileApp.Views.Popups;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Input;
using Xamarin.CommunityToolkit.Extensions;
using Xamarin.Forms;

namespace CRMMobileApp.Abstractions
{
    public abstract class MainMenuAbsPage : ContentPage
    {
        #region Навигация между вкладками
        private ICommand goToMainMenuPage;
        public ICommand GoToMainMenuPage
        {
            get => goToMainMenuPage ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PushAsync(new MainMenu()));
        }
        private ICommand goToEquipmentPage;
        public ICommand GoToEquipmentPage
        {
            get => goToEquipmentPage ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PushAsync(new MainMenu()));
        }
        private ICommand goToStockPage;
        public ICommand GoToStockPage
        {
            get => goToStockPage ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PushAsync(new MainMenu()));
        }
        #endregion

        private ICommand openMainPage;
        public ICommand OpenMainPage
        {
            get => openMainPage ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
        private ICommand showAboutSoftwarePopup;
        public ICommand ShowAboutSoftwarePopup
        {
            get => showAboutSoftwarePopup ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PushPopupAsync(new AboutSoftwarePopup()));
        }
    }
}
