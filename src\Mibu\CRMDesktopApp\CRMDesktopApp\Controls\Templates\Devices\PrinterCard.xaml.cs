﻿using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using TermoPrintingLib;

namespace CRMDesktopApp.Controls.Templates.Devices
{
    /// <summary>
    /// Логика взаимодействия для PrinterCard.xaml
    /// </summary>
    public partial class PrinterCard : UserControl
    {
        protected double ExpandedHeightRequest = -1;
        protected double CollapsedHeightRequest = 60;

        public PrinterCard(Printer printer)
        {
            ExpandedHeightRequest = 95;
            Model = printer;
            InitializeComponent();
        }
        public static readonly DependencyProperty ModelProperty =
          DependencyProperty.Register(nameof(Model), typeof(Printer), typeof(PrinterCard));
        public Printer Model
        {
            get { return (Printer)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        #region Экспандер 
        private bool isExpanded;
        public bool IsExpanded
        {
            get => isExpanded;
            set
            {
                isExpanded = value;
                OnPropertyChanged(nameof(IsExpanded));

                if (value)
                {
                    this.Height = ExpandedHeightRequest;
                }
                else
                {
                    this.Height = CollapsedHeightRequest;
                }
            }
        }
        private void ToggleExpand(object sender, MouseButtonEventArgs e)
        {
            IsExpanded = !IsExpanded;
        }
        #endregion

        #region Функции
        private ICommand checkConnection;
        public ICommand CheckConnection
        {
            get => checkConnection ??= new RelayCommand(async obj =>
            {
                var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                ApplicationState.CurrentStore.Id);
                var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                             ApplicationState.CurrentStore.Id);
                var printer = new TermoPrinter(Model, commonSettings, printSettings);
                if (printer.CheckConnection())
                {
                    connectionCircleFrame.Background = Brushes.Green;
                }
                else
                {
                    connectionCircleFrame.Background = Brushes.Red;
                }
            });
        }
        #endregion




        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }
    }
}
