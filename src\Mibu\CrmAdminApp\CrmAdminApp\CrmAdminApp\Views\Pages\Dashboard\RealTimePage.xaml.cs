﻿using CRM.Models.Reports.Mobile.Admin.RealTime;
using CRM.Models.Reports.Mobile.Admin.Waiters;
using CRM.Models.Reports.Mobile.Admin.Workshops;
using CRM.Models.Reports.Mobile.AdminCarousel;
using CRMAdminMoblieApp.Abstractions;
using CRMAdminMoblieApp.Enums;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Popups;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Pages.Dashboard
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class RealTimePage : BaseDashboardPage
    {
        public override DashboardPageType PageType => DashboardPageType.RealTimePage;
        public RealTimePage() : base()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

			DutyLabel = dutyLabel;

			//Load();
		}




        public override async Task RefreshData()
        {
			if (ApplicationState.CurrentDuty?.Id > 0)
            {
                Task.Run(async () =>
                {
					RealTimeReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetRealTimeReport(ApplicationState.CurrentDomain,
                                                                                                        ApplicationState.CurrentTradeNetwork.Id,
                                                                                                        ApplicationState.CurrentStore.Id,
																										ApplicationState.CurrentDuty.Id);

					RealTimeReport.ByBonuses = Math.Round(RealTimeReport.ByBonuses, 0);
					RealTimeReport.ByCash = Math.Round(RealTimeReport.ByCash, 0);
					RealTimeReport.ByCard = Math.Round(RealTimeReport.ByCard, 0);
					RealTimeReport.Revenue = Math.Round(RealTimeReport.Revenue, 0);


					DonutChartSections = new ObservableCollection<Tuple<int, string>>()
					{
						new Tuple<int, string>((int)Math.Round(RealTimeReport.ByCardPercent,0), "#7265FB"),
						new Tuple<int, string>((int)Math.Round(RealTimeReport.ByCashPercent,0), "#26E27C"),
						new Tuple<int, string>((int)Math.Round(RealTimeReport.ByBonusesPercent,0), "#F2994A")
					};
				});

                Task.Run(async () =>
                {
                    CarouselReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetAdminDashboardCarouselReport(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id,
                                                                                                                    ApplicationState.CurrentDuty.Id);

                    CarouselReport.Sum = Math.Round(CarouselReport.Sum, 0);
                    CarouselReport.AverageCheck = Math.Round(CarouselReport.AverageCheck, 1);
                });


				Task.Run(async () =>
                {
                    WorkshopsReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetWorkshopsReport(ApplicationState.CurrentDomain,
                                                                                                 ApplicationState.CurrentTradeNetwork.Id,
                                                                                                 ApplicationState.CurrentStore.Id,
                                                                                                 ApplicationState.CurrentDuty.Id);

                    await Device.InvokeOnMainThreadAsync(() => workshopReportEmptyLabel.IsVisible = WorkshopsReport.Workshops.Count == 0);
                });

                Task.Run(async () =>
                {

                    WaitersReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetWaitersReport(ApplicationState.CurrentDomain,
                                                                                                         ApplicationState.CurrentTradeNetwork.Id,
                                                                                                         ApplicationState.CurrentStore.Id,
                                                                                                         ApplicationState.CurrentDuty.Id);

					await Device.InvokeOnMainThreadAsync(() => waitersReportEmptyLabel.IsVisible = WaitersReport.Waiters.Count == 0);
				});
            }
            else
			{

                DonutChartSections = new ObservableCollection<Tuple<int, string>>()
                {
                    new Tuple<int, string>((int)0, "#7265FB"),
                    new Tuple<int, string>((int)0, "#26E27C"),
                    new Tuple<int, string>((int)0, "#F2994A")
                };

                RealTimeReport = new RealTimeReport();
                CarouselReport = new AdminDashboardCarouselReport();
                WorkshopsReport = new WorkshopsReport();
                WaitersReport = new WaitersReport();



                Device.InvokeOnMainThreadAsync(() =>
                {
                    workshopReportEmptyLabel.IsVisible = true;
                    waitersReportEmptyLabel.IsVisible = true;
                });

			}
        }

        public bool IsLoaded { get; private set; }
		public void Load()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (IsLoaded) return;


                IconImageSource = ImageSource.FromFile("scheduling.png");
                Title = "Сейчас";




                var storeSettings = ApplicationState.CurrentStore.CommonStoreSettings;
                storeTitleLabel.Text = storeSettings.Title;
                storeAddressLabel.Text = $"{storeSettings.City}, {storeSettings.Address}";

                if (Device.Idiom == TargetIdiom.Phone)
                {
                    realTimeTabletStats.IsVisible = false;
                }
                if (Device.Idiom == TargetIdiom.Tablet)
                {
                    realTimePhoneStats.IsVisible = false;
                }


                OnDutyChanged(null, ApplicationState.CurrentDuty);

                //RefreshData();

                if (Device.Idiom == TargetIdiom.Phone)
                {
                    scrollCarousel.ScrollToAsync(225, 0, true);
                }

                IsLoaded = true;
            });
		}

        #region Properties

        private ObservableCollection<Tuple<int, string>> donutChartSections = new ObservableCollection<Tuple<int, string>>
        {

	        new Tuple<int, string>((int)0, "#7265FB"),
			new Tuple<int, string>((int)0, "#26E27C"),
			new Tuple<int, string>((int)0, "#F2994A")
		};
        public ObservableCollection<Tuple<int, string>> DonutChartSections
        {
            get => donutChartSections;
            set { donutChartSections = value; OnPropertyChanged(nameof(DonutChartSections)); }
        }


        private AdminDashboardCarouselReport carouselReport = new AdminDashboardCarouselReport();
        public AdminDashboardCarouselReport CarouselReport
        {
            get => carouselReport;
            set { carouselReport = value; OnPropertyChanged(nameof(CarouselReport)); }
        }
        private RealTimeReport realTimeReport = new RealTimeReport();
        public RealTimeReport RealTimeReport
        {
            get => realTimeReport;
            set { realTimeReport = value; OnPropertyChanged(nameof(RealTimeReport)); }
        }
        private WorkshopsReport workshopsReport = new WorkshopsReport();
        public WorkshopsReport WorkshopsReport
        {
            get => workshopsReport;
            set { workshopsReport = value; OnPropertyChanged(nameof(WorkshopsReport)); }
        }
        private WaitersReport waitersReport = new WaitersReport();
        public WaitersReport WaitersReport
        {
            get => waitersReport;
            set { waitersReport = value; OnPropertyChanged(nameof(WaitersReport)); }
        }

        #endregion

        private ICommand openNetworksPopup;
        public ICommand OpenNetworksPopup
        {
            get => openNetworksPopup ??= new RelayCommand(obj =>
            {
                var popup = new NetworksPopup();
                popup.StoreSet += (o, e) =>
                {
                    Device.InvokeOnMainThreadAsync(() =>
                    {
                        var storeSettings = ApplicationState.CurrentStore.CommonStoreSettings;
                        storeTitleLabel.Text = storeSettings.Title;
                        storeAddressLabel.Text = $"{storeSettings.City}, {storeSettings.Address}";

                        RefreshData();
                    });
                };
                App.Current.MainPage.Navigation.PushPopupAsync(popup);
            });
        }
    }
}