﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для SupportPopup.xaml
    /// </summary>
    public partial class SupportPopup : BaseWindow
    {
        public SupportPopup()
        {
            InitializeComponent();
            Load();
        }

        private void Load()
        {
            if (!string.IsNullOrEmpty(ApplicationState.CurrentDomain))
            {
                domainTextBlock.Text = ApplicationState.CurrentDomain;
            }
            if(ApplicationState.CurrentStore != null)
            {
                storeTextBlock.Text = ApplicationState.CurrentStore.CommonStoreSettings.Title;
                addressTextBlock.Text = $"{ApplicationState.CurrentStore.CommonStoreSettings.City}, {ApplicationState.CurrentStore.CommonStoreSettings.Address}";
            }
        }
    }
}
