﻿using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AboutSoftwarePopup : PopupPage
    {
        public AboutSoftwarePopup()
        {
            InitializeComponent();
            Render();
        }

        private void Render()
        {
            domainLabel.Text = $"{ApplicationState.CurrentDomain}.mibu24.ru";
            if(ApplicationState.CurrentStore != null)
            {
                addressLabel.Text = ApplicationState.CurrentStore.CommonStoreSettings.ToString();
            }
        }


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}