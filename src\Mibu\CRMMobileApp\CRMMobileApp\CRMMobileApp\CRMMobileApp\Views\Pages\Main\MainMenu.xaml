﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             xmlns:extensions="http://xamarin.com/schemas/2020/toolkit" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts" 
             xmlns:effects="http://sharpnado.com"
             BackgroundColor="White"
             xmlns:devices="clr-namespace:CRMMobileApp.Controls.Templates.Devices"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:tabview="clr-namespace:CRMMobileApp.Controls.Parts.TabView"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Main.MainMenu">
    <ContentPage.Content>
        <Grid RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="50"/>
            </Grid.RowDefinitions>

            <parts:HeaderSearch 
                Title="Меню"
                Grid.Row="0"/>

            <Grid Grid.Row="1"
                  BackgroundColor="White">


                <tabview:CustomTabView
                    HorizontalOptions="Center"
                    WidthRequest="600"
                    Margin="0,30,0,0"
                    VerticalOptions="Fill">

                    <tabview:CustomTabViewItem>
                        <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                            <Grid>
                                <Label 
                                   FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                   FontFamily="TTFirsNeue-Regular"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   TextColor="{StaticResource dark_purple}"
                                   Text="Главное">
                                </Label>
                                <BoxView 
                                     VerticalOptions="End"
                                     HorizontalOptions="Fill"
                                     HeightRequest="2"
                                     BackgroundColor="{StaticResource dark_purple}"/>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                        <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                            <Grid>
                                <Label 
                                     FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                     FontFamily="TTFirsNeue-Regular"
                                     VerticalOptions="Center"
                                     HorizontalOptions="Center"
                                     TextColor="{StaticResource text_gray}"
                                     Text="Главное">
                                </Label>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                        <tabview:CustomTabViewItem.TabContent>
                            <ScrollView VerticalScrollBarVisibility="Never">

                                <Grid 
                                    HorizontalOptions="Center"
                                    Padding="0,50,0,0"
                                    ColumnSpacing="40"
                                    RowSpacing="30">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60"/>
                                        <RowDefinition Height="60"/>
                                        <RowDefinition Height="60"/>
                                        <RowDefinition Height="60"/>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="220"/>
                                        <ColumnDefinition Width="220"/>
                                    </Grid.ColumnDefinitions>


                                    <Frame
                                        HasShadow="False"
                                        BackgroundColor="{StaticResource bg_purple}"
                                        CornerRadius="10"
                                        Padding="0"
                                        Grid.Column="0"
                                        Grid.Row="0">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToClosedOrders}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                            Spacing="0"
                                            Margin="10,0,0,0"
                                            Orientation="Horizontal">
                                            <Image 
                                                Source="{OnPlatform Default=closedOrders.png, WPF='pack://application:,,,/Images/closedOrders.png'}"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="22"
                                                WidthRequest="22"/>
                                            <Label
                                                Margin="14,0,0,0"
                                                VerticalOptions="Center"
                                                TextColor="{StaticResource dark_purple}"
                                                FontFamily="TTFirsNeue-Regular"
                                                FontSize="16"
                                                Text="Закрытые заказы"/>
                                        </StackLayout>
                                    </Frame>

                                    <Frame
                                        HasShadow="False"
                                        BackgroundColor="{StaticResource bg_purple}"
                                        CornerRadius="10"
                                        Padding="0"
                                        Grid.Column="1"
                                        Grid.Row="0">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToCreateTransaction}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                            Spacing="0"
                                            Margin="10,0,0,0"
                                            Orientation="Horizontal">
                                            <Image 
                                                Source="{OnPlatform Default=create_transaction.png, WPF='pack://application:,,,/Images/create_transaction.png'}"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="22"
                                                WidthRequest="22"/>
                                            <Label
                                                Margin="14,0,0,0"
                                                VerticalOptions="Center"
                                                TextColor="{StaticResource dark_purple}"
                                                FontFamily="TTFirsNeue-Regular"
                                                FontSize="16"
                                                Text="Создать транзакцию"/>
                                        </StackLayout>
                                    </Frame>

                                    <Frame
                                        HasShadow="False"
                                        BackgroundColor="{StaticResource bg_purple}"
                                        CornerRadius="10"
                                        Padding="0"
                                        Grid.Column="0"
                                        Grid.Row="1">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ShowXCashierReport}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                            Spacing="0"
                                            Margin="10,0,0,0"
                                            Orientation="Horizontal">
                                            <Image 
                                                Source="{OnPlatform Default=x_report_cashier.png, WPF='pack://application:,,,/Images/x_report_cashier.png'}"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="22"
                                                WidthRequest="17"/>
                                            <Label
                                                Margin="14,0,0,0"
                                                VerticalOptions="Center"
                                                TextColor="{StaticResource dark_purple}"
                                                FontFamily="TTFirsNeue-Regular"
                                                FontSize="16"
                                                Text="X-отчет кассира"/>
                                        </StackLayout>
                                    </Frame>

                                    <Frame
                                        HasShadow="False"
                                        BackgroundColor="{StaticResource bg_purple}"
                                        CornerRadius="10"
                                        Padding="0"
                                        Grid.Column="1"
                                        Grid.Row="1">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ShowXReport}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                            Spacing="0"
                                            Margin="10,0,0,0"
                                            Orientation="Horizontal">
                                            <Image 
                                                Source="{OnPlatform Default=x_report.png, WPF='pack://application:,,,/Images/x_report.png'}"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="22"
                                                WidthRequest="17"/>
                                            <Label
                                                Margin="14,0,0,0"
                                                VerticalOptions="Center"
                                                TextColor="{StaticResource dark_purple}"
                                                FontFamily="TTFirsNeue-Regular"
                                                FontSize="16"
                                                Text="X-отчет"/>
                                        </StackLayout>
                                    </Frame>

                                    <!--<Frame
                                            HasShadow="False"
                                            BackgroundColor="{StaticResource bg_purple}"
                                            CornerRadius="10"
                                            Padding="0"
                                            Grid.Column="0"
                                            Grid.Row="2">
                                            <Frame.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenCashBox}"/>
                                            </Frame.GestureRecognizers>
                                            <StackLayout
                                                Spacing="0"
                                                Margin="10,0,0,0"
                                                Orientation="Horizontal">
                                                <Image 
                                                    Source="{OnPlatform Default=open_cashbox.png, WPF='pack://application:,,,/Images/open_cashbox.png'}"
                                                    VerticalOptions="Center"
                                                    Aspect="Fill"
                                                    HeightRequest="17"
                                                    WidthRequest="22"/>
                                                <Label
                                                    Margin="14,0,0,0"
                                                    VerticalOptions="Center"
                                                    TextColor="{StaticResource dark_purple}"
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="16"
                                                    Text="Открыть денежный ящик"/>
                                            </StackLayout>
                                        </Frame>-->

                                    <Frame
                                      x:Name="deliveryFrame"
                                      HasShadow="False"
                                      BackgroundColor="{StaticResource bg_purple}"
                                      CornerRadius="10"
                                      Padding="0"
                                      Grid.Column="0"
                                      Grid.Row="2">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToDeliveriesPage}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                          Spacing="0"
                                          Margin="10,0,0,0"
                                          Orientation="Horizontal">
                                            <Image 
                                                  Source="{OnPlatform Default=delivery.png, WPF='pack://application:,,,/Images/delivery.png'}"
                                                  VerticalOptions="Center"
                                                  Aspect="Fill"
                                                  HeightRequest="22"
                                                  WidthRequest="22"/>
                                            <Label
                                              Margin="14,0,0,0"
                                              VerticalOptions="Center"
                                              TextColor="{StaticResource dark_purple}"
                                              FontFamily="TTFirsNeue-Regular"
                                              FontSize="16"
                                              Text="Доставка"/>
                                        </StackLayout>
                                    </Frame>



                                    <Frame
                                        HasShadow="False"
                                        BackgroundColor="{StaticResource bg_purple}"
                                        CornerRadius="10"
                                        Padding="0"
                                        Grid.Column="1"
                                        Grid.Row="2">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=CloseDuty}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                            Spacing="0"
                                            Margin="10,0,0,0"
                                            Orientation="Horizontal">
                                            <Image 
                                                Source="{OnPlatform Default=close_duty.png, WPF='pack://application:,,,/Images/close_duty.png'}"
                                                VerticalOptions="Center"
                                                Aspect="Fill"
                                                HeightRequest="22"
                                                WidthRequest="22"/>
                                            <Label
                                                Margin="14,0,0,0"
                                                VerticalOptions="Center"
                                                TextColor="{StaticResource dark_purple}"
                                                FontFamily="TTFirsNeue-Regular"
                                                FontSize="16"
                                                Text="Закрыть смену"/>
                                        </StackLayout>
                                    </Frame>


                                    <Frame
                                        x:Name="gamificationFrame"
                                        HasShadow="False"
                                        IsVisible="False"
                                        BackgroundColor="{StaticResource bg_purple}"
                                        CornerRadius="10"
                                        Padding="0"
                                        Grid.Column="0"
                                        Grid.Row="3">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToGamification}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                           Spacing="0"
                                           Margin="10,0,0,0"
                                           Orientation="Horizontal">
                                            <Image 
                                               Source="{OnPlatform Default=gamificationIcon.png, WPF='pack://application:,,,/Images/gamificationIcon.png'}"
                                               VerticalOptions="Center"
                                               Aspect="Fill"
                                               HeightRequest="22"
                                               WidthRequest="36"/>
                                            <Label
                                               Margin="14,0,0,0"
                                               VerticalOptions="Center"
                                               TextColor="{StaticResource dark_purple}"
                                               FontFamily="TTFirsNeue-Regular"
                                               FontSize="16"
                                               Text="Геймификация"/>
                                        </StackLayout>
                                    </Frame>


                                </Grid>

                            </ScrollView>
                        </tabview:CustomTabViewItem.TabContent>
                    </tabview:CustomTabViewItem>

                    <tabview:CustomTabViewItem>
                        <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                            <Grid>
                                <Label 
                                      FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                      FontFamily="TTFirsNeue-Regular"
                                      VerticalOptions="Center"
                                      HorizontalOptions="Center"
                                      TextColor="{StaticResource dark_purple}"
                                      Text="Склад">
                                </Label>
                                <BoxView 
                                    VerticalOptions="End"
                                    HorizontalOptions="Fill"
                                    HeightRequest="2"
                                    BackgroundColor="{StaticResource dark_purple}"/>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                        <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                            <Grid>
                                <Label 
                                    FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                    FontFamily="TTFirsNeue-Regular"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    TextColor="{StaticResource text_gray}"
                                    Text="Склад">
                                </Label>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                        <tabview:CustomTabViewItem.TabContent>
                            <ScrollView VerticalScrollBarVisibility="Never">

                                <Grid
                                     HorizontalOptions="Center"
                                     Padding="0,50,0,0"
                                     ColumnSpacing="40"
                                     RowSpacing="30">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="60"/>
                                        <RowDefinition Height="60"/>
                                        <RowDefinition Height="60"/>
                                        <RowDefinition Height="60"/>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="220"/>
                                        <ColumnDefinition Width="220"/>
                                    </Grid.ColumnDefinitions>

                                    <Frame
                                         HasShadow="False"
                                         BackgroundColor="{StaticResource bg_purple}"
                                         CornerRadius="10"
                                         Padding="0"
                                         Grid.Column="0"
                                         Grid.Row="0">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToBalanceAtStock}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                             Spacing="0"
                                             Margin="10,0,0,0"
                                             Orientation="Horizontal">
                                            <Image 
                                                 Source="{OnPlatform Default=balance_at_stock.png, WPF='pack://application:,,,/Images/balance_at_stock.png'}"
                                                 VerticalOptions="Center"
                                                 Aspect="Fill"
                                                 HeightRequest="22"
                                                 WidthRequest="22"/>
                                            <Label
                                                 Margin="14,0,0,0"
                                                 VerticalOptions="Center"
                                                 TextColor="{StaticResource dark_purple}"
                                                 FontFamily="TTFirsNeue-Regular"
                                                 FontSize="16"
                                                 Text="Остатки на складе"/>
                                        </StackLayout>
                                    </Frame>

                                    <Frame
                                         HasShadow="False"
                                         BackgroundColor="{StaticResource bg_purple}"
                                         CornerRadius="10"
                                         Padding="0"
                                         Grid.Column="1"
                                         Grid.Row="0">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToSalesOnDuty}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                             Spacing="0"
                                             Margin="10,0,0,0"
                                             Orientation="Horizontal">
                                            <Image 
                                                 Source="{OnPlatform Default=sales_on_duty.png, WPF='pack://application:,,,/Images/sales_on_duty.png'}"
                                                 VerticalOptions="Center"
                                                 Aspect="Fill"
                                                 HeightRequest="22"
                                                 WidthRequest="18"/>
                                            <Label
                                                 Margin="14,0,0,0"
                                                 VerticalOptions="Center"
                                                 TextColor="{StaticResource dark_purple}"
                                                 FontFamily="TTFirsNeue-Regular"
                                                 FontSize="16"
                                                 Text="Продажи за смену"/>
                                        </StackLayout>
                                    </Frame>

                                    <Frame
                                         HasShadow="False"
                                         IsClippedToBounds="True"
                                         BackgroundColor="{StaticResource bg_purple}"
                                         CornerRadius="10"
                                         Padding="0"
                                         Grid.Column="0"
                                         Grid.Row="1">
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToSalesByIngridient}"/>
                                        </Frame.GestureRecognizers>
                                        <StackLayout
                                             Spacing="0"
                                             Margin="10,0,0,0"
                                             Orientation="Horizontal">
                                            <Image 
                                                 Source="{OnPlatform Default=sales_by_ingridients.png, WPF='pack://application:,,,/Images/sales_by_ingridients.png'}"
                                                 VerticalOptions="Center"
                                                 Aspect="Fill"
                                                 HeightRequest="22"
                                                 WidthRequest="17"/>
                                            <Label
                                                 Margin="14,0,0,0"
                                                 VerticalOptions="Center"
                                                 TextColor="{StaticResource dark_purple}"
                                                 FontFamily="TTFirsNeue-Regular"
                                                 FontSize="16"
                                                 LineBreakMode="WordWrap"
                                                 MaxLines="2"
                                                 Text="{OnPlatform WPF='Продажи по &#x0a;ингридиентам', Default='Продажи по ингридиентам'}"/>
                                        </StackLayout>
                                    </Frame>
                                </Grid>

                            </ScrollView>
                        </tabview:CustomTabViewItem.TabContent>
                    </tabview:CustomTabViewItem>

                    <tabview:CustomTabViewItem>
                        <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                            <Grid>
                                <Label 
                                     FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                     FontFamily="TTFirsNeue-Regular"
                                     VerticalOptions="Center"
                                     HorizontalOptions="Center"
                                     TextColor="{StaticResource dark_purple}"
                                     Text="Оборудование">
                                </Label>
                                <BoxView 
                                   VerticalOptions="End"
                                   HorizontalOptions="Fill"
                                   HeightRequest="2"
                                   BackgroundColor="{StaticResource dark_purple}"/>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                        <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                            <Grid>
                                <Label 
                                   FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                   FontFamily="TTFirsNeue-Regular"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   TextColor="{StaticResource text_gray}"
                                   Text="Оборудование">
                                </Label>
                            </Grid>
                        </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                        <tabview:CustomTabViewItem.TabContent>
                            <ScrollView 
                                VerticalScrollBarVisibility="Never">

                                <Grid   
                                    HorizontalOptions="Fill">

                                    <tabview:CustomTabView 
                                        Margin="0,30,0,0">

                                        <!--<tabview:CustomTabViewItem>
                                            <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                                                <Grid BackgroundColor="{StaticResource bg_purple}">
                                                    <Label 
                                                       FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                                       FontFamily="TTFirsNeue-Regular"
                                                       VerticalOptions="Center"
                                                       HorizontalOptions="Center"
                                                       TextColor="{StaticResource text_gray}"
                                                       Text="Эквайринг" />
                                                </Grid>
                                            </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                                            <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                                                <Grid BackgroundColor="{StaticResource bg_purple}">
                                                    <Label 
                                                       FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                                       FontFamily="TTFirsNeue-Regular"
                                                       VerticalOptions="Center"
                                                       HorizontalOptions="Center"
                                                       TextColor="{StaticResource purple}"
                                                       Text="Эквайринг" />
                                                </Grid>
                                            </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                                            <tabview:CustomTabViewItem.TabContent>
                                                <Grid>
                                                    <FlexLayout
                                                       HorizontalOptions="Fill"
                                                       VerticalOptions="Fill"
                                                       Margin="0,30,0,0"
                                                       x:Name="acquringFlexLayout"
                                                       AlignContent="Start"
                                                       AlignItems="Start"
                                                       Direction="Row"
                                                       Wrap="Wrap">

                                                    </FlexLayout>

                                                    <Label
                                                       x:Name="noAcquiringItemsLabel"
                                                       IsVisible="False"
                                                       VerticalOptions="Center"
                                                       HorizontalOptions="Center"
                                                       HorizontalTextAlignment="Center"
                                                       WidthRequest="300"
                                                       TextColor="{StaticResource dark_purple}"
                                                       FontFamily="TTFirsNeue-Regular"
                                                       Text="Нет доступных POS-терминалов. Добавить терминал можно в web панели Mibu"
                                                       FontSize="14" />

                                                </Grid>
                                            </tabview:CustomTabViewItem.TabContent>
                                        </tabview:CustomTabViewItem>-->

                                        <tabview:CustomTabViewItem>
                                            <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                                                <Grid BackgroundColor="{StaticResource bg_purple}">
                                                    <Label 
                                                       FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                                       FontFamily="TTFirsNeue-Regular"
                                                       VerticalOptions="Center"
                                                       HorizontalOptions="Center"
                                                       TextColor="{StaticResource text_gray}"
                                                       Text="Принтеры" />
                                                </Grid>
                                            </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                                            <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                                                <Grid BackgroundColor="{StaticResource bg_purple}">
                                                    <Label 
                                                       FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                                       FontFamily="TTFirsNeue-Regular"
                                                       VerticalOptions="Center"
                                                       HorizontalOptions="Center"
                                                       TextColor="{StaticResource purple}"
                                                       Text="Принтеры" />
                                                </Grid>
                                            </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                                            <tabview:CustomTabViewItem.TabContent>
                                                <Grid>

                                                    <FlexLayout
                                                        Margin="0,30,0,0"
                                                        x:Name="printersFlexLayout"
                                                        AlignContent="Start"
                                                        AlignItems="Start"
                                                        Direction="Row"
                                                        Wrap="Wrap">

                                                    </FlexLayout>

                                                    <Label
                                                         x:Name="noPrinterItemsLabel"
                                                         IsVisible="False"
                                                         VerticalOptions="Center"
                                                         HorizontalOptions="Center"
                                                         HorizontalTextAlignment="Center"
                                                         WidthRequest="300"
                                                         TextColor="{StaticResource dark_purple}"
                                                         FontFamily="TTFirsNeue-Regular"
                                                         Text="Нет доступных чековых принтеров. Добавить принтер можно в web панели Mibu"
                                                         FontSize="14" />


                                                </Grid>
                                            </tabview:CustomTabViewItem.TabContent>
                                        </tabview:CustomTabViewItem>

                                        <tabview:CustomTabViewItem>
                                            <tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                                                <Grid BackgroundColor="{StaticResource bg_purple}">
                                                    <Label 
                                                       FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                                       FontFamily="TTFirsNeue-Regular"
                                                       VerticalOptions="Center"
                                                       HorizontalOptions="Center"
                                                       TextColor="{StaticResource text_gray}"
                                                       Text="Кассовая техника" />
                                                </Grid>
                                            </tabview:CustomTabViewItem.TabHeaderUnselectedStateContent>
                                            <tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                                                <Grid BackgroundColor="{StaticResource bg_purple}">
                                                    <Label 
                                                           FontSize="{OnPlatform WPF=20,Android=20,iOS=17}"
                                                           FontFamily="TTFirsNeue-Regular"
                                                           VerticalOptions="Center"
                                                           HorizontalOptions="Center"
                                                           TextColor="{StaticResource purple}"
                                                           Text="Кассовая техника" />
                                                </Grid>
                                            </tabview:CustomTabViewItem.TabHeaderSelectedStateContent>
                                            <tabview:CustomTabViewItem.TabContent>
                                                <Grid>

                                                    <FlexLayout
                                                        Margin="0,30,0,0"
                                                        x:Name="fiscalFlexLayout"
                                                        AlignContent="Start"
                                                        AlignItems="Start"
                                                        Direction="Row"
                                                        Wrap="Wrap">

                                                    </FlexLayout>

                                                    <Label
                                                          x:Name="noFiscalItemsLabel"
                                                          IsVisible="False"
                                                          VerticalOptions="Center"
                                                          HorizontalOptions="Center"
                                                          HorizontalTextAlignment="Center"
                                                          WidthRequest="300"
                                                          TextColor="{StaticResource dark_purple}"
                                                          FontFamily="TTFirsNeue-Regular"
                                                          Text="Нет доступных фискальных регистраторов. Добавить регистратор можно в web панели Mibu"
                                                          FontSize="14" />


                                                </Grid>
                                            </tabview:CustomTabViewItem.TabContent>
                                        </tabview:CustomTabViewItem>

                                    </tabview:CustomTabView>
                                    
      
                                </Grid>

                            </ScrollView>
                        </tabview:CustomTabViewItem.TabContent>
                    </tabview:CustomTabViewItem>

                </tabview:CustomTabView>

           

            </Grid>

            <StackLayout
                Grid.Row="2"
                Margin="{OnPlatform Default='0,0,0,0',WPF='0,0,0,12'}"
                HorizontalOptions="Center"
                VerticalOptions="End"
                Orientation="Horizontal">
                <Image
                    VerticalOptions="Center"
                    WidthRequest="16"
                    HeightRequest="16"
                    Source="{OnPlatform Default=info.png, WPF='pack://application:,,,/Images/info.png'}"/>
                <Button 
                    Command="{Binding Source={x:Reference this},Path=AboutSoftware}"
                    Style="{StaticResource transparent_btn}"
                    Text="Информация о программе"
                    VerticalOptions="Center"/>
            </StackLayout>

        </Grid>
    </ContentPage.Content>
</ContentPage>