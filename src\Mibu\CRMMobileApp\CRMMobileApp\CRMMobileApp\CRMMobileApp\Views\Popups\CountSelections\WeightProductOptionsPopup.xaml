﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="CRMMobileApp.Views.Popups.WeightProductOptionsPopup">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="450"
            HeightRequest="250"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="{OnPlatform Android=Start, iOS=Start, WPF=Center}"
            Margin="{OnPlatform Android='0,200,0,0',iOS='0,200,0,0',WPF='0,0,0,0'}"
            CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>


                <Grid 
                    Grid.Row="0"
                    Margin="0,20,0,0">

                    <Grid
                         Margin="40,0,50,0"
                         VerticalOptions="Start"
                         Padding="0"
                         HeightRequest="40">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Frame
                             Grid.Column="0"
                             Margin="0,0,0,0"
                             HasShadow="False"
                             Padding="0"
                             VerticalOptions="Center"
                             HorizontalOptions="Center"
                             IsClippedToBounds="True"
                             BackgroundColor="White"
                             Background="White"
                             CornerRadius="20"
                             WidthRequest="40"
                             HeightRequest="40">
                            <Image 
                              Source="{Binding Source={x:Reference this},Path=Product.Product.ImgPath}"
                              WidthRequest="40"
                              HeightRequest="40"/>
                        </Frame>

                        <Label
                             Grid.Column="1"
                             VerticalOptions="Center"
                             TextColor="{StaticResource dark_purple}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="20"
                             LineBreakMode="TailTruncation"
                             FontAttributes="Bold"
                             Text="{Binding Source={x:Reference this},Path=Product.Product.Title}"/>


                    </Grid>




                    <ImageButton 
                        Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        BackgroundColor="Transparent"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                       Padding="10"
                        WidthRequest="36"
                        HeightRequest="36"
                        Margin="0,0,10,0"/>
                </Grid>


                <StackLayout 
                    Grid.Row="1"
                    Orientation="Horizontal">


                    <Label
                        Margin="40,0,0,0"
                        VerticalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"
                        FontAttributes="Bold"
                        Text="Количество товара : "/>

                    <Label
                        VerticalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"
                        FontAttributes="Bold"
                        Text="{Binding Source={x:Reference this},Path=Amount}"/>

                    <ImageButton 
                        Margin="30,0,0,0"
                        Background="Transparent"
                        BackgroundColor="{StaticResource grayBg}"
                        Source="{OnPlatform Default=create_transaction.png, WPF='pack://application:,,,/Images/create_transaction.png'}"
                        Command="{Binding Source={x:Reference this},Path=OpenAmountKeyboard}"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        CornerRadius="15"
                        WidthRequest="30"
                        HeightRequest="30"/>

                </StackLayout>

                <Grid Grid.Row="2">

                    <StackLayout
                        VerticalOptions="End"
                        HorizontalOptions="End"
                        Margin="0,0,30,30"
                        Spacing="20"
                        Orientation="Horizontal">

                        <Button 
                            Command="{Binding Source={x:Reference this},Path=Close}"
                            Style="{StaticResource bg_purple_btn}"
                            VerticalOptions="Center"
                            Text="Отмена"
                            WidthRequest="170"
                            HeightRequest="40"/>
                        <Button 
                            Command="{Binding Source={x:Reference this},Path=AddToOrder}"
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalOptions="Center"
                            Text="Выбрать"
                            WidthRequest="170"
                            HeightRequest="40"/>

                    </StackLayout>

                </Grid>
            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>