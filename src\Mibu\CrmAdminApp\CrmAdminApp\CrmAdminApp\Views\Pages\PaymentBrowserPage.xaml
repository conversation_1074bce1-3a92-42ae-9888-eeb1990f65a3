﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             
             x:Class="CRMAdminMoblieApp.Views.Pages.PaymentBrowserPage">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <StackLayout>
            <StackLayout.Padding>
                <OnPlatform x:TypeArguments="Thickness">
                    <OnPlatform.Platforms>
                        <On Platform="iOS" Value="0, 0, 0, 0" />
                        <On Platform="Android" Value="0, 0, 0, 0" />
                    </OnPlatform.Platforms>
                </OnPlatform>
            </StackLayout.Padding>
            <Grid 
                HeightRequest="{OnPlatform Android='50',iOS='80'}"
                BackgroundColor="{DynamicResource blueColor}">
                <Button
                    Grid.Row="0"
                    Command="{Binding Source={x:Reference this},Path=GoBack}"
                    HorizontalOptions="Start"
                    VerticalOptions="End"
                    Margin="0,0,0,0"
                    ImageSource="LeftArrow.png"
                    FontAttributes="Bold"
                    TextColor="White"
                    Background="Transparent"
                    FontSize="{OnPlatform Android=19,iOS=14}"
                    Text="Mibu CRM"/>
            </Grid>
            <!--<WebView Source="google.com"></WebView>-->
        </StackLayout>
    </ContentPage.Content>
</ContentPage>