﻿using CRM.Models.Enums;
using CRM.Models.Network;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace CRMAdminMoblieApp.Helpers
{
    public static class Auth
    {
        public static NetworkTerminalUser User { get; set; }
        public static POSTerminal POSTerminal { get; set; }

        public static async Task<bool> AuthorizeToPOS(string domain,string login,string password)
        {
            POSTerminal = await MobileAPI.MainMethods.AuthorizeToBaristaApp(domain, login, password);
            if(POSTerminal != null)
            {
                ApplicationState.CurrentDomain = domain;
                ApplicationState.CurrentStore = POSTerminal.Store;
                ApplicationState.CurrentTradeNetwork = await MobileAPI.MainMethods.GetTradeNetworkByPOS(ApplicationState.CurrentDomain, POSTerminal.Id);

                await ApplicationState.LoadEquipment();
            }
            return POSTerminal != null;
        }
        public static async Task<bool> AuthorizeByPIN(string pin)
        {
            User = await MobileAPI.MainMethods.AuthorizeToBaristaAppByPin(ApplicationState.CurrentDomain, POSTerminal.Id, pin);
            return User != null;
        }
        public static bool CheckPinCode(string pin)
        {
            return User.PIN == pin;
        }

        public static async Task Logout()
        {
            User = null;
            POSTerminal = null;
          //  await App.Current.MainPage.Navigation.PopToRootAsync();
        }
        public static async Task LogoutUser()
        {
            User = null;
          //  for(int i= App.Current.MainPage.Navigation.NavigationStack.Count - 1; i > 1; i--)
            {
            //    var page = App.Current.MainPage.Navigation.NavigationStack[i];
             //   App.Current.MainPage.Navigation.RemovePage(page); 
            }
          //  await App.Current.MainPage.Navigation.PushAsync(new MainPage());
        }


        public static async Task<bool> CheckAllowance(AllowanceLevel allowance)
        {
            if (User.Role == NetworkTerminalUserRole.Admin) return true;
            if (allowance == AllowanceLevel.Allow) return true;
            if (allowance == AllowanceLevel.AllowUsingAdminPassword)
            {
                var popup = new AdminPasswordPopup();
                popup.ShowDialog();
                while (!popup.IsCompleted)
                {
                    await Task.Delay(200);
                }
                if (!popup.IsRightPassword)
                {
                    new OneButtonedPopup("Вы не имеете прав, чтобы совершить данную операцию", "Ошибка").ShowDialog();
                }
                return popup.IsRightPassword;
            }
            if (allowance == AllowanceLevel.Deny)
            {
                new OneButtonedPopup("Вы не имеете прав, чтобы совершить данную операцию", "Ошибка").ShowDialog();
                return false;
            }
            return false;
        }
    }
}
