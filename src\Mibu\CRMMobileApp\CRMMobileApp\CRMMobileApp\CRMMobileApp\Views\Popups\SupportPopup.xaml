﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.SupportPopup">
    <Grid>


        <Frame
              HasShadow="{OnPlatform WPF=True, Default=False}"
              BackgroundColor="White"
              Background="White"
              CornerRadius="20"
              Padding="0"
              HorizontalOptions="Center"
              VerticalOptions="{OnPlatform Android=Start, iOS=Start, WPF=Center}"
              Margin="{OnPlatform Android='0,200,0,0',iOS='0,200,0,0',WPF='0,0,0,0'}"
              WidthRequest="600"
              HeightRequest="300">
            <Grid BackgroundColor="Transparent">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="60"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Image 
                          Grid.Column="0"
                          Margin="20,20,0,0"
                          HorizontalOptions="Center"
                          VerticalOptions="Center"
                          Source="{OnPlatform Default=logo.png, WPF='pack://application:,,,/Images/logo.png'}"/>

                    <StackLayout
                           Grid.Column="1"
                           Spacing="2"
                           Margin="30,0,0,0"
                           HorizontalOptions="Center"
                           VerticalOptions="Center">
                        <Label 
                              FontFamily="TTFirsNeue-Regular"
                              TextColor="{StaticResource dark_purple}"
                              Text="Домен:"/>
                        <Label 
                              x:Name="domainLabel"
                              FontFamily="TTFirsNeue-Regular"
                              FontAttributes="Bold"
                              FontSize="16"
                              TextColor="{StaticResource dark_purple}"
                              Text="XXXXXXXXXXXXXXXX"/>
                        <Label 
                              FontFamily="TTFirsNeue-Regular"
                              Margin="0,10,0,0"
                              TextColor="{StaticResource dark_purple}"
                              Text="Точка продаж:"/>
                        <Label 
                              x:Name="storeLabel"
                              FontFamily="TTFirsNeue-Regular"
                              FontAttributes="Bold"
                              FontSize="16"
                              TextColor="{StaticResource dark_purple}"
                              Text="XXXXXXXXXXXXXXXX"/>
                        <Label 
                              FontFamily="TTFirsNeue-Regular"
                              Margin="0,10,0,0"
                              TextColor="{StaticResource dark_purple}"
                              Text="Адрес:"/>
                        <Label 
                              x:Name="addressLabel"
                              FontFamily="TTFirsNeue-Regular"
                              FontAttributes="Bold"
                              FontSize="16"
                              TextColor="{StaticResource dark_purple}"
                              Text="XXXXXXXXXXXXXXXX"/>
                    </StackLayout>


                    <Grid Grid.Column="2">
                        <ImageButton 
                              Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                              Command="{Binding Source={x:Reference this},Path=Close}"
                              BackgroundColor="Transparent"
                              VerticalOptions="Start"
                              HorizontalOptions="End"
                              Padding="10"
                              WidthRequest="35"
                              HeightRequest="35"
                              Margin="0,10,10,0"/>
                    </Grid>
                </Grid>

                <Grid Grid.Row="1"
                    BackgroundColor="{DynamicResource blueColor}">
                    <Label 
                          FontFamily="TTFirsNeue-Regular"
                          Margin="40,0,0,0"
                          TextColor="White"
                          FontSize="20"
                          HorizontalOptions="Start"
                          VerticalOptions="Center"
                          Text="Телефон поддержки:"/>
                    <Label 
                          FontFamily="TTFirsNeue-Regular"
                          Margin="0,0,40,0"
                          TextColor="White"
                          FontAttributes="Bold"
                          FontSize="22"
                          HorizontalOptions="End"
                          VerticalOptions="Center"
                          Text="8 800 555 35 35"/>
                </Grid>
            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>