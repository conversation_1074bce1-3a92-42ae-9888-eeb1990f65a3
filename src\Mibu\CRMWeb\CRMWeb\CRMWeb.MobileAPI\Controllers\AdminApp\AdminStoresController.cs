﻿using CRM.Database.Core;
using CRM.Models;
using CRM.Models.Network;
using CRM.Models.Network.Finances;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.AdminApp
{
    [ApiController]
    [Route("[controller]")]
    public class AdminStoresController : AbsController
    {
        public AdminStoresController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpPost, Route("CreateStore")]
        public async Task<Store> CreateStore(string domain,int networkId, Store store)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks.Include(o => o.FinanceCategories)
                                                  .FirstOrDefault(o => o.Id == networkId);


                    var cashAcc = new Account()
                    {
                        Title = $"Наличные {store.CommonStoreSettings.Title}",
                        Balance = 0,
                        Type = CRM.Models.Enums.AccountType.Cash
                    };
                    var cardAcc = new Account()
                    {
                        Title = $"Карта {store.CommonStoreSettings.Title}",
                        Balance = 0,
                        Type = CRM.Models.Enums.AccountType.Cash
                    };

                    network.Accounts.Add(cashAcc);
                    network.Accounts.Add(cardAcc);

                    store.AccountForCash = new CRM.Models.Stores.StoreAccount
                    { 
                        Account = cashAcc,
                        FinanceCategory = network.FinanceCategories.FirstOrDefault(o => o.IsDefault),
                    };
                    store.AccountForCard = new CRM.Models.Stores.StoreAccount
                    {
                        Account = cardAcc,
                        FinanceCategory = network.FinanceCategories.FirstOrDefault(o => o.IsDefault),
                    };


                    network.Stores.Add(store);

                    db.TradeNetworks.Update(network);
                    db.SaveChanges();

                    return store;
                }
                catch { return null; }
            }
        }
        [HttpPut, Route("UpdateStore")]
        public async Task<Store> UpdateStore(string domain, Store store)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    db.Stores.Update(store);
                    db.SaveChanges();

                    return store;
                }
                catch { return null; }
            }
        }
        [HttpDelete, Route("DeleteStore")]
        public async Task DeleteStore(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores.FirstOrDefault(o => o.Id == storeId);
                    store.IsDeleted = true;

                    db.Stores.Update(store);
                    db.SaveChanges();
                }
                catch { }
            }
        }
    }
}
