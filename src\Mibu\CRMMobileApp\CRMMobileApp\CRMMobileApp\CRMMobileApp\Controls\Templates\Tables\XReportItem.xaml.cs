﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class XReportItem : ContentView
    {
        public XReportItem()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty TextFontSizeProperty =
            BindableProperty.Create(nameof(TextFontSize), typeof(double), typeof(XReportItem),20.0d);
        public double TextFontSize
        {
            get { return (double)GetValue(TextFontSizeProperty); }
            set { SetValue(TextFontSizeProperty, value); }
        }



        public static readonly BindableProperty FirstColumnProperty =
         BindableProperty.Create(nameof(FirstColumn), typeof(string), typeof(XReportItem));
        public string FirstColumn
        {
            get { return (string)GetValue(FirstColumnProperty); }
            set { SetValue(FirstColumnProperty, value); }
        }
        public static readonly BindableProperty SecondColumnProperty =
            BindableProperty.Create(nameof(SecondColumn), typeof(string), typeof(XReportItem));
        public string SecondColumn
        {
            get { return (string)GetValue(SecondColumnProperty); }
            set { SetValue(SecondColumnProperty, value); }
        }
    }
}