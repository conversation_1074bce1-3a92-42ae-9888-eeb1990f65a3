﻿using CRMMobileApp.Controls.Parts.TabView;
using DevExpress.Data.Native;
using DevExpress.XamarinForms.CollectionView;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Internals;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.ListViews
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CustomCrossCollectionView : ContentView
    {
        public CustomCrossCollectionView()
        {
            InitializeComponent();
        }

        

        public static readonly BindableProperty MinItemSizeProperty = BindableProperty.Create(nameof(MinItemSize), typeof(int), typeof(CustomCrossCollectionView));
        public int MinItemSize
        {
            get { return (int)GetValue(MinItemSizeProperty); }
            set { SetValue(MinItemSizeProperty, value); }
        }

        public static readonly BindableProperty ItemSpacingProperty = BindableProperty.Create(nameof(ItemSpacing), typeof(double), typeof(CustomCrossCollectionView));
        public double ItemSpacing
        {
            get { return (double)GetValue(ItemSpacingProperty); }
            set { SetValue(ItemSpacingProperty, value); }
        }

        public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameof(ItemsSource), typeof(IEnumerable), typeof(CustomCrossCollectionView),propertyChanged: delegate (BindableObject x, object oldValue, object newValue)
        {
            //var thisControl = (x as CustomCrossCollectionView);
            //thisControl.RenderList();
        });
        public IEnumerable ItemsSource
        {
            get { return (IEnumerable)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }



        public static readonly BindableProperty ItemTemplateForMobileProperty = BindableProperty.Create(nameof(ItemTemplateForMobile), typeof(DataTemplate), typeof(CustomCrossCollectionView));
        public DataTemplate ItemTemplateForMobile
        {
            get { return (DataTemplate)GetValue(ItemTemplateForMobileProperty); }
            set { SetValue(ItemTemplateForMobileProperty, value); }
        }
        public static readonly BindableProperty ItemTemplateForWPFProperty = BindableProperty.Create(nameof(ItemTemplateForWPF), typeof(DataTemplate), typeof(CustomCrossCollectionView));
        public DataTemplate ItemTemplateForWPF
        {
            get { return (DataTemplate)GetValue(ItemTemplateForWPFProperty); }
            set { SetValue(ItemTemplateForWPFProperty, value); }
        }



        public static readonly BindableProperty SelectionModeProperty = BindableProperty.Create(nameof(SelectionMode), typeof(SelectionMode), typeof(CustomCrossCollectionView), SelectionMode.None);
        public SelectionMode SelectionMode
        {
            get { return (SelectionMode)GetValue(SelectionModeProperty); }
            set { SetValue(SelectionModeProperty, value); }
        }




        //private void RenderList()
        //{
        //    if (Device.RuntimePlatform == Device.WPF)
        //    {
        //        RenderListForWPF();
        //    }
        //    else if (Device.RuntimePlatform == Device.Android || Device.RuntimePlatform == Device.iOS)
        //    {
        //        RenderListForMobile();
        //    }
        //}

        //private void RenderListForMobile()
        //{
        //    forAndroidIOSlistView.ItemTemplate = this.ItemTemplateForMobile;
        //    forAndroidIOSlistView.ItemsSource = this.ItemsSource;
        //}
        //private void RenderListForWPF()
        //{
        //    try
        //    {
        //        forWPFlistView.ItemTemplate = this.ItemTemplateForWPF;
        //        forWPFlistView.ItemsSource = this.ItemsSource;

        //        var selectionMode = ListViewSelectionMode.None;
        //        switch (this.SelectionMode)
        //        {
        //            case SelectionMode.None:
        //                selectionMode = ListViewSelectionMode.None;
        //                break;
        //            case SelectionMode.Single:
        //            case SelectionMode.Multiple:
        //                selectionMode = ListViewSelectionMode.Single;
        //                break;
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //    }



        //}
    }

  
}