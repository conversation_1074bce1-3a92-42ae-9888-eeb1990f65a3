﻿<UserControl x:Class="CRMDesktopApp.Controls.Modifiers.OneOptionModifierOptionItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Modifiers"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450" 
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="6*"/>
            <ColumnDefinition Width="2.5*"/>
            <ColumnDefinition Width="1.5*"/>
        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0">
            <TextBlock 
                Text="{Binding ElementName=this,Path=Model.ModifierOption.Title}"
                FontSize="16"
                Foreground="{StaticResource dark_purple}"
                FontFamily="TTFirsNeue-Regular"
                Margin="15,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"/>
        </Grid>

        <Grid Grid.Column="1">
            <TextBlock      
                FontSize="16"
                Foreground="{StaticResource dark_purple}"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                Margin="5,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center">
                <Run Text="{Binding ElementName=this,Path=Model.ModifierOption.Price}"/>
                <Run Text=" р"/>
            </TextBlock>
        </Grid>


        <Grid Grid.Column="2">
            <RadioButton 
                x:FieldModifier="public"
                x:Name="radioButton"
                Margin="0,0,15,0"
                Background="Transparent"
                BorderBrush="{StaticResource dark_purple}"
                Foreground="{StaticResource dark_purple}"
                Checked="onChecked"
                Unchecked="onChecked"
                VerticalAlignment="Center"
                HorizontalAlignment="Right"/>
        </Grid>

    </Grid>
</UserControl>
