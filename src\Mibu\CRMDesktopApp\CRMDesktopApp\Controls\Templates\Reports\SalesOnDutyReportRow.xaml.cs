﻿using CRMMobileApp.Models.Reports;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Reports
{
    /// <summary>
    /// Логика взаимодействия для SalesOnDutyReportRow.xaml
    /// </summary>
    public partial class SalesOnDutyReportRow : UserControl
    {
        public SalesOnDutyReportRow()
        {
            InitializeComponent();
        }
        public static readonly DependencyProperty ModelProperty =
         DependencyProperty.Register(nameof(Model), typeof(SalesOnDutyRow), typeof(SalesOnDutyReportRow));
        public SalesOnDutyRow Model
        {
            get { return (SalesOnDutyRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
