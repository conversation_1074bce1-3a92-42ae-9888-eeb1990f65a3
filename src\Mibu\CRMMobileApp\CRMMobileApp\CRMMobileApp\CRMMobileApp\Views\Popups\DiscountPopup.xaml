﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:effects="http://sharpnado.com"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates" xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
    x:Class="CRMMobileApp.Views.Popups.DiscountPopup">

    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="670"
            HeightRequest="310"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="{OnPlatform Android=Start, iOS=Start, WPF=Center}"
            Margin="{OnPlatform Android='0,200,0,0',iOS='0,200,0,0',WPF='0,0,0,0'}"
            CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>


                <Grid 
                    Grid.Row="0"
                    Margin="30,20,0,0">
                    <StackLayout Orientation="Horizontal">
                        <Image 
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Source="{OnPlatform Default=discount.png, WPF='pack://application:,,,/Images/discount.png'}"
                            WidthRequest="35"
                            HeightRequest="30"/>
                        <Label
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="18"
                            VerticalOptions="Center"
                            FontAttributes="Bold"
                            Text="Скидка"/>
                    </StackLayout>


                    <ImageButton 
                        Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        BackgroundColor="Transparent"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Padding="10"
                        WidthRequest="36"
                        HeightRequest="36"
                        Margin="0,5,10,0"/>
                </Grid>





                <Grid 
                    Grid.Row="1">


                    <listviews:CustomCrossHorizontalListView
                          x:Name="listView"
                          ItemHeight="110"
                          ItemWidth="130"
                          ItemSpacing="10"
                          Margin="35,0,0,0"
                          TapCommand="{Binding Source={x:Reference this},Path=OnDiscountSelected}">
                        <listviews:CustomCrossHorizontalListView.ItemTemplateForWPF>
                            <DataTemplate>
                                <ViewCell>
                                    <templates:DiscountCard 
                                        WidthRequest="110"
                                        HeightRequest="130"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Start"
                                        Model="{Binding}"/>
                                </ViewCell>
                            </DataTemplate>
                        </listviews:CustomCrossHorizontalListView.ItemTemplateForWPF>
                        <listviews:CustomCrossHorizontalListView.ItemTemplateForMobile>
                            <DataTemplate>
                                <ViewCell>
                                    <templates:DiscountCard 
                                        Margin="0,0,10,0"
                                        WidthRequest="110"
                                        HeightRequest="130"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Start"
                                        Model="{Binding}"/>
                                </ViewCell>
                            </DataTemplate>
                        </listviews:CustomCrossHorizontalListView.ItemTemplateForMobile>
                    </listviews:CustomCrossHorizontalListView>


                    <Label
                         x:Name="noItemsLabel"
                         IsVisible="False"
                         VerticalOptions="Center"
                         HorizontalOptions="Center"
                         HorizontalTextAlignment="Center"
                         WidthRequest="300"
                         TextColor="{StaticResource dark_purple}"
                         FontFamily="TTFirsNeue-Regular"
                         Text="По данный момент скидки не заданы. Настроить скидки можно в web-панели Mibu в разделе программ лояльности"
                         FontSize="14" />


                </Grid>



                <Grid Grid.Row="2">

                    <StackLayout
                        VerticalOptions="End"
                        HorizontalOptions="End"
                        Margin="0,0,30,30"
                        Spacing="20"
                        Orientation="Horizontal">

                        <Button 
                            x:Name="useBtn"
                            Command="{Binding Source={x:Reference this},Path=ApplyDiscount}"
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalOptions="Center"
                            Text="Применить"
                            WidthRequest="170"
                            HeightRequest="40"/>

                    </StackLayout>

                </Grid>
            </Grid>
        </Frame>

    </Grid>
    
</animations:PopupPage>