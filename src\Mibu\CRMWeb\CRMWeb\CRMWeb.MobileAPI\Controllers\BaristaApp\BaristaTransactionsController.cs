﻿using CRM.Database.Core;
using CRM.Models.Enums;
using CRM.Models.Enums.Statuses;
using CRM.Models.Network;
using CRM.Models.Network.Finances;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;


namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaTransactionsController : AbsController
    {
        public BaristaTransactionsController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpGet, Route("GetTransactions")]
        public List<Transaction> GetTransactions(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Transactions).ThenInclude(o => o.Category)
                        .Include(o => o.Transactions).ThenInclude(o => o.FromAccount)
                        .Include(o => o.Transactions).ThenInclude(o => o.ToAccount)
                        .Include(o => o.Transactions).ThenInclude(o => o.Category)
                        .Include(o => o.Transactions).ThenInclude(o => o.User)
                        .Where(o => !o.IsDeleted)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.Transactions;
                }
                catch { return new List<Transaction>(); }
            }
        }
        [HttpPost, Route("CreateTransaction")]
        public async Task<Transaction> CreateTransaction(string domain, int networkId, int storeId, Transaction transaction)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks       
                        .Include(o => o.NetworkNotifications)
                        .FirstOrDefault(o => o.Id == networkId);
                    var store = db.Stores
                        .Include(o => o.CommonStoreSettings)
                        .Include(o => o.AccountForCard).ThenInclude(o => o.Account)
                        .Include(o => o.AccountForCash).ThenInclude(o => o.Account)
                        .FirstOrDefault(o => o.Id == storeId);


                    transaction.ToAccountId = store.AccountForCash.AccountId;
                    transaction.CategoryId = store.AccountForCash.FinanceCategoryId;
                    transaction.TransactionDate = DateTime.UtcNow;
                    transaction.Type = TransactionType.Cassa;
                    transaction.TradeNetworkId = networkId;


                    if (transaction.Operation == FinanceOperation.Expense 
                        || transaction.Operation == FinanceOperation.Incassaction)
                    {
                        transaction.Sum = -transaction.Sum;

                        store.AccountForCash.Account.Balance -= transaction.Sum;
                    }
                    else if(transaction.Operation == FinanceOperation.Income)
                    {
                        store.AccountForCash.Account.Balance += transaction.Sum;
                    }


                    if (transaction.Operation == FinanceOperation.Incassaction)
                    {
                        network.NetworkNotifications.Add(new NetworkNotification
                        {
                            Category = NotificationCategory.Incassation,
                            Description = store.CommonStoreSettings.Title,
                            CreatedAt = DateTime.UtcNow,
                        });
                        db.TradeNetworks.Update(network);  
                    }

                    store.Transactions.Add(transaction);


                    db.Stores.Update(store);
                    await db.SaveChangesAsync();

                    return transaction;
                }
                catch { return null; }
            }
        }
    }
}
