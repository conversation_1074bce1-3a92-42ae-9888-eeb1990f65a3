﻿using CRM.Models.Enums.General;
using CRMAdminMoblieApp.Helpers;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using CRMDesktopApp.Views.Pages.Delivery;
using CRMDesktopApp.Views.Pages;
using CRMDesktopApp.Views.Pages.Orders;
using CRMDesktopApp;
using System.Windows.Controls;

namespace CRMMobileApp.Helpers
{
    public static class NavigationHelper
    {
        public static async Task GoBackIfCafe()
        {
            var frame = (App.Current.MainWindow as MainWindow).frame;
            if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe)
            {        
                if (OrdersHelper.FromTable != null && OrdersHelper.CurrentOrder.Delivery == null)
                {       
                    while (frame.NavigationService.CanGoBack)
                    {
                        if (frame.NavigationService.Content is not HallPage)
                        {
                            frame.NavigationService.RemoveBackEntry();
                        }
                        else
                        {
                            break;
                        }
                    
                    }
                    OrdersHelper.FromTable = null;
                }
                else if (OrdersHelper.CurrentOrder.Delivery != null)
                {
                    while (frame.NavigationService.Content is not DeliveriesPage && frame.NavigationService.CanGoBack)
                    {
                        frame.NavigationService.RemoveBackEntry();
                    }  
                }
            }
            else
            {
                frame.NavigationService.GoBack();
            }
        }
    }
}
