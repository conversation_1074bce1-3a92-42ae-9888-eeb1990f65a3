/* here you can put your own css to customize and override the theme */
.portlet:focus {
    outline: none!important;
}
.portlet.bordered tr.ui-widget-header {
    background: white;
    color: #333333;
}
.portlet tr.ui-widget-header {
    background: #f1f4f7;
    color: #333333;
}
.portlet:not(.bordered) tr.ui-widget-header th {
    /*font-weight: 400;*/
    padding: 3px 0!important;
}
.actions:not(.bordered) [role="btn"] {
    font-size: 18px;
    display: inline-block;
    height: 18px;
    margin-right: 5px;
    margin-top: 8px;
    opacity: 1;
    color: #777777;
    cursor: pointer;
}
.portlet table [data-act] {
    opacity: 1;
    font-size: 15px;
    cursor: pointer;
}
.actions:not(.bordered) [role="btn"]:hover,  .portlet table [data-act]:hover {
    opacity: .4;
    transition-property: opacity;
    transition-duration: .3s;
}