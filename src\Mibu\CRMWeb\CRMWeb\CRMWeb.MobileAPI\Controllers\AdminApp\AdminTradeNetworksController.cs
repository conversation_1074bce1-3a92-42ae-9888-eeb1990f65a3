﻿using CRM.Database.Core;
using CRM.Models.Network;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.AdminApp
{
    [ApiController]
    [Route("[controller]")]
    public class AdminTradeNetworksController : AbsController
    {
        public AdminTradeNetworksController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpPost, Route("CreateTradeNetwork")]
        public async Task<TradeNetwork> CreateTradeNetwork(string domain, TradeNetwork network)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    db.TradeNetworks.Add(network);
                    db.SaveChanges();

                    return network;
                }
                catch { return null; }
            }
        }
        [HttpPut, Route("UpdateTradeNetwork")]
        public async Task<TradeNetwork> UpdateTradeNetwork(string domain, TradeNetwork network)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    db.TradeNetworks.Update(network);
                    db.SaveChanges();

                    return network;
                }
                catch { return null; }
            }
        }
        [HttpDelete, Route("DeleteTradeNetwork")]
        public async Task DeleteTradeNetwork(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks.Include(o => o.Stores)
                                                  .FirstOrDefault(o => o.Id == networkId);
                    network.IsDeleted = true;

                    foreach(var store in network.Stores)
                    {
                        store.IsDeleted = true;
                    }

                    db.TradeNetworks.Update(network);
                    db.SaveChanges();
                }
                catch { }
            }
        }
    }
}
