﻿using CRM.Models;
using CRM.Models.Network;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Models.Wrappers;
using CRMAdminMoblieApp.Views.Pages;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMMobileApp.Core;
using MarkupCreator.Helpers.Converters;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NetworksPopup : PopupPage
    {
        public NetworksPopup()
        {
            InitializeComponent();
            TradeNetwork = ApplicationState.CurrentTradeNetwork;

            foreach(var store in TradeNetwork.Stores)
            {
                Stores.Add(new StoreWrapper { Store = store });
			}
        }
        private TradeNetwork tradeNetwork;
        public TradeNetwork TradeNetwork
        {
            get => tradeNetwork;
            set { tradeNetwork = value; OnPropertyChanged(nameof(TradeNetwork)); }
        }
        

        private List<StoreWrapper> stores = new List<StoreWrapper>();
        public List<StoreWrapper> Stores
        {
            get => stores;
            set { stores = value; OnPropertyChanged(nameof(Stores)); }
        }



        private StoreWrapper selectedStore;
        public StoreWrapper SelectedStore
        {
            get => selectedStore;
            set { selectedStore = value; OnPropertyChanged(nameof(SelectedStore)); }
        }

        private ICommand itemSelected;
        public ICommand ItemSelected
        {
            get => itemSelected ??= new RelayCommand(async obj =>
            {
                SelectedStore = obj as StoreWrapper;
                if (SelectedStore != null)
                    ApplicationState.CurrentStore = SelectedStore.Store;

                SelectedStore.IsSelected = true;

                var otherDuties = Stores.Where(o => o != obj);
                foreach (var otherDuty in otherDuties)
                    otherDuty.IsSelected = false;
            });
        }





        private ICommand changeNetwork;
        public ICommand ChangeNetwork
        {
            get => changeNetwork ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushAsync(new TradeNetworks());
            });
        }



        public event EventHandler<Store> StoreSet;
        private ICommand setStore;
        public ICommand SetStore
        {
            get => setStore ??= new RelayCommand(async obj =>
            {
                if(SelectedStore == null)
                {
                    DisplayAlert("Уведомление", "Необходимо выбрать точку", "Ок");
                    return;
                }

                ApplicationState.CurrentStore = SelectedStore.Store;
                await ApplicationState.GetDuty();

                StoreSet?.Invoke(this, SelectedStore.Store);
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

    }
}