﻿using CRM.Models;
using CRM.Models.Core;
using CRM.Models.Gamification;
using CRM.Models.Gamification.General;
using CRM.Models.Network;
using CRM.Models.Stores;
using CrmAdminApp.Helpers;
using CRMGamificationAPIWrapper;
using CRMMoblieApiWrapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CRMAdminMoblieApp.Helpers
{
    internal static class ApplicationState
    {
        public static string CurrentDomain { get; set; }
        public static UserDomainSubscription Subscription { get; set; }

        public static TradeNetwork CurrentTradeNetwork { get; set; }
        public static Store CurrentStore { get; set; }


        public static Duty CurrentDuty { get; set; }
        public static List<Duty> Duties { get; set; } = new List<Duty>();



        //public static List<Store> SelectedStoresInGamification { get; set; } = new List<Store>();
        //public static List<int> SelectedStoresInGamificationIds => SelectedStoresInGamification.Select(o => o.Id).ToList();

        public static GamificationSettings GamificationSettings { get; set; }



        public async static Task GetDuty()
        {
            Duties = await MobileAPI.BaristaMethods.BaristaDutiesMethods.GetDuties(CurrentDomain, CurrentStore.Id);
            CurrentDuty = await MobileAPI.BaristaMethods.BaristaDutiesMethods.GetCurrentDuty(CurrentDomain, CurrentStore.Id);
            if(CurrentDuty == null)
            {
                CurrentDuty = new Duty();
            }
            OnDutyChanged?.Invoke(null, CurrentDuty);
        }

        public static event EventHandler<Duty> OnDutyChanged;
        public async static Task SetDuty(Duty duty)
        {
            CurrentDuty = duty;
            if (duty is null) return;
            OnDutyChanged?.Invoke(null, duty); 
        }


        #region Сохранение и загрузка
        public async static Task LoadData()
        {
            var data = GetFromFile<SavedData>(SavedData.FilePath);

            CurrentDomain = data.CurrentDomain;
            if (!string.IsNullOrEmpty(CurrentDomain))
            {
                Subscription = await MobileAPI.MainMethods.GetDomainSubscription(CurrentDomain);
            }

            

            if (data.CurrentTradeNetworkId != null)
            {
                CurrentTradeNetwork = await MobileAPI.MainMethods.GetTradeNetwork(CurrentDomain, (int)data.CurrentTradeNetworkId);
            }
            if(data.CurrentStoreId != null)
            {
                CurrentStore = await MobileAPI.MainMethods.GetStore(CurrentDomain, (int)data.CurrentStoreId);
                //SelectedStoresInGamification = new List<Store> { CurrentStore };
            }
            
            if(data.NetworkAdminTabUserToken != null)
            {
                var user = await MobileAPI.AdminMethods.AdminProfileMethods.GetProfileBySessionToken(CurrentDomain, data.NetworkAdminTabUserToken);
                await Auth.UpdateUser(user);

                if (data.GamificationUserId != null && user != null)
                {
                    var gamificationUser = await GamificationAPI.Barista.Profile.GetProfileByAdminTabCRMUser(CurrentDomain, user.Id);
                    Auth.GamificationUser = gamificationUser;
                }
            }          
        }
        public static void SaveChangesToMemory()
        {
            var data = new SavedData()
            {
                CurrentDomain = CurrentDomain,
                CurrentStoreId = CurrentStore?.Id,
                CurrentTradeNetworkId = CurrentTradeNetwork?.Id,

                NetworkAdminTabUserToken = Auth.User?.SessionToken,
                GamificationUserId = Auth.GamificationUser?.Id
            };

            SaveToFile(data, SavedData.FilePath);
        }


        public static T GetFromFile<T>(string filepath) where T : class, new()
        {
            T obj = null;
            if (File.Exists(filepath))
            {
                using (StreamReader file = File.OpenText(filepath))
                {
                    try
                    {
                        var json = file.ReadToEnd();
                        obj = JsonConvert.DeserializeObject<T>(json);

                    }
                    catch { }
                }
            }
            if (obj is null) obj = new T();
            return obj;
        }
        public static void SaveToFile(object file, string filepath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(file);
                File.WriteAllText(filepath, json);
            }
            catch { }
        }

        #endregion
    }
}
