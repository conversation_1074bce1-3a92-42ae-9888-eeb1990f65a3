﻿using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates.Orders;
using CRMDesktopApp.Views.Pages.Orders;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.Halls
{
    /// <summary>
    /// Логика взаимодействия для TableCurrentOrdersPopup.xaml
    /// </summary>
    public partial class TableCurrentOrdersPopup : BaseWindow
    {
        private AbsTable table;
        public AbsTable Table
        {
            get => table;
            set { table = value; OnPropertyChanged(nameof(Table)); }
        }
        public TableCurrentOrdersPopup(AbsTable table)
        {
            InitializeComponent();
            Table = table;
            LoadData();
        }

        private async void LoadData()
        {
            ordersStackPanel.Children.Clear();
            foreach (var order in Table.TableOrders)
            {
                var control = new TableOrderTemplate(order)
                {
                    Height = 38,
                    Margin = new Thickness(0, 4, 0, 0)
                };
                control.ItemTapped += Control_ItemTapped;
                ordersStackPanel.Children.Add(control);
            }
        }
        private async void Control_ItemTapped(object sender, CRM.Models.Stores.Order e)
        {
            this.Close();

            OrdersHelper.FromTable = Table;

            OrdersHelper.CurrentOrder = e;
            (App.Current.MainWindow as MainWindow).frame.NavigationService.Navigate(new CategoriesPage());
        }



        private ICommand createOrder;
        public ICommand CreateOrder
        {
            get => createOrder ??= new RelayCommand(async obj =>
            {
                this.Close();
                new GuestsCountPopup(Table).ShowDialog();
            });
        }

    }
}
