﻿using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Models;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Orders
{
    /// <summary>
    /// Логика взаимодействия для CreateOrder.xaml
    /// </summary>
    public partial class OrderPrepaymentPage : BasePage
    {
        private Order Order { get; set; }
        private NewDeliveryPopup _ownerPopup;
        public OrderPrepaymentPage(Order order, NewDeliveryPopup ownerPopup)
        {
            InitializeComponent();
            Loaded += OrderPrepaymentPage_Loaded;

            Order = order;
            _ownerPopup = ownerPopup;
        }

        private async void OrderPrepaymentPage_Loaded(object sender, RoutedEventArgs e)
        {
            keyboard.ButtonTapped += Keyboard_ButtonTapped;

            var types = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetPaymentTypes(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);

            PaymentTypes = new ObservableCollection<PaymentTypeWrapper>();
            foreach (var type in types)
            {
                try
                {
                    var wrapper = new PaymentTypeWrapper();
                    wrapper.PaymentType = type;
                    wrapper.Title = type.Title;
                    wrapper.ImgPath = type.ImgPath;
                    PaymentTypes.Add(wrapper);
                }
                catch (Exception ex)
                {

                }
            }
        }

        public double CurrentSum => PaymentTypes.Sum(o => o.Sum);



        private PaymentTypeWrapper selectedPaymentType;
        public PaymentTypeWrapper SelectedPaymentType
        {
            get => selectedPaymentType;
            set
            { 
                selectedPaymentType = value;
                OnPropertyChanged(nameof(SelectedPaymentType));

                foreach (var discount in PaymentTypes)
                {
                    discount.IsSelected = false;
                }

                if (selectedPaymentType != null)
                {
                    selectedPaymentType.IsSelected = true;
                }
            }
        }
        private ICommand onSelectedPaymentType;
        public ICommand OnSelectedPaymentType
        {
            get => onSelectedPaymentType ??= new RelayCommand(async obj =>
            {
               
            });
        }
        private ObservableCollection<PaymentTypeWrapper> paymentTypes;
        public ObservableCollection<PaymentTypeWrapper> PaymentTypes
        {
            get => paymentTypes;
            set { paymentTypes = value; OnPropertyChanged(nameof(PaymentTypes)); }
        }



        private ICommand addSumBtnPressed;
        public ICommand AddSumBtnPressed
        {
            get => addSumBtnPressed ??= new RelayCommand(async obj =>
            {
                int val = Convert.ToInt32(obj.ToString());
                if (SelectedPaymentType != null)
                {
                    var sum = SelectedPaymentType.Sum + val;
                    SelectedPaymentType.SumStr = sum.ToString();
                }
            });
        }

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (SelectedPaymentType is null) return;

            if (e == "backspace" && SelectedPaymentType.SumStr.Length > 0)
            {
                SelectedPaymentType.SumStr = SelectedPaymentType.SumStr.Remove(SelectedPaymentType.SumStr.Length - 1, 1);
            }
            if (e == "close")
            {
                SelectedPaymentType.SumStr = "0";
            }
            if (e != "backspace")
            {
                if (e == "." && SelectedPaymentType.SumStr.Contains(".")) return;
                if (SelectedPaymentType.SumStr == "0")
                {
                    SelectedPaymentType.SumStr = e;
                }
                else
                {
                    SelectedPaymentType.SumStr += e;
                }

            }
        }



        private ICommand addPrepayment;
        public ICommand AddPrepayment
        {
            get => addPrepayment ??= new RelayCommand(async obj =>
            {
                var payments = PaymentTypes.Where(o => o.Sum > 0);
                foreach (var payment in payments)
                {
                    Order.OrderPayments.Add(new CRM.Models.Stores.OrderPayment
                    {
                        PaymentType = payment.PaymentType,
                        Sum = payment.Sum,
                        IsPrepeyment = true
                    });
                }
                NavigationService.GoBack();

                _ownerPopup.SetPrepaymentSumText();
                _ownerPopup.Visibility = Visibility.Visible;
            });
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                NavigationService.GoBack();
                _ownerPopup.ShowDialog();
                _ownerPopup.SetPrepaymentSumText();
            });
        }


    }
}
