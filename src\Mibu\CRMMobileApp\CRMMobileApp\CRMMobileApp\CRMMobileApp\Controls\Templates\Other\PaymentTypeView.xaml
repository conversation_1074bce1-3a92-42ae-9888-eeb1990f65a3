﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             x:Name="this"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             x:Class="CRMMobileApp.Controls.Templates.Other.PaymentTypeView">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            Padding="0"
            CornerRadius="10"
            BackgroundColor="Transparent">
            <Frame.Style>
                <Style TargetType="Frame">
                    <Style.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="True">
                            <Setter Property="BorderColor" Value="{StaticResource purple}"></Setter>
                        </DataTrigger>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}" Value="False">
                            <Setter Property="BorderColor" Value="{StaticResource text_gray}"></Setter>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Frame.Style>
            <Grid
                HorizontalOptions="Center"
                VerticalOptions="Center">
                <StackLayout>
                    <Image 
                        HorizontalOptions="Center"
                        Source="{Binding Source={x:Reference this},Path=Model.ImgPath}"
                        Aspect="Fill"
                        HeightRequest="40"
                        WidthRequest="40"/>
                    <Label
                        HorizontalOptions="Center"
                        TextColor="{StaticResource text_gray}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        Text="{Binding Source={x:Reference this},Path=Model.Title}"/>
                    <custom:AndroidStyleEntry  
                        IsReadOnly="True"
                        InputTransparent="True"
                        TextColor="{StaticResource text_gray}"
                        HorizontalOptions="Center"
                        WidthRequest="80"
                        VerticalOptions="Start"
                        HeightRequest="41"
                        Text="{Binding Source={x:Reference this},Path=Model.SumStr,Mode=TwoWay}"
                        HorizontalTextAlignment="Center"/>
                </StackLayout>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>