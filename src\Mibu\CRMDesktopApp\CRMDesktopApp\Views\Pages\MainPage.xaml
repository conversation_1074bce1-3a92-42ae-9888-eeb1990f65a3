﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages"
      xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
      xmlns:controls1="clr-namespace:CRMDesktopApp.Controls"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="MainPage">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid Background="{StaticResource bg_purple}">
        <Image
            Stretch="Fill"
            Source="main_decor.png"/>

        <customcontrols:ImageButton 
            Command="{Binding ElementName=this,Path=GoBack}"
            HorizontalAlignment="Left"
            VerticalAlignment="Top"
            Source="pack://application:,,,/Resources/Images/arrowBack.png"
            Width="10"
            Height="20"
            Background="Transparent"
            Margin="30,40,0,0"/>


        <StackPanel     
            HorizontalAlignment="Center">
            <Image
                Margin="0,50,0,0"
                HorizontalAlignment="Center"
                Source="pack://application:,,,/Resources/Images/logo.png"
                Height="120"
                Width="120"/>

            <TextBlock
                FontSize="25"
                Margin="35,0,0,0"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                HorizontalAlignment="Center"
                Text="Введите пин-код для входа"/>
            <StackPanel
                Margin="35,0,0,0"
                HorizontalAlignment="Center"
                Orientation="Horizontal">
                <customcontrols:AndroidStyleEntry 
                    Margin="10,0,0,0"
                    x:Name="passwordEntry1"
                    HorizontalTextAlignment="Center"
                    TextFontSize="50"
                    PlaceholderFontSize="50"
                    IsReadOnly="True"
                    TextColor="{StaticResource dark_purple}"
                    BorderColor="{StaticResource dark_purple}"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalAlignment="Top"
                    Height="60"
                    Width="50"/>
                <customcontrols:AndroidStyleEntry 
                    Margin="10,0,0,0"
                    x:Name="passwordEntry2"
                    HorizontalTextAlignment="Center"
                    TextFontSize="50"
                    PlaceholderFontSize="50"
                    IsReadOnly="True"
                    TextColor="{StaticResource dark_purple}"
                    BorderColor="{StaticResource dark_purple}"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalAlignment="Top"
                    Height="60"
                    Width="50"/>
                <customcontrols:AndroidStyleEntry 
                    Margin="10,0,0,0"
                    x:Name="passwordEntry3"
                    HorizontalTextAlignment="Center"
                    TextFontSize="50"
                    PlaceholderFontSize="50"
                    IsReadOnly="True"
                    TextColor="{StaticResource dark_purple}"
                    BorderColor="{StaticResource dark_purple}"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalAlignment="Top"
                    Height="60"
                    Width="50"/>
                <customcontrols:AndroidStyleEntry  
                    Margin="10,0,0,0"
                    x:Name="passwordEntry4"
                    HorizontalTextAlignment="Center"
                    TextFontSize="50"
                    PlaceholderFontSize="50"
                    IsReadOnly="True"
                    TextColor="{StaticResource dark_purple}"
                    BorderColor="{StaticResource dark_purple}"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalAlignment="Top"
                    Height="60"
                    Width="50"/>
            </StackPanel>

            <controls1:AuthKeyboard 
                Margin="30,15,0,0"
                HorizontalAlignment="Center"
                x:Name="keyboard"/>
        </StackPanel>

        <Button 
            Command="{Binding ElementName=this,Path=OpenSupportPopup}"
            Style="{StaticResource transparent_btn}"
            Margin="30,0,0,15"
            Content="Помощь"
            VerticalAlignment="Bottom"
            HorizontalAlignment="Center" />


        <Grid Visibility="Hidden">

            <Image
                Stretch="Fill"
                Source="pack://application:,,,/Resources/Images/main_gradient.png"/>


            <StackPanel
                VerticalAlignment="Center"
                HorizontalAlignment="Center">

                <TextBlock
                    FontSize="20"
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    HorizontalAlignment="Center"
                    Foreground="White"
                    Text="Устанавливается соединение с сервером..."/>


                <Image
                    x:Name="loadingWheel"
                    Stretch="Fill"
                    Margin="0,70,0,0"
                    HorizontalAlignment="Center"
                    Width="90"
                    Height="90"
                    Source="pack://application:,,,/Resources/Images/loading_wheel.png">
                    <Image.LayoutTransform>
                        <RotateTransform  x:Name="imgRotate" Angle="0"/>
                    </Image.LayoutTransform>
                </Image>

            </StackPanel>

        </Grid>


    </Grid>
</abstactions:BasePage>
