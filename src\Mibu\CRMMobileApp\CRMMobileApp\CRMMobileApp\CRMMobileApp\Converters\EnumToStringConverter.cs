﻿using MarkupCreator.EnumToTupleConverters;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Text;
using System.Windows;
using Xamarin.Forms;

namespace MarkupCreator.Converters
{
    public class EnumToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            Enum @enum = value as Enum;

            try
            {
                var enumType = @enum.GetType();
                var attributes = enumType.GetField(@enum.ToString())?.GetCustomAttributes(typeof(DescriptionAttribute), false);
                if (attributes==null || attributes?.Length == 0) return "Нет аттрибута description";
                var attribute = attributes[0] as DescriptionAttribute;
                return attribute.Description;

            }catch { return "";}
           
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return null;
            var @enum = EnumToListConverter.GetEnumByStringDescription(targetType, value.ToString());
            return @enum;

        }
    }
}
