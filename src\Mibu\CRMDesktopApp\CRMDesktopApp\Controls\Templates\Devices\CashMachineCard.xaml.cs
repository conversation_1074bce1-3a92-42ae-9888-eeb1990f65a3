﻿using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using Fiscalization;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Devices
{
    /// <summary>
    /// Логика взаимодействия для CashMachineCard.xaml
    /// </summary>
    public partial class CashMachineCard : UserControl
    {
        protected double ExpandedHeightRequest = -1;
        protected double CollapsedHeightRequest = 60;

        private Fiscalizer fiscalizer;
        public CashMachineCard(FiscalRegistrator fiscalRegistrator, Fiscalizer driver)
        {
            Model = fiscalRegistrator;
            fiscalizer = driver;

            ExpandedHeightRequest = 165;
            InitializeComponent();
        }

        public static readonly DependencyProperty ModelProperty =
              DependencyProperty.Register(nameof(Model), typeof(FiscalRegistrator), typeof(CashMachineCard));
        public FiscalRegistrator Model
        {
            get { return (FiscalRegistrator)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        #region Экспандер 
        private bool isExpanded;
        public bool IsExpanded
        {
            get => isExpanded;
            set
            {
                isExpanded = value;
                OnPropertyChanged(nameof(IsExpanded));

                if (value)
                {
                    this.Height = ExpandedHeightRequest;
                }
                else
                {
                    this.Height = CollapsedHeightRequest;
                }
            }
        }

        private void ToggleExpand(object sender, MouseButtonEventArgs e)
        {
            IsExpanded = !IsExpanded;
        }
        #endregion

        #region Функции
        private ICommand checkConnection;
        public ICommand CheckConnection
        {
            get => checkConnection ??= new RelayCommand(async obj =>
            {
                bool isConn = fiscalizer.CheckConnection();


                if (!isConn)
                {
                    isConn = fiscalizer.TryOpenConnection();
                }

                if (isConn)
                {
                    connectionCircleFrame.Background = Brushes.Green;
                }
                else
                {
                    connectionCircleFrame.Background = Brushes.Red;
                }
            });
        }
        private ICommand closeShift;
        public ICommand CloseShift
        {
            get => closeShift ??= new RelayCommand(async obj =>
            {
                fiscalizer.CloseShift();
            });
        }
        private ICommand cancelCheque;
        public ICommand CancelCheque
        {
            get => cancelCheque ??= new RelayCommand(async obj =>
            {
                fiscalizer.CancelReceipt();
            });
        }
        #endregion




        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }
    }
}
