﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:editors="http://schemas.devexpress.com/xamarin/2014/forms/editors" 
             xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
             x:Name="this"
             Background="Transparent"
             x:Class="CRMMobileApp.Controls.Parts.Editors.CustomCrossDateEdit">
  <ContentView.Content>
      <Grid>

            <editors:DateEdit
                  x:Name="forAndroidIOSdateEdit"
                  IsVisible="{OnPlatform Android=True, iOS=True, Default=False}"
                  HorizontalOptions="Fill"
                  VerticalOptions="Fill"
                  Date="{Binding Source={x:Reference this},Path=Date}"
                  PickerDisplayDate="{Binding Source={x:Reference this},Path=Date}"
                  MinDate="{Binding Source={x:Reference this},Path=MinDate}"
                  MaxDate="{Binding Source={x:Reference this},Path=MaxDate}"
                  LabelText="{Binding Source={x:Reference this},Path=LabelText}"
                  DisplayFormat="{Binding Source={x:Reference this},Path=DisplayFormat}"
                  PlaceholderText="{Binding Source={x:Reference this},Path=PlaceholderText}"
                  CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
                  PlaceholderColor="{Binding Source={x:Reference this},Path=PlaceholderColor}"
                  BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor}"
                  TextColor="{Binding Source={x:Reference this},Path=TextColor}"
                  BorderColor="{Binding Source={x:Reference this},Path=BorderColor}"/>


            <editors1:DateEditForWPF
                x:Name="forWPFdateEdit"
                IsVisible="{OnPlatform WPF=True, Default=False}"
                HorizontalOptions="Fill"
                VerticalOptions="Fill"
                Date="{Binding Source={x:Reference this},Path=Date}"
                Format="{Binding Source={x:Reference this},Path=DisplayFormat}"
                MinimumDate="{Binding Source={x:Reference this},Path=MinDate}"
                MaximumDate="{Binding Source={x:Reference this},Path=MaxDate}"
                PlaceholderText="{Binding Source={x:Reference this},Path=PlaceholderText}"
                CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
                PlaceholderColor="{Binding Source={x:Reference this},Path=PlaceholderColor}"
                BackgroundColor="{Binding Source={x:Reference this},Path=BackgroundColor}"
                TextColor="{Binding Source={x:Reference this},Path=TextColor}"
                BorderColor="{Binding Source={x:Reference this},Path=BorderColor}"/>


        </Grid>
  </ContentView.Content>
</ContentView>