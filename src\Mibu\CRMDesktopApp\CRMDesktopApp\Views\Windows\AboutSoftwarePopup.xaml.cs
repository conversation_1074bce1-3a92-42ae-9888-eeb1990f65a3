﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для AboutSoftwarePopup.xaml
    /// </summary>
    public partial class AboutSoftwarePopup : BaseWindow
    {
        public AboutSoftwarePopup()
        {
            InitializeComponent();
            Render();
        }

        private void Render()
        {
            domainTextBlock.Text = $"{ApplicationState.CurrentDomain}.mibu24.ru";
            if (ApplicationState.CurrentStore != null)
            {
                addressTextBlock.Text = ApplicationState.CurrentStore.CommonStoreSettings.ToString();
            }
        }
    }
}
