﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Other.PaymentTypeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Other" 
             xmlns:custom="clr-namespace:CRMDesktopApp.CustomControls"
             x:Name="this"
             mc:Ignorable="d" 
             d:DesignHeight="150"
             d:DesignWidth="150">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        Padding="0"
        CornerRadius="10"
        BorderThickness="1"
        Background="Transparent">
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="True">
                        <Setter Property="BorderBrush" Value="{StaticResource purple}"></Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="False">
                        <Setter Property="BorderBrush" Value="{StaticResource text_gray}"></Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>
        <Grid
            HorizontalAlignment="Center"
            VerticalAlignment="Center">
            <StackPanel>
                <Image 
                    HorizontalAlignment="Center"
                    Source="{Binding ElementName=this,Path=Model.ImgPath}"
                    Stretch="Fill"
                    Height="55"
                    Width="52"/>
                <TextBlock
                    HorizontalAlignment="Center"
                    Foreground="{StaticResource text_gray}"
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    FontSize="16"
                    Text="{Binding ElementName=this,Path=Model.Title}"/>
                <custom:AndroidStyleEntry  
                    Margin="0,10,0,0"
                    IsReadOnly="True"
                    TextColor="{StaticResource text_gray}"
                    HorizontalAlignment="Center"
                    Width="80"
                    Text="{Binding ElementName=this,Path=Model.SumStr,Mode=TwoWay}"
                    HorizontalTextAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
