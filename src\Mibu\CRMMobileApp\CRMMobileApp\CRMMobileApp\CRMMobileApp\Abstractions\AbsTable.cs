﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Reports.Mobile.Models.Halls;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace CRMMobileApp.Abstractions
{
    public abstract class AbsTable : ContentView
    {
        private Table table;
        public Table Table
        {
            get => table;
            set { table = value; OnPropertyChanged(nameof(Table)); }
        }
  
        public AbsTable(Table table)
        {
            Table = table;

            this.HorizontalOptions = LayoutOptions.Start;
            this.VerticalOptions = LayoutOptions.Start;
            this.Margin = new Thickness(Table.X, Table.Y, 0, 0);

            this.WidthRequest = Table.Width;
            this.HeightRequest = Table.Height;

            GetTableOrders();
        }

        public AbsTable(TableWithOrders table)
        {
            Table = table.Table;

            this.HorizontalOptions = LayoutOptions.Start;
            this.VerticalOptions = LayoutOptions.Start;
            this.Margin = new Thickness(Table.X, Table.Y, 0, 0);

            this.WidthRequest = Table.Width;
            this.HeightRequest = Table.Height;

            TableOrders = table.Orders;
            SetStyle();
        }

        protected abstract void SetStyle();


        public List<Order> TableOrders { get; protected set; } = new List<Order>();
        protected async void GetTableOrders()
        {
            await Task.Run(async () =>
            {
                TableOrders = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetTableCurrentOrders(ApplicationState.CurrentDomain,
                                                                                                 ApplicationState.CurrentStore.Id,
                                                                                                 ApplicationState.CurrentDuty.Id,
                                                                                                 Table.Id);
            });
            SetStyle();
        }
        public void AddOrder(Order order)
        {
            TableOrders.Add(order);
            SetStyle();
        }
        public void RemoveOrder(Order order)
        {
            TableOrders.Remove(order);
            SetStyle();
        }


        public event EventHandler<Table> TableTapped;
        public void onCardTapped(object sender, EventArgs e)
        {
            TableTapped?.Invoke(this, Table);
        }


    }
}
