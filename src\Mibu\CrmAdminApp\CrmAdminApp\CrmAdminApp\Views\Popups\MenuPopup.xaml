﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage 
             xmlns:animations="http://rotorgames.com"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             
             x:Class="CRMAdminMoblieApp.Views.Popups.MenuPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </animations:PopupPage.Resources>

    <Frame 
        HasShadow="False"
        CornerRadius="15"
        Padding="0,40,0,0"
        Margin="0,-70,0,150"
        BackgroundColor="#FFFFFF">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="100"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <Grid Grid.Row="0">
                <Image 
                    Margin="30,30,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Start"
                    WidthRequest="85"
                    HeightRequest="50"
                    Source="logo.png"/>


                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=ClosePopup}"
                    Source="close.png"
                    BackgroundColor="Transparent"
                    WidthRequest="16"
                    HeightRequest="16"
                    Margin="0,32,32,0"
                    VerticalOptions="Start"
                    HorizontalOptions="End"/>
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout
                    Margin="30,0,30,0"
                    Spacing="30">
                    
                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToRealTimePage}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="realTime.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Сейчас"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToOrders}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="orders.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Заказы"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToSales}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="sales.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Продажи"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToStock}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="stocks.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Склад"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToCassa}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="cashRegister.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Касса"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToNotifications}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="notifications.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Уведомления"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        IsVisible="False"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToPayment}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="payment.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Оплата"/>
                    </StackLayout>

                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=TradeNetworks}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="networks.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Сети продаж"/>
                    </StackLayout>

                    <StackLayout 
                        x:Name="gamificationStackLayout"
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToGamification}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                             Source="personGamificationMenuIcon.png"
                             HeightRequest="15"
                             WidthRequest="15"/>
                        <Label 
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             TextColor="{x:StaticResource dark_purple}"
                             VerticalOptions="Center"
                             Text="Сотрудники"/>
                    </StackLayout>


                    <StackLayout 
                        Spacing="10"
                        Orientation="Horizontal">
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Logout}"/>
                        </StackLayout.GestureRecognizers>
                        <Image
                            Source="logout.png"
                            HeightRequest="18"
                            WidthRequest="18"/>
                        <Label 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Выйти"/>
                    </StackLayout>
                </StackLayout>
            </Grid>
            
        </Grid>
    </Frame>
  
</animations:PopupPage>