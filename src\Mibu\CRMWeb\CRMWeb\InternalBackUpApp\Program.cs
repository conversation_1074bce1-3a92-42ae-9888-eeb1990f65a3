﻿using CRM.Database.Core;
using CRM.Models.Core;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using static System.Net.WebRequestMethods;
using File = System.IO.File;

namespace InternalBackUpApp
{
    public class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Hello, World!");

         
            while (true)
            {

                Console.WriteLine(new string('-', 50));

                using (CoreDBContext dBContext = new CoreDBContext())
                {
                    foreach (var domain in dBContext.UserDomains.ToList())
                    {
                        BackupDB(domain);
                        Console.WriteLine($"База {domain.Name} скопирована. Время: {DateTime.Now.ToString("dd.MM.yyyy HH:mm")}");
                    }
                }

                Console.WriteLine(new string('-', 50));
                BackupDB(new UserDomain { Name = "coredb" });
                Console.WriteLine($"Главная база скопирована. Время: {DateTime.Now.ToString("dd.MM.yyyy HH:mm")}");
                BackupDB(new UserDomain { Name = "mibuadmintab" });
                Console.WriteLine($"Админская база скопирована. Время: {DateTime.Now.ToString("dd.MM.yyyy HH:mm")}");


                CopyMainAppUploads();
                Console.WriteLine($"Файлы основного приложения скопированы. Время: {DateTime.Now.ToString("dd.MM.yyyy HH:mm")}");

                CopyAdminAppUploads();
                Console.WriteLine($"Файлы админского приложения скопированы. Время: {DateTime.Now.ToString("dd.MM.yyyy HH:mm")}");


                DeleteOldBackups(5, "C:\\Users\\<USER>\\Desktop\\backups\\databases");
                DeleteOldBackups(5, "C:\\Users\\<USER>\\Desktop\\backups\\uploads\\mibu");
                DeleteOldBackups(5, "C:\\Users\\<USER>\\Desktop\\backups\\uploads\\admin");
                Console.WriteLine($"Старые копии удалены");


                Console.WriteLine("Все скопировано. Еще раз скопируем через день");
                Console.WriteLine("\r\n \r\n \r\n");

                await Task.Delay(TimeSpan.FromDays(1));
            }
         
            
        }

        private static void BackupDB(UserDomain domain)
        {
            string constring = DatabaseSettings.GenerateConnectionString(domain.Name);

            var dateStr = DateTime.Now.ToString("dd-MM-yyyy");

            string file = $"C:\\Users\\<USER>\\Desktop\\backups\\databases\\{dateStr}\\{domain.Name}.sql";
            using (MySqlConnection conn = new MySqlConnection(constring))
            {
                using (MySqlCommand cmd = new MySqlCommand())
                {
                    using (MySqlBackup mb = new MySqlBackup(cmd))
                    {
                        mb.ExportInfo.AddCreateDatabase = true;

                        cmd.Connection = conn;
                        conn.Open();
                        mb.ExportToFile(file);
                        conn.Close();
                    }
                }
            }
        }
        private static void CopyMainAppUploads()
        {
            var dateStr = DateTime.Now.ToString("dd.MM.yyyy");

            string fromPath = "C:\\Users\\<USER>\\Desktop\\crm_web\\wwwroot\\uploads";
            string toPath = $"C:\\Users\\<USER>\\Desktop\\backups\\uploads\\mibu\\{dateStr}";

            if (!Directory.Exists(toPath))
            {
                Directory.CreateDirectory(toPath);
            }
            CopyFilesRecursively(fromPath, toPath);
        }
        private static void CopyAdminAppUploads()
        {
            var dateStr = DateTime.Now.ToString("dd.MM.yyyy");

            string fromPath = "C:\\Users\\<USER>\\Desktop\\mibuAdmin";
            string toPath = $"C:\\Users\\<USER>\\Desktop\\backups\\uploads\\admin\\{dateStr}";

            if (!Directory.Exists(toPath))
            {
                Directory.CreateDirectory(toPath);
            }
            CopyFilesRecursively(fromPath, toPath);
        }



        private static void DeleteOldBackups(int days, string basePath)
        {
            var directories = Directory.GetDirectories(basePath);
            foreach(var dirPath in directories)
            {
                var dirName = Path.GetFileName(dirPath);
                DateTime date = DateTime.Parse(dirName);
                if (date < DateTime.Now.AddDays(-days).Date)
                {
                    Directory.Delete(dirPath, true);
                }
            }
        }


        private static void CopyFilesRecursively(string sourcePath, string targetPath)
        {
            //Now Create all of the directories
            foreach (string dirPath in Directory.GetDirectories(sourcePath, "*", SearchOption.AllDirectories))
            {
                Directory.CreateDirectory(dirPath.Replace(sourcePath, targetPath));
            }

            //Copy all the files & Replaces any files with the same name
            foreach (string newPath in Directory.GetFiles(sourcePath, "*.*", SearchOption.AllDirectories))
            {
                File.Copy(newPath, newPath.Replace(sourcePath, targetPath), true);
            }
        }


    }
}