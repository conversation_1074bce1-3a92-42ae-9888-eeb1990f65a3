﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:ListCollection="clr-namespace:System.Collections.Generic;assembly=netstandard"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls" 
    xmlns:converters="clr-namespace:MarkupCreator.Converters"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
    x:Class="CRMMobileApp.Views.Popups.CreateTransactionPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary>
            <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
            <converters:EnumListToStringsConverter x:Key="EnumListToStringsConverter" />
        </ResourceDictionary>
    </animations:PopupPage.Resources>

    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="690"
            HeightRequest="220"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,40,0,0"
            CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>


                <Grid 
                    Grid.Row="0"
                    Margin="30,20,0,0">
                    <StackLayout Orientation="Horizontal">
                        <Image 
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Source="{OnPlatform Default=comment.png, WPF='pack://application:,,,/Images/comment.png'}"
                            WidthRequest="30"
                            HeightRequest="30"/>
                        <Label
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="18"
                            VerticalOptions="Center"
                            FontAttributes="Bold"
                            Text="Новая транзакция"/>
                    </StackLayout>



                    <StackLayout
                        Orientation="Horizontal"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Spacing="25"
                        Margin="0,5,10,0">

                        <Frame
                            BackgroundColor="{StaticResource bg_purple}"
                            HasShadow="False"
                            Padding="0"
                            WidthRequest="140"
                            CornerRadius="10"
                            HeightRequest="30">
                            <Grid>
                                <Label
                                    x:Name="transactionDateLabel"
                                    TextColor="{StaticResource purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="12"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    Text="12 марта 2022"/>
                            </Grid>
                        </Frame>

                        <ImageButton 
                            Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                            Command="{Binding Source={x:Reference this},Path=Close}"
                            BackgroundColor="Transparent"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            Padding="10"
                            WidthRequest="35"
                            HeightRequest="35"/>

                    </StackLayout>


                </Grid>







                <Grid Grid.Row="1">

                    <StackLayout
                        Spacing="30"
                        Margin="30,0,0,0"
                        VerticalOptions="Center"
                        Orientation="Horizontal">


                        <editors1:CustomCrossComboBoxEdit 
                            Margin="{OnPlatform Default='0,3,0,0',WPF='0,1,0,0'}"
                            BackgroundColor="#F6F6FB"
                            TextColor="{StaticResource dark_purple}"
                            BorderColor="#F6F6FB"
                            WidthRequest="180"
                            HeightRequest="{OnPlatform Default=40, WPF=49}"
                            HorizontalOptions="Start"
                            VerticalOptions="Start"
                            CornerRadius="10"
                            SelectedIndex="0"
                            PlaceholderText="Пол"
                            PlaceholderColor="#9795B1"
                            SelectedItem="{Binding Source={x:Reference this},Path=SelectedFinanceOperationString,Mode=TwoWay}"
                            ItemsSource="{Binding Source={x:Reference this},Path=FinanceOperationStrings,Mode=TwoWay}" />

                        <Grid
                            HorizontalOptions="Start"
                            Padding="0"
                            WidthRequest="170"
                            HeightRequest="50">

                            <controls:EntryOutlined 
                                HorizontalOptions="Start"
                                VerticalOptions="Start"
                                Text="{Binding Source={x:Reference this},Path=NewTransaction.Sum,Mode=TwoWay}"
                                Style="{StaticResource white_cornered_entry}"
                                Placeholder="Сумма"
                                WidthRequest="170"
                                CornerRadius="10"
                                HeightRequest="50"/>

                            <Label
                                Margin="0,0,20,0"
                                TextColor="{StaticResource purple}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                HorizontalOptions="End"
                                VerticalOptions="Center"
                                Text="₽"/>
                        </Grid>
                    </StackLayout>


                    <StackLayout 
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        Margin="0,0,30,0"
                        Orientation="Horizontal">
                        <Frame 
                            VerticalOptions="Start"
                            HorizontalOptions="Start"
                            HeightRequest="30"
                            WidthRequest="30"
                            CornerRadius="15"
                            HasShadow="False"
                            BackgroundColor="White"
                            Background="White"
                            IsClippedToBounds="True"
                            Padding="0">
                            <Image
                                Aspect="Fill"
                                Source="{OnPlatform Default=userAvatar.png, WPF='pack://application:,,,/Images/userAvatar.png'}"/>
                        </Frame>
                        <StackLayout Spacing="0">
                            <Label
                                x:Name="waiterNameLabel"
                                HorizontalOptions="End"
                                TextColor="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                Text="Мария Иванова"/>
                            <Label
                                x:Name="waiterRoleLabel"
                                HorizontalOptions="End"
                                TextColor="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                Text="Официант"/>
                        </StackLayout>

                    </StackLayout>

                </Grid>

                <Grid Grid.Row="2">

                    <controls:EntryOutlined 
                        Margin="30,0,0,0"
                        Text="{Binding Source={x:Reference this},Path=NewTransaction.Comment,Mode=TwoWay}"
                        HorizontalOptions="Start"
                        VerticalOptions="Start"
                        CornerRadius="10"
                        Style="{StaticResource white_cornered_entry}"
                        Placeholder="Ваш комментарий"
                        WidthRequest="380"
                        HeightRequest="50"/>


                    <StackLayout
                        VerticalOptions="End"
                        HorizontalOptions="End"
                        Margin="0,0,30,30"
                        Spacing="20"
                        Orientation="Horizontal">

                        <Button 
                            Command="{Binding Source={x:Reference this},Path=CreateTransaction}"
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalOptions="Center"
                            Text="Создать"
                            WidthRequest="140"
                            HeightRequest="40"/>

                    </StackLayout>

                </Grid>
            </Grid>
        </Frame>

    </Grid>
    
</animations:PopupPage>