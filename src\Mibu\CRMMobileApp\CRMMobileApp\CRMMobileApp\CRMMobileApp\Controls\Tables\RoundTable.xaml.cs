﻿using CRM.Models.Reports.Mobile.Models.Halls;
using CRM.Models.Stores.Settings.Tables;
using CRMMobileApp.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class RoundTable : AbsTable
    {
        public RoundTable(Table table) : base(table)
        {
            InitializeComponent();
            SetStyle();
        }
        public RoundTable(TableWithOrders table) : base(table)
        {
            InitializeComponent();
            SetStyle();
        }

        protected override void SetStyle()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (this.TableOrders.Any())
                {
                    frame.Fill = Color.FromHex("#524E7D");
                    titleLabel.TextColor = Color.FromHex("#AFADC5");

                    ordersCountLabel.Text = this.TableOrders.Count().ToString();
                    ordersSumLabel.Text = $"{this.TableOrders.Sum(o => o.Sum)} Р";
                }
                else
                {
                    frame.Fill = Color.FromHex("#AFADC5");
                    titleLabel.TextColor = Color.FromHex("#524E7D");

                    ordersCountLabel.Text = "";
                    ordersSumLabel.Text = "";
                }
            });
        }
       
    }
}