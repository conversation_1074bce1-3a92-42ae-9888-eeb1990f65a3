﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:CRMMobileApp.Controls"
    xmlns:custrb="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.GuestsCountPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary>

            <Style TargetType="custrb:CustomCrossRadioButton" x:Key="custrbStyle">
                <Setter Property="HeightRequest" Value="45"></Setter>
                <Setter Property="WidthRequest" Value="45"></Setter>
                <Setter Property="GroupName" Value="putinlalala"></Setter>
                <Setter Property="VerticalOptions" Value="Center"></Setter>
                <Setter Property="HorizontalOptions" Value="Center"></Setter>
                <Setter Property="CheckedStateTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                Padding="0"
                                HasShadow="False"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill"
                                BackgroundColor="{StaticResource dark_purple}"
                                CornerRadius="22">
                                <Grid>
                                    <Label
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        TextColor="{StaticResource bg_purple}"
                                        FontSize="14"
                                        Text="{TemplateBinding Text}"/>


                                    <ContentPresenter />
                                    
                                </Grid>
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Setter Property="UncheckedStateTemplate">
                    <Setter.Value>
                        <ControlTemplate>
                            <Frame
                                  Padding="0"
                                  HasShadow="False"
                                  HorizontalOptions="Fill"
                                  VerticalOptions="Fill"
                                  BackgroundColor="Transparent"
                                  BorderColor="{StaticResource dark_purple}"        
                                  CornerRadius="22">
                                <Grid>
                                    <Label
                                       VerticalOptions="Center"
                                       HorizontalOptions="Center"
                                       FontFamily="TTFirsNeue-Regular"
                                       TextColor="{StaticResource dark_purple}"
                                       FontSize="14"
                                       Text="{TemplateBinding Text}"/>

                                    <ContentPresenter />

                                </Grid>
                            </Frame>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style TargetType="RadioButton" x:Key="rbStyle">
                <!--<Style.Triggers>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Frame
                                        Padding="0"
                                        HasShadow="False"
                                        BackgroundColor="{StaticResource dark_purple}"
                                        CornerRadius="22">
                                        <Label
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            FontFamily="TTFirsNeue-Regular"
                                            TextColor="{StaticResource bg_purple}"
                                            FontSize="14"
                                            Text="{TemplateBinding Content}"/>
                                    </Frame>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Frame
                                        Padding="0"
                                        HasShadow="False"
                                        BackgroundColor="Transparent"
                                        BorderColor="{StaticResource dark_purple}"        
                                        CornerRadius="22">
                                        <Label
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            FontFamily="TTFirsNeue-Regular"
                                            TextColor="{StaticResource dark_purple}"
                                            FontSize="14"
                                            Text="{TemplateBinding Content}"/>
                                    </Frame>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>-->
            </Style>
        </ResourceDictionary>
    </animations:PopupPage.Resources>
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="350"
            HeightRequest="250"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,70,0,0"
            CornerRadius="20">
            <Grid
                ColumnSpacing="0"
                RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="60"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Label
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Количество гостей"/>
                </Grid>

                <Grid Grid.Row="1">

                    <Grid
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        ColumnSpacing="10"
                        RowSpacing="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="45"/>
                            <RowDefinition Height="45"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="45"/>
                            <ColumnDefinition Width="45"/>
                            <ColumnDefinition Width="45"/>
                            <ColumnDefinition Width="45"/>
                            <ColumnDefinition Width="45"/>
                        </Grid.ColumnDefinitions>

                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="0"
                            Grid.Column="0"
                            Text="1"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="0"
                            Grid.Column="1"
                            Text="2"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="0"
                            Grid.Column="2"
                            Text="3"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="0"
                            Grid.Column="3"
                            Text="4"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="0"
                            Grid.Column="4"
                            Text="5"/>


                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="1"
                            Grid.Column="0"
                            Text="6"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="1"
                            Grid.Column="1"
                            Text="7"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="1"
                            Grid.Column="2"
                            Text="8"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="1"
                            Grid.Column="3"
                            Text="9"/>
                        <custrb:CustomCrossRadioButton
                            CheckedChanged="rbCheckedChanged"
                            Style="{StaticResource custrbStyle}"
                            Grid.Row="1"
                            Grid.Column="4"
                            Text="10"/>
                    </Grid>

                </Grid>

                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Button 
                        Grid.Column="0"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Style="{StaticResource gray_cornered_filled_btn}"
                        VerticalOptions="Center"
                        HorizontalOptions="End"
                        Margin="0,0,10,0"
                        Text="Отмена"
                        WidthRequest="120"
                        HeightRequest="40"/>

                    <Button 
                        Grid.Column="1"
                        Command="{Binding Source={x:Reference this},Path=Apply}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Margin="10,0,0,0"
                        Text="Ок"
                        WidthRequest="120"
                        HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>