﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для TechCardOptionsPopup.xaml
    /// </summary>
    public partial class TechCardOptionsPopup : BaseWindow
    {
        private OrderItem technicalCard;
        public OrderItem TechnicalCard
        {
            get => technicalCard;
            set { technicalCard = value; OnPropertyChanged(nameof(TechnicalCard)); }
        }
        public TechCardOptionsPopup(OrderItem product)
        {
            InitializeComponent();
            TechnicalCard = product;
        }
        public TechCardOptionsPopup(OrderItem product, double amount)
        {
            InitializeComponent();
            TechnicalCard = product;
            Amount = (int)amount;

            _isNewItem = false;
        }
        private bool _isNewItem = true;


        #region Количество товара
        private void IncrementAmount(object sender, MouseButtonEventArgs e)
        {
            Amount++;
        }
        private void DecrementAmount(object sender, MouseButtonEventArgs e)
        {
            if (Amount > 1)
                Amount--;
        }

        private int amount = 1;
        public int Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }
        #endregion





        private ICommand addToOrder;
        public ICommand AddToOrder
        {
            get => addToOrder ??= new RelayCommand(async obj =>
            {
                TechnicalCard.CreatedAt = DateTime.Now;
                TechnicalCard.Amount = Amount;

                if (_isNewItem) OrdersHelper.AddOrderItem(TechnicalCard);
                else await OrdersHelper.UpdateOrderItem(TechnicalCard);

                this.Close();
            });
        }

       
    }
}
