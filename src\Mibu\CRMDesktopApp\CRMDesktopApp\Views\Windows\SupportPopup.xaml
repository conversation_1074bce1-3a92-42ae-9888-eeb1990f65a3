﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.SupportPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Поддержка"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="300"
        Width="600">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <customcontrols:ClippingBorder
        Background="White"
        CornerRadius="20"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        Width="590"
        Height="295">
        <customcontrols:ClippingBorder.Effect>
            <DropShadowEffect Color="LightGray"/>
        </customcontrols:ClippingBorder.Effect>
        <Grid Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Image 
                    Grid.Column="0"
                    Margin="20,20,0,0"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Source="pack://application:,,,/Resources/Images/logo.png"/>

                <StackPanel
                     Grid.Column="1"
                     Grid.ColumnSpan="2"
                     Margin="30,0,0,0"
                     HorizontalAlignment="Stretch"
                     VerticalAlignment="Center">
                    <TextBlock 
                        Margin="0,2,0,0"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        Foreground="{StaticResource dark_purple}"
                        Text="Домен:"/>
                    <TextBlock 
                        x:Name="domainTextBlock"
                        Margin="0,2,0,0"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontWeight="Bold"
                        FontSize="16"
                        Foreground="{StaticResource dark_purple}"
                        Text="XXXXXXXXX"/>
                    <TextBlock 
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        Margin="0,10,0,0"
                        Foreground="{StaticResource dark_purple}"
                        Text="Точка продаж:"/>
                    <TextBlock 
                        x:Name="storeTextBlock"
                        Margin="0,2,0,0"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontWeight="Bold"
                        FontSize="16"
                        Foreground="{StaticResource dark_purple}"
                        Text="XXXXXXXXX"/>
                    <TextBlock 
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        Margin="0,10,0,0"
                        Foreground="{StaticResource dark_purple}"
                        Text="Адрес:"/>
                    <TextBlock
                        x:Name="addressTextBlock"
                        Margin="0,2,0,0"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontWeight="Bold"
                        FontSize="16"
                        Foreground="{StaticResource dark_purple}"
                        Text="XXXXXXXXX"/>
                </StackPanel>


                <Grid Grid.Column="2">
                    <customcontrols:ImageButton 
                        Source="pack://application:,,,/Resources/Images/close.png"
                        Command="{Binding ElementName=this,Path=CloseWindow}"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Right"
                        Width="10"
                        Height="10"
                        Margin="0,35,35,0"/>
                </Grid>
            </Grid>

            <Grid Grid.Row="1"
                    Background="{DynamicResource blueColor}">
                <TextBlock 
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    Margin="40,0,0,0"
                    Foreground="White"
                    FontSize="20"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Text="Телефон поддержки:"/>
                <TextBlock 
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    Margin="0,0,40,0"
                    Foreground="White"
                    FontWeight="Bold"
                    FontSize="22"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Text="8 XXX XXX XX XX"/>
            </Grid>
        </Grid>
    </customcontrols:ClippingBorder>
</abstactions:BaseWindow>
