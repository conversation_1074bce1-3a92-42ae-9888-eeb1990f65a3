﻿using CRMDesktopApp.CustomControls.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.CustomControls
{
    /// <summary>
    /// Логика взаимодействия для EntryOutlined.xaml
    /// </summary>
    public partial class EntryOutlined : BaseEntry
    {
        public EntryOutlined()
        {
            InitializeComponent();
            placeHolderLabel = PlaceHolderLabel;
            textBox = TextBox;
        }


        public static readonly DependencyProperty AcceptsReturnProperty =
               DependencyProperty.Register(nameof(AcceptsReturn), typeof(bool), typeof(EntryOutlined), new PropertyMetadata(false));
        public bool AcceptsReturn
        {
            get { return (bool)GetValue(AcceptsReturnProperty); }
            set { SetValue(AcceptsReturnProperty, value); }
        }



        #region Colors and border
        public static readonly DependencyProperty EntryBackgroundProperty =
             DependencyProperty.Register(nameof(EntryBackground), typeof(Brush), typeof(EntryOutlined), new PropertyMetadata(Brushes.Blue));
        public Brush EntryBackground
        {
            get { return (Brush)GetValue(EntryBackgroundProperty); }
            set { SetValue(EntryBackgroundProperty, value); }
        }
        public static readonly DependencyProperty BorderColorProperty =
             DependencyProperty.Register(nameof(BorderColor), typeof(Brush), typeof(EntryOutlined), new PropertyMetadata(Brushes.Blue));
        public Brush BorderColor
        {
            get { return (Brush)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }
        public static readonly DependencyProperty CornerRadiusProperty =
             DependencyProperty.Register(nameof(CornerRadius), typeof(CornerRadius), typeof(EntryOutlined), new PropertyMetadata(new CornerRadius(0)));
        public CornerRadius CornerRadius
        {
            get { return (CornerRadius)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }
        #endregion


        public static readonly DependencyProperty VerticalTextAlignmentProperty =
           DependencyProperty.Register(nameof(VerticalTextAlignment), typeof(VerticalAlignment), typeof(EntryOutlined), new PropertyMetadata(VerticalAlignment.Center));
        public VerticalAlignment VerticalTextAlignment
        {
            get { return (VerticalAlignment)GetValue(VerticalTextAlignmentProperty); }
            set { SetValue(VerticalTextAlignmentProperty, value); }
        }

    }
}
