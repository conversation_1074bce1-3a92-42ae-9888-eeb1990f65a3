﻿using CRM.Models;
using CRM.Models.Network;
using CRM.Models.Reports.Mobile.Admin;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TradeNetworkExpandableCard : ContentView
    {
        public TradeNetworkExpandableCard(TradeNetwork network)
        {
            InitializeComponent();
            Model = network;
            Init();
        }

        private static readonly string ArrowDown = "arrowForward.png";
        private static readonly string ArrowUp = "arrowDown.png";

      

        public static readonly BindableProperty IsExpandedProperty =
            BindableProperty.CreateAttached(nameof(IsExpanded), typeof(bool), typeof(TradeNetworkExpandableCard), false, BindingMode.TwoWay);
        public bool IsExpanded
        {
            get => (bool)GetValue(IsExpandedProperty);
            set => SetValue(IsExpandedProperty, value);
        }
        public static readonly BindableProperty ModelProperty =
          BindableProperty.Create(nameof(Model), typeof(TradeNetwork), typeof(TradeNetworkExpandableCard));
        public TradeNetwork Model
        {
            get { return (TradeNetwork)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }




        public event EventHandler<TradeNetwork> OnSelectBtnTapped;

        private ICommand selectNetwork;
        public ICommand SelectNetwork
        {
            get => selectNetwork ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    OnSelectBtnTapped?.Invoke(this, Model);
                });
            });
        }


		public event EventHandler<TradeNetwork> OnDeleteBtnTapped;
		private ICommand deleteNetwork;
        public ICommand DeleteNetwork
        {
            get => deleteNetwork ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    OnDeleteBtnTapped?.Invoke(this, Model);
                });
            });
        }




        private string _expandIcon;
        public string ExpandIcon
        {
            get => _expandIcon;
            set
            {
                _expandIcon = value;
                OnPropertyChanged(nameof(ExpandIcon));
            }
        }


        private void Init()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                ExpandIcon = ArrowDown;
                BodyContentView.IsVisible = false;

                BriefReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetBriefTradeNetworkReport(ApplicationState.CurrentDomain, Model.Id);
            });

         
        }
        public static readonly BindableProperty BriefReportProperty =
        BindableProperty.Create(nameof(BriefReport), typeof(BriefTradeNetworkReport), typeof(TradeNetworkExpandableCard));
        public BriefTradeNetworkReport BriefReport
        {
            get { return (BriefTradeNetworkReport)GetValue(BriefReportProperty); }
            set { SetValue(BriefReportProperty, value); }
        }


        private void HeaderContent_OnTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!IsExpanded)
                {
                    BodyContentView.Opacity = 0;
                }

                IsExpanded = !IsExpanded;
                ExpandIcon = IsExpanded ? ArrowUp : ArrowDown;

                BodyContentView.IsVisible = IsExpanded;
                BodyContentView.FadeTo(1, 400, Easing.SpringOut);


                if (IsExpanded)
                {
                    this.HeightRequest = 200;
                    //this.HeightRequest = 250;
                }
                else
                {
                    this.HeightRequest = 100;
                }
            });
        }
    }
}