﻿using CRM.Models.Stores;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для ClosedOrderTemplate.xaml
    /// </summary>
    public partial class OrderDivisionItemTemplate : UserControl
    {
        private OrderItem model;
        public OrderItem Model
        {
            get => model;
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }


        public OrderDivisionItemTemplate(OrderItem item)
        {
            Model = item;
            InitializeComponent();

            amountLabel.Text = Math.Round(Model.Amount, 3).ToString();

            RenderModifiers();
        }


        private void RenderModifiers()
        {
            foreach (var modifier in Model.SelectedModifiers)
            {
                var item = new OrderItemModifierTemplate(modifier);
                modifiersLayout.Children.Add(item);
            }
            double height = 37;
            foreach (var modifier in Model.SelectedModifiers)
            {
                height += modifier.SelectedOptions.Count * 30;
            }
            this.Height = height;
        }






        private string itemTitle;
        public string ItemTitle
        {
            get
            {
                if (Model.TechnicalCard != null)
                    return Model.TechnicalCard.Title;
                if (Model.Product != null)
                    return Model.Product.Title;
                return "";
            }
            set
            {
                itemTitle = value;
                OnPropertyChanged(nameof(ItemTitle));
            }
        }



        public event EventHandler<OrderItem> DeleteBtnTapped;
        private void onDeleteBtnTapped(object sender, MouseButtonEventArgs e)
        {
            DeleteBtnTapped?.Invoke(this, Model);
        }


        #region Выбор позиции
        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }

        public event EventHandler<OrderItem> OrderItemTapped;
        private void onOrderItemTapped(object sender, MouseButtonEventArgs e)
        {
            OrderItemTapped?.Invoke(this, Model);
        }
        #endregion

        #region Редактирование кол-ва через кнопки
        public event EventHandler<OrderItem> AmountChanged;

        private void IncrementAmount(object sender, MouseButtonEventArgs e)
        {
            Model.Amount++;
            amountLabel.Text = Math.Round(Model.Amount, 3).ToString();

            AmountChanged?.Invoke(this, Model);
        }
        private void DecrementAmount(object sender, MouseButtonEventArgs e)
        {
            if (Model.Amount > 1)
                Model.Amount--;

            amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
            AmountChanged?.Invoke(this, Model);
        }


        #endregion



        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }

    }
}
