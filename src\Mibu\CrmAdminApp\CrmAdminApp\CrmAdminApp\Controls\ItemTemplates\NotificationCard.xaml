﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:converters="clr-namespace:MarkupCreator.Converters"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.NotificationCard">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
        <ResourceDictionary>
            <converters:EnumToStringConverter x:Key="EnumToStringConverter"/>
        </ResourceDictionary>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame 
          Padding="0"
          HasShadow="False"
          BackgroundColor="{x:StaticResource bg_purple}"
          CornerRadius="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <StackLayout
                        Margin="23,0,0,0"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Orientation="Horizontal">


                        <Grid
                            HeightRequest="20"
                            WidthRequest="20">

                            <Image 
                                x:Name="dutyClosingSign"
                                IsVisible="False"
                                Source="dutyClosing.png"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="15"
                                WidthRequest="15"/>
                            
                            <Image 
                                x:Name="dutyOpeningSign"
                                IsVisible="False"
                                Source="dutyOpening.png"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="15"
                                WidthRequest="15"/>

                            <Image 
                                x:Name="incassationSign"
                                IsVisible="False"
                                Source="cash.png"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="15"
                                WidthRequest="15"/>


                            <Image 
                                x:Name="criticalBalanceSign"
                                IsVisible="False"
                                Source="criticalBalance.png"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="15"
                                WidthRequest="15"/>

                        </Grid>
                   
                        
                        
                        
                        <Label 
                            x:Name="notificationCategoryLabel"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="{Binding Source={x:Reference this},Path=Model.Category,Converter={StaticResource EnumToStringConverter}}"/>
                    </StackLayout>

                    <Label 
                        x:Name="notificationDateLabel"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        VerticalOptions="Center"
                        HorizontalOptions="End"
                        Margin="0,0,20,0"/>

                </Grid>

                <Grid Grid.Row="1">
                    <Label 
                        x:Name="notificationDescriptionLabel"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource text_gray}"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Margin="23,0,0,0"
                        Text="{Binding Source={x:Reference this},Path=Model.Description}"/>

                    <StackLayout
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Margin="0,0,20,0"
                        Orientation="Horizontal">
                        <Image 
                            Source="clock_gray.png"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            HeightRequest="15"
                            WidthRequest="15"/>
                        <Label 
                            x:Name="notificationTimeLabel"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource text_gray}"
                            VerticalOptions="Center"/>
                    </StackLayout>

                </Grid>

            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>