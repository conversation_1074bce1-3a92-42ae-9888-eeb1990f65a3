﻿using CRM.Models.Stores.Orders;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Modifiers
{
    /// <summary>
    /// Логика взаимодействия для SomeOptionsModifierOptionItem.xaml
    /// </summary>
    public partial class SomeOptionsModifierOptionItem : UserControl
    {
        public SomeOptionsModifierOptionItem(OrderItemModifierOption option)
        {
            InitializeComponent();
            Model = option;
        }

        public static readonly DependencyProperty ModelProperty =
          DependencyProperty.Register(nameof(Model), typeof(OrderItemModifierOption), typeof(SomeOptionsModifierOptionItem));
        public OrderItemModifierOption Model
        {
            get { return (OrderItemModifierOption)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


        private void decrementAmount(object sender, MouseButtonEventArgs e)
        {
            if (Model.Amount > 0)
                Model.Amount--;
            amountLabel.Text = $"{Model.Amount}";
        }

        private void incrementAmount(object sender, MouseButtonEventArgs e)
        {
            if (!Model.ModifierOption.MaxCount.IsLimited)
            {
                Model.Amount++;
                amountLabel.Text = $"{Model.Amount}";
            }
            else if(Model.ModifierOption.MaxCount.Limit >= Model.Amount + 1)
            {
                Model.Amount++;
                amountLabel.Text = $"{Model.Amount}";
            }
            else
            {
                new OneButtonedPopup("Достигнут лимит по количеству модификатора", "Уведомление").ShowDialog();
            }
     
        }
    }
}
