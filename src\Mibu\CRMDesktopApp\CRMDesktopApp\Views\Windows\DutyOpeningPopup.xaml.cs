﻿using CRM.Models.Enums.General;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages;
using CRMDesktopApp.Views.Pages.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Mibu.Landing.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для DutyOpeningPopup.xaml
    /// </summary>
    public partial class DutyOpeningPopup : BaseWindow
    {
        public DutyOpeningPopup()
        {
            InitializeComponent();
            Closing += DutyOpeningPopup_Closing;
        }

     

        private double cashInCassa;
        public double CashInCassa
        {
            get => cashInCassa;
            set { cashInCassa = value; OnPropertyChanged(nameof(CashInCassa)); }
        }

        private ICommand openDuty;
        public ICommand OpenDuty
        {
            get => openDuty ??= new RelayCommand(async obj =>
            {
                dutyOpened = true;

                await ApplicationState.OpenDuty(CashInCassa);
                this.Close();
                await OrdersHelper.GetCurrentOrder();

                if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe
                              && ApplicationState.Subscription.Subscription.Type != SubscriptionType.Start)
                {
                    (App.Current.MainWindow as MainWindow).frame.Navigate(new HallPage());
                }
                else
                {
                    (App.Current.MainWindow as MainWindow).frame.Navigate(new CategoriesPage());
                }
            });
        }
        bool dutyOpened = false;
        private async void DutyOpeningPopup_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            if (!dutyOpened)
            {
                (App.Current.MainWindow as MainWindow).frame.Navigate(new MainPage());
                await Auth.LogoutUser();
            }
        }

        private ICommand closePopup;
        public ICommand ClosePopup
        {
            get => closePopup ??= new RelayCommand(async obj =>
            {
                this.Close();      
            });
        }
    }
}
