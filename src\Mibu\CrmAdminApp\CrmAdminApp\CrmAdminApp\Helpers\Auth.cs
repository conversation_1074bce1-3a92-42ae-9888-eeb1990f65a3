﻿using CRM.Models.Gamification.General;
using CRM.Models.Network;
using CRMGamificationAPIWrapper;
using CRMMoblieApiWrapper;
using Plugin.FirebasePushNotification;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace CRMAdminMoblieApp.Helpers
{
    public static class Auth
    {
        public static NetworkAdminTabUser User { get; set; }
        public static GamificationUser GamificationUser { get; set; }

        public static async Task<bool> Authorize(string domain,string login,string password)
        {
            User = await MobileAPI.MainMethods.AuthorizeToAdminApp(domain, login, password);
            return User != null;
        }

        public static async Task UpdateUser(NetworkAdminTabUser editedUser)
        {
            User = await MobileAPI.AdminMethods.AdminProfileMethods.UpdateProfile(ApplicationState.CurrentDomain, 
                                                                                  editedUser, 
                                                                                  CrossFirebasePushNotification.Current.Token);
        }
		public static async Task<NetworkAdminTabUser> UpdateUserAvatar(string domain, string avatarFileName, byte[] avatarBytes)
		{
			await MobileAPI.AdminMethods.AdminProfileMethods.UpdateProfileAvatar(domain, User.Id, avatarFileName, avatarBytes);
			await Authorize(ApplicationState.CurrentDomain, User.Login, User.Password);

			return User;
		}



		public static async Task<bool> AuthorizeInGamification()
        {
            GamificationUser = await GamificationAPI.Barista.Profile.GetProfileByAdminTabCRMUser(ApplicationState.CurrentDomain, User.Id);
            return GamificationUser != null;
        }



        public static async Task Logout()
        {
            User = null;
            GamificationAPI.Shared.PushNotifications.DeleteDevice(CrossFirebasePushNotification.Current.Token);
            await App.Current.MainPage.Navigation.PopToRootAsync();
        }
    }
}
