﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Orders.OrderPrepaymentPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Orders" 
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts"
      xmlns:other="clr-namespace:CRMDesktopApp.Controls.Templates.Other" 
      xmlns:flexboxlayout="clr-namespace:FlexboxLayout"
      xmlns:controls="clr-namespace:CRMDesktopApp.Controls"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="CreateOrder">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/OtherStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid Background="#ffffff">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="130"/>
        </Grid.RowDefinitions>


        <parts:Header Grid.Row="0"/>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="4*"/>
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">

                <StackPanel 
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center">

                    <TextBlock
                        Margin="10,0,0,0"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Предоплата по заказу"/>
                    <ListView
                        Margin="0,20,0,0"
                        BorderThickness="0"
                        ItemContainerStyle="{StaticResource listViewItems}"
                        SelectedItem="{Binding ElementName=this,Path=SelectedPaymentType,Mode=TwoWay}"
                        ItemsSource="{Binding ElementName=this,Path=PaymentTypes}"
                        Width="580"
                        Height="220">
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel/>
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <other:PaymentTypeView 
                                    Margin="10,0,0,0"
                                    Width="160"
                                    Height="160"
                                    Model="{Binding}"/>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackPanel>

                
                
            </Grid>

            <Grid Grid.Column="1">

                <Border 
                    CornerRadius="10"
                    Background="{StaticResource bg_purple}"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Width="330"
                    Height="420">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="60"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">


                        </Grid>

                        <Grid Grid.Row="0">

                            <controls:AuthKeyboard
                                x:Name="keyboard"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"/>


                        </Grid>

                        <Grid Grid.Row="1">

                            <flexboxlayout:Flexbox
                                Direction="Row"
                                AlignItems="Center"
                                JustifyContent="Center"
                                HorizontalAlignment="Stretch"
                                AlignContent="SpaceAround">
                                <Button 
                                    Command="{Binding ElementName=this,Path=AddSumBtnPressed}"
                                    CommandParameter="100"
                                    Foreground="{StaticResource dark_purple}"
                                    Background="Transparent"
                                    Content="100₽"
                                    Padding="0"
                                    FontSize="12"
                                    Width="70"
                                    Height="40"/>
                                <Button 
                                    Command="{Binding ElementName=this,Path=AddSumBtnPressed}"
                                    CommandParameter="500"
                                    Foreground="{StaticResource dark_purple}"
                                    Background="Transparent"
                                    Content="500₽"
                                    Padding="0"
                                    FontSize="12"
                                    Width="70"
                                    Height="40"/>
                                <Button 
                                    Command="{Binding ElementName=this,Path=AddSumBtnPressed}"
                                    CommandParameter="1000"
                                    Foreground="{StaticResource dark_purple}"
                                    Background="Transparent"
                                    Content="1000₽"
                                    Padding="0"
                                    FontSize="12"
                                    Width="70"
                                    Height="40"/>
                            </flexboxlayout:Flexbox>

                        </Grid>

                    </Grid>
                </Border>


            </Grid>


        </Grid>

        <Grid Grid.Row="2">
            <Button 
                Command="{Binding ElementName=this,Path=AddPrepayment}"
                VerticalAlignment="Top"
                HorizontalAlignment="Center"
                Foreground="White"
                Content="Внести предоплату"
                FontSize="20"
                Style="{StaticResource purple_gradient_btn}"
                Width="280"
                Height="60"/>

        </Grid>



    </Grid>
</abstactions:BasePage>
