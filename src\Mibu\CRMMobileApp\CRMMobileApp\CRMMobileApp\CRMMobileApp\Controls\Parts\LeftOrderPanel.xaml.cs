﻿using CRM.Models.Enums;
using CRM.Models.Enums.General;
using CRM.Models.Network.ReferenceBooks;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages;
using CRMMobileApp.Views.Pages.Delivery;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using TermoPrintingLib;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class LeftOrderPanel : ContentView
    {
        public LeftOrderPanel()
        {
            InitializeComponent();
            
        }

        public void Init()
        {
            //OrdersHelper.OnOrderItemsCountChanged += OrdersHelper_OnOrderItemsCountChanged;
            OrdersHelper.OnOrderItemAdded += OrdersHelper_OnOrderItemAdded;
            OrdersHelper.OnOrderItemModified += OrdersHelper_OnOrderItemModified;
            OrdersHelper.OnOrderItemDeleted += OrdersHelper_OnOrderItemDeleted;
            OrdersHelper.OnOrderChanged += OrdersHelper_OnOrderChanged;
            OrdersHelper.OnOrderInfoModified += OrdersHelper_OnOrderInfoModified;

            CurrentOrder = OrdersHelper.CurrentOrder;
            RenderItems();
            SetOrderCommentFrameVisibility();
            SetSendToWorkBtnVisibility();
            SetPriceText();

            splitOrderStackLayout.IsVisible = ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe;
        }

        private void OrdersHelper_OnOrderInfoModified(object sender, Order e)
        {
            CurrentOrder = OrdersHelper.CurrentOrder;

            SetOrderCommentFrameVisibility();
            SetSendToWorkBtnVisibility();
            SetPriceText();

            splitOrderStackLayout.IsVisible = ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe;
        }
        private void OrdersHelper_OnOrderChanged(object sender, Order e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                CurrentOrder = OrdersHelper.CurrentOrder;
                if (OrdersHelper.CurrentOrder is null)
                {
                    orderItemsLayout.Children.Clear();
                    orderTotalLabel.Text = $"Итого: 0.00₽";
                    orderDiscountLabel.Text = "Скидка: 0%";
                    clientLabel.Text = "Клиент: не выбран";
                    orderTotalWithDiscountLabel.Text = $"0.00₽";

                    if (Device.RuntimePlatform != Device.WPF)
                    {
                        createCancelOrderImg.Source = "create_transaction.png";
                    }
                    else
                    {
                        createCancelOrderImg.Source = "pack://application:,,,/Images/create_transaction.png";
                    }

                    orderCreateCloseText.Text = "Открыть заказ";
                    return;
                }

                SetOrderCommentFrameVisibility();
                SetSendToWorkBtnVisibility();
                SetPriceText();
            });

            RenderItems();
        }
        private void OrdersHelper_OnOrderItemDeleted(object sender, OrderItem e)
        {
            CurrentOrder = OrdersHelper.CurrentOrder;

            for (int  i = 0; i < orderItemsLayout.Children.Count; i++)
            {
                var view = orderItemsLayout.Children[i];
                if (view is OrderItemTemplate template && template.Model.Id == e.Id)
                {
                    orderItemsLayout.Children.Remove(view);
                    SetPriceText();
                    SetSendToWorkBtnVisibility();
                    break;
                }
            }
        }
        private void OrdersHelper_OnOrderItemModified(object sender, OrderItem e)
        {
            CurrentOrder = OrdersHelper.CurrentOrder;

            for (int i = 0; i < orderItemsLayout.Children.Count; i++)
            {
                var view = orderItemsLayout.Children[i];
                if (view is OrderItemTemplate template && template.Model.Id == e.Id)
                {
                    template.Render();
                    SetPriceText();
                    break;
                }
            }      
        }
        private void OrdersHelper_OnOrderItemAdded(object sender, OrderItem e)
        {
            CurrentOrder = OrdersHelper.CurrentOrder;

            RenderItem(e);
            SetSendToWorkBtnVisibility();
            SetPriceText();
        }




        #region Для закрытого заказа

        /// <summary>
        /// Метод для показа закрытого заказа
        /// </summary>
        /// <param name="order"></param>
        public void SetItems(Order order)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                orderItemsLayout.Children.Clear();
                if (order is null) return;

                CurrentOrder = order;


                orderIdSpan.Text = order.OrderNumber.ToString();
                returnOrderBtn.IsVisible = !order.WasReturned;
                activeOrderFooter.IsVisible = false;

                activeOrderBottomLayout.IsVisible = false;
                activeOrderBottomLayoutRowDef.Height = 0.1;

                foreach (var item in order.Items)
                {
                    var view = new OrderItemTemplate(item);
                    view.DeleteBtnTapped += View_DeleteBtnTapped;
                    orderItemsLayout.Children.Add(view);
                }
            });
        }
        public void SetClosedOrderMode()
        {
            orderIdFrame.InputTransparent = true;

            orderIdSpan.Text = "XX";
            createCancelOrderFrame.IsVisible = false;
            activeOrderFooter.IsVisible = false;

            activeOrderBottomLayout.IsVisible = false;
            activeOrderBottomLayoutRowDef.Height = 0.1;

            orderCommentFrame.IsVisible = false;
            sendItemsToWork.IsVisible = false;

            closedOrderFooter.IsVisible = true;

        }

        #region Печать чека закрытого заказа
        private ICommand printClosedOrderCheque;
        public ICommand PrintClosedOrderCheque
        {
            get => printClosedOrderCheque ??= new RelayCommand(async obj =>
            {
                if (CurrentOrder == null || CurrentOrder.IsActive)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Выберите заказ для возврата", "Ошибка"));
                    return;
                }


                if (ApplicationState.StorePrinters.Count == 0)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Печать невозможна, нет ни одного принтера для печати чеков", "Ошибка"));
                }
                else
                {     

                    if (ApplicationState.StorePrinters.Count == 1)
                    {
                        var printer = ApplicationState.StorePrinters[0];
                        PrintClosedOrder(printer);
                    }
                    else
                    {
                        var popup = new AvailablePrintersPopup(CRM.Models.Enums.Equipment.PrinterType.OrdersForWorkshops);
                        popup.ItemSelected += (o, e) =>
                        {
                            PrintClosedOrder(e);
                        };
                        await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                    }
                }
            });
        }

        private async void PrintClosedOrder(Printer printer)
        {
           

            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);

            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintOrderCheque(CurrentOrder);
            if (!result)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Ошибка печати", "Ошибка"));
            }
        }
        #endregion


        private ICommand returnClosedOrder;
        public ICommand ReturnClosedOrder
        {
            get => returnClosedOrder ??= new RelayCommand(async obj =>
            {
                if (CurrentOrder == null || CurrentOrder.IsActive)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Выберите заказ для возврата", "Ошибка"));
                    return;
                }


                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.OrderReturning))
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new ReturnOrder(CurrentOrder));
                }
            });
        }

        #endregion


        #region Рендер и т.д.
        private void RenderItems()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                SelectedOrderItem = null;
                orderItemsLayout.Children.Clear();

                if (CurrentOrder is null) return;


                foreach (var item in CurrentOrder.Items)
                {
                    RenderItem(item);
                }
            });
        }

        private void RenderItem(OrderItem item)
        {
            var view = new OrderItemTemplate(item);
            view.DeleteBtnTapped += View_DeleteBtnTapped;
            view.OrderItemTapped += View_OrderItemTapped;
            view.AmountChanged += View_AmountChanged;
            orderItemsLayout.Children.Add(view);
        }

        public void SetPriceText()
        {
            if (CurrentOrder is null) return;

            orderTotalLabel.Text = $"Итого: {Math.Round(CurrentOrder.Sum, 2)}₽";

            if (CurrentOrder.Discount != null)
            {
                orderDiscountLabel.Text = $"Скидка: {CurrentOrder.Discount.Percent}%";
            }
            else
            {
                orderDiscountLabel.Text = "Скидка: 0%";
            }

            if(CurrentOrder.Customer != null)
            {
                string clientFIO = $"Клиент: {CurrentOrder.Customer.Name} {CurrentOrder.Customer.Surname}";
                string discount = "";
                if(CurrentOrder.Customer.LoyaltyProgram?.LoyaltyProgramType == LoyaltyProgramType.Discount)
                {
                    discount = $", {CurrentOrder.Customer.LoyaltyProgram.GetClientMaxPossiblePercent(CurrentOrder.Customer)}%";
                }
                clientLabel.Text = $"{clientFIO}{discount}";
            }
            else
            {
                clientLabel.Text = "Клиент: не выбран";
            }

            orderTotalWithDiscountLabel.Text = $"{Math.Round(CurrentOrder.SumWithDiscount, 2)}₽";
        }
        private async void View_AmountChanged(object sender, OrderItem e)
        {
            //CurrentOrder = OrdersHelper.CurrentOrder;
            OrdersHelper.UpdateOrderItem(e);
            //SetPriceText();
          //  OrdersHelper.SaveOrder();
        }


        public void SetOrderCommentFrameVisibility()
        {
            orderCommentFrame.IsVisible = !string.IsNullOrEmpty(CurrentOrder?.Comment);
            SetActiveOrderBottomLayoutRowDefSize();
        }
        public void SetSendToWorkBtnVisibility()
        {
            if(CurrentOrder is null || ApplicationState.CurrentStore.StoreMode == StoreMode.Point)
            {
                sendItemsToWork.IsVisible = false;
            }
            else
            {
                sendItemsToWork.IsVisible = CurrentOrder.Items.Any(o => !o.IsInWork);
            }

            SetActiveOrderBottomLayoutRowDefSize();
        }

        private void SetActiveOrderBottomLayoutRowDefSize()
        {
            if (!activeOrderFooter.IsVisible)
            {
                activeOrderBottomLayoutRowDef.Height = 0.1;
            }
            else
            {
                double height = 125;
                if (!orderCommentFrame.IsVisible)
                {
                    height -= 70 + 5 + 5 + 6; //frame height + padding top + padding bottom + stacklayout spacing                  
                    if (!sendItemsToWork.IsVisible)
                    {
                        height = 0.1;
                    }
                }
                else if (!sendItemsToWork.IsVisible)
                {
                    height -= 40 + 6; //btn height + stacklayout spacing            
                }
                activeOrderBottomLayoutRowDef.Height = height;
            }
        }

        #endregion

        private Order currentOrder;
        public Order CurrentOrder
        {
            get => currentOrder;
            set 
            { 
                if(value != null)
                {
                    if (value.ClosedAtLocalAuto.HasValue)
                    {
                        orderIdSpan.Text = "XX";

                        if(Device.RuntimePlatform != Device.WPF)
                        {
                            createCancelOrderImg.Source = "create_transaction.png";
                        }
                        else
                        {
                            createCancelOrderImg.Source = "pack://application:,,,/Images/create_transaction.png";
                        }
            
                        orderCreateCloseText.Text = "Открыть заказ";
                    }
                    else
                    {
                       

                        if (Device.RuntimePlatform != Device.WPF)
                        {
                            createCancelOrderImg.Source = "close_duty.png";
                        }
                        else
                        {
                            createCancelOrderImg.Source = "pack://application:,,,/Images/close_duty.png";
                        }


                        orderCreateCloseText.Text = "Отменить заказ";
                        orderIdSpan.Text = value.OrderNumber.ToString();
                    }       
                }
                else
                {
                    orderIdSpan.Text = "XX";

                    if (Device.RuntimePlatform != Device.WPF)
                    {
                        createCancelOrderImg.Source = "create_transaction.png";
                    }
                    else
                    {
                        createCancelOrderImg.Source = "pack://application:,,,/Images/create_transaction.png";
                    }


                    orderCreateCloseText.Text = "Открыть заказ";
                }
                SetOrderCommentFrameVisibility();
                SetSendToWorkBtnVisibility();

                currentOrder = value;
                OnPropertyChanged(nameof(CurrentOrder));
            }
        }

        #region Верхние кнопки

        private ICommand showActiveOrders;
        public ICommand ShowActiveOrders
        {
            get => showActiveOrders ??= new RelayCommand(async obj =>
            {
                await OrdersHelper.GetActiveOrders();
                if(OrdersHelper.ActiveOrders.Count == 0)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("На данный момент нет открытых заказов","Уведомление"));
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new ActiveOrdersPopup());
                }  
            });
        }


        private ICommand openOrCloseOrder;
        public ICommand OpenOrCloseOrder
        {
            get => openOrCloseOrder ??= new RelayCommand(async obj =>
            {
                if (CurrentOrder != null)
                {
                    if (CurrentOrder.ClosedAtLocalAuto.HasValue)
                    {
                        await OrdersHelper.OpenOrder();
                        CurrentOrder = OrdersHelper.CurrentOrder;
                    }
                    else
                    {
                        await NavigationHelper.GoBackIfCafe(false);

                        await OrdersHelper.CancelOrder();
                        CurrentOrder = OrdersHelper.CurrentOrder;
                    }
                }
                else
                {
                    await OrdersHelper.OpenOrder();
                    CurrentOrder = OrdersHelper.CurrentOrder;
                }
            });
        }

        #endregion

        #region Меню внизу
        private ICommand openMenu;
        public ICommand OpenMenu
        {
            get => openMenu ??= new RelayCommand(async obj =>
            {
                menuFrame.IsVisible = true;
            });
        }
        private ICommand closeMenu;
        public ICommand CloseMenu
        {
            get => closeMenu ??= new RelayCommand(async obj =>
            {
                menuFrame.IsVisible = false;
            });
        }


        private ICommand splitOrder;
        public ICommand SplitOrder
        {
            get => splitOrder ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OrderDivisionPopup());
            });
        }


        private ICommand setClient;
        public ICommand SetClient
        {
            get => setClient ??= new RelayCommand(async obj =>
            {
                if(OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                await App.Current.MainPage.Navigation.PushPopupAsync(new ClientsPopup(CurrentOrder));
                menuFrame.IsVisible = false;
            });
        }

        private ICommand setDiscount;
        public ICommand SetDiscount
        {
            get => setDiscount ??= new RelayCommand(async obj =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                await App.Current.MainPage.Navigation.PushPopupAsync(new DiscountPopup(CurrentOrder));
                menuFrame.IsVisible = false;
            });
        }

        private ICommand setOrderComment;
        public ICommand SetOrderComment
        {
            get => setOrderComment ??= new RelayCommand(async obj =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                await App.Current.MainPage.Navigation.PushPopupAsync(new OrderCommentPopup(CurrentOrder));
                menuFrame.IsVisible = false;
            });
        }
        private ICommand clearOrderItems;
        public ICommand ClearOrderItems
        {
            get => clearOrderItems ??= new RelayCommand(async obj =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }


                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.CheckPositionRemoving))
                {
                    if (!CurrentOrder.Items.Any(o => o.IsInWork))
                    {
                        SelectedOrderItem = null;

                        OrdersHelper.ClearOrderItems();
                        menuFrame.IsVisible = false;
                    }
                    else
                    {
                        var items = CurrentOrder.Items.Where(o => o.IsInWork).ToList();
                        await App.Current.MainPage.Navigation.PushPopupAsync(new CancelOrderItemPopup(items));
                    }
                }            
            });
        }



        #endregion

        #region Работа с выбранной позицией заказа

        private OrderItem selectedOrderItem = null;
        private OrderItem SelectedOrderItem
        {
            get => selectedOrderItem;
            set
            {
                editAmountStackLayout.IsVisible = value != null;
                editModifiersStackLayout.IsVisible = value != null;
                orderItemCommentStackLayout.IsVisible = value != null;

                selectedOrderItem = value;
                OnPropertyChanged(nameof(SelectedOrderItem));
            }
        }

        private void View_OrderItemTapped(object sender, OrderItem e)
        {
            foreach (var item in orderItemsLayout.Children.Cast<OrderItemTemplate>())
            {
                item.IsSelected = false;
            }
            (sender as OrderItemTemplate).IsSelected = true;
            SelectedOrderItem = e;
        }

        private async void View_DeleteBtnTapped(object sender, OrderItem e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.CheckPositionRemoving))
            {
                if (!e.IsInWork)
                {
                    SelectedOrderItem = null;
                    OrdersHelper.DeleteOrderItem(e);

                    //var view = sender as OrderItemTemplate;
                    //orderItemsLayout.Children.Remove(view);
				}
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new CancelOrderItemPopup(e));
                }
            }
        }

        private ICommand editAmount;
        public ICommand EditAmount
        {
            get => editAmount ??= new RelayCommand(async obj =>
            {
                if (SelectedOrderItem.TechnicalCard != null)
                {
                    if (SelectedOrderItem.TechnicalCard.IsWeightProduct)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new WeightTechCardOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new TechCardOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                }
                else if (SelectedOrderItem.Product != null)
                {
                    if (SelectedOrderItem.Product.IsWeightProduct)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new WeightProductOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new ProductOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount));
                    }
                }
            });
        }


        private ICommand editModifiers;
        public ICommand EditModifiers
        {
            get => editModifiers ??= new RelayCommand(async obj =>
            {
                List<Modifier> availableModifiers = new List<Modifier>();
                if(SelectedOrderItem.TechnicalCard != null)
                {
                    availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForTechCard(ApplicationState.CurrentDomain,
                                                                                                                              ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                              SelectedOrderItem.TechnicalCard.Id);
                }
                else if (SelectedOrderItem.Product != null)
                {
                    availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForProduct(ApplicationState.CurrentDomain,
                                                                                                                             ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                             SelectedOrderItem.Product.Id);
                }


                if (availableModifiers.Any())
                {
                    var popup = new ModifiersPopup(SelectedOrderItem, availableModifiers, true);
                    await App.Current.MainPage.Navigation.PushPopupAsync(popup);

                    while (!popup.IsCompleted)
                        await Task.Delay(200);

                    if (!popup.IsSuccessfully)
                    {
                        return;
                    }

                    await OrdersHelper.SetOrderItemModifiers(SelectedOrderItem);
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Для данной позиции нет доступных модификаторов","Уведомление"));
                }
            });
        }


        private ICommand goToOrderItemCommentPopup;
        public ICommand GoToOrderItemCommentPopup
        {
            get => goToOrderItemCommentPopup ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OrderItemCommentPopup(SelectedOrderItem));
            });
        }

        #endregion


        private ICommand sendItemsToWorkCommand;
        public ICommand SendItemsToWorkCommand
        {
            get => sendItemsToWorkCommand ??= new RelayCommand(async obj =>
            {
                OrdersHelper.SendItemsToWork();
            });
        }


        private ICommand lockScreen;
        public ICommand LockScreen
        {
            get => lockScreen ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new MainPage());
            });
        }
        private ICommand payOrder;
        public ICommand PayOrder
        {
            get => payOrder ??= new RelayCommand(async obj =>
            {

                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нельзя оплатить заказ, не открыв его", "Ошибка"));
                }
                else if (CurrentOrder.ClosedAtLocalAuto.HasValue)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нельзя оплатить уже закрытый заказ", "Ошибка"));
                }
                else if (await Auth.CheckAllowance(rights.CheckPayment))
                {
                    if (CurrentOrder.Items.Count == 0)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нельзя оплатить пустой заказ", "Ошибка"));
                    }
                    else if (CurrentOrder.SumWithDiscountDebtAfterPrepaymens < 0)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup($"Не вся предоплата ({Math.Round(CurrentOrder.PrepaymentsSum,2)}₽) еще израсходована", "Ошибка"));
                    }
                    else
                    {
                        await App.Current.MainPage.Navigation.PushAsync(new CreateOrder());
                    }
                }
            });
        }
    }
}