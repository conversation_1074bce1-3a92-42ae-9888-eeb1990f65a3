﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.TechnicalCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="200"
             d:DesignWidth="200">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        CornerRadius="10"
        Padding="0"
        MouseDown="onCardClicked"
        Background="{StaticResource bg_purple}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="6*"/>
                <RowDefinition Height="2*"/>
                <RowDefinition Height="2*"/>
            </Grid.RowDefinitions>
            
            
            
            <Image 
                Grid.Row="0"
                Stretch="Fill"
                Source="{Binding ElementName=this,Path=Model.ImgPath}"/>
            <TextBlock 
                x:Name="lettersLabel"
                Grid.Row="0"
                FontSize="65"
                Foreground="{StaticResource dark_purple}"
                FontFamily="TTFirsNeue-Regular"
                TextAlignment="Center"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"/>
            
            
            
            
            <TextBlock 
                Text="{Binding ElementName=this,Path=Model.Title}"
                Grid.Row="1"
                Margin="0,0,0,10"
                FontSize="16"
                Foreground="{StaticResource dark_purple}"
                FontFamily="TTFirsNeue-Regular"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"/>
            <TextBlock 
                x:Name="priceLabel"
                Grid.Row="2"
                Margin="0,0,0,10"
                Text="0 р"
                Foreground="{StaticResource dark_purple}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="16"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"/>
        </Grid>
    </Border>
</UserControl>
