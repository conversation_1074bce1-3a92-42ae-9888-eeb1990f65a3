/* --- FONTS --- */
@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-Light.eot');
    src: local('TT Firs Neue Light'), local('TTFirsNeue-Light'),
        url('../font/TTFirsNeue-Light.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Light.woff2') format('woff2'),
        url('../font/TTFirsNeue-Light.woff') format('woff'),
        url('../font/TTFirsNeue-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-Regular.eot');
    src: local('../font/TT Firs Neue Regular'), local('TTFirsNeue-Regular'),
        url('../font/TTFirsNeue-Regular.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Regular.woff2') format('woff2'),
        url('../font/TTFirsNeue-Regular.woff') format('woff'),
        url('../font/TTFirsNeue-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-BoldItalic.eot');
    src: local('TT Firs Neue Bold Italic'), local('TTFirsNeue-BoldItalic'),
        url('../font/TTFirsNeue-BoldItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-BoldItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-BoldItalic.woff') format('woff'),
        url('../font/TTFirsNeue-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-DemiBoldItalic.eot');
    src: local('TT Firs Neue DemiBold Italic'), local('TTFirsNeue-DemiBoldItalic'),
        url('../font/TTFirsNeue-DemiBoldItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-DemiBoldItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-DemiBoldItalic.woff') format('woff'),
        url('../font/TTFirsNeue-DemiBoldItalic.ttf') format('truetype');
    font-weight: 600;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-ExtraLight.eot');
    src: local('TT Firs Neue ExtraLight'), local('TTFirsNeue-ExtraLight'),
        url('../font/TTFirsNeue-ExtraLight.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-ExtraLight.woff2') format('woff2'),
        url('../font/TTFirsNeue-ExtraLight.woff') format('woff'),
        url('../font/TTFirsNeue-ExtraLight.ttf') format('truetype');
    font-weight: 200;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-ThinItalic.eot');
    src: local('TT Firs Neue Thin Italic'), local('TTFirsNeue-ThinItalic'),
        url('../font/TTFirsNeue-ThinItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-ThinItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-ThinItalic.woff') format('woff'),
        url('../font/TTFirsNeue-ThinItalic.ttf') format('truetype');
    font-weight: 100;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-MediumItalic.eot');
    src: local('TT Firs Neue Medium Italic'), local('TTFirsNeue-MediumItalic'),
        url('../font/TTFirsNeue-MediumItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-MediumItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-MediumItalic.woff') format('woff'),
        url('../font/TTFirsNeue-MediumItalic.ttf') format('truetype');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-DemiBold.eot');
    src: local('TT Firs Neue DemiBold'), local('TTFirsNeue-DemiBold'),
        url('../font/TTFirsNeue-DemiBold.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-DemiBold.woff2') format('woff2'),
        url('../font/TTFirsNeue-DemiBold.woff') format('woff'),
        url('../font/TTFirsNeue-DemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-BlackItalic.eot');
    src: local('TT Firs Neue Black Italic'), local('TTFirsNeue-BlackItalic'),
        url('../font/TTFirsNeue-BlackItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-BlackItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-BlackItalic.woff') format('woff'),
        url('../font/TTFirsNeue-BlackItalic.ttf') format('truetype');
    font-weight: 900;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-LightItalic.eot');
    src: local('TT Firs Neue Light Italic'), local('TTFirsNeue-LightItalic'),
        url('../font/TTFirsNeue-LightItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-LightItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-LightItalic.woff') format('woff'),
        url('../font/TTFirsNeue-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-ExtraLightItalic.eot');
    src: local('TT Firs Neue ExtraLight Italic'), local('TTFirsNeue-ExtraLightItalic'),
        url('../font/TTFirsNeue-ExtraLightItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-ExtraLightItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-ExtraLightItalic.woff') format('woff'),
        url('../font/TTFirsNeue-ExtraLightItalic.ttf') format('truetype');
    font-weight: 200;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-Bold.eot');
    src: local('TT Firs Neue Bold'), local('TTFirsNeue-Bold'),
        url('../font/TTFirsNeue-Bold.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Bold.woff2') format('woff2'),
        url('../font/TTFirsNeue-Bold.woff') format('woff'),
        url('../font/TTFirsNeue-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-ExtraBold.eot');
    src: local('TT Firs Neue ExtraBold'), local('TTFirsNeue-ExtraBold'),
        url('../font/TTFirsNeue-ExtraBold.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-ExtraBold.woff2') format('woff2'),
        url('../font/TTFirsNeue-ExtraBold.woff') format('woff'),
        url('../font/TTFirsNeue-ExtraBold.ttf') format('truetype');
    font-weight: 800;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-Medium.eot');
    src: local('TT Firs Neue Medium'), local('TTFirsNeue-Medium'),
        url('../font/TTFirsNeue-Medium.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Medium.woff2') format('woff2'),
        url('../font/TTFirsNeue-Medium.woff') format('woff'),
        url('../font/TTFirsNeue-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-Thin.eot');
    src: local('TT Firs Neue Thin'), local('TTFirsNeue-Thin'),
        url('../font/TTFirsNeue-Thin.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Thin.woff2') format('woff2'),
        url('../font/TTFirsNeue-Thin.woff') format('woff'),
        url('../font/TTFirsNeue-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-ExtraBoldItalic.eot');
    src: local('TT Firs Neue ExtraBold Italic'), local('TTFirsNeue-ExtraBoldItalic'),
        url('../font/TTFirsNeue-ExtraBoldItalic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-ExtraBoldItalic.woff2') format('woff2'),
        url('../font/TTFirsNeue-ExtraBoldItalic.woff') format('woff'),
        url('../font/TTFirsNeue-ExtraBoldItalic.ttf') format('truetype');
    font-weight: 800;
    font-style: italic;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('../font/TTFirsNeue-Black.eot');
    src: local('TT Firs Neue Black'), local('TTFirsNeue-Black'),
        url('../font/TTFirsNeue-Black.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Black.woff2') format('woff2'),
        url('../font/TTFirsNeue-Black.woff') format('woff'),
        url('../font/TTFirsNeue-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: 'TT Firs Neue';
    src: url('TTFirsNeue-Italic.eot');
    src: local('TT Firs Neue Italic'), local('TTFirsNeue-Italic'),
        url('../font/TTFirsNeue-Italic.eot?#iefix') format('embedded-opentype'),
        url('../font/TTFirsNeue-Italic.woff2') format('woff2'),
        url('../font/TTFirsNeue-Italic.woff') format('woff'),
        url('../font/TTFirsNeue-Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
}

/* --- PAGE --- */
.container.displaycenter {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    align-content: center;
}

.row.displayrowcenter {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
}

.row.displayrowbottom{
    display: flex;
    align-content: center;
    align-items: flex-end;
    justify-content: center;
}

/* --- BRAND --- */
.classlogotype {
    display: flex;
    justify-content: center;
}

.logotype {
    display: flex;
    width: 80px;
    height: 50px;
    background: url(/media/img/logotype.svg);
    background-repeat: no-repeat;
    margin-bottom: 30px;
}

.welcome {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 30px;
    line-height: 30px;
    text-align: center;
    color: #524E7D;
    margin-bottom: 40px;
}

.classnavauth {
    display: flex;
    margin-bottom: 40px;
    justify-content: center;
    align-content: center;
    align-items: center;
}

.classnavauth .nav-link.active, .nav-pills .show>.nav-link {
    background: transparent;
    color: #524E7D;
    border-bottom: 1px solid #524E7D;
}

.classnavauth .nav-link {
    color: #9795B1;
    border-radius: 0;
    padding: 0;
    margin: 0 30px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
}

/* --- BLOCKS --- */
.block {
    background: #F6F6FB;
    padding: 30px 40px;
    border-radius: 10px;
}

.block >.block-header {

}

.block > .block-content {

}

.block-register {
    width: 520px;
}

/* --- INPUTS AND LABEL --- */
.form-control {
    background: #FFFFFF;
    border-radius: 5px;
    border: none !important;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

.form-select {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

input:after[type="date"]  {
    content: '';
    position:absolute;
    right: 10px;
    top: 5px;
    width: 10px;
    height: 10px;
    background: red;
}

.passinput {
	position: relative;
}

.calendar-gr {
	position: relative;
}

.passwordform-control {
	position: absolute;
	top: 6px;
	right: 6px;
	display: inline-block;
	width: 20px;
	height: 20px;
	background: url(/media/img/icons/noun-eye-4849668\ 1.svg) 0 0 no-repeat;
}

.passwordform-control.view {
	background: url(/media/img/icons/noun-eye-4849668\ 1.svg) 0 0 no-repeat;
}

.passwordform-control2 {
	position: absolute;
	top: 6px;
	right: 6px;
	display: inline-block;
	width: 20px;
	height: 20px;
	background: url(/media/img/icons/noun-eye-close.svg) 0 0 no-repeat;
}

.labelform {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
}

.calendar-control {
    position: absolute;
    top: 10px;
    right: 12px;
    display: inline-block;
    width: 20px;
    height: 20px;
    background-color: #fff;
    mask: url(/media/img/icons/noun-calendar-4782725\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-calendar-4782725\ 2.svg);
}

.calendar-control.view {
	background: url(/media/img/icons/noun-calendar-4782725\ 2.svg) 0 0 no-repeat;
}

.form-check-label {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

.form-check-input {
    background: #FFFFFF;
    border-radius: 5px;
}

.authline {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
}


input::-webkit-calendar-picker-indicator{
    position: absolute;
	top: 6px;
	right: 6px;
	display: inline-block;
	width: 20px;
	height: 20px;
	background: url(/media/img/icons/noun-calendar-purple.svg) 0 0 no-repeat;
}

input.search-input {
    background: #FFFFFF;
    border-radius: 20px;
    border: none;
    padding: 2px 20px 2px 40px;
    outline: none;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    color: #9795B1;
}

input.search-input.gray {
    background: #F6F6FB;
    border-radius: 20px;
}

.search-input::placeholder {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    color: #9795B1;
}

select.form-select2 {
    background: #FFFFFF;
    border-radius: 5px;
    border: none !important;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
    padding: 7px 15px;
    width: 100%;
    margin-top: 4px;
}

/* --- LINKS AND BOTTONS --- */
.buttonclass {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    padding: 10px;
    color: #fff;
    border-radius: 30px;
    border: none;
    outline: none;
    text-decoration: none;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    text-align: center;
}

.authrestore {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    text-decoration: none;
    text-align: right;
    color: #9795B1;
}

.authcenter {
    display: flex;
    justify-content: center;
    align-content: center;
    margin-top: 30px;
}

.auttbutton {
    width: 240px;
}

.illustrator {
    background: url(/media/img/barista.svg);
    width: 680px;
    height: 660px;
}

.element-search {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.element-search.mini {
    margin: 20px 20px 0 20px;
}

.element-search > .search-update {
    margin-left: 4px !important;
}

.iconbtn {
    background: #FFF;
    height: 20px;
    width: 20px;
    margin-left: 10px;
}

.iconbtn.gift {
    mask: url(/media/img/iconbtn/noun-gift-1165617\ 1.svg);
    -webkit-mask: url(/media/img/iconbtn/noun-gift-1165617\ 1.svg);
}

.iconbtn.load {
    mask: url(/media/img/iconbtn/noun-cloud-upload-3778011\ 1.svg);
    -webkit-mask: url(/media/img/iconbtn/noun-cloud-upload-3778011\ 1.svg);
}

.iconbtn.download {
    mask: url(/media/img/iconbtn/noun-save-1374177\ 1.svg);
    -webkit-mask: url(/media/img/iconbtn/noun-save-1374177\ 1.svg);
}

.search-update {
    background: transparent;
    mask: url(/media/img/icons/noun-update-3325488\ 1.svg);
    -webkit-mask: url(/media/img/icons/noun-update-3325488\ 1.svg);
    background: #9795B1;
    height: 20px;
    width: 20px;
    margin-left: 15px;
}

.search-add {
    background: transparent;
    mask: url(/media/img/icons/plus.svg);
    -webkit-mask: url(/media/img/icons/plus.svg);
    background: #9795B1;
    height: 20px;
    width: 20px;
}

.policon {
    display: flex;
    align-content: center;
    align-items: center;
}

.iconmibu.search {
    mask: url(/media/img/icons/fe_search.svg);
    -webkit-mask: url(/media/img/icons/fe_search.svg);
    background: #AFADC5;
    position: absolute;
    margin: 10px;
}

.buttonclassic {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #FFFFFF;
    border: none;
    padding: 11px 40px;
    width: 100%;
    outline: none;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

button.btn.btn-primary {
    border: none;
    background: #7265FB;
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    padding: 5px 30px;
}

button.btn.btn-success {
    border: none;
    background: #26E27C;
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    padding: 5px 30px;
}


button.btn.btn-gold {
    background: linear-gradient(91.43deg, #F5B276 0%, #DF8534 100%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    align-content: center;
    padding: 1px 30px;
}

button.btn.btn-green {
    background: linear-gradient(101.67deg, #7CE9AE 0%, #22BA68 99.98%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    align-content: center;
    padding: 1px 30px;
}

button.btn.btn-purple {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    align-content: center;
    padding: 1px 30px;
}

.btn.btn-purple {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    align-content: center;
    padding: 1px 30px;
}

.btn-panel {
    display: flex;
    justify-content: center;
    padding: 6px 20px !important;
    width: 100%;
    align-items: center;
}

.btn-group.btn-group-width {
    width: 100%;
}

.margin-btn {
    margin-right: 10px;
}

button.btn.dropdown-toggle::after {
    display: block;
    vertical-align: 0;
    content: "";
    border-top: 0;
    border-right: 0;
    border-bottom: 0;
    border-left: 0;
    display: inline-flex;
    height: 6px;
    width: 11px;
    background: url(/media/img/icons/arrows/arrow-down.svg);
    mask: url(/media/img/icons/arrows/arrow-down.svg);
    -webkit-mask: url(/media/img/icons/arrows/arrow-down.svg);
    background: #fff;
}

button.btn.btn-primary.dropdown-toggle.btn-sm {
    padding: 0px 30px;
}

.button-header {
    padding: 0 20px;
}

.label {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
    margin-right: 20px;
}

.label.wrappright {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
}

.label.wrappright > b {
    margin-left: 5px;
}

.badge {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    color: #FFFFFF;
    border-radius: 5px;
}

.badge.text-bg-primary {
    background-color: #7265FB !important;
}

.badge-dp {

}

.edittable {
    display: inline-flex;
    mask: url(/media/img/icons/noun-pencil-1278728\ 4.svg);
    -webkit-mask: url(/media/img/icons/noun-pencil-1278728\ 4.svg);
    height: 20px;
    width: 20px;
    background-color: #23CF72;
    margin-right: 5px;
}

.removetable {
    display: inline-flex;
    mask: url(/media/img/icons/noun-trash-2149063\ 4.svg);
    -webkit-mask: url(/media/img/icons/noun-trash-2149063\ 4.svg);
    height: 20px;
    width: 20px;
    background-color: #7265FB;
    margin-right: 5px;
}

.addtable {
    display: inline-flex;
    mask: url(/media/img/icons/plus.svg);
    -webkit-mask: url(/media/img/icons/plus.svg);
    height: 20px;
    width: 20px;
    background-color: #F2994A;
    margin-right: 5px;
}

/* --- SIDEBAR --- */
.bg-light {
    position: fixed;
}

.bgpanel {
    background: #F6F6FB;
    height: 100vh;
}

.logotype-sidebar {
    width: 60px;
    height: 30px;
    background: url(/media/img/logodashboard.svg);
    background-repeat: no-repeat;
}

.headersidebar {
    display: flex;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    text-transform: uppercase;
    color: #524E7D;
    border-bottom: 1px solid #CBCAD8 !important;
    border: none;
    border-radius: 0px !important;
    width: 100%;
    padding: 0 0 10px 0;
    justify-content: space-between;
    margin-bottom: 20px;
}

.headersidebar::before {
    height: 6px;
    width: 11px;
    background: url(/media/img/icons/arrows/arrow-down.svg);
}

.headersidebar:hover {
    border-bottom: 1px solid #CBCAD8 !important;
    outline: none;
}

.p-dashboard {
    padding: 10px 0;
}

.link-panel {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
    padding: 10px 0;
    outline: none;
    border: none;
}

.nav-dashboard {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-transform: uppercase;
    color: #524E7D;
    padding: 10px 0;
}

.nav-user > .username {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    text-align: right;
    color: #524E7D;
}

.nav-user > .groupsustem {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 10px;
    text-align: right;
    color: #524E7D;
}

.nav-dableitem > .dropdown-toggle::after {
    display: block;
    vertical-align: 0;
    content: "";
    border-top: 0;
    border-right: 0;
    border-bottom: 0;
    border-left: 0;
    display: flex;
    height: 6px;
    width: 11px;
    background: url(/media/img/icons/arrows/arrow-down.svg);
    position: absolute;
    right: -5px;
    top: 25px;
}

.nav-dableitem > .show.dropdown-toggle::after {
    display: block;
    vertical-align: 0;
    content: "";
    border-top: 0;
    border-right: 0;
    border-bottom: 0;
    border-left: 0;
    display: flex;
    height: 6px;
    width: 11px;
    background: url(/media/img/icons/arrows/arrow-down-mini.svg);
    transform: rotate(180deg);
    position: absolute;
    right: -5px;
    top: 25px;
}

.nav-dashboard.dropdown-toggle::after {
    vertical-align: 0;
    content: "";
    border-top: 0;
    border-right: 0;
    border-bottom: 0;
    border-left: 0;
    display: inline-flex;
    height: 6px;
    width: 11px;
    background: url(/media/img/icons/arrows/arrow-down.svg);
}

.nav-dashboard.show.dropdown-toggle::after {
    vertical-align: 0;
    content: "";
    border-top: 0;
    border-right: 0;
    border-bottom: 0;
    border-left: 0;
    display: inline-flex;
    height: 6px;
    width: 11px;
    background: url(/media/img/icons/arrows/arrow-down.svg);
    transform: rotate(180deg);
}

.nav-right-user {
    display: flex;
    align-items: center;
}

.chatbutton {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 10px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    color: #FFFFFF;
    border: none;
    display: flex;
    padding: 3px 30px;
    outline: none;
    align-items: center;
}

.sidebar-footer {
    position: fixed;
    bottom: 0;
    margin-bottom: 30px;
}

.sublink-panel > .name {
    display: flex;
    align-content: center;
    align-items: center;
}

.headersidebar.btn-toggle::after {
    width: 12px;
    line-height: 0;
    content: url(/media/img/icons/arrows/arrow-up.svg);
    transition: transform .35s ease;
    transform-origin: 0.5em 50%;
}

.headersidebar.collapsed::after {
    width: 12px;
    line-height: 0;
    content: url(/media/img/icons/arrows/arrow-up.svg);
    transition: transform .35s ease;
    transform-origin: 0.5em 50%;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
}

.sublink-panel {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.sub-nav {
    margin-left: 40px;
}

.sublink-panel.btn-toggle::after {
    width: 4px;
    line-height: 0;
    content: url(/media/img/icons/arrows/arrow-down-mini.svg);
    transition: transform .35s ease;
    transform-origin: 0.5em 50%;
}

.sublink-panel.collapsed::after {
    width: 4px;
    line-height: 0;
    content: url(/media/img/icons/arrows/arrow-left.svg);
    transition: transform .35s ease;
    transform-origin: 0.5em 50%;
}

.iconinfo > .labelchat {
    background: #26E27C;
    display: inline-flex;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 8px;
    line-height: 8px;
    padding: 2.5px;
    margin: -3px 0 0 -5px;
}

a.link-panel.d-inline-flex.text-decoration-none.rounded.active {
    border-left: #26E27C solid 3px;
    border-radius: 0px !important;
    margin: 0 0 0 -28px;
    padding: 10px 0 10px 25px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

.link-panel.d-inline-flex.text-decoration-none.rounded.active > .iconmibu.iconnav {
    background-color: #7265FB;
}

.nav.nav-panel > .nav-item > a.nav-link {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 300;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
}

.nav.nav-panel > .nav-item > a.nav-link.active {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    text-align: right;
    color: #524E7D;
}

/* --- ICONS --- */
.iconmibu {
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
}

.iconmibumi {
    height: 12px;
    width: 12px;
    background-repeat: no-repeat;
}

.iconmibumi.rigth {
    margin-right: 10px;
}

.iconmibumi.white { background-color: #FFFFFF; }

.iconinfo {
    display: inline-flex;
    margin-right: 5px;
}

.iconmibu.iconnav {
    margin-right: 20px;
    background-color: #AFADC5;
}

.iconmibu.iconbutton {
    display: inline-flex;
}

.iconmibu.statistic {
    mask: url(/media/img/icons/statistic-up.svg);
    -webkit-mask: url(/media/img/icons/statistic-up.svg);
}

.iconmibu.basket {
    mask: url(/media/img/icons/basket.svg);
    -webkit-mask: url(/media/img/icons/basket.svg);
}

.iconmibu.box {
    mask: url(/media/img/icons/noun-box-1409388\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-box-1409388\ 2.svg);
}

.iconmibu.tasklist {
    mask: url(/media/img/icons/fluent_clipboard-task-list-ltr-24-regular.svg);
    -webkit-mask: url(/media/img/icons/fluent_clipboard-task-list-ltr-24-regular.svg);
}

.iconmibu.settings {
    mask: url(/media/img/icons/noun-settings-1557085\ 3.svg);
    -webkit-mask: url(/media/img/icons/noun-settings-1557085\ 3.svg);
}

.iconmibu.chartlist {
    mask: url(/media/img/icons/bi_bar-chart-line.svg);
    -webkit-mask: url(/media/img/icons/bi_bar-chart-line.svg);
}

.iconmibu.book {
    mask: url(/media/img/icons/noun-book-2993109\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-book-2993109\ 2.svg);
}

.iconmibu.money {
    mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
}

.iconmibu.discount {
    mask: url(/media/img/icons/noun-discount-3500408\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-discount-3500408\ 2.svg);
}

.iconmibu.chat {
    background: url(/media/img/icons/chat-white.svg);
}

.iconmibu.report {
    mask: url(/media/img/icons/noun-report-1410554\ 5.svg);
    -webkit-mask: url(/media/img/icons/noun-report-1410554\ 5.svg);
}

.iconmibu.account {
    mask: url(/media/img/icons/noun-account-3190826\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-account-3190826\ 2.svg);
}

.iconmibu.wallet {
    mask: url(/media/img/icons/wallet-b.svg);
    -webkit-mask: url(/media/img/icons/wallet-b.svg);
}

.iconmibu.search {
    mask: url(/media/img/icons/fe_search.svg);
    -webkit-mask: url(/media/img/icons/fe_search.svg);
}

.iconmibumi.plus-mini {
    mask: url(/media/img/icons/plus-mini.svg);
    -webkit-mask: url(/media/img/icons/plus-mini.svg);
}

.icontable {
    display: inline-flex;
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.icontable.bank {
    mask: url(/media/img/icons/noun-credit-cards-2372005\ 7.svg);
    -webkit-mask: url(/media/img/icons/noun-credit-cards-2372005\ 7.svg);
    background-color: #7265FB;
}

.icontable.cash {
    mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
    background-color: #23CF72;
}

.icontable.other {
    mask: url(/media/img/icons/other.svg);
    -webkit-mask: url(/media/img/icons/other.svg);
    background-color: #9795B1;
    height: 17px;
    width: 3px;
}

.iconmibucrl {
    height: 40px;
    width: 40px;
}

.iconmibucrl.purple { background-color: #7265FB; }
.iconmibucrl.green { background-color: #26E27C; }
.iconmibucrl.orange { background-color: #F2994A; }
.iconmibucrl.blue { background-color: #2D9CDB; }
.iconmibucrl.gray { background-color: #524E7D; }
.iconmibucrl.yellow { background-color: #F2C94C; }

.iconmibucrl.money {
    mask: url(/media/img/iconcrl/noun-money-1314881\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
}

.iconmibucrl.ruble {
    mask: url(/media/img/iconcrl/noun-ruble-2874291\ 2.svg);
    -webkit-mask: url(/media/img/iconcrl/noun-ruble-2874291\ 2.svg);
}

.iconmibucrl.purse {
    mask: url(/media/img/iconcrl/noun-purse-3362985\ 1.svg);
    -webkit-mask: url(/media/img/iconcrl/noun-purse-3362985\ 1.svg);
}

.iconmibucrl.cards {
    mask: url(/media/img/iconcrl/noun-credit-cards-2372005\ 4.svg);
    -webkit-mask: url(/media/img/iconcrl/noun-credit-cards-2372005\ 4.svg);
}

.iconmibucrl.discount {
    mask: url(/media/img/iconcrl/noun-discount-3500408\ 2.svg);
    -webkit-mask: url(/media/img/iconcrl/noun-discount-3500408\ 2.svg);
}

.iconmibucrl.report {
    mask: url(/media/img/iconcrl/noun-report-1410554\ 1.svg);
    -webkit-mask: url(/media/img/iconcrl/noun-report-1410554\ 1.svg);
}

.payment-status.bank {
    display: flex;
    align-items: center;
    align-content: center;
    color: #7265FB;
}

.payment-status.cash {
    display: flex;
    align-items: center;
    align-content: center;
    color: #23CF72;
}

.icon-arrowdown-nav {
    height: 4px;
    width: 9px;
    background: url(/media/img/icons/arrows/arrow-down.svg);
}

/* --- BREADCRUMBS --- */
.breadcrumb {
    --bs-breadcrumb-margin-bottom: 0 !important;
}

.breadcrumb-item+.breadcrumb-item::before {
    float: left;
    content: url(/media/img/icons/arrows/arrow-right.svg);
    height: 8px;
    width: 4px;
    margin-right: 8px;
}

.breadcrumb-dashboard {
    display: flex;
    background: #FFFFFF;
    border: 1px solid #AFADC5;
    border-radius: 5px;
    align-items: center;
    align-content: center;
    flex-direction: row;
    --bs-breadcrumb-margin-bottom: 0 !important;
    padding: 7px 20px 10px 20px;
    text-decoration: none;
}

.breadcrumb-item > a {
    text-decoration: none;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
}

.breadcrumb-item.active {
    text-decoration: none;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
}

/* --- CALENDAR INPUT --- */
.calendar-input {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #FFFFFF;
    border: none;
    padding: 11px 40px 11px 11px;
    width: 100%;
    outline: none;
    text-align: center;
    cursor: pointer;
}

.calendar-input::after {
    mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
    -webkit-mask: url(/media/img/icons/noun-money-1314881\ 2.svg);
    background-size: 20px;
}

.calendar-input::placeholder {
    color: #fff;
    text-align: center;
}

/* --- CHART --- */
canvas.statpay {
    height: 160px;
    width: 130px;
}

canvas.positperiod {
    height: 240px;
    width: 200px;
}

.iconwallet {
    background: url(/media/img/icons/wallet-b.svg);
    height: 50px;
    width: 50px;
}

.diagram-content.diagramrsl {
    display: flex;
    align-items: center;
    align-content: center;
}

.diagram-graphic.dgrigth {
    margin-right: 100px;
}

.digitalcanvas {
    position: relative;
}

.text-canvas {
    position: absolute;
    top: 115px;
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    text-align: center;
}

.text-canvas > .title {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    text-align: center;
    text-transform: uppercase;
    color: #524E7D;
}

.text-canvas > .total {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    text-align: center;
    color: #9795B1;
}

/* --- STATISTIC BLOCK --- */
.element-content.fit-content.paymentorder {
    display: flex;
    justify-content: center;
}

.element-bottom {
    margin-bottom: 20px;
}

.bonus-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.bonus-graphic {
    display: inline-flex;
    margin-right: 30px;
}

.bonus-info {
    display: inline-flex;
    flex-direction: column;
}

.bonus-info > .title-info {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

.bonus-info > .title-info > .colorinfo {
    display: inline-flex;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #7265FB;
}

.bonus-info > .procent-info {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 20px;
    color: #9795B1;
}

.bonus-info > .cost-info {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    color: #7265FB;
}

.statdiagram {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.diagram-graphic {
    display: inline-flex;
    margin-right: 30px;
}

.diagram-display {
    margin-bottom: 10px;
}

.diagram-content {
    display: flex;
    align-items: flex-end;
    align-content: center;
}

.diagram-display > .title-info {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

.diagram-display > .title-info > .colorinfo {
    display: inline-flex;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #7265FB;
}

.diagram-display > .title-info > .colorinfo.green {
    background: #26E27C;
}

.diagram-display > .title-info > .colorinfo.orange {
    background: #F2994A;
}

.diagram-display > .procent-info {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 20px;
    color: #9795B1;
}

.diagram-display > .cost-info {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    color: #7265FB;
}

.diagram-display > .cost-info.green {
    color: #26E27C;
}

.diagram-display > .cost-info.orange {
    color: #F2994A;
}

.stats {
    float: left;
    margin-left: 50px;
}

/* --- CONTENT BOTTOM MARGIN --- */
.bottom-content-30 {
    margin-bottom: 30px;
}

.bottom-content-20 {
    margin-bottom: 20px;
}

.bottom-content-10 {
    margin-bottom: 10px;
}

/* --- ELEMENTS PAGE (BLOCK) --- */
.element {
    background: #F6F6FB;
    border-radius: 5px;
}

.element > .element-header {
    padding: 20px 0 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.element > .element-header > .element-title {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
    border-left: 3px solid #949494;
    padding: 0 0 0 20px;
}

.element > .element-header > .element-title.purple {
    border-left: 3px solid #7265FB !important;
}

.element > .element-header > .element-title.green {
    border-left: 3px solid #26E27C !important;
}

.element-content {
    padding: 20px;
}

.element-content.fit-content {
    padding: 20px;
    display: flex;
    justify-content: space-between;
}

.statblock {
    display: inline-flex;
    background: #FFFFFF;
    border-radius: 10px;
    width: 100%;
    padding: 20px 0;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    align-content: center;
    margin: 0 10px;
}

.statblock > .statquan {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    color: #524E7D;
}

.statblock > .statquan > .currency {
    display: inline-flex;
    color: #7265FB;
}

.statblock > .desc {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
}

.statblock.active {
    background: #7265FB;
    box-shadow: 4px 4px 25px rgb(142 149 182 / 30%);
    border-radius: 10px;
    color: #fff;
}

.statblock.active > .statquan, .statblock.active > .statquan > .currency, .statblock.active > .desc {
    color: #fff;
}

/* --- TABLE --- */
th {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
}

td {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #9795B1;
}

.costgradient {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 10px;
    color: #fff;
    padding: 6px 20px;
    display: inline-flex;
}

table.table-scroll, table.table-scroll20 { display: table; width: 100%; }

table.table-scroll, table.table-scroll19 { display: table;width: 100%; }

table.table-scroll thead, table.table-scroll tbody, table.table-scroll20 thead, table.table-scroll20 tbody { float: left; width: 100%; }

table.table-scroll thead, table.table-scroll tbody, table.table-scroll19 thead, table.table-scroll19 tbody { float: left; width: 100%; }

table.table-scroll tbody, table.table-scroll20 tbody { overflow: auto; height: 190px; }

table.table-scroll tbody, table.table-scroll19 tbody { overflow: auto; height: 190px; }

table.table-scroll tr, table.table-scroll20 tr { width: 100%; display: table; text-align: left; }

table.table-scroll tr, table.table-scroll19 tr { width: 100%; display: table; text-align: left; }

table.table-scroll th, table.table-scroll td { width: 33%; }

table.table-scroll20 th, table.table-scroll20 td { width: 20%; }

table.table-scroll19 th, table.table-scroll19 td { width: 19.6%; }

table.table-scroll10 th, table.table-scroll10 td { width: 12.24%; }

table.table-scroll10 th.col.min, table.table-scroll10 td.min { width: 2%; }

table.table-scroll19 th.col.min, table.table-scroll19 td.min { width: 2%; }

td > .status {
    background: #acacac;
    color: #fff;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    text-align: center;
    border-radius: 5px;
    color: #FFFFFF;
    padding: 1px;
}

td > .status.remove {
    background: #7265FB;
}

td > .status.payment {
    background: #26E27C;
}

td > .icontable {
    text-align: center;
}

td.min {
    text-align: center;
}

/* --- TOOLTIP --- */
.tooltip-inner {
    max-width: 236px !important;
    font-size: 12px;
    padding: 10px 15px 10px 20px;
    background: #FFFFFF;
    color: rgba(0, 0, 0, .7);
    border: none;
    box-shadow: 0px 0px 25px rgba(121, 121, 121, 0.2);
    border-radius: 5px;
}

.tooltip.show {
    opacity: 1;
}

/* --- CIRCLE --- */
.circle-proc {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 500;
    font-size: 11px;
    line-height: 20px;
    text-align: center;
    color: #524E7D;
}

.circle-contents {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: center;
}

.circle-contents {
    left: 30px;
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: center;
    top: 20px;
}

.circle-contents-add {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: center;
    top: 35px;
    left: 0 !important;
}

.statprogress > .statprogress-content > div {
    position: relative;
}

div#bonus1 {
    position: relative;
}

.iconpoint {
    display: inline-flex;
    width: 50px;
    height: 50px;
    background-color: #AFADC5;
    background-size: 50px 50px;
}

.iconpoint.discount {
    mask: url(/media/img/circle/discount.svg);
    -webkit-mask: url(/media/img/circle/discount.svg);
}

.iconpoint.purse {
    mask: url(/media/img/circle/purse.svg);
    -webkit-mask: url(/media/img/circle/purse.svg);
}

.icon-canvas {
    position: absolute;
    top: 60px;
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    text-align: center;
}

/* --- STATPROGRESS --- */
.statprogress > .statprogress-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    align-content: center;
}

.statprogress > .statprogress-info > .statprogress-title {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    text-align: center;
    color: #9795B1;
}

.statprogress > .statprogress-info > .statprogress-cost {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 600;
    font-size: 13px;
    line-height: 20px;
    text-align: center;
    color: #524E7D;
}

/* --- MODAL WINDOW --- */
.fade {
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: var(--bs-modal-color);
    pointer-events: auto;
    background-color: #F6F6FB;
    background-clip: padding-box;
    border: none;
    border-radius: 5px;
    outline: 0;
}

.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: var(--bs-modal-header-padding);
    border-bottom: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.modal-footer {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * .5);
    background-color: var(--bs-modal-footer-bg);
    border-top: none;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.modal-title {
    margin-bottom: 0;
    line-height: var(--bs-modal-title-line-height);
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
    border-left: 3px solid #2CEF85;
    padding: 0 0 0 20px;
    margin-left: -16px;
}

/* --- DROPDOWN --- */
.dropdown-menu {
    --bs-dropdown-min-width: 10rem;
    --bs-dropdown-padding-x: 0;
    --bs-dropdown-padding-y: 0.5rem;
    --bs-dropdown-spacer: 0.125rem;
    --bs-dropdown-font-size: 1rem;
    --bs-dropdown-color: #fff;
    --bs-dropdown-bg: #7264fb;
    --bs-dropdown-border-color: var(--bs-border-color-translucent);
    --bs-dropdown-border-radius: 0.375rem;
    --bs-dropdown-border-width: 1px;
    --bs-dropdown-inner-border-radius: calc(0.375rem - 1px);
    --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
    --bs-dropdown-divider-margin-y: 0.5rem;
    --bs-dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --bs-dropdown-link-color: #212529;
    --bs-dropdown-link-hover-color: #1e2125;
    --bs-dropdown-link-hover-bg: #e9ecef;
    --bs-dropdown-link-active-color: #fff;
    --bs-dropdown-link-active-bg: #0d6efd;
    --bs-dropdown-link-disabled-color: #adb5bd;
    --bs-dropdown-item-padding-x: 1rem;
    --bs-dropdown-item-padding-y: 0.25rem;
    --bs-dropdown-header-color: #FFF;
    --bs-dropdown-header-padding-x: 1rem;
    --bs-dropdown-header-padding-y: 0.5rem;
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: var(--bs-dropdown-min-width);
    padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
    margin: 0;
    font-size: var(--bs-dropdown-font-size);
    color: #FFF;
    text-align: left;
    list-style: none;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 0px;
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    width: 100%;
}

/* --- TAB --- */
.tab-content {
    width: 100%;
}

.tab-content > .active {
    display: block;
    width: 100%;
}

/* --- SCROLLBAR SIDEBAR --- */
.sidebar {
    overflow: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.sidebar::-webkit-scrollbar {
    width: 0;
    height: 0;
}

/* --- INPUT FILE --- */
.loadfiles .form-group{padding:1em;margin:1em}
.loadfiles input[type=file]{outline:0;opacity:0;pointer-events:none;user-select:none}
.loadfiles .label{width:120px;border:2px dashed grey;border-radius:5px;display:block;padding:1.2em;transition:border 300ms ease;cursor:pointer;text-align:center}
.loadfiles .label i{display:block;font-size:42px;padding-bottom:16px}
.loadfiles .label i,.example-1 .label .title{color:grey;transition:200ms color}
.loadfiles .label:hover{border:2px solid #000}
.loadfiles .label:hover i,.example-1 .label:hover .title{color:#000}

button.btn.dropdown-toggle.btn-light {
    background: #FFFFFF;
    border-radius: 5px;
    border: none !important;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
    padding: 7px 15px;
    width: 212%;
    margin-top: 4px;
}

a.buttonopen1 {
    display: flex;
    background: #FFFFFF;
    border-radius: 5px;
    border: none !important;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;
    color: #524E7D;
    padding: 7px 15px;
    width: 100%;
    margin-top: 4px;
    text-decoration: none;
    cursor: pointer;
}

.treeopen {
    display: none;
}

/* --- FILE INPUT 2 --- */
.elementimage {
    display: flex;
    width: 100%;
    height: 15em;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    justify-content: center;
    align-items: center;
    background-color: #524E7D;
}

label.loadfile1 {
    display: flex;
    width: 100%;
    height: 15em;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border: 2px dashed #524E7D;
    cursor: pointer;
    justify-content: center;
    align-items: center;
    color: #524E7D;
}
  
input.pctfile {
    display: none;
}

ul.dropdown-menu.show {
    background: #f6f6fb;
    width: 100%;
    box-shadow: 0 0 20px 0px rgb(0 0 0 / 10%);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
    clear: both;
    font-weight: 400;
    color: var(--bs-dropdown-link-color);
    text-align: inherit;
    text-decoration: none;
    white-space: break-spaces;
    background-color: transparent;
    border: 0;
}

.central-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

.central-content > .central {
    width: 60%;
    text-align: center;
}

form.form-trans {
    padding: 10px 20px;
}

form.form-object {
    position: relative;
    background: #ededf3;
    padding: 20px 20px 10px 20px;
    margin-bottom: 20px;
}

.delete_me {
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 16px;
}

.mb-3.displaydown {
    display: flex;
    flex-direction: column;
}

.central-btns {
    display: flex;
    gap: 20px;
    flex-direction: row;
}

.costelement {
    background: #fff;
    padding: 20px;
    background-position: center;
    transition: .4s;
    width: 180px;
}

.costelement > .title {
    font-size: 20px;
    font-weight: bold;
}

.costelement > .costs {
    margin-top: 30px;
}

.costelement > .costs > .count, .costelement > .costs > .count.default {
    color: #AFADC5;
    font-size: 10px;
}

.costs-border.borderel {
    border: 1px solid rgb(0 0 0 / 15%);
}

.content-costs {
    display: inline-flex;
    cursor: pointer;
}

.costelement:nth-child(odd){
    background: #fff;
    background-position: center;
  }

.costelement:hover {
    background: url(/media/img/dustbin-bin-trush-svgrepo-com.svg) no-repeat center #dfdfdf;
    background-size: 50px;
    transition: .4s;
    z-index: 1;
}

#one {
    display: flex !important;
    flex-direction: row;
    gap: 10px;
}

.count.big {
    font-size: 120% !important;
}

.count.medium {
    font-size: 90% !important;
}

.gridgap2.none {
    gap: 0 !important;
}

.cost.medium {
    font-size: 120% !important;
}

.cost.big {
    font-size: 150% !important;
}

div#var1 {
    display: flex;
}

div#var2 {
    display: flex;
}

.gridgap2.tengap {
    margin: 5px !important;
}

.title.big {
    font-size: 200%;
}

.title.medium {
    font-size: 150%;
}

div#var1 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

div#var2 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.formselect {
    width: 50%;
}

.chatform {
    position: fixed;
    display: none;
    background: #FFFFFF;
    padding: 10px;
    box-shadow: 0px 0px 25px rgb(121 121 121 / 20%);
    border-radius: 16px;
    bottom: 80px;
    left: 30px;
    width: 300px;
    height: 400px;
}

.line-send {
    border: 1px solid #CBCAD8;
    margin: 10px 0;
}

/* --- SCROLLBAR --- */
::-webkit-scrollbar {
    width: 6px;
}
 
::-webkit-scrollbar-track {
	background: transparent;
}
 
::-webkit-scrollbar-thumb {
	background: #7265FB;
    border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
	background: #a29bfe;
}

::-webkit-scrollbar-thumb:active {
	background: #8c7dff;
}

.printelement {
    display: flex;
    margin: 10px 0;
}

.flexmenu {
    display: flex;
    gap: 10px;
}

.headline-chat {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 20px;
    color: #524E7D;
    padding: 10px 0 15px 0;
}

.dialogs {
    display: flex;
    flex-direction: column;
    gap: 5px 0;
    max-height: 280px;
    overflow: hidden;
    overflow-y: scroll;
}

.content-dialog {
    display: flex;
    justify-content: space-between;
    background: #f6f6fb;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    gap: 10px;
}

.avatar {
    display: inline-flex;
    height: 45px;
    width: 45px;
    background: #000;
    border-radius: 50%;
}

.content-name {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
}

.content-name > .msg {
    display: flex;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    color: #9795B1;
    align-items: center;
    gap: 5px;
}

.content-name > .name {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    color: #524E7D;
}

.msgdialog {
    display: flex;
    gap: 15px;
}

.msgdate {
    display: flex;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    color: #9795B1;
    align-items: center;
}

.msgnew {
    display: inline-flex;
    background: #7265fb;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    justify-content: center;
    font-size: 10px;
    color: #FFF;
    align-items: center;
}

.start-dialog {
    background: linear-gradient(101.67deg, #9A91FF 0%, #594DD2 99.98%);
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    align-content: center;
    padding: 1px 30px;
    text-decoration: none;
    margin-bottom: 20px;
    width: 100%;
    justify-content: center;
}

.messager {
    display: none;
    position: relative;
    height: 100%;
}

.sendmsg {
    position: absolute;
    width: 100%;
    bottom: 10px;
}

.msgline {
    border-bottom: 1px solid #CBCAD8;
    margin-bottom: 10px;
}

.sendmsg > .send {
    display: flex;
    align-content: center;
    align-items: center;
}

.msg0 {
    display: flex;
    gap: 10px;
    align-items: center;
}

.msg0 > .avatarmini {
    display: inline-flex;
    height: 35px;
    width: 35px;
    background: #000;
    border-radius: 50%;
}

.msgcont > .name {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    color: #9795B1;
}

.msgcont > .text {
    display: inline-flex;
    background: #F5F4FF;
    border-radius: 5px;
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #524E7D;
    padding: 10px;
    width: 180px;
}

.msgcontainer > .right {
    display: flex;
    justify-content: flex-end;
}

.msgcontainer > .left {
    display: flex;
    justify-content: flex-start;
}

.messages {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

button.next {
    display: flex;
    background: #7265fc;
    border: none;
    border-radius: 50%;
    color: #fff;
    height: 30px;
    width: 30px;
    align-items: center;
    justify-content: center;
    align-content: center;
}

.headerchat {
    display: flex;
    align-items: center;
}

.headname {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}

.headname > .name {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #524E7D;
}

.headname > .status {
    font-family: 'TT Firs Neue';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    color: #9795B1;
}

.headerchat {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.msgcontainer {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 250px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 15px 5px;
}

.messager1 {
    position: relative;
    background: #FFF;
    width: 100%;
    height: 400px;
    padding: 10px;
}

.messager1 > .sendmsg1 > .send1 {
    width: 100%;
    display: flex;
    align-items: center;
}

.messager1 > .sendmsg1 {
    position: absolute;
    bottom: 10px;
    width: 98%;
}

.msgcontainer1 {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 330px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 15px 5px;
}

.msgcontainer1 > .right {
    display: flex;
    justify-content: flex-end;
}

.msgcontainer1 > .left {
    display: flex;
    justify-content: flex-start;
}

a.fileatt {
    display: flex;
    align-items: center;
    text-decoration: none;
    gap: 5px;
    cursor: pointer;
}

#table_wrapper {
    width: 100%;
}

.dataTables_length > label {
    display: inline-flex;
    color: #524E7D;
    font-size: 14px;
    align-items: center;
    margin-bottom: 10px;
}

.dataTables_length > label > select.form-select.form-select-sm {
    margin: 0 8px;
}

a.page-link {
    border: none;
    color: #524E7D;
}

.active>.page-link, .page-link.active {
    background: #7265FB;
}

.disabled>.page-link, .page-link.disabled {
    color: #9795B1;
}

.sendgo {
    color: #9795B1;
    background: transparent;
    border: none;
    font-size: 20px;
}

#uploadImagesList {
    list-style: none;
    padding: 0;
    height: 20px;
    margin-bottom: -10px;
    display: flex;
    align-items: center;
}
#uploadImagesList .item {
    float: left;
    display: flex;
    gap: 10px;
    font-size: 10px;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
}
#uploadImagesList .item .img-wrap {
    display: flex;
    height: 20px;
    width: 20px;
    background-position: center;
    margin-bottom: -10px;
    overflow: hidden;
    border-radius: 8px;
}

#uploadImagesList .item .namefile0 {
    color: #9795B1;
    margin-top: 2px;
}

#uploadImagesList .item .img-wrap .icondef0 {
    font-size: 14px;
    color: #524E7D;
}

#uploadImagesList .item .img-wrap img{
    height: inherit;
}
#uploadImagesList .item .delete-link {
    cursor: pointer;
    display: inline-flex;
    color: #7f3030;
    font-size: 14px;
    margin-top: 2px;
}
.clear {
    clear: both;
}

@media print {
    @page { margin: 0; }
    body{
        visibility: hidden;
        -webkit-print-color-adjust:exact;
      }
    body * {
      visibility: hidden;
    }
    .gridgap2, .gridgap2 * {
        visibility: visible;
    }
    .printing-content {
        position: absolute;
        left: 0;
        top: 0;
    }
    .costelement {
        background: #fff;
        padding: 20px;
        width: 180px;
    }
  }