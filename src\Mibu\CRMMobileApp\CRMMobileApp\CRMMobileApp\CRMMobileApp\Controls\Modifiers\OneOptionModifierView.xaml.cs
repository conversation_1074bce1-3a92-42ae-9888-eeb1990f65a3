﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Modifiers
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OneOptionModifierView : ContentView
    {
        public OneOptionModifierView(OrderItemModifier itemModifier)
        {
            Model = itemModifier;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!itemModifier.Modifier.IsRequired)
                    optionalModifierCheckBox.IsVisible = true;

                optionalModifierCheckBox.IsChecked = itemModifier.SelectedOptions.Any();

                RenderOptions();
            });
        }

        private void RenderOptions()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                foreach (var option in Model.AllOptions)
                {
                    var optionView = new OneOptionModifierOptionItem(option)
                    {
                        HeightRequest = 40
                    };

                    if (option.IsSelected.HasValue)
                    {
                        optionView.radioButton.IsChecked = (bool)option.IsSelected;
                    }


                    optionView.OnChecked += OptionView_OnChecked;
                    modifiersLayout.Children.Add(optionView);

                }
                this.HeightRequest = Model.AllOptions.Count * 40 + 60;
            });
        }

        private void OptionView_OnChecked(object sender, OrderItemModifierOption e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                foreach (OneOptionModifierOptionItem child in modifiersLayout.Children)
                {
                    if (child != sender)
                    {
                        child.Model.IsSelected = false;
                        child.radioButton.IsChecked = false;
                    }
                }
            });
        }

        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(OrderItemModifier), typeof(OneOptionModifierView));
        public OrderItemModifier Model
        {
            get { return (OrderItemModifier)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}