﻿using CRM.Models.Enums.General;
using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для OrderCommentPopup.xaml
    /// </summary>
    public partial class OrderDivisionPopup : BaseWindow
    {
        public OrderDivisionPopup()
        {
            InitializeComponent();
            Loaded += OrderDivisionPopup_Loaded;

            orderItems = new List<OrderItem>(OrdersHelper.CurrentOrder.Items);
            orderNumberSpan.Text = OrdersHelper.CurrentOrder.OrderNumber.ToString();
        }

       

        #region Свойства
        private List<OrderItem> orderItems = new List<OrderItem>();
        private List<OrderItem> newOrderItems = new List<OrderItem>();

        private double orderItemsTotal;
        public double OrderItemsTotal
        {
            get => orderItemsTotal;
            set { orderItemsTotal = value; OnPropertyChanged(nameof(OrderItemsTotal)); }
        }
        private double newOrderItemsTotal;
        public double NewOrderItemsTotal
        {
            get => newOrderItemsTotal;
            set { newOrderItemsTotal = value; OnPropertyChanged(nameof(NewOrderItemsTotal)); }
        }

        #endregion


        private void OrderDivisionPopup_Loaded(object sender, RoutedEventArgs e)
        {
            RenderData();
        }
        private void RenderData()
        {
            OrderItemsTotal = RenderItems(orderItemsStackLayout, orderItems);
            NewOrderItemsTotal = RenderItems(newOrderItemsStackLayout, newOrderItems);
        }
        private double RenderItems(StackPanel layout, List<OrderItem> items)
        {
            layout.Children.Clear();
            foreach (var item in items)
            {
                var control = new OrderDivisionItemTemplate(item);
                control.Margin = new Thickness(0, 5, 0, 0);
                control.OrderItemTapped += Control_OrderItemTapped;
                layout.Children.Add(control);
            }
            return items.Sum(o => o.Total);
        }

        private void Control_OrderItemTapped(object sender, OrderItem e)
        {
            if (orderItems.Contains(e))
            {
                orderItems.Remove(e);
                newOrderItems.Add(e);
            }
            else
            {
                newOrderItems.Remove(e);
                orderItems.Add(e);
            }
            RenderData();
        }



        private ICommand divideOrder;
        public ICommand DivideOrder
        {
            get => divideOrder ??= new RelayCommand(async obj =>
            {

                if (!orderItems.Any() || !newOrderItems.Any())
                {
                    new OneButtonedPopup("В обоих заказах должна присутсвовать как минимум одна позиция", "Уведомление").ShowDialog();
                    return;
                }

                OrdersHelper.CurrentOrder.Items = orderItems;

                var newOrder = new Order()
                {
                    OpenedAt = DateTime.UtcNow,
                    DutyId = ApplicationState.CurrentDuty.Id,
                    Items = newOrderItems,
                    UserId = Auth.User.Id,
                    Comment = OrdersHelper.CurrentOrder.Comment,
                };

                if(ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe
                   && OrdersHelper.CurrentOrder.OrderEnvironment != null)
                {
                    newOrder.OrderEnvironment = new OrderEnvironment
                    {
                        GuestsCount = OrdersHelper.CurrentOrder.OrderEnvironment.GuestsCount,
                        TableId = OrdersHelper.CurrentOrder.OrderEnvironment.TableId,
                    };
                }


                newOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.CreateOrder(ApplicationState.CurrentDomain,
                                                                                           ApplicationState.CurrentStore.Id,
                                                                                           newOrder);

                await OrdersHelper.SaveOrder();
                new OneButtonedPopup("Заказ успешно разделен", "Уведомление").ShowDialog();
                this.Close();
            });
        }
    }
}
