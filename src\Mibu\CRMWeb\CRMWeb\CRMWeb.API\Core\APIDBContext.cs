﻿using Microsoft.EntityFrameworkCore;

namespace CRMWeb.API.Core
{
    public class APIDBContext : DbContext
    {
        public DbSet<PostModel> Posts { get; set; }
        public APIDBContext()
        {
            Database.EnsureCreated();
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseMySql(
                       "server=localhost;" +
                       "user=root;" +
                       "password=H2c7V7p6;" +
                       "port=3306;" +
                       "database=marketwb;",
                       new MySqlServerVersion(new Version(8, 0, 11))
                   );
        }
    }
}
