using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace XamarinSamples.Views.Controls
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class EntryOutlined : ContentView
    {
        public EntryOutlined()
        {
            InitializeComponent();
            TextBox.OnBackspace += TextBox_OnBackspace;
        }

        public bool FocusToTextBox()
        {
            return TextBox.Focus();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            
        }
        #region Types
        public static readonly BindableProperty IsPasswordProperty =
         BindableProperty.Create(nameof(IsPassword), typeof(bool), typeof(EntryOutlined), false);
        public bool IsPassword
        {
            get { return (bool)GetValue(IsPasswordProperty); }
            set { SetValue(IsPasswordProperty, value); }
        }
        public static readonly BindableProperty KeyboardProperty =
             BindableProperty.Create(nameof(Keyboard), typeof(Keyboard), typeof(EntryOutlined), Keyboard.Default);
        public Keyboard Keyboard
        {
            get { return (Keyboard)GetValue(KeyboardProperty); }
            set { SetValue(KeyboardProperty, value); }
        }
        #endregion

        #region Backspace
        public delegate void BackspaceEventHandler(object sender, EventArgs e);

        public event BackspaceEventHandler OnBackspace;
        public void OnBackspacePressed()
        {
            if (OnBackspace != null)
            {
                OnBackspace(null, null);
            }
        }
        private void TextBox_OnBackspace(object sender, EventArgs e)
        {
            OnBackspacePressed();
        }
        #endregion


        #region Alignment
        public static readonly BindableProperty PlaceholderHorizontalOptionsProperty =
      BindableProperty.Create(nameof(PlaceholderHorizontalOptions), typeof(LayoutOptions), typeof(EntryOutlined), LayoutOptions.Start);
        public LayoutOptions PlaceholderHorizontalOptions
        {
            get { return (LayoutOptions)GetValue(PlaceholderHorizontalOptionsProperty); }
            set { SetValue(PlaceholderHorizontalOptionsProperty, value); }
        }
        public static readonly BindableProperty HorizontalTextAlignmentProperty =
      BindableProperty.Create(nameof(HorizontalTextAlignment), typeof(TextAlignment), typeof(EntryOutlined), TextAlignment.Start);
        public TextAlignment HorizontalTextAlignment
        {
            get { return (TextAlignment)GetValue(HorizontalTextAlignmentProperty); }
            set { SetValue(HorizontalTextAlignmentProperty, value); }
        }
        #endregion

        #region Fontsize
        public static readonly BindableProperty PlaceholderFontSizeProperty =
    BindableProperty.Create(nameof(PlaceholderFontSize), typeof(double), typeof(EntryOutlined), 16d);
        public double PlaceholderFontSize
        {
            get { return (double)GetValue(PlaceholderFontSizeProperty); }
            set { SetValue(PlaceholderFontSizeProperty, value); }
        }
        public static readonly BindableProperty TextFontSizeProperty =
            BindableProperty.Create(nameof(TextFontSize), typeof(double), typeof(EntryOutlined), 16d);
        public double TextFontSize
        {
            get { return (double)GetValue(TextFontSizeProperty); }
            set { SetValue(TextFontSizeProperty, value); }
        }

        #endregion

        #region Text
        public static readonly BindableProperty TextMarginProperty =
          BindableProperty.Create(nameof(TextMargin), typeof(Thickness), typeof(EntryOutlined));
        public Thickness TextMargin
        {
            get { return (Thickness)GetValue(TextMarginProperty); }
            set { SetValue(TextMarginProperty, value); }
        }

        public static readonly BindableProperty TextProperty =
            BindableProperty.Create(nameof(Text), typeof(string), typeof(EntryOutlined), null);
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }
        public static readonly BindableProperty TextColorProperty =
          BindableProperty.Create(nameof(TextColor), typeof(Color), typeof(EntryOutlined), Color.Black);
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }
        public static readonly BindableProperty FontFamilyProperty =
          BindableProperty.Create(nameof(FontFamily), typeof(string), typeof(EntryOutlined), "Arial");
        public string FontFamily
        {
            get { return (string)GetValue(FontFamilyProperty); }
            set { SetValue(FontFamilyProperty, value); }
        }
        #endregion

        #region Placeholder

        public static readonly BindableProperty PlaceholderMarginProperty =
      BindableProperty.Create(nameof(PlaceholderMargin), typeof(Thickness), typeof(EntryOutlined));
        public Thickness PlaceholderMargin
        {
            get { return (Thickness)GetValue(PlaceholderMarginProperty); }
            set { SetValue(PlaceholderMarginProperty, value); }
        }

        public static readonly BindableProperty PlaceholderProperty =
            BindableProperty.Create(nameof(Placeholder), typeof(string), typeof(EntryOutlined), null);
        public string Placeholder
        {
            get { return (string)GetValue(PlaceholderProperty); }
            set { SetValue(PlaceholderProperty, value); }
        }

        public static readonly BindableProperty PlaceholderColorProperty =
            BindableProperty.Create(nameof(PlaceholderColor), typeof(Color), typeof(EntryOutlined), Color.Blue);
        public Color PlaceholderColor
        {
            get { return (Color)GetValue(PlaceholderColorProperty); }
            set { SetValue(PlaceholderColorProperty, value); }
        }

        #endregion

        #region Colors and border
        public static readonly BindableProperty EntryBackgroundProperty =
             BindableProperty.Create(nameof(EntryBackground), typeof(Color), typeof(EntryOutlined), Color.Blue);
        public Color EntryBackground
        {
            get { return (Color)GetValue(EntryBackgroundProperty); }
            set { SetValue(EntryBackgroundProperty, value); }
        }
        public static readonly BindableProperty BorderColorProperty =
    BindableProperty.Create(nameof(BorderColor), typeof(Color), typeof(EntryOutlined), Color.Blue);
        public Color BorderColor
        {
            get { return (Color)GetValue(BorderColorProperty); }
            set { SetValue(BorderColorProperty, value); }
        }
        public static readonly BindableProperty CornerRadiusProperty =
    BindableProperty.Create(nameof(CornerRadius), typeof(float), typeof(EntryOutlined), 0f);
        public float CornerRadius
        {
            get { return (float)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }
        #endregion


        async void TextBox_Focused(object sender, FocusEventArgs e)
        {
           // await TranslateLabelToTitle();
        }

        async void TextBox_Unfocused(object sender, FocusEventArgs e)
        {
           // await TranslateLabelToPlaceHolder();
        }

        async Task TranslateLabelToTitle()
        {
            if (string.IsNullOrEmpty(this.Text))
            {
                var placeHolder = this.PlaceHolderLabel;
                var distance = GetPlaceholderDistance(placeHolder);
                await placeHolder.TranslateTo(0, -distance);
            }
            
        }

        async Task TranslateLabelToPlaceHolder()
        {
            if(string.IsNullOrEmpty(this.Text))
            {
                await this.PlaceHolderLabel.TranslateTo(0, 0);                
            }
        }

        double GetPlaceholderDistance(Label control)
        {
            var distance = 0d;
            if(Device.RuntimePlatform == Device.iOS) distance = 0;
            else distance = 5;
            
            distance = control.Height + distance;
            return distance;
        }
        
        public event EventHandler<TextChangedEventArgs> TextChanged;
        public virtual void OnTextChanged(System.Object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            TextChanged?.Invoke(this, e);
            PlaceHolderLabel.IsVisible = string.IsNullOrEmpty(Text);
        }

    }
}