﻿using CRM.Models.Enums;
using CRM.Models.Network.Finances;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using MarkupCreator.Helpers.Converters;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CreateTransactionPopup : PopupPage
    {
        public CreateTransactionPopup()
        {
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                NewTransaction = new Transaction();
                NewTransaction.UserId = Auth.User.Id;

                FinanceOperationStrings = new List<string>
                {
                    "Приход",
                    "Расход",
                    "Инкассация",
                };
                SelectedFinanceOperationString = FinanceOperationStrings.FirstOrDefault();



                waiterNameLabel.Text = $"{Auth.User.Name} {Auth.User.Surname}";
                waiterRoleLabel.Text = EnumDescriptionHelper.GetDescription(Auth.User.Role);

                transactionDateLabel.Text = DateTime.Now.ToString("dd MMMM yyyy");
            });
        }


        private string selectedFinanceOperationString;
        public string SelectedFinanceOperationString
        {
            get => selectedFinanceOperationString;
            set { selectedFinanceOperationString = value; OnPropertyChanged(nameof(SelectedFinanceOperationString)); }
        }

        private List<string> financeOperationStrings = new List<string>();
        public List<string> FinanceOperationStrings
        {
            get => financeOperationStrings;
            set { financeOperationStrings = value; OnPropertyChanged(nameof(FinanceOperationStrings)); }
        }



        private Transaction newTransaction = new Transaction();
        public Transaction NewTransaction
        {
            get => newTransaction;
            set { newTransaction = value; OnPropertyChanged(nameof(NewTransaction)); }
        }






        private ICommand createTransaction;
        public ICommand CreateTransaction
        {
            get => createTransaction ??= new RelayCommand(async obj =>
            {
                NewTransaction.TransactionDate = DateTime.Now;

                switch (SelectedFinanceOperationString)
                {
                    case "Приход":
                        NewTransaction.Operation = FinanceOperation.Income;
                        break;
                    case "Расход":
                        NewTransaction.Operation = FinanceOperation.Expense;
                        break;
                    case "Инкассация":
                        NewTransaction.Operation = FinanceOperation.Incassaction;
                        break;
                }

                await MobileAPI.BaristaMethods.BaristaTransactionsMethods.CreateTransaction(ApplicationState.CurrentDomain,
                                                                                            ApplicationState.CurrentTradeNetwork.Id,
                                                                                            ApplicationState.CurrentStore.Id,
                                                                                            NewTransaction);

                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}