﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CAFF3264-FA8E-4871-854D-0622E4C76D2E}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{c9e5eea5-ca05-42a1-839b-61506e0a37df}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>CRMMobileApp.Droid</RootNamespace>
    <AssemblyName>CRMMobileApp.Android</AssemblyName>
    <Deterministic>True</Deterministic>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <TargetFrameworkVersion>v13.0</TargetFrameworkVersion>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    <BundleAssemblies>false</BundleAssemblies>
    <MandroidI18n>CJK;Mideast;Rare;West;Other</MandroidI18n>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    <BundleAssemblies>false</BundleAssemblies>
    <AndroidLinkMode>SdkOnly</AndroidLinkMode>
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <AndroidSupportedAbis>x86;x86_64;arm64-v8a</AndroidSupportedAbis>
    <AndroidPackageFormat>aab</AndroidPackageFormat>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidCreatePackagePerAbi>false</AndroidCreatePackagePerAbi>
    <AndroidEnableMultiDex>true</AndroidEnableMultiDex>
    <MandroidI18n>CJK;Mideast;Rare;West;Other</MandroidI18n>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Alfateam.Plugins.Popup">
      <Version>2.1.2</Version>
    </PackageReference>
    <PackageReference Include="DevExpress.XamarinForms.Editors">
      <Version>22.1.7</Version>
    </PackageReference>
    <PackageReference Include="Plugin.FirebasePushNotification">
      <Version>3.4.35</Version>
    </PackageReference>
    <PackageReference Include="Sharpnado.CollectionView">
      <Version>2.1.0</Version>
    </PackageReference>
    <PackageReference Include="Sharpnado.MaterialFrame">
      <Version>1.3.0</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfProgressBar">
      <Version>22.2.8</Version>
    </PackageReference>
    <PackageReference Include="Xam.Plugins.Forms.ProgressRing">
      <Version>0.1.2</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Migration">
      <Version>1.0.10</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2662" />
    <PackageReference Include="Xamarin.Forms.PancakeView">
      <Version>2.3.0.759</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Google.Android.Material">
      <Version>1.4.0.4</Version>
    </PackageReference>
    <PackageReference Include="System.Buffers">
      <Version>4.5.1</Version>
      <IncludeAssets>none</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Memory">
      <Version>4.5.4</Version>
      <IncludeAssets>none</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xamarin.GooglePlayServices.Base">
      <Version>118.3.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomEditorRenderer.cs" />
    <Compile Include="CustomEntryRenderer.cs" />
    <Compile Include="MainActivity.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TouchableFrameRenderer.cs" />
    <Compile Include="UI\Effects\PlatformTouchRippleEffect.cs" />
    <Compile Include="UI\Renderers\BorderlessEditorRenderer.cs" />
    <Compile Include="UI\Renderers\BorderlessEntryRenderer.cs" />
    <Compile Include="UI\Renderers\BordlessPickerRenderer.cs" />
    <Compile Include="UI\Renderers\DropdownRenderer.cs" />
  </ItemGroup>
  <ItemGroup>
    <GoogleServicesJson Include="google-services.json" />
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Properties\AndroidManifest.xml" />
    <None Include=".DS_Store" />
    <None Include="CRMMobileApp.Android.csproj" />
    <None Include="CRMMobileApp.Android.csproj.user" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\styles.xml" />
    <AndroidResource Include="Resources\values\colors.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon_round.xml" />
    <AndroidResource Include="Resources\mipmap-hdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-hdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\drawable\blueBg.jpg">
      <SubType>
      </SubType>
      <Generator>
      </Generator>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close_white.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\MibuGame\SharedGamificationBarista\SharedGamificationBarista\SharedGamificationBarista\SharedGamificationBarista.csproj">
      <Project>{C56AFDF9-9E09-4D23-ACC3-B3F76BBE73F8}</Project>
      <Name>SharedGamificationBarista</Name>
    </ProjectReference>
    <ProjectReference Include="..\CRMMobileApp\CRMMobileApp.csproj">
      <Project>{937E3120-DF6B-49ED-8CEF-E4C1463C9F2D}</Project>
      <Name>CRMMobileApp</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\LeftArrow.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close_duty.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\completed.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\create_transaction.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\open_cash.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sales_by_ingridients.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sales_on_duty.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\stock_balance.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\x_report.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\x_report_cashier.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\card_colored.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cash_colored.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\goods_selection_img.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\calendar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cloud.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\comment.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\customer.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\delete.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\error_red.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\hamburger.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\i.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\info.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\locker.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\lock_small.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\login.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\minus.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\modificators.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\options.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\percent.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\plus.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\pos.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\print.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\printer.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\receipt.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\search.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\card.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cash.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cash_machine.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon.png">
      <Generator>MSBuild:UpdateGeneratedFiles</Generator>
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ico.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\launcher_foreground.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\client.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\burger.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\breadcrump.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\bonuses.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\balance_at_stock.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\backspace_main.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\availableOnPlayMarket.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowDown.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowBack.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close_main.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\closedOrders.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\discount.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\deleteAll.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\loading_wheel.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\open_cashbox.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\main_gradient.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\main_decor.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowRight.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\backspace_small.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\bonusesChecked.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cardChecked.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cashChecked.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\mixed.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\setAmount.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\delivery.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\orderComment.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\editModifiers.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\divideOrder.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\bronze.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\bg.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Avatar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\article_1.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\article_2.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\article.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\active_level.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\active_ach.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\chartActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\chart.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\back.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Cinema.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\clock.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close_white.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\down.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\erase.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\empty.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\filter.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\fire.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gold.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Headphone.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Karma.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\kepka.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\idea.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\mail.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profile.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profileActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\question.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\readable.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\send.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\search.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\send_message.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sms.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\star.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\small_karma.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\small_coin.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\silver.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\success.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\track.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\test.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Tshirt.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\up.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\user.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuAchievementIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuPostsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuRatingIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuShopIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuTestsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\searchPurple.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuAchievementIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuPostsIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuRatingIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuShopIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuTestsIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuProfileIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuProfileIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowBackGamification.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuDashboardIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuDashboardSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\messagesIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\notificationsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\filterArrowUp.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\filterArrowDown.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationPassingTime.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationQuestionsCount.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationFilter.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationSendMessage.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\readMsgIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\taskNew.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\taskSuccess.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\taskFailed.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\userAvatar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationCheckMarkSet.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowDownWhiteGamification.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gay_logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\closeGamificationWhite.png" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
</Project>