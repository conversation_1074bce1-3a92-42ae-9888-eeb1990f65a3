using CRM.Database.Core;
using Gamification.Landing.Components.Popups;
using Hydro.Utils;
using Mibu.Landing.Admin.Abstractions.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Gamification.Landing.Components.Auth
{
    public class DialogLogin : BaseDialog
    {
        private readonly GamificationCoreDBContext _db;
        private readonly ICache _cache;

        public string Login { get; set; }

        public string Password { get; set; }

        public DialogLogin(GamificationCoreDBContext db, GamificationContext gamificationContext,
            ITemplatedEmailsService emails,
            ICache cache)
        {
            _db = db;
            _gamifications = gamificationContext;
            _cache = cache;
            _emails = emails;
        }

        public void ForgotPassword()
        {
            Close();
            ExecuteJs("openModal(`#forgotPasswordPopup`)");
        }

        public override Task RenderAsync()
        {
            if (firstTime)
            {
                firstTime = false;


            }

            return base.RenderAsync();
        }

        public override void Bind(PropertyPath property, object value)
        {
            base.Bind(property, value);
        }

        private bool firstTime;
        private readonly GamificationContext _gamifications;
        private readonly ITemplatedEmailsService _emails;

        public override void Mount()
        {
            firstTime = true;
        }



        public async Task Submit()
        {
            try
            {
                if (string.IsNullOrEmpty(Password)
                    || string.IsNullOrEmpty(Login)
                    )
                {
                    throw new Exception("Не заполнены необходимые поля");
                }

                var user = _db.Users.Include(o => o.Domains)
                    .FirstOrDefault(o => o.Email.ToLower() == Login.ToLower()
                                         && o.Password == Password && !o.IsDeleted);
                if (user == null)
                {
                    this.Error = "Неверные имя пользователя или пароль";
                    return;
                }

                await MakeAuth(user);

                Close();

                string url = Url.Action("Index", "Cabinet");

                Redirect(url);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                this.Error = $"{e}";
            }


        }

        public string Error { get; set; }



    }

}
