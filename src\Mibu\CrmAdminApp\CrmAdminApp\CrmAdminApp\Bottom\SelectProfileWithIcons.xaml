﻿<?xml version="1.0" encoding="utf-8" ?>
<bottomSheet:BaseBottomSheet xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             ParentHeight="600"
             xmlns:xct="http://xamarin.com/schemas/2020/toolkit"
             xmlns:custom="clr-namespace:MobPhone.Custom"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             xmlns:bottomSheet="clr-namespace:BottomSheet.Core;assembly=BottomSheetXF" 
             xmlns:gamification="clr-namespace:CrmAdminApp.Controls.Gamification"
             x:Class="MobPhone.Bottom.SelectProfileWithIcons">
    <bottomSheet:BaseBottomSheet.View>
        <ContentView>
            <Frame 
                CornerRadius="20" 
                Padding="16, 26, 16, 0" 
                BackgroundColor="#fff" 
                HeightRequest="600">
                <StackLayout 
                    Spacing="32">
                    
                    <StackLayout 
                        Orientation="Horizontal" 
                        HorizontalOptions="Fill">
                        <StackLayout 
                            Orientation="Horizontal" 
                            HorizontalOptions="CenterAndExpand">
                            <Image 
                                VerticalOptions="Center"
                                Source="userAdd"/>
                            <Label 
                                VerticalOptions="Center"
                                Text="Выбрать сотрудника" 
                                Style="{x:StaticResource BoldText}" 
                                FontSize="14"/>
                        </StackLayout>
                        <ImageButton 
                            Source="close" 
                            Clicked="ClosePopup"
                            WidthRequest="15"
                            HeightRequest="15"
                            HorizontalOptions="End" 
                            Padding="0" 
                            BackgroundColor="Transparent"/>
                    </StackLayout>

                    <Frame
                        Padding="0"
                        VerticalOptions="Start"
                        CornerRadius="12"
                        BackgroundColor="#f5f5f5"
                        HasShadow="False"
                        HeightRequest="95">
                        <Grid Padding="0">
                            
                            <custom:CustomEntry 
                                 x:Name="taskText" 
                                 TextChanged="onEntryTextChanged"
                                 Focused="onEntryFocused"
                                 Unfocused="onEntryUnfocused"
                                 HorizontalTextAlignment="Center" 
                                 FontFamily="noto_sans_light" 
                                 TextColor="#1E1584" 
                                 HorizontalOptions="FillAndExpand"
                                 
                                 VerticalOptions="Center"
                                 HeightRequest="95"
                                 BackgroundColor="Transparent"
                                 FontSize="10"/>

                            <StackLayout
                                x:Name="placeholderLayout"
                                HorizontalOptions="Center"
                                InputTransparent="True"
                                VerticalOptions="Center"
                                Orientation="Horizontal">

                                <Image
                                    Source="searchPurple.png"
                                    HeightRequest="12"
                                    WidthRequest="12"/>

                                <Label
                                    TextColor="#524E7D"
                                    FontSize="10"
                                    Text="Поиск"/>

                            </StackLayout>
                            
                        </Grid>
                    </Frame>
                    
                   

                    <StackLayout 
                        x:Name="allCheckboxLayout"
                        Orientation="Horizontal">
                        <CheckBox 
                            x:Name="allCheckBox" 
                            IsChecked="False" 
                            CheckedChanged="ToggleAllSelected"  
                            Color="#7265FB" 
                            VerticalOptions="Center"/>
                        <Label 
                            Text="Добавить всех сотрудников" 
                            VerticalOptions="Center" 
                            Style="{x:StaticResource RegularText}" />
                    </StackLayout>

                    <CollectionView 
                        x:Name="itemsCollectionView"
                        BackgroundColor="Transparent"
                        VerticalScrollBarVisibility="Never">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <gamification:UserCheckboxCard
                                    SelectionChanged="OnCardSelectionChanged"
                                    BindingContext="{Binding}"/>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>

                    <Button 
                        Clicked="SaveChanges" 
                        Margin="0, 5, 0, 20" 
                        Text="Сохранить" 
                        Background="{x:StaticResource blueGradient}" 
                        TextTransform="None" 
                        BackgroundColor="#5242FF" 
                        CornerRadius="12" 
                        FontFamily="noto_sans_regular" 
                        TextColor="White" />
                </StackLayout>
            </Frame>
        </ContentView>
    </bottomSheet:BaseBottomSheet.View>

</bottomSheet:BaseBottomSheet>