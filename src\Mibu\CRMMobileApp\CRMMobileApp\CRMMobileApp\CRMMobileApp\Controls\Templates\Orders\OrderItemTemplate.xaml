﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.OrderItemTemplate">
    <ContentView.Content>
        <Grid
            VerticalOptions="Start"
            Padding="0">
            <StackLayout   
                Spacing="6"
                VerticalOptions="Start">
                <Grid
                    VerticalOptions="Start"
                    MinimumHeightRequest="30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="30"/>
                        <ColumnDefinition Width="130"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="60"/>
                    </Grid.ColumnDefinitions>
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="onOrderItemTapped"/>
                    </Grid.GestureRecognizers>

                    <Frame
                        IsVisible="{Binding Source={x:Reference this},Path=IsSelected}"
                        BackgroundColor="Transparent"
                        HasShadow="False"
                        VerticalOptions="Center"
                        Grid.Column="0"
                        Padding="0">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Tapped="onDeleteBtnTapped"/>
                        </Frame.GestureRecognizers>
                        <Image                    
                            WidthRequest="15"
                            HeightRequest="15"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            Source="{OnPlatform Default=close_white.png, WPF='pack://application:,,,/Images/close_white.png'}"/>
                    </Frame>



                    <Grid 
                        Grid.Column="1"
                        VerticalOptions="Start"
                        HorizontalOptions="Start">
                        <Label 
                            x:Name="itemTitleLabel"
                            Text="{Binding Source={x:Reference this},Path=ItemTitle}"
                            VerticalOptions="Start"
                            HorizontalOptions="Start"
                            LineBreakMode="WordWrap"
                            FontSize="14"
                            TextColor="White"/>
                        <Image 
                            x:Name="itemCommentSign"
                            IsVisible="False"
                            VerticalOptions="Start"
                            Margin="0,5,-14,0"
                            HorizontalOptions="End"
                            WidthRequest="11"
                            HeightRequest="11"
                            Source="{OnPlatform Default=info.png, WPF='pack://application:,,,/Images/info.png'}"/>
                    </Grid>
                 



                    <Grid
                        Margin="-20,0,0,0"
                        HorizontalOptions="Start"
                        VerticalOptions="Start"
                        HeightRequest="21"
                        Grid.Column="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                        </Grid.ColumnDefinitions>

                        <Frame
                            Grid.Column="0"
                            x:Name="decrementAmountFrameBtn"
                            IsVisible="False"
                            Margin="0,0,0,0"
                            BackgroundColor="#F6F6FB"
                            HasShadow="False"
                            Padding="0"
                            CornerRadius="7"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            HeightRequest="15"
                            WidthRequest="15">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=DecrementAmount}"/>
                            </Frame.GestureRecognizers>
                            <Image 
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="{OnPlatform Default=minus.png, WPF='pack://application:,,,/Images/minus.png'}"
                                WidthRequest="10"     
                                HeightRequest="10"/>
                        </Frame>

                        <Label 
                            x:Name="amountLabel"
                            Grid.Column="1"
                            FontSize="14"
                            TextColor="White"
                            Text=""
                            VerticalOptions="Center"
                            HorizontalOptions="Center"/>

                        <Frame
                            Grid.Column="2"
                            x:Name="incrementAmountFrameBtn"
                            IsVisible="False"
                            Margin="0,0,0,0"
                            BackgroundColor="#7265FB"
                            HasShadow="False"
                            Padding="0"
                            CornerRadius="7"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            HeightRequest="15"
                            WidthRequest="15">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=IncrementAmount}"/>
                            </Frame.GestureRecognizers>
                            <Image 
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="{OnPlatform Default=plus.png, WPF='pack://application:,,,/Images/plus.png'}"
                                WidthRequest="10"
                                HeightRequest="10"/>
                        </Frame>

                    </Grid>
                  
                    <Label 
                        Grid.Column="3"
                        FontSize="14"
                        TextColor="White"
                        Text="{Binding Source={x:Reference this},Path=Model.ProductPrice}"
                        Margin="-8,0,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"/>
                    <Label 
                        x:Name="sumLabel"
                        Grid.Column="4"
                        FontSize="14"
                        TextColor="White"
                        Text="{Binding Source={x:Reference this},Path=Sum}"
                        VerticalOptions="Start"
                        Margin="-15,0,0,0"
                        HorizontalOptions="Start"/>
                </Grid>

                <StackLayout 
                    Margin="30,0,0,0"
                    Spacing="6"
                    VerticalOptions="Start"
                    x:Name="modifiersLayout">
                    
                    
                    
                </StackLayout>
                
                
            </StackLayout>
        </Grid>
  </ContentView.Content>
</ContentView>