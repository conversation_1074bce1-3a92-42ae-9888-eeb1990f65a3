﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts"
             x:Name="this"
             xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" 
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:converters="clr-namespace:MarkupCreator.Converters" 
             xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates" 
             xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview" xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews" xmlns:editors1="clr-namespace:CRMMobileApp.Controls.Parts.Editors"
             ios:Page.UseSafeArea="true"
             Background="White"
             BackgroundColor="White"
             x:Class="CRMMobileApp.Views.Pages.Delivery.DeliveriesPage">
    <ContentPage.Content>
        <Grid>
            
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>


            <parts:Header Grid.Row="0"/>

            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <ImageButton
                            Command="{Binding Source={x:Reference this},Path=FilterOrders}"
                            BackgroundColor="Transparent"
                            WidthRequest="20"
                            HeightRequest="20"
                            Source="{OnPlatform Default=search.png, WPF='pack://application:,,,/Images/search.png'}"
                            HorizontalOptions="End"
                            VerticalOptions="Center"/>
                    </Grid>

                    <Grid Grid.Column="1">

                        <ImageButton
                            Command="{Binding Source={x:Reference this},Path=DecrementDay}"
                            BackgroundColor="Transparent"
                            WidthRequest="20"
                            HeightRequest="20"
                            Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"
                            Margin="5,0,0,0"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"/>


                        <Label
                            x:Name="filterDate"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text=""/>

                        <ImageButton
                            Command="{Binding Source={x:Reference this},Path=IncrementDay}"
                            BackgroundColor="Transparent"
                            WidthRequest="20"
                            HeightRequest="20"
                            Source="{OnPlatform Default=arrowRight.png, WPF='pack://application:,,,/Images/arrowRight.png'}"
                            Margin="0,0,5,0"
                            HorizontalOptions="End"
                            VerticalOptions="Center"/>

                    </Grid>

                    <Grid Grid.Column="2">
                        <editors1:CustomCrossComboBoxEdit 
                            Margin="0,0,0,0"
                            BackgroundColor="#F6F6FB"
                            TextColor="{StaticResource dark_purple}"
                            BorderColor="#F6F6FB"
                            HeightRequest="40"
                            HorizontalOptions="Fill"
                            VerticalOptions="Center"
                            CornerRadius="0"
                            PlaceholderText="Пол"
                            PlaceholderColor="#9795B1"
                            ItemsSource="{Binding Source={x:Reference this},Path=DeliveryServices,Mode=TwoWay}"
                            SelectedItem="{Binding Source={x:Reference this},Path=SelectedDeliveryService,Mode=TwoWay}"/>
                    </Grid>

                    <Grid Grid.Column="3">
                        <editors1:CustomCrossComboBoxEdit   
                            Margin="0,0,0,0"
                            BackgroundColor="#F6F6FB"
                            TextColor="{StaticResource dark_purple}"
                            BorderColor="#F6F6FB"
                            HeightRequest="40"
                            HorizontalOptions="Fill"
                            VerticalOptions="Center"
                            CornerRadius="0"
                            PlaceholderText="Пол"
                            PlaceholderColor="#9795B1"
                            ItemsSource="{Binding Source={x:Reference this},Path=DeliveryMen,Mode=TwoWay}"
                            SelectedItem="{Binding Source={x:Reference this},Path=SelectedDeliveryMan,Mode=TwoWay}" />
                    </Grid>

                    <Grid Grid.Column="4">
                        <Button 
                            HeightRequest="40"
                            Command="{Binding Source={x:Reference this},Path=CreateOrderWithDelivery}"
                            TextColor="White"
                            Text="Новый заказ"
                            Padding="0"
                            FontSize="16"
                            TextTransform="None"
                            Background="{StaticResource purple_gradient}"/>
                    </Grid>

                </Grid>

                <Grid Grid.Row="1">

                    <listviews:CustomCrossCollectionView
                        MinItemSize="53"
                        Margin="30,10,30,0"
                        Background="White"
                        BackgroundColor="White"
                        SelectionMode="None"
                        ItemsSource="{Binding Source={x:Reference this},Path=Orders}">
                        <listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                            <DataTemplate>
                                <templates:OrderWithDeliveryTemplate
                                    Tapped="onItemTapped"
                                    Model="{Binding}"
                                    Margin="0,7,0,0"
                                    VerticalOptions="Start"
                                    HeightRequest="53"/>
                            </DataTemplate>
                        </listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                        <listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                            <DataTemplate>
                                <ViewCell>
                                    <templates:OrderWithDeliveryTemplate
                                        Tapped="onItemTapped"
                                        Model="{Binding}"
                                        Margin="0,7,0,0"
                                        VerticalOptions="Start"
                                        HeightRequest="53"/>
                                </ViewCell>
                            </DataTemplate>
                        </listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                    </listviews:CustomCrossCollectionView>

                    <Label
                         x:Name="noItemsLabel"
                         IsVisible="False"
                         VerticalOptions="Center"
                         HorizontalOptions="Center"
                         HorizontalTextAlignment="Center"
                         WidthRequest="300"
                         TextColor="{StaticResource dark_purple}"
                         FontFamily="TTFirsNeue-Regular"
                         Text="По данным критериям нет заказов с доставкой"
                         FontSize="14" />

                </Grid>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>