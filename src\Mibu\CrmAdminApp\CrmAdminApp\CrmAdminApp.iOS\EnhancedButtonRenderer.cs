﻿using CoreGraphics;
using CRMMobileApp.iOS;
using Foundation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UIKit;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;

[assembly: Export<PERSON>enderer(typeof(Button), typeof(EnhancedButtonRenderer))]
namespace CRMMobileApp.iOS
{
    public class EnhancedButtonRenderer : ButtonRenderer
    {
        UIImage GetMyBackgroundImage(UIView control, Brush brush)
        {
            if (control == null || brush == null || brush.IsEmpty || control.Bounds == CGRect.Empty)
                return null;

            var backgroundLayer = control.GetBackgroundLayer(brush);

            if (backgroundLayer == null)
                return null;

            UIGraphics.BeginImageContextWithOptions(backgroundLayer.Bounds.Size, false, UIScreen.MainScreen.Scale);

            if (UIGraphics.GetCurrentContext() == null)
                return null;

            backgroundLayer.RenderInContext(UIGraphics.GetCurrentContext());
            UIImage gradientImage = UIGraphics.GetImageFromCurrentImageContext();
            UIGraphics.EndImageContext();

            return gradientImage;
        }

        protected override void SetBackground(Brush brush)
        {
            if (Control == null)
                return;

            try
            {
                UIColor backgroundColor = Element.BackgroundColor == Color.Default ? null : Element.BackgroundColor.ToUIColor();

                if (!Brush.IsNullOrEmpty(brush))
                {
                    if (brush is SolidColorBrush solidColorBrush)
                        backgroundColor = solidColorBrush.Color.ToUIColor();
                    else
                    {
                        var backgroundImage = GetMyBackgroundImage(this, brush);
                        backgroundColor = backgroundImage != null ? UIColor.FromPatternImage(backgroundImage) : UIColor.Clear;
                    }
                }

                Control.BackgroundColor = backgroundColor;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }


    }
}