﻿using CRM.Models.Network;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.Breadcrumps
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class BreadcrumpPart : ContentView
    {
        public MenuCategoriesTreeItem Category { get; private set; }
        public BreadcrumpPart(MenuCategoriesTreeItem category)
        {
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                Category = category;
                categoryTitle.Text = category.Category.Title;
            });
        }

        public void SetNextItemArrowVisibility(bool isVisible)
        {
            nextItemArrow.IsVisible = isVisible;
        }

        public event EventHandler<MenuCategoriesTreeItem> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                Tapped?.Invoke(this, Category);
            });
        }
    }
}