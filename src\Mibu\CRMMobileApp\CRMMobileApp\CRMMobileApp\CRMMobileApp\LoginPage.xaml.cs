﻿using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Views.Pages.Main;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using SharedGamificationBarista.Abstractions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.Extensions;
using Xamarin.Forms;

namespace CRMMobileApp
{
    public partial class LoginPage : BasePage
    {
        public LoginPage()
        {
            InitializeComponent();

            this.KeyboardResizedEvent += LoginPage_KeyboardResizedEvent;

#if DEBUG
            Domain = "crm";
            Username = "test_login";
            Password = "0000";
#endif
        }

      

        private string domain = string.Empty;
        public string Domain
        {
            get => domain;
            set { domain = value; OnPropertyChanged(nameof(Domain)); }
        }
        private string username = string.Empty;
        public string Username
        {
            get => username;
            set { username = value; OnPropertyChanged(nameof(Username)); }
        }
        private string password = string.Empty;
        public string Password
        {
            get => password;
            set { password = value; OnPropertyChanged(nameof(Password)); }
        }

        private ICommand auth;
        public ICommand Auth
        {
            get => auth ??= new RelayCommand(async obj =>
            {
                await Device.InvokeOnMainThreadAsync(async () =>
                {
                    if (string.IsNullOrEmpty(Password) || string.IsNullOrEmpty(Username) || string.IsNullOrEmpty(Domain))
                    {
                        errorMessageLabel.Text = "Все поля должны быть заполнеными";
                        return;
                    }

                    bool authResult = await CRMAdminMoblieApp.Helpers.Auth.AuthorizeToPOS(Domain, Username, Password);
                    if (authResult)
                    {
                        ApplicationState.Subscription = await MobileAPI.MainMethods.GetDomainSubscription(Domain);
                        ApplicationState.SaveChangesToMemory();

                        if (ApplicationState.Subscription.IsExpired)
                        {
                            errorMessageLabel.Text = "Необходимо оплатить подписку";
                            return;
                        }

                        await App.Current.MainPage.Navigation.PushAsync(new MainPage());
                    }
                    else
                    {
                        errorMessageLabel.Text = "Неверный логин или пароль";
                    }
                });
            });
        }
        private ICommand openSupportPopup;
        public ICommand OpenSupportPopup
        {
            get => openSupportPopup ??= new RelayCommand(async obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new SupportPopup());
                });
            });
        }






        private void LoginPage_KeyboardResizedEvent(object sender, double e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                authStackLayout.Margin = new Thickness(0, -20 - e, 0, 0);
            });
        }
    }
}
