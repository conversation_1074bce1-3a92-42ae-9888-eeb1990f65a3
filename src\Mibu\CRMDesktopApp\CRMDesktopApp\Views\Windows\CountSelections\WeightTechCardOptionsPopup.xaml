﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.CountSelections.WeightTechCardOptionsPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.CountSelections" xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Выбор количества позиции"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="450"
        Height="250">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="440"
        Height="245"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>


            <Grid 
                Grid.Row="0"
                Margin="0,20,0,0">
                <StackPanel Orientation="Horizontal">
                    <Border
                        Margin="40,0,0,0"
                        Padding="0"
                        VerticalAlignment="Center"
                        CornerRadius="20"
                        Width="40"
                        Height="40">
                        <Image 
                            Stretch="Fill"
                            Source="{Binding ElementName=this,Path=TechnicalCard.TechnicalCard.ImgPath}"
                            Width="40"
                            Height="40"/>
                    </Border>

                    <TextBlock
                        VerticalAlignment="Center"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        FontWeight="Bold"
                        Text="{Binding ElementName=this,Path=TechnicalCard.TechnicalCard.Title}"/>
                </StackPanel>




                <customcontrols:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="15"
                    Height="15"
                    Margin="0,15,35,0"/>
            </Grid>


            <StackPanel 
                Grid.Row="1"
                Orientation="Horizontal">


                <TextBlock
                    Margin="40,0,0,0"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="18"
                    FontWeight="Bold"
                    Text="Количество товара : "/>

                <TextBlock
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="18"
                    FontWeight="Bold"
                    Text="{Binding ElementName=this,Path=Amount}"/>

                <customcontrols:ImageButton 
                    Margin="30,0,0,0"
                    Source="pack://application:,,,/Resources/Images/create_transaction.png"
                    Command="{Binding ElementName=this,Path=OpenAmountKeyboard}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Width="30"
                    Height="30"/>

            </StackPanel>

            <Grid Grid.Row="2">

                <StackPanel
                    VerticalAlignment="Bottom"
                    HorizontalAlignment="Right"
                    Margin="0,0,30,30"
                    Orientation="Horizontal">

                    <Button 
                        Margin="0,0,20,0"
                        Command="{Binding ElementName=this,Path=CloseWindow}"
                        Style="{StaticResource bg_purple_btn}"
                        VerticalAlignment="Center"
                        Content="Отмена"
                        Width="170"
                        Height="40"/>
                    <Button 
                        Margin="0,0,20,0"
                        Command="{Binding ElementName=this,Path=AddToOrder}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Center"
                        Content="Выбрать"
                        Width="170"
                        Height="40"/>

                </StackPanel>

            </Grid>
        </Grid>
    </Border>
</abstactions:BaseWindow>
