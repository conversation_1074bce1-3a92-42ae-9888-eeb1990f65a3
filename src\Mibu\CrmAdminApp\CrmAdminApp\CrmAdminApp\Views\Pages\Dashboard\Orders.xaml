﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:BaseDashboardPage
             xmlns:abstractions="clr-namespace:CRMAdminMoblieApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Views.Pages.Dashboard.Orders"
             x:Name="this"
             xmlns:xct="http://xamarin.com/schemas/2020/toolkit" 
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates" 
             xmlns:views="clr-namespace:SVGChart.Nuget.Views;assembly=SVGChart.Nuget"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             Background="#ffffff"
             ios:Page.UseSafeArea="true"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts" xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview">
    <abstractions:BaseDashboardPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </abstractions:BaseDashboardPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="70" />
                <RowDefinition Height="*" />
                <RowDefinition Height="80" />
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">
                <parts:Header x:Name="header"/>
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout Spacing="0">


                    <Frame
                        Margin="20,20,20,0"
                        HasShadow="False"
                        CornerRadius="5"
                        Background="{x:StaticResource purple_gradient}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Padding="0"
                        HeightRequest="40">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenDutyPopup}"/>
                        </Frame.GestureRecognizers>
                        <Grid>
                            <StackLayout
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,21,0"
                                Spacing="21"
                                Orientation="Horizontal">

                                <Label 
                                     x:Name="dutyLabel"
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     TextColor="White"
                                     VerticalOptions="Center"
                                     Text=""/>
                                <Image 
                                    WidthRequest="22"
                                    HeightRequest="22"
                                    Source="calendar.png"/>

                            </StackLayout>
                        </Grid>
                    </Frame>

                </StackLayout>

            </Grid>

            <Grid Grid.Row="2">

                <collectionview:DXCollectionView
                    x:Name="collectionView"
                    Margin="20,30,20,0"      
                    Background="#ffffff"
                    VerticalOptions="Fill"
                    MinItemSize="70"
                    ItemSpacing="10"
                    IsScrollBarVisible="False"
                    BackgroundColor="#ffffff">
                    <collectionview:DXCollectionView.ItemTemplate>
                        <DataTemplate>
                            <itemtemplates:OrderCard 
                                HeightRequest="70"
                                VerticalOptions="Start"
                                HorizontalOptions="FillAndExpand" />
                        </DataTemplate>
                    </collectionview:DXCollectionView.ItemTemplate>
                </collectionview:DXCollectionView>

                <Label 
                     x:Name="noItemsLabel"
                     IsVisible="False"
                     FontFamily="TTFirsNeue-Regular"
                     FontSize="14"
                     TextColor="{x:StaticResource dark_purple}"
                     VerticalOptions="Center"
                     HorizontalOptions="Center"
                     Text="На данный момент нет заказов"/>

            </Grid>


            <Grid Grid.Row="3">
                <parts:Footer IsOrdersPage="True"/>
            </Grid>

        </Grid>
    </ContentPage.Content>
</abstractions:BaseDashboardPage>