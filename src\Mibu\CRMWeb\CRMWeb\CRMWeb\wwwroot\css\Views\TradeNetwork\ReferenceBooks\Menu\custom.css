a[href^="http://www.amcharts.com"] {
        display: none!important;
}
@media print {
        #customer-chat-iframe,
        .pay-alert-message {
                display: none!important;
        }
}
.input-group-addon {
        padding: 9px 12px;
        /* font-size: 14px; */
        font-weight: 400;
        /* line-height: 1; */
        color: #555;
        text-align: center;
        background-color: #eee;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-top: -1px;
}
.input-group-addon .uniform {
        padding: 6px 10px;
        /* font-size: 14px; */
        font-weight: 400;
        /* line-height: 1; */
        color: #555;
        text-align: center;
        background-color: #eee;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-top: -1px;
}
@media screen and (min-width: 992px) {
        .col-md-pl0 {
                padding-left: 0!important;
        }
        .col-md-pr0 {
                padding-right: 0!important;
        }

}
.mt20 {
        margin-top: 20px;
}
.mb10 {
        margin-bottom: 10px;
}
.ml3 {
        margin-left: 3px;
}
.ml10 {
        margin-left: 10px;
}
.mr20 {
        margin-right: 20px;
}
.mr5 {
        margin-right: 5px;
}
.mt11 {
       margin-top: 11px;
}
.mb0 {
        margin-bottom: 0!important;
}
.p0 {
        padding: 0!important;
}
.p10 {
        padding: 10px!important;
}
.p11 {
        padding: 11px!important;
}
.m0 {
        margin: 0!important;
}
.pt2 {
        padding-top: 2px;
}
.pt5 {
        padding-top: 5px;
}
.pl10 {
        padding-left: 10px;
}
.pl0 {
        padding-left: 0px!important;
}
.pr0 {
        padding-right: 0px!important;
}
.pr10 {
        padding-right: 10px;
}
.pr15 {
        padding-right: 15px;
}
.pl15 {
        padding-left: 15px;
}
.pb20 {
        padding-bottom: 20px;
}
.h401 {
        height: 401px;
}
.h360 {
        height: 361px;
}
.h40 {
        height: 40px;
}
.mt-20 {
        margin-top: -20px;
}
.p15 {
        padding: 15px;
}
.m5 {
        margin: 5px;
}
.overflowys {
        overflow-y: scroll;
}
.search-wrap {
        height: 43px;
        padding: 5px;
}
.search-wrap input.form-control {
        background: #bfcad1!important;
        color: #e9edef!important;
}
.search-wrap input.form-control:focus {
        border-color: transparent!important;
        background: #d8e0e4!important;
        color: #7b7b7b!important;
}
.search-wrap .input-icon>i {
        color: #fff;
}
.input-icon>i.clearSearch {
        display: none;
        right: 10px;
        top: -2px;
        left: auto;
        cursor: pointer;
        color: #15a6ff;
}
.input-icon>i.clearSearch:hover {
        color: #909090;
}
.flowersCatalogHelp {
        color: #dbda00;
        display: inline-block;
        font-size: 33px;
        line-height: 33px;
}
.flowersCatalogHelp:hover {
        color: #a9a808;
}
#totalSelected {
        display: inline-block;
        vertical-align: middle;
        padding-top: 2px;
        padding-left: 5px;
}
.checkControls {
        font-size: 20px;
        width: 40px;
        text-align: center;
        display: inline-block;
        vertical-align: middle;
}
.checkControls a {
        color: #bfcbd2;
        transition-duration: .2s;
        transition-property: color;
}
.checkControls a:hover {
        color: #12b9f3;
}
.node-flowersCatalogCategoriesTree {
        color: #8a8a8a;
}
.flowersCatalogPgination {
        float: right;
        margin: -2px 0;
}
.list-group-item {
        border: 1px solid #ececec;
}
.flowersCatalogList>li {
        border-left: none!important;
}
.flowersCatalogList>li>.list-icon-container {
        border: none!important;
        border-radius: 0!important;
        padding: 0!important;
        float: left;
        text-align: center;
        width: 45px;
        height: 45px;
        font-size: 20px;
}
.flowersCatalogList>li>.list-item-content>p {
        color: #999999;
}
.flowersCatalogList>li>.list-icon-container>a {
        display: inline-block;
        width: 45px;
        height: 45px;
        line-height: 45px;
}
.flowersCatalogList>li>.list-icon-container a i {
        display: inline-block;
        margin-top: 15px;
        transition-duration: .2s;
        transition-property: color;
}
.flowersCatalogList>li>.list-icon-container a i.fa-check-circle-o {
        color: #5bb9f3;
}
.flowersCatalogList>li>.list-icon-container a i.fa-circle-o {
        color: #ccd1d6;
}
.flowersCatalogList>li:hover {
        background-color: #f9f9f9!important;
}
.flowersCatalogList .list-right-content {
        text-align: right;
        float: right;
        width: 60px;
        line-height: 45px;
}
.flowersCatalogList .list-right-content > i {
        line-height: 45px;
        font-size: 20px;
}
.flowersCatalogList .list-right-content > i.yellow {
        color: #dada00;
}
.flowersCatalogList .list-right-content > i.green {
        color: #5ac381;
}
.flowersCatalogPgination>li>a, .flowersCatalogPgination>li>span {
        line-height: 1;
}
.flowersCatalogPgination>li>a {
        color: #a9a9a9;
}
.flowersCatalogPgination>li>span {
        color: #d4d4d4;
}
.flowersCatalogPgination>li>span:hover {
        color: #d4d4d4;
        background-color: #fff;
        border-color: #ddd;
}
.flowersCatalogPgination>li>a.active, .flowersCatalogPgination>li>span.active {
        color: #12b9f3;
        font-weight: bold;
}
.list-image {
        display: inline-block;
        height: 71px;
        width: 71px;
        float: left;
        margin-top: -15px;
        margin-right: 15px;
        margin-left: 15px;
}
.list-image > img{
        height: 71px;
        width: 71px;
}
.theme-panel-btn {
        float: right;
        margin-top: 8px;
        margin-left: 10px;
        font-size: 12px;
}
.modal-full2.modal-dialog {
        width: 90%;
}
.modal-full3.modal-dialog {
        width: 84%;
}
[role="paging"] {
        display: block; /*!important;*/
}
.report_foot_l1 {
        text-align: right;
        font-weight: bold;
}
.report_foot_l2 {
        text-align: right;
        font-weight: normal;
}
.report_head_l1 {
        font-weight: bold;
}
.report_head_l1_green {
        font-weight: bold;
        background-color: #ebffeb;
}

.report_head_l1_darkgreen {
        font-weight: bold;
        background-color: #4DB3A2;
}
.report_head_l1_pink {
        font-weight: bold;
        background-color: #fdf2f2;
}
.report_head_l2 {
        font-style: italic;
}
button.dt-button, div.dt-button, a.dt-button {
        position: relative;
        box-sizing: border-box;
        height: 30px!important;
        margin-bottom: -20px;
        margin-top: -25px;
        cursor: pointer;
        font-size: 0.88em;
        white-space: nowrap;
        overflow: hidden;
        -moz-user-select: none;
        -ms-user-select: none;
}
.mt-6 {
        margin-top: -6px;
}
.btn-floating {
        display: inline-block;
        color: #fff;
        position: fixed;
        overflow: hidden;
        z-index: 10001;
        width: 40px;
        height: 40px;
        line-height: 42px;
        padding: 0;
        background-color: #12b9f3;
        border-radius: 50%!important;
        text-align: center;
        cursor: pointer;
        vertical-align: middle;
        right: 16px;
        bottom: 50px;
        box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.28);
        transform: scale(1);
        -moz-transition: all .4s ease;
        -webkit-transition: all .4s ease;
        -o-transition: all .4s ease;
        transition: all .4s ease;
}
.btn-floating:hover {
        background-color: #78d8f9;
        box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.4);
}
.btn-floating.btn-no-showed {
        -moz-transform: scale(0.01) rotate(-180deg);
        -webkit-transform: scale(0.01) rotate(-180deg);
        -o-transform: scale(0.01) rotate(-180deg);
        transform: scale(0.01) rotate(-180deg);
}
.absolute {position: absolute;}
.t23 {top: 23px;}
.r425 {right: 425px;}
.l170 {left: 170px;}
.ui-tooltip {
        z-index: 9999000!important;
}
.portlet:focus {
        outline: none!important;
}
.portlet.bordered tr.ui-widget-header {
        background: white;
        color: #333333;
}
.portlet tr.ui-widget-header {
        background: #f1f4f7;
        color: #333333;
}
.portlet:not(.bordered) tr.ui-widget-header th {
        /*font-weight: 400;*/
        padding: 3px 0!important;
}
.actions:not(.bordered) [role="btn"] {
        font-size: 18px;
        display: inline-block;
        height: 18px;
        margin-right: 5px;
        margin-top: 8px;
        opacity: 1;
        color: #777777;
        cursor: pointer;
}
.portlet table [data-act] {
        opacity: 1;
        font-size: 15px;
        cursor: pointer;
}
.actions:not(.bordered) [role="btn"]:hover,  .portlet table [data-act]:hover {
        opacity: .4;
        transition-property: opacity;
        transition-duration: .3s;
}
.pay-alert-message {
        display: none;
}
.pay-alert .pay-alert-message {
        display: block;
        background: yellow;
        text-align: center;
        height: 30px;
        line-height: 30px;
        position: fixed;
        right: 0;
        left: 0;
        z-index: 9995;
        top: 0;
}
.pay-alert .navbar-fixed-top {
        margin-top: 30px;
}
.pay-alert.page-header-fixed .page-container {
        margin-top: 80px;
}
.close-pay-alert-message {
        display: inline-block;
        float: right;
        margin-right: 10px;
        margin-top: 7px;
        cursor: pointer;
}
.close-pay-alert-message:hover {
        opacity: 0.5;
}
.change_cash {
        padding-top: 10px;
        font-weight: 600;
        color: #616161;
}
@media (max-width: 768px) {
        .portlet {
                margin-top: 0;
                margin-bottom: 25px;
                padding: 0;
                border-radius: 4px;
                overflow-x: auto;
        }
}

.form-horizontal .popover-content .form-group {
        margin-left: 0px;
        margin-right: 0px;
}

.popover-content .form-group .editable-buttons .btn {
        margin-right: 5px;
        margin-left: 0px;
}

tr[role="search_panel"] div.form-control.select2 {
        min-width: 0px!important;
}
.treeview > .list-group {
        margin-bottom: 0px;
}
/* finance_report_table */
/*.finance_report_table_wrapper {*/
        /*overflow-x: auto;*/
        /*overflow-y: auto;*/
        /*position: relative;*/
/*}*/
.finance_report_table {

}
.finance_report_table td, .finance_report_table th {
        padding: 5px;
}
.finance_report_table thead th:not(:first-child) {
        text-align: center;
        background: #ebeced;
        /*width: 100px;*/
}
.finance_report_table tbody td:not(:first-child) {
        text-align: right;
        /*width: 100px;*/
}
.finance_report_table td:first-child ,
.finance_report_table th:first-child {
        min-width: 180px;
        max-width: 180px;
        background: #ebeced;
}
.finance_report_table tbody tr td {
        border-top: 1px solid #f2f4f7;
        position: relative;
}
.finance_report_table tr th:not(:first-child),
.finance_report_table tr td:not(:first-child) {
        border-left: 1px solid #f2f4f7;
}

.finance_report_table {
        position: relative;
        background-color: #fff;
        overflow: hidden;
        border-collapse: collapse;
}
.finance_report_table thead {
        position: relative;
        display: block;
        overflow: visible;
}

.finance_report_table thead th {
        min-width: 150px;
        max-width: 150px;
        height: 50px;
        font-weight: normal;
}
.finance_report_table thead th:nth-child(1) {/*first cell in the header*/
        position: relative;
        line-height: 40px;
        display: block;
        background: #ebeced;
}
.finance_report_table tbody {
        position: relative;
        display: block;
        /*height: 239px;*/
        overflow: scroll;
}

.finance_report_table tbody td {
        min-width: 150px;
        max-width: 150px;
}
.finance_report_table tbody tr td:nth-child(1) {  /*the first cell in each tr*/
        position: relative;
        display: block;
        height: 30px;
        z-index: 1;
}
.finance_report_table_expense {
        background: #fbefef;
}
.finance_report_table_income {
        background: #e9f7e9;
}
.finance_report_table_total_expense {
        background: #ffe7e7;
}
.finance_report_table_total_income {
        background: #ccf9cc;
}
.finance_report_table_total {
        background: #ebeced;
        font-weight: bold;
}
.finance_report_table tbody tr:hover td:not(:first-child) {
        background: rgba(0,0,0,.04);
}
.page-bar .page-toolbar .form-control-fit-height {
        margin-right: -3px;
        margin-top: -4px;
}
.page-bar .page-toolbar .form-control-fit-height button {
        padding-top: 8px;
        padding-bottom: 8px;
}

.sum-green {
        color: green;
        white-space: nowrap;
}
.sum-grey {
        color: grey;
        white-space: nowrap;
}
.sum-red {
        color: red;
        white-space: nowrap;
}

.finance_report_table tbody tr td .more-info {
        position: absolute;
        left: -1px;
        top: 1px;
        font-size: 20px;
        color: #5a98d2;
        cursor: pointer;
        opacity: 0;
        transition-duration: .2s;
        /*display: none;*/
}
.finance_report_table tbody tr td .more-info:hover {
        color: #4d80af;
}
.finance_report_table tbody tr td:hover .more-info {
        opacity: 1;
        left: 5px;
}
.get_freport_xls {
        margin-right: -5px;
}
.get_freport_xls i {
        font-size: 22px;
        vertical-align: sub;
}
.svg-mask {
        position: absolute;
        left: 0px;
        top: 0px;
        right: 0px;
        bottom: 0px;
        mix-blend-mode: lighten;
}
#menu-icon-choice {
        position: absolute;
        left: 1px;
        top: 1px;
        right: 1px;
        bottom: 1px;
        background: #00000088;
        text-align: center;
        padding-top: 18%;
}
#menu-icon-choice > div {
        height: 50%;
}
.menu-icon-choice-btn {
        vertical-align: middle;
        transform: scale(1.2);
}
.svgSelector_svgList {
    height: 300px;
    overflow-y: auto;
}
.underline-dotted {
        border-bottom: 1px dashed;
        opacity: .9;
}
.svgSelector_svgList {
        font-size: 0;
}
.svgSelector_svgItem {
        display: inline-block;
        position: relative;
        height: 100px;
        width: 100px;
        line-height: 100px;
        text-align: center;
        border-radius: 25px!important;
}
.svgSelector_svgItem:hover {
        box-shadow: 0 0 10px 2px #f5f5f5 inset;

}
.svgSelector_svgItem.active {
        /*box-shadow: 0 0 1px 1px rgba(18, 185, 243, 0.49) inset;*/
        box-shadow: 0 0 10px 2px #dedede inset;
}
.svgSelector_svgItem > img{
        position: relative;
        width: 70px;
        height: 70px;
        vertical-align: middle;
        transition-duration: .2s;
}
.svgSelector_svgItem:hover > img{
        width: 80px;
        height: 80px;
}
.svgSelector_svgItem > .svg-mask {
        left: -1px;
        top: -1px;
        right: -1px;
        bottom: -1px;
        transition-duration: .5s;
}
.svgSelector_svgItem.active > img{
        width: 88px;
        height: 88px;
}
.svgSelector_colorList {
        padding-top: 5px;
}
.svgSelector_colorItem {
        position: relative;
        display: inline-block;
        height: 30px;
        width: 30px;
        margin: 4px;
        border-radius: 35% !important;
        text-align: center;
        transition-duration: .2s;
}
.svgSelector_colorItem:hover {
        box-shadow: 0 0 4px 0px #00000055;
        transform: scale(1.2);
        border-radius: 45% !important;
}
.svgSelector_colorItem.svgSelector_customColor {
        background: conic-gradient(red, yellow, lime, aqua, blue, magenta, red);
}
.svgSelector_customColor > input {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        opacity: 0;
}
.label.label-sm.b2 {
        padding: 0px 3px;
        border-width: 2px;
        border-style: solid;
}