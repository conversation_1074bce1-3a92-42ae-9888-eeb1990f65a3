﻿using CRM.Models.Enums.General;
using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderDivisionPopup : PopupPage
    {
       
        public OrderDivisionPopup()
        {
            InitializeComponent();

            orderItems = new List<OrderItem>(OrdersHelper.CurrentOrder.Items);
            orderNumberSpan.Text = OrdersHelper.CurrentOrder.OrderNumber.ToString();
        }

        #region Свойства
        private List<OrderItem> orderItems = new List<OrderItem>();
        private List<OrderItem> newOrderItems = new List<OrderItem>();

        private double orderItemsTotal;
        public double OrderItemsTotal
        {
            get => orderItemsTotal;
            set { orderItemsTotal = value; OnPropertyChanged(nameof(OrderItemsTotal)); }
        }
        private double newOrderItemsTotal;
        public double NewOrderItemsTotal
        {
            get => newOrderItemsTotal;
            set { newOrderItemsTotal = value; OnPropertyChanged(nameof(NewOrderItemsTotal)); }
        }

        #endregion


        protected override async void OnAppearing()
        {
            base.OnAppearing();
            RenderData();
        }
        private void RenderData()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                OrderItemsTotal = RenderItems(orderItemsStackLayout, orderItems);
                NewOrderItemsTotal = RenderItems(newOrderItemsStackLayout, newOrderItems);
            });
        }
        private double RenderItems(StackLayout layout,List<OrderItem> items)
        {
            layout.Children.Clear();
            foreach (var item in items)
            {
                var control = new OrderDivisionItemTemplate(item);
                control.OrderItemTapped += Control_OrderItemTapped;
                layout.Children.Add(control);
            }

            return items.Sum(o => o.Total);
        }

        private void Control_OrderItemTapped(object sender, OrderItem e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (orderItems.Contains(e))
                {
                    orderItems.Remove(e);
                    newOrderItems.Add(e);
                }
                else
                {
                    newOrderItems.Remove(e);
                    orderItems.Add(e);
                }
                RenderData();
            });
        }



        private ICommand divideOrder;
        public ICommand DivideOrder
        {
            get => divideOrder ??= new RelayCommand(async obj =>
            {

                if(!orderItems.Any() || !newOrderItems.Any())
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("В обоих заказах должна присутсвовать как минимум одна позиция","Уведомление"));
                    return;
                }

                OrdersHelper.CurrentOrder.Items = orderItems;

                var newOrder = new Order()
                {
                    OpenedAt = DateTime.UtcNow,
                    DutyId = ApplicationState.CurrentDuty.Id,
                    Items = newOrderItems,
                    UserId = Auth.User.Id,
                    Comment = OrdersHelper.CurrentOrder.Comment,
                };
                if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe
                   && OrdersHelper.CurrentOrder.OrderEnvironment != null)
                {
                    newOrder.OrderEnvironment = new OrderEnvironment
                    {
                        GuestsCount = OrdersHelper.CurrentOrder.OrderEnvironment.GuestsCount,
                        TableId = OrdersHelper.CurrentOrder.OrderEnvironment.TableId,
                    };
                }


                newOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.CreateOrder(ApplicationState.CurrentDomain,
                                                                                           ApplicationState.CurrentStore.Id,
                                                                                           newOrder);

                await OrdersHelper.SaveOrder();
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Заказ успешно разделен", "Уведомление"));
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}