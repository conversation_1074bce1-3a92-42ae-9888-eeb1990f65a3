﻿using CRM.Models.Network;
using CRM.Models;
using System;
using System.Collections.Generic;
using System.Text;

namespace CrmAdminApp.Helpers
{
    public class SavedData
    {
        public readonly static string FilePath = System.Environment.GetFolderPath(System.Environment.SpecialFolder.Personal) + @"/savedData.json";

        public string CurrentDomain { get; set; }




        public int? CurrentTradeNetworkId { get; set; }
        public int? CurrentStoreId { get; set; }



        public int? POSTerminalId { get; set; }
        public int? NetworkTerminalUserId { get; set; }
        public int? GamificationUserId { get; set; }
    }
}
