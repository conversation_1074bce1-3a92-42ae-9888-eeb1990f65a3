﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:BaseDashboardPage
              xmlns:abstractions="clr-namespace:CRMAdminMoblieApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Views.Pages.Dashboard.Cassa"
             x:Name="this" 
             xmlns:xct="http://xamarin.com/schemas/2020/toolkit" 
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates" 
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             Background="#ffffff"
             ios:Page.UseSafeArea="true"
             xmlns:views="clr-namespace:SVGChart.Nuget.Views;assembly=SVGChart.Nuget" 
             xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview">
    <abstractions:BaseDashboardPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </abstractions:BaseDashboardPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="230" />
                <RowDefinition Height="*" />
                <RowDefinition Height="80" />
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">
                <parts:Header x:Name="header"/>
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout Spacing="0">


                    <Frame
                        Margin="20,20,20,0"
                        HasShadow="False"
                        CornerRadius="5"
                        Background="{x:StaticResource purple_gradient}"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"
                        Padding="0"
                        HeightRequest="40">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenDutyPopup}"/>
                        </Frame.GestureRecognizers>
                        <Grid>
                            <StackLayout
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,21,0"
                                Spacing="21"
                                Orientation="Horizontal">

                                <Label 
                                     x:Name="dutyLabel"
                                     FontFamily="TTFirsNeue-Regular"
                                     FontSize="14"
                                     TextColor="White"
                                     VerticalOptions="Center"
                                     Text=""/>
                                <Image 
                                    WidthRequest="22"
                                    HeightRequest="22"
                                    Source="calendar.png"/>

                            </StackLayout>
                        </Grid>
                    </Frame>

                    <Grid
                        x:Name="phoneStats"
                        Margin="0,20,0,0"
                        HeightRequest="150">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">

                            <views:DonutChartView         
                                 VerticalOptions="Center"
                                 HorizontalOptions="Center"
                                 HeightRequest="140"
                                 WidthRequest="140"
                                 ItemSource="{Binding Source={x:Reference this},Path=DonutChartSections}"
                                 x:Name="donutChart" />


                            <Image 
                                 Margin="0,-10,0,0"
                                 VerticalOptions="Center"
                                 HorizontalOptions="Center"
                                 WidthRequest="38"
                                 HeightRequest="30"
                                 Source="wallet.png"/>

                        </Grid>

                        <Grid Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <StackLayout 
                                      VerticalOptions="Center"
                                      HorizontalOptions="Start"
                                      Orientation="Horizontal">
                                    <Ellipse
                                         Fill="{x:StaticResource purple}"
                                         VerticalOptions="Start"
                                         HorizontalOptions="Start"
                                         HeightRequest="10"
                                         WidthRequest="10"/>
                                    <StackLayout
                                        VerticalOptions="Start"
                                        Spacing="1">
                                        <Label 
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="14"
                                             TextColor="{x:StaticResource dark_purple}"
                                             Text="Картой"/>
                                        <Label 
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="12"
                                             TextColor="{x:StaticResource text_gray}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCardPercent}"/>
                                                        <Span Text="%"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                        <Label 
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="20"
                                             TextColor="{x:StaticResource purple}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCard}"/>
                                                        <Span Text="₽"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </StackLayout>
                            </Grid>

                            <Grid Grid.Row="1">
                                <StackLayout
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Orientation="Horizontal">
                                    <Ellipse
                                         Fill="{x:StaticResource green}"
                                         VerticalOptions="Start"
                                         HorizontalOptions="Start"
                                         HeightRequest="10"
                                         WidthRequest="10"/>
                                    <StackLayout
                                        VerticalOptions="Start"
                                        Spacing="1">
                                        <Label 
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="14"
                                            TextColor="{x:StaticResource dark_purple}"
                                            Text="Наличными"/>
                                        <Label 
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="12"
                                            TextColor="{x:StaticResource text_gray}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCashPercent}"/>
                                                        <Span Text="%"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                        <Label 
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="20"
                                            TextColor="{x:StaticResource green}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCash}"/>
                                                        <Span Text="₽"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </StackLayout>
                            </Grid>

                        </Grid>

                    </Grid>

                    <Grid
                        x:Name="tabletStats"
                        Margin="0,20,0,0"
                        HeightRequest="300">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">

                            <views:DonutChartView         
                                 VerticalOptions="Center"
                                 HorizontalOptions="End"
                                 HeightRequest="340"
                                 WidthRequest="340"
                                 ItemSource="{Binding Source={x:Reference this},Path=DonutChartSections}"
                                 x:Name="donutChart2" />


                            <Image 
                                 Margin="0,-10,145,0"
                                 VerticalOptions="Center"
                                 HorizontalOptions="End"
                                 WidthRequest="100"
                                 HeightRequest="100"
                                 Source="wallet.png"/>

                        </Grid>

                        <Grid Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <StackLayout 
                                      VerticalOptions="Center"
                                      HorizontalOptions="Start"
                                      Orientation="Horizontal">
                                    <Ellipse
                                         Fill="{x:StaticResource purple}"
                                         Margin="0,4,0,0"
                                         VerticalOptions="Start"
                                         HorizontalOptions="Start"
                                         HeightRequest="25"
                                         WidthRequest="25"/>
                                    <StackLayout
                                        VerticalOptions="Start"
                                        Spacing="1">
                                        <Label 
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="25"
                                             TextColor="{x:StaticResource dark_purple}"
                                             Text="Картой"/>
                                        <Label 
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="20"
                                             TextColor="{x:StaticResource text_gray}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCardPercent}"/>
                                                        <Span Text="%"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                        <Label 
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="20"
                                             TextColor="{x:StaticResource purple}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCard}"/>
                                                        <Span Text="₽"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </StackLayout>
                            </Grid>

                            <Grid Grid.Row="1">
                                <StackLayout
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Orientation="Horizontal">
                                    <Ellipse
                                         Fill="{x:StaticResource green}"
                                         Margin="0,4,0,0"
                                         VerticalOptions="Start"
                                         HorizontalOptions="Start"
                                         HeightRequest="25"
                                         WidthRequest="25"/>
                                    <StackLayout
                                        VerticalOptions="Start"
                                        Spacing="1">
                                        <Label 
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="25"
                                            TextColor="{x:StaticResource dark_purple}"
                                            Text="Наличными"/>
                                        <Label 
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="20"
                                            TextColor="{x:StaticResource text_gray}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCashPercent}"/>
                                                        <Span Text="%"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                        <Label 
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="20"
                                            TextColor="{x:StaticResource green}">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="{Binding Source={x:Reference this},Path=CashRegisterReport.ByCash}"/>
                                                        <Span Text="₽"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </StackLayout>
                            </Grid>

                        </Grid>

                    </Grid>

                    
                    
                    
                </StackLayout>

            </Grid>

            <Grid Grid.Row="2">


                <collectionview:DXCollectionView
                   x:Name="collectionView"
                   Margin="20,30,20,20"      
                   Background="#ffffff"
                   VerticalOptions="Fill"
                   MinItemSize="60"
                   ItemSpacing="10"
                   IsScrollBarVisible="False"
                   BackgroundColor="#ffffff">
                    <collectionview:DXCollectionView.ItemTemplate>
                        <DataTemplate>
                            <itemtemplates:TransactionCard 
                               HeightRequest="60"
                               VerticalOptions="Start"
                               HorizontalOptions="FillAndExpand" />
                        </DataTemplate>
                    </collectionview:DXCollectionView.ItemTemplate>
                </collectionview:DXCollectionView>

                <Label 
                     x:Name="noItemsLabel"
                     IsVisible="True"
                     FontFamily="TTFirsNeue-Regular"
                     FontSize="14"
                     TextColor="{x:StaticResource dark_purple}"
                     VerticalOptions="Center"
                     HorizontalOptions="Center"
                     HorizontalTextAlignment="Center"
                     WidthRequest="200"
                     Text="На данный момент нет транзакций"/> 


            </Grid>
            
            
            <Grid Grid.Row="3">
                <parts:Footer IsCassaPage="True"/>
            </Grid>
            
            
        </Grid>
    </ContentPage.Content>
</abstractions:BaseDashboardPage>