﻿using CRM.Models.Enums;
using CRM.Models.Network.Finances;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для CreateTransactionPopup.xaml
    /// </summary>
    public partial class CreateTransactionPopup : BaseWindow
    {
        public CreateTransactionPopup()
        {
            InitializeComponent();

            NewTransaction = new Transaction();
            NewTransaction.UserId = Auth.User.Id;

            FinanceOperationStrings = new List<string>
            {
                "Приход",
                "Расход",
                "Инкассация",
            };

            waiterNameLabel.Text = $"{Auth.User.Name} {Auth.User.Surname}";
            waiterRoleLabel.Text = EnumDescriptionHelper.GetDescription(Auth.User.Role);

            transactionDateLabel.Text = DateTime.Now.ToString("dd MMMM yyyy");
        }


        private string selectedFinanceOperationString;
        public string SelectedFinanceOperationString
        {
            get => selectedFinanceOperationString;
            set { selectedFinanceOperationString = value; OnPropertyChanged(nameof(SelectedFinanceOperationString)); }
        }

        private List<string> financeOperationStrings = new List<string>();
        public List<string> FinanceOperationStrings
        {
            get => financeOperationStrings;
            set { financeOperationStrings = value; OnPropertyChanged(nameof(FinanceOperationStrings)); }
        }



        private Transaction newTransaction = new Transaction();
        public Transaction NewTransaction
        {
            get => newTransaction;
            set { newTransaction = value; OnPropertyChanged(nameof(NewTransaction)); }
        }






        private ICommand createTransaction;
        public ICommand CreateTransaction
        {
            get => createTransaction ??= new RelayCommand(async obj =>
            {
                NewTransaction.TransactionDate = DateTime.Now;

                switch (SelectedFinanceOperationString)
                {
                    case "Приход":
                        NewTransaction.Operation = FinanceOperation.Income;
                        break;
                    case "Расход":
                        NewTransaction.Operation = FinanceOperation.Expense;
                        break;
                    case "Инкассация":
                        NewTransaction.Operation = FinanceOperation.Incassaction;
                        break;
                }

                await MobileAPI.BaristaMethods.BaristaTransactionsMethods.CreateTransaction(ApplicationState.CurrentDomain,
                                                                                            ApplicationState.CurrentTradeNetwork.Id,
                                                                                            ApplicationState.CurrentStore.Id,
                                                                                            NewTransaction);

                this.Close();
            });
        }
    }
}
