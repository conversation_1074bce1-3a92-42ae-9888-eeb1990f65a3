﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using MarkupCreator.Converters;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PrinterItemTemplate : ContentView
    {
        public static readonly BindableProperty PrinterProperty =
            BindableProperty.Create(nameof(Printer), typeof(Printer), typeof(PrinterItemTemplate));
        public Printer Printer
        {
            get { return (Printer)GetValue(PrinterProperty); }
            set { SetValue(PrinterProperty, value); }
        }

        public PrinterItemTemplate(Printer printer)
        {
            InitializeComponent();
            Printer = printer;
        }




        public event EventHandler<Printer> ItemTapped;
        private void onItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ItemTapped?.Invoke(this, Printer);
            });
        }
    }
}