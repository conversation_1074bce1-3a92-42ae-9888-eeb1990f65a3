﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             HeightRequest="60"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Parts.Header">
    <ContentView.Content>
        <Grid 
            BackgroundColor="{StaticResource bg_purple}"
            Grid.Row="0">

            <StackLayout 
                Spacing="0"
                VerticalOptions="Center"
                Orientation="Horizontal">

                <Grid 
                    VerticalOptions="Fill"
                    HorizontalOptions="Start"
                    BackgroundColor="Transparent"
                    WidthRequest="50">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                    </Grid.GestureRecognizers>
                    <ImageButton 
                        InputTransparent="True"
                        Command="{Binding Source={x:Reference this},Path=GoBack}"
                        Margin="30,0,0,0"
                        VerticalOptions="Center"
                        WidthRequest="10"
                        HeightRequest="20"
                        BackgroundColor="Transparent"
                        CornerRadius="0"
                        Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"/>
                </Grid>
                
             

                <Image
                    Margin="20,0,0,0"
                    VerticalOptions="Center"
                    Aspect="Fill"
                    Source="{OnPlatform Default=logo.png, WPF='pack://application:,,,/Images/logo.png'}"
                    HeightRequest="50"
                    WidthRequest="60"/>

            </StackLayout>


            <Label
                VerticalOptions="Center"
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="20"
                Text="{Binding Source={x:Reference this},Path=Title}"/>



            <Grid 
               WidthRequest="300"
               HeightRequest="60"
               VerticalOptions="Center"
               Margin="0,0,30,0"
               HorizontalOptions="End">

                <StackLayout 
                    Padding="0"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    Orientation="Horizontal">
                    <Frame 
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        HeightRequest="40"
                        WidthRequest="40"
                        CornerRadius="20"
                        HasShadow="False"
                        IsClippedToBounds="True"
                        BackgroundColor="White"
                        Background="White"
                        Padding="0">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoMainMenu}"/>
                        </Frame.GestureRecognizers>
                        <Image
                            x:Name="userAvatar"
                            Aspect="Fill"
                            Source="{OnPlatform Default=userAvatar.png, WPF='pack://application:,,,/Images/userAvatar.png'}"/>
                    </Frame>
                    <StackLayout
                         VerticalOptions="Center"
                        Spacing="0">
                        <Label
                           VerticalOptions="Center"
                            x:Name="baristaFIOLabel"
                            HorizontalOptions="End"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Мария Иванова"/>
                        <Label
                            VerticalOptions="Center"
                            x:Name="baristaRoleLabel"
                            HorizontalOptions="End"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Официант"/>
                    </StackLayout>
                    <ImageButton 
                        VerticalOptions="Center"
                        Source="{OnPlatform Default=arrowDown.png, WPF='pack://application:,,,/Images/arrowDown.png'}"
                        WidthRequest="9"
                        HeightRequest="4"
                        Aspect="Fill"
                        BackgroundColor="Transparent"/>

                </StackLayout>
                

            </Grid>

        </Grid>

    </ContentView.Content>
</ContentView>