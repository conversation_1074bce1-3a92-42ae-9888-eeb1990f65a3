﻿using CRM.Models.Enums.Info;
using CRM.Models.Network.Delivery;
using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NewDeliveryPopup : PopupPage
    {

        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }
        public NewDeliveryPopup()
        {
            InitializeComponent();
            Order = new Order();
            Order.Delivery = new Delivery();

            Load();
        }

        public NewDeliveryPopup(Order order)
        {
            InitializeComponent();
            Order = order;

            Load();
        }



        protected async void Load()
        {

            await Task.Run(async () =>
            {
                DeliveryServices = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliveryServices(ApplicationState.CurrentDomain,
                                                                                                            ApplicationState.CurrentTradeNetwork.Id);
                SelectedDeliveryService = DeliveryServices.FirstOrDefault();


                DeliveryMen = await MobileAPI.BaristaMethods.BaristaDeliveryMethods.GetDeliverymen(ApplicationState.CurrentDomain,
                                                                                                   ApplicationState.CurrentTradeNetwork.Id);
                DeliveryMen.Insert(0, new DeliveryMan
                {
                    Name = "Не выбран"
                });
                SelectedDeliveryMan = DeliveryMen.FirstOrDefault();



                //AvailableCustomers = await MobileAPI.BaristaMethods.BaristaClientsMethods.GetClients(ApplicationState.CurrentDomain,
                //                                                                                     ApplicationState.CurrentTradeNetwork.Id);
            });

            await Device.InvokeOnMainThreadAsync(() =>
            {
                personsCountCB.SelectedIndex = 0;

                deliveryDateDateEdit.MinDate = DateTime.Now.Date;
                deliveryDateDateEdit.Date = DateTime.Now;
            });
            
        }



        #region Главное
        private List<int> personsCount = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
        public List<int> PersonsCount
        {
            get => personsCount;
            set { personsCount = value; OnPropertyChanged(nameof(PersonsCount)); }
        }





        //public List<Customer> AvailableCustomers { get; set; } = new List<Customer>();

        private Customer selectedCustomer;
        public Customer SelectedCustomer
        {
            get => selectedCustomer;
            set
            { 
                selectedCustomer = value;
                OnPropertyChanged(nameof(SelectedCustomer));
                if(value != null)
                {
                    clientFIOEntry.Text = $"{value.Name} {value.Surname}";
                }
            }
        }

        private string phoneNumber;
        public string PhoneNumber
        {
            get => phoneNumber;
            set
            { 
                phoneNumber = value;
                OnPropertyChanged(nameof(PhoneNumber));
            }
        }

        private ICommand goToNewClient;
        public ICommand GoToNewClient
        {
            get => goToNewClient ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    var popup = new ClientsPopup(Order, true);
                    popup.OnCustomerSelected += Popup_OnCustomerSelected;
                    App.Current.MainPage.Navigation.PushPopupAsync(popup);
                });
            });
        }

        private void Popup_OnCustomerSelected(object sender, Customer e)
        {
            SelectedCustomer = e;
            PhoneNumber = e.Phone;
        }


        #endregion

        #region Аванс

        public void SetPrepaymentSumText()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                var sum = Order.OrderPayments.Where(o => o.IsPrepeyment).Sum(o => o.Sum);
                prepaymentSumSpan.Text = Math.Round(sum, 2).ToString();
            });
        }


        private ICommand goToPrepayment;
        public ICommand GoToPrepayment
        {
            get => goToPrepayment ??= new RelayCommand(async obj =>
            {

                //if(Order.Items.Count == 0)
                //{
                //    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Не выбрано ни одной позиции в заказе", "Уведомление"));
                //    return;
                //}

                await App.Current.MainPage.Navigation.RemovePopupPageAsync(this);
                await App.Current.MainPage.Navigation.PushAsync(new OrderPrepaymentPage(Order,this));
            });
        }
        #endregion

        #region Исполнители

        private List<DeliveryService> deliveryServices = new List<DeliveryService>();
        public List<DeliveryService> DeliveryServices
        {
            get => deliveryServices;
            set { deliveryServices = value; OnPropertyChanged(nameof(DeliveryServices)); }
        }
        private List<DeliveryMan> deliveryMen = new List<DeliveryMan>();
        public List<DeliveryMan> DeliveryMen
        {
            get => deliveryMen;
            set { deliveryMen = value; OnPropertyChanged(nameof(DeliveryMen)); }
        }

        private DeliveryService selectedDeliveryService;
        public DeliveryService SelectedDeliveryService
        {
            get => selectedDeliveryService;
            set
            {
                selectedDeliveryService = value;
                OnPropertyChanged(nameof(SelectedDeliveryService));

                if (value is null) return;

                deliveryManComboBox.IsEnabled = value.OurDeliverymans;
                if (!value.OurDeliverymans)
                {
                    SelectedDeliveryMan = DeliveryMen.FirstOrDefault(o => o.Id == 0);
                }
            }
        }
        private DeliveryMan selectedDeliveryMan;
        public DeliveryMan SelectedDeliveryMan
        {
            get => selectedDeliveryMan;
            set { selectedDeliveryMan = value; OnPropertyChanged(nameof(SelectedDeliveryMan)); }
        }

        #endregion



        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand createOrder;
        public ICommand CreateOrder
        {
            get => createOrder ??= new RelayCommand(async obj =>
            {
                Order.Customer = SelectedCustomer;

                Order.Delivery.Date = (DateTime)deliveryDateDateEdit.Date;
                Order.Delivery.Service = SelectedDeliveryService;
                Order.Delivery.DeliveryMan = SelectedDeliveryMan;
                Order.DutyId = ApplicationState.CurrentDuty.Id;
                Order.UserId = Auth.User.Id;

                Order = await MobileAPI.BaristaMethods.BaristaOrdersMethods.CreateOrder(ApplicationState.CurrentDomain,
                                                                                ApplicationState.CurrentStore.Id,
                                                                                Order);
                OrdersHelper.CurrentOrder = Order;

                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());

            });
        }

    }
}