﻿<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">



    <Style TargetType="Switch" x:Key="blue_rb">
        <Style.Triggers>
            <Trigger TargetType="Switch" Property="IsChecked" Value="True">
                <Setter Property="BorderColor" Value="#05AEF1"/>
                <Setter Property="BackgroundColor" Value="#05AEF1"/>
            </Trigger>
            <Trigger TargetType="Switch" Property="IsChecked" Value="False">
                <Setter Property="BorderColor" Value="#05AEF1"/>
                <Setter Property="BackgroundColor" Value="#05AEF1"/>
            </Trigger>
        </Style.Triggers>
    </Style>

  
    
    
</ResourceDictionary>