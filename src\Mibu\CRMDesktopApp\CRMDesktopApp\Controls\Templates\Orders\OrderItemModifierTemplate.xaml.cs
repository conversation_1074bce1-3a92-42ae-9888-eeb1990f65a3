﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для OrderItemModifierTemplate.xaml
    /// </summary>
    public partial class OrderItemModifierTemplate : UserControl
    {
        public OrderItemModifierTemplate(OrderItemModifier item)
        {
            Model = item;
            InitializeComponent();

            this.Height = Model.SelectedOptions.Count * 25;
        }
        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(OrderItemModifier), typeof(OrderItemModifierTemplate));
        public OrderItemModifier Model
        {
            get { return (OrderItemModifier)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
