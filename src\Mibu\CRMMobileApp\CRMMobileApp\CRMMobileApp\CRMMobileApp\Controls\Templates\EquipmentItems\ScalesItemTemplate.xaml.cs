﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using MarkupCreator.Converters;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ScalesItemTemplate : ContentView
    {
        public static readonly BindableProperty ScalesProperty =
            BindableProperty.Create(nameof(Scales), typeof(Scales), typeof(ScalesItemTemplate));
        public Scales Scales
        {
            get { return (Scales)GetValue(ScalesProperty); }
            set { SetValue(ScalesProperty, value); }
        }

        public ScalesItemTemplate(Scales scales)
        {
            InitializeComponent();
            Scales = scales;
        }




        public event EventHandler<Scales> ItemTapped;
        private void onItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ItemTapped?.Invoke(this, Scales);
            });
        }
    }
}