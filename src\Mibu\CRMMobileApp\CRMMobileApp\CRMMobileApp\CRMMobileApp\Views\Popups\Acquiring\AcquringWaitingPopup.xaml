﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.AcquringWaitingPopup">
    <Grid>



        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="400"
            HeightRequest="200"
            BackgroundColor="White"
            Background="White"
            Padding="0"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,40,0,0"
            CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <StackLayout
                        Margin="15,30,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Spacing="0"
                        Orientation="Horizontal">

                        <!--<Image 
                    Margin="25,0,0,0"
                    Aspect="Fill"
                    Source="{OnPlatform Default=client.png, WPF='pack://application:,,,/Images/client.png'}"
                    WidthRequest="30"
                    HeightRequest="30"
                    VerticalOptions="Center"/>-->

                        <Label
                            Margin="15,0,0,0"
                            VerticalOptions="Center"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text="Оплата картой"/>

                    </StackLayout>



                    <ImageButton 
                        Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        BackgroundColor="Transparent"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Padding="10"
                        WidthRequest="36"
                        HeightRequest="36"
                        Margin="0,10,10,0"/>



                </Grid>


                <StackLayout Grid.Row="1">

                    <Label
                        x:Name="sumLabel"
                        Margin="35,0,0,0"
                        VerticalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span Text="Сумма: "/>
                                    <Span Text="{Binding Source={x:Reference this},Path=Sum}"/>
                                    <Span Text=" руб."/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                    <Label
                        Margin="35,0,0,0"
                        VerticalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"
                        Text="Ожидание ответа"/>

                </StackLayout>

                <Grid Grid.Row="2">

                    <Button 
                         Command="{Binding Source={x:Reference this},Path=ContinueWithoutAquiring}"
                         Style="{StaticResource bg_purple_btn}"
                         VerticalOptions="Center"
                         HorizontalOptions="Start"
                         Margin="20,0,0,0"
                         Text="Продолжить без экваринга"
                         WidthRequest="250"
                         HeightRequest="40"/>

                    <Button 
                         Command="{Binding Source={x:Reference this},Path=Close}"
                         Style="{StaticResource bg_purple_btn}"
                         VerticalOptions="Center"
                         HorizontalOptions="End"
                         Margin="0,0,20,0"
                         Text="Отмена"
                         WidthRequest="100"
                         HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>



    </Grid>
</animations:PopupPage>