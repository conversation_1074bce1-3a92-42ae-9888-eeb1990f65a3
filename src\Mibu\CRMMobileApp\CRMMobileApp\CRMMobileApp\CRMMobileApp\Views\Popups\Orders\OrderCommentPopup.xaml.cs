﻿using CRM.Models.Stores;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderCommentPopup : PopupPage
    {
        public OrderCommentPopup(Order order)
        {
            Order = order;
            Comment = Order.Comment;
            InitializeComponent();
        }
        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }




        private string comment;
        public string Comment
        {
            get => comment;
            set { comment = value; OnPropertyChanged(nameof(Comment)); }
        }

        private ICommand saveChanges;
        public ICommand SaveChanges
        {
            get => saveChanges ??= new RelayCommand(async obj =>
            {
                OrdersHelper.SetComment(Comment);
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}