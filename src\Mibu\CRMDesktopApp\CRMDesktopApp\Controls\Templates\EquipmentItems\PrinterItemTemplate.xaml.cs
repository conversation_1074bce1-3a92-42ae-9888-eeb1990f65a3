﻿using CRM.Models.Stores.Settings.Equipment;
using CRMMobileApp.Core;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using AcquiringTerminal = CRM.Models.Stores.Settings.Equipment.Acquiring;

namespace CRMDesktopApp.Controls.Templates.Devices
{
    /// <summary>
    /// Логика взаимодействия для PrinterCard.xaml
    /// </summary>
    public partial class PrinterItemTemplate : UserControl
    {

        public static readonly DependencyProperty PrinterProperty =
           DependencyProperty.Register(nameof(Printer), typeof(Printer), typeof(PrinterItemTemplate));
        public Printer Printer
        {
            get { return (Printer)GetValue(PrinterProperty); }
            set { SetValue(PrinterProperty, value); }
        }

        public PrinterItemTemplate(Printer printer)
        {
            InitializeComponent();
            Printer = printer;
        }




        public event EventHandler<Printer> ItemTapped;
        private void onItemTapped(object sender, MouseButtonEventArgs e)
        {
            ItemTapped?.Invoke(this, Printer);
        }
    }
}
