﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.DiscountPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        xmlns:templates="clr-namespace:CRMDesktopApp.Controls.Templates"
        mc:Ignorable="d"
        Title="Скидки" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        x:Name="this"
        WindowStartupLocation="CenterScreen"
        Width="670"
        Height="310">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="660"
        Height="305"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>


            <Grid 
                Grid.Row="0"
                Margin="30,20,0,0">
                <StackPanel Orientation="Horizontal">
                    <Image 
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Source="pack://application:,,,/Resources/Images/discount.png"
                        Width="30"
                        Height="30"/>
                    <TextBlock
                        Margin="0,3,0,0"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="18"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="Скидка"/>
                </StackPanel>


                <customcontrols:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,15,35,0"/>
            </Grid>





            <Grid Grid.Row="1">


                <ListBox
                     BorderThickness="0"
                     Margin="35,0,0,0"
                     ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                     ScrollViewer.VerticalScrollBarVisibility="Hidden"
                     SelectedItem="{Binding ElementName=this,Path=SelectedDiscount,Mode=TwoWay}"
                     ItemsSource="{Binding ElementName=this,Path=Discounts,Mode=TwoWay}"
                     ItemContainerStyle="{StaticResource lbItemContainerStyle}">
                    <ListBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal">

                            </StackPanel>
                        </ItemsPanelTemplate>
                    </ListBox.ItemsPanel>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <templates:DiscountCard
                                 Width="130"
                                 Height="110"
                                 Margin="0,0,10,0"
                                 Model="{Binding}"/>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <TextBlock
                    x:Name="noItemsTextBlock"
                    Visibility="Hidden"
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    TextAlignment="Center"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource text_gray}"
                    FontSize="15"
                    Width="300"
                    TextWrapping="Wrap"
                    Text="По данный момент скидки не заданы. Настроить скидки можно в web-панели Mibu в разделе программ лояльности"/>

            </Grid>

       

            <Grid Grid.Row="2">

                <StackPanel
                    VerticalAlignment="Bottom"
                    HorizontalAlignment="Right"
                    Margin="0,0,30,30"
                    Orientation="Horizontal">

                    <Button 
                        Margin="20,0,0,0"
                        Command="{Binding ElementName=this,Path=ApplyDiscount}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Center"
                        Content="Применить"
                        Width="170"
                        Height="40"/>

                </StackPanel>

            </Grid>
        </Grid>
    </Border>
</abstactions:BaseWindow>
