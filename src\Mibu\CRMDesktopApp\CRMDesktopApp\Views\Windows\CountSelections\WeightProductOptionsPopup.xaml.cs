﻿using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для WeightProductOptionsPopup.xaml
    /// </summary>
    public partial class WeightProductOptionsPopup : BaseWindow
    {
        private OrderItem product;
        public OrderItem Product
        {
            get => product;
            set { product = value; OnPropertyChanged(nameof(Product)); }
        }
        public WeightProductOptionsPopup(OrderItem product)
        {
            InitializeComponent();
            Product = product;
        }
        public WeightProductOptionsPopup(OrderItem product, double amount)
        {
            InitializeComponent();
            Product = product;
            Amount = amount;

            _isNewItem = false;
        }
        private bool _isNewItem = true;


        #region Количество товара
        private ICommand openAmountKeyboard;
        public ICommand OpenAmountKeyboard
        {
            get => openAmountKeyboard ??= new RelayCommand(async obj =>
            {
                var popup = new ProductAmountKeyboardPopup(Product.Price);
                popup.onApplyingValue += Popup_onApplyingValue;
                popup.ShowDialog();
            });
        }
        private void Popup_onApplyingValue(object sender, double e)
        {
            Amount = e;
        }

        private double amount = 1.000;
        public double Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }
        #endregion





        private ICommand addToOrder;
        public ICommand AddToOrder
        {
            get => addToOrder ??= new RelayCommand(async obj =>
            {
                Product.CreatedAt = DateTime.Now;
                Product.Amount = Amount;

                if (_isNewItem) OrdersHelper.AddOrderItem(Product);
                else await OrdersHelper.UpdateOrderItem(Product);

                this.Close();
            });
        }
    }
}
