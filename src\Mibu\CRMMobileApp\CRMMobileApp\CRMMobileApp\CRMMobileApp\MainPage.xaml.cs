﻿using CRM.Models.Enums.General;
using CRMAdminMoblieApp.Custom;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Popups;
using Mibu.Landing.Models.Enums;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMMobileApp
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
            keyboard.ButtonTapped += Keyboard_ButtonTapped;
        }


        protected override void OnAppearing()
        {
            base.OnAppearing();

            Device.StartTimer(TimeSpan.FromMilliseconds(15), () =>
            {
                loadingWheel.Rotation += 2;
                return true;
            });

            passwordEntries = new List<AndroidStyleEntry>
            {
                passwordEntry1,
                passwordEntry2,
                passwordEntry3,
                passwordEntry4
            };
        }

        List<AndroidStyleEntry> passwordEntries;
        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (e == "backspace" && Password.Length > 0)
            {
                passwordEntries[Password.Length - 1].Text = "";
            }
            else if (e == "close")
            {
                passwordEntry1.Text = "";
                passwordEntry2.Text = "";
                passwordEntry3.Text = "";
                passwordEntry4.Text = "";
            }
            else if (e != "backspace")
            {
                passwordEntries[Password.Length].Text = e;
            }
        }
        private void onPasswordEntryTextChanged(object sender, TextChangedEventArgs e)
        {
            Password = passwordEntry1.Text + passwordEntry2.Text + passwordEntry3.Text + passwordEntry4.Text;
        }

        private string password = string.Empty;
        public string Password
        {
            get => password;
            set
            {
                password = value;
                OnPropertyChanged(nameof(Password));

                if (value.Length == 4)
                {
                    TryAuth();
                } 
            }
        }

        private async Task TryAuth()
        {
            await Task.Delay(400);
            await Device.InvokeOnMainThreadAsync(async () =>
            {
                if (ApplicationState.Subscription.IsExpired)
                {
                    errorMessageLabel.Text = "Необходимо оплатить подписку";
					return;
                }


                bool authResult = await Auth.AuthorizeByPIN(Password);
                if (authResult)
                {
                    foreach (var o in passwordEntries)
					{
						o.Text = "";
					}
					keyboard.IsEnabled = false;

                    await ApplicationState.LoadEquipment();

                    if (ApplicationState.IsFiscalizationAvailable() == Enums.FiscalizationStatus.HasNoRegistrators)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нет ни одного фискального регистратора. " +
                            "Чтобы продолжить необходимо добавить ККТ в web-панели либо отключите фискализацию в настройках точки","Ошибка"));
                        keyboard.IsEnabled = true;
                        return;
                    }
                    else if(ApplicationState.IsFiscalizationAvailable() == Enums.FiscalizationStatus.CanBeDone)
                    {

                        if (!ApplicationState.IsFiscalizerAvailableOnThisPlatform())
                        {
                            App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("На данном устройстве нельзя подключиться к ККТ. Продолжение операции невозможно", "Ошибка", "Хорошо"));
                            keyboard.IsEnabled = true;
                            return;
                        }

                        var fiscalizers = ApplicationState.StoreFiscalRegistrators;
                        if (fiscalizers.Count() == 1)
                        {
                            ApplicationState.FiscalizerInUse = fiscalizers.FirstOrDefault().Value;
                            if (!ApplicationState.FiscalizerInUse.TryOpenConnection())
                            {
                                App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нет подключения к фискальному аппарату. Продолжение операции невозможно", "Ошибка", "Хорошо"));
                                keyboard.IsEnabled = true;
                                return;
                            }


                            OpenWorkPlace();
                        }
                        else
                        {
                            var popup = new AvailableFiscalRegistratiorsPopup();
                            popup.ItemSelected += (o, e) =>
                            {
                                var card = o as FiscalRegisterItemTemplate;
                                ApplicationState.FiscalizerInUse = card.Driver;
                                if (!ApplicationState.FiscalizerInUse.TryOpenConnection())
                                {
                                    App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нет подключения к фискальному аппарату. Продолжение операции невозможно", "Ошибка", "Хорошо"));
                                    keyboard.IsEnabled = true;
                                    return;
                                }

                                OpenWorkPlace();
                            };
                            await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                        }
                    }
                    else
                    {
                        ApplicationState.FiscalizerInUse = null;
                        OpenWorkPlace();
                    }               

                    keyboard.IsEnabled = true;
                }
                else
                {
                    await Task.Delay(50);
                    foreach (var o in passwordEntries)
                    {
                        o.Text = "";
                    }

                    errorMessageLabel.Text = "Не удалось авторизоваться";
				}
            });
        }

        private void OpenWorkPlace()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                await ApplicationState.LoadMenu();
                await ApplicationState.GetLastDuty();



                bool isCategoriesPageOpened = false;


                if (ApplicationState.CurrentDuty != null)
                {
                    await OrdersHelper.GetCurrentOrder();
                }

                if (ApplicationState.CurrentStore.StoreMode == StoreMode.Point)
                {
                    App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
                    isCategoriesPageOpened = true;
                }


                if (ApplicationState.CurrentDuty is null || ApplicationState.CurrentDuty.ClosedAtLocalAuto.HasValue)
                {
                    App.Current.MainPage.Navigation.PushPopupAsync(new DutyOpeningPopup());
                }
                else
                {
                    if (ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe
                        && ApplicationState.Subscription.Subscription.Type != SubscriptionType.Start)
                    {
                        App.Current.MainPage.Navigation.PushAsync(new HallPage());
                    }
                    else
                    {
                        if (!isCategoriesPageOpened)
                        {
                            App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
                        }
                    }
                }
            });
        }

        private ICommand openSupportPopup;
        public ICommand OpenSupportPopup
        {
            get => openSupportPopup ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new SupportPopup());
            });
        }

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                if(App.Current.MainPage.Navigation.ModalStack.Count > 1)
                {
                    await App.Current.MainPage.Navigation.PopAsync();
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushAsync(new LoginPage());
                }
              
            });
        }

     
    }
}
