﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Modifiers
{
    /// <summary>
    /// Логика взаимодействия для OneOptionModifierOptionItem.xaml
    /// </summary>
    public partial class OneOptionModifierOptionItem : UserControl
    {
        public OneOptionModifierOptionItem(OrderItemModifierOption option)
        {
            InitializeComponent();
            Model = option;
        }

        public static readonly DependencyProperty ModelProperty =
          DependencyProperty.Register(nameof(Model), typeof(OrderItemModifierOption), typeof(OneOptionModifierOptionItem));
        public OrderItemModifierOption Model
        {
            get { return (OrderItemModifierOption)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        public event EventHandler<OrderItemModifierOption> OnChecked;
        private void onChecked(object sender, RoutedEventArgs e)
        {
            Model.IsSelected = radioButton.IsChecked;
            if (radioButton.IsChecked == false) return;
            OnChecked?.Invoke(this, Model);
        }
    }
}
