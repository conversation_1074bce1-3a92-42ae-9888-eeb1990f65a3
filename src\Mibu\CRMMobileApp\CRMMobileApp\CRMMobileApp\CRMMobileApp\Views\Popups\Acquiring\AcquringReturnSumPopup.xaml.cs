﻿using AcquiringProviders.Abstractions;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AcquringReturnSumPopup : PopupPage
    {
        private AbsProvider _provider;
        public AcquringReturnSumPopup(AbsProvider provider)
        {
            _provider = provider;
            InitializeComponent();
        }


        private double sum;
        public double Sum
        {
            get => sum;
            set { sum = value; OnPropertyChanged(nameof(Sum)); }
        }

        private ICommand returnSum;
        public ICommand ReturnSum
        {
            get => returnSum ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (Sum <= 0)
                    {
                        App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Укажите сумму для возврата", "Уведомление"));
                    }
                    else
                    {
                        _provider.Refund(Sum);
                        App.Current.MainPage.Navigation.PopPopupAsync();
                    }
                });
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
    }
}