﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.ClientListItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        Background="Transparent"
        BorderThickness="1"
        Padding="0"
        CornerRadius="10">
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="False">
                        <Setter Property="BorderBrush" Value="Transparent"></Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="True">
                        <Setter Property="BorderBrush" Value="{StaticResource purple}"></Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="0"
                Margin="20,0,0,0"
                VerticalAlignment="Center"
                HorizontalAlignment="Left"
                Foreground="{StaticResource text_gray}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="11">
                  <Run Text="{Binding ElementName=this,Path=Model.Model.Surname}"/>
                  <Run Text=" "/>
                  <Run Text="{Binding ElementName=this,Path=Model.Model.Name}"/>
            </TextBlock>


            <TextBlock
                Grid.Column="1"
                Margin="0,0,0,0"
                VerticalAlignment="Center"
                HorizontalAlignment="Left"
                Foreground="{StaticResource text_gray}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="11"
                Text="{Binding ElementName=this,Path=Model.Model.Phone}"/>

        </Grid>
    </Border>
</UserControl>
