﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.ClosedOrderTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="50">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        Background="Transparent"
        Padding="0"
        CornerRadius="0"
        MouseDown="onTapped">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2.5*"/>
                <ColumnDefinition Width="2.5*"/>
                <ColumnDefinition Width="4*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">

                <StackPanel Orientation="Horizontal">
                    <TextBlock 
                        FontSize="18"
                        VerticalAlignment="Top"
                        HorizontalAlignment="Center"
                        FontWeight="Bold"
                        Foreground="{DynamicResource blueColor}">
                       <Run Text="№"/>
                       <Run Text="{Binding ElementName=this,Path=Model.Id}"/>
                    </TextBlock>


                    <Border
                        x:Name="returnedItemFrame"
                        Visibility="Hidden"
                        CornerRadius="5"
                        Height="20"
                        Width="100"
                        Padding="0"
                        VerticalAlignment="Top"
                        Margin="5,3,0,0"
                        Background="{DynamicResource blueColor}">
                        <TextBlock 
                            Text="Возвращен"
                            FontSize="16"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Foreground="White"/>
                    </Border>


                </StackPanel>

            </Grid>



            <TextBlock 
                Grid.Column="1"
                FontSize="18"
                VerticalAlignment="Top"
                HorizontalAlignment="Center">
                <Run Text="{Binding ElementName=this,Path=Model.Sum,Mode=OneWay}"/>
                <Run Text="руб."/>
            </TextBlock>
            <TextBlock 
                Grid.Column="2"
                FontSize="16"
                VerticalAlignment="Top"
                HorizontalAlignment="Center">
                <Run Text="{Binding ElementName=this,Path=Model.User.Name}"/>
                <Run Text=""/>
                <Run Text="{Binding ElementName=this,Path=Model.User.Surname}"/>
                <Run Text="   "/>
                <Run x:Name="closedAtSpan" Text=""/>
            </TextBlock>
            
            
            <Image
                Grid.Column="3"
                x:Name="cashIcon"
                Visibility="Collapsed"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                Width="25"
                Height="25"
                Source="pack://application:,,,/Resources/Images/cash.png"/>
            <Image
                Grid.Column="3"
                x:Name="cardIcon"
                Visibility="Collapsed"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                Width="25"
                Height="25"
                Source="pack://application:,,,/Resources/Images/card.png"/>
            <Image
                Grid.Column="3"
                x:Name="mixedIcon"
                Visibility="Collapsed"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                Width="43"
                Height="25"
                Source="pack://application:,,,/Resources/Images/mixed.png"/>
            <Image
                Grid.Column="3"
                x:Name="bonusesIcon"
                Visibility="Collapsed"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                Width="18"
                Height="25"
                Source="pack://application:,,,/Resources/Images/bonuses.png"/>

        </Grid>
    </Border>
</UserControl>
