﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

using Tech = CRM.Models.Network.ReferenceBooks.Menu.TechnicalCard;
using TechnicalCard = CRMMobileApp.Controls.Templates.TechnicalCard;


namespace CRMMobileApp.Views.Views
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CategoriesView : ContentView
    {
        public bool fromRoot { get; private set; } = true;

        public MenuCategoriesTreeItem CurrentCategory { get; set; }

        public CategoriesView()
        {
            InitializeComponent();
            Load();
        }

        protected void Load()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (fromRoot)
                {           
                    allCategories = new ObservableCollection<MenuCategoriesTreeItem>(ApplicationState.StoreMenuTree.Nodes.FirstOrDefault().ChildNodes);
                    CurrentCategory = ApplicationState.StoreMenuTree.Nodes.FirstOrDefault();

                    breadCrumps.SetRootCategory(CurrentCategory);
                }
                else
                {
                    allCategories = new ObservableCollection<MenuCategoriesTreeItem>(CurrentCategory.ChildNodes);
                }

                categoryProducts = new List<Product>(ApplicationState.StoreAllProducts.Where(o => o.MenuCategoryId == CurrentCategory.Category.Id));
                categoryTechnicalCards = new List<Tech>(ApplicationState.StoreAllTechnicalCards.Where(o => o.MenuCategoryId == CurrentCategory.Category.Id));

                CreateControls();
            });
        }


        private void breadCrumpsCategoryTapped(object sender, MenuCategoriesTreeItem e)
        {
            CurrentCategory = e;
            Load();
        }

        public void GoToRoot()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!fromRoot)
                {
                    fromRoot = true;
                    Load();
                }
            });
        }


        private List<Product> categoryProducts = new List<Product>();
        private List<Tech> categoryTechnicalCards = new List<Tech>();
        private ObservableCollection<MenuCategoriesTreeItem> allCategories = new ObservableCollection<MenuCategoriesTreeItem>();
        private List<View> allControls = new List<View>();
        private void CreateControls()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                multiList.Children.Clear();
                allControls.Clear();

                foreach (var card in allCategories)
                {
                    var control = new CategoryCard(card)
                    {
                        Margin = new Thickness(30, 30, 0, 0),
                        WidthRequest = 200,
                        HeightRequest = 200
                    };
                    control.CardTapped += CategoryTapped;
                    multiList.Children.Add(control);
                    allControls.Add(control);
                }

                foreach (var product in categoryProducts)
                {
                    var control = new ProductCard(product)
                    {
                        Margin = new Thickness(30, 30, 0, 0),
                        WidthRequest = 200,
                        HeightRequest = 200
                    };
                    control.CardTapped += ProductCardTapped;
                    multiList.Children.Add(control);
                    allControls.Add(control);
                }

                foreach (var product in categoryTechnicalCards)
                {
                    var control = new TechnicalCard(product)
                    {
                        Margin = new Thickness(30, 30, 0, 0),
                        WidthRequest = 200,
                        HeightRequest = 200
                    };
                    control.CardTapped += TechCardTapped;
                    multiList.Children.Add(control);
                    allControls.Add(control);
                }


                noItemsLabel.IsVisible = !multiList.Children.Any();
            });
        }


        private void CategoryTapped(object sender, MenuCategoriesTreeItem e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                CurrentCategory = e;
                fromRoot = false;

                breadCrumps.AddLevel(e);
                Load();
            });
        }

        private void TechCardTapped(object sender, Tech e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                var availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForTechCard(ApplicationState.CurrentDomain,
                                                                                                                     ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                     e.Id);
                var orderItem = new OrderItem()
                {
                    TechnicalCard = e,
                    TechnicalCardId = e.Id
                };
                if (availableModifiers.Any())
                {
                    var popup = new ModifiersPopup(orderItem, availableModifiers, false);
                    await App.Current.MainPage.Navigation.PushPopupAsync(popup);

                    while (!popup.IsCompleted)
                        await Task.Delay(200);

                    if (!popup.IsSuccessfully) return;
                }


                if (e.IsWeightProduct)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new WeightTechCardOptionsPopup(orderItem));
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new TechCardOptionsPopup(orderItem));
                }
            });
        }



        private void ProductCardTapped(object sender, Product e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (OrdersHelper.CurrentOrder is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                    return;
                }
                var availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForProduct(ApplicationState.CurrentDomain,
                                                                                                                      ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                      e.Id);
                var orderItem = new OrderItem()
                {
                    Product = e,
                    ProductId = e.Id
                };

                if (availableModifiers.Any())
                {
                    var popup = new ModifiersPopup(orderItem, availableModifiers, false);
                    await App.Current.MainPage.Navigation.PushPopupAsync(popup);

                    while (!popup.IsCompleted)
                        await Task.Delay(200);

                    if (!popup.IsSuccessfully) return;
                }

                if (e.IsWeightProduct)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new WeightProductOptionsPopup(orderItem));
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new ProductOptionsPopup(orderItem));
                }
            });
        }




        private string searchQuery;
        public string SearchQuery
        {
            get => searchQuery;
            set
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    var filteredControls = new List<View>();
                    multiList.Children.Clear();
                    if (string.IsNullOrEmpty(value))
                    {
                        filteredControls = new List<View>(allControls);
                    }
                    else
                    {
                        var productControls = allControls.Where(o => o is ProductCard).Cast<ProductCard>().Where(o => o.Model.Title.Contains(value));
                        var techCardControls = allControls.Where(o => o is TechnicalCard).Cast<TechnicalCard>().Where(o => o.Model.Title.Contains(value));
                        var categories = allControls.Where(o => o is CategoryCard);

                        filteredControls.AddRange(categories);
                        filteredControls.AddRange(productControls);
                        filteredControls.AddRange(techCardControls);

                    }

                    foreach (var view in filteredControls)
                    {
                        multiList.Children.Add(view);
                    }

                    noItemsLabel.IsVisible = !multiList.Children.Any();

                    searchQuery = value;
                    OnPropertyChanged(nameof(SearchQuery));
                });
            }
        }

       
    }
}