﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.ReturnOrder"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Возврат заказа"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="670"
        Height="310">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Background="White"
        Width="660"
        Height="305"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>

            <Grid 
                Grid.Row="0"
                Margin="0,20,0,0">
                <StackPanel Orientation="Horizontal">
                    <Image 
                        Width="40"
                        Height="40"/>
                    <TextBlock
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="18"
                        FontWeight="Bold"
                        Text="Возврат заказа"/>
                </StackPanel>


                <customcontrols:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,5,25,0"/>
            </Grid>


            <Grid Grid.Row="1">
                <customcontrols:EntryOutlined
                    Margin="20,0,20,0"
                    Height="120"
                    Placeholder="Введите причину возврата"
                    VerticalTextAlignment="Top"
                    PlaceholderMargin="10,10,0,0"
                    TextMargin="10,10,0,0"
                    AcceptsReturn="True"
                    Text="{Binding ElementName=this,Path=Reason,Mode=TwoWay}"
                    Style="{StaticResource white_cornered_entry}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Top"/>
            </Grid>



            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <StackPanel
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Margin="20,0,0,0">
                    <CheckBox 
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Content="Напечатать чек"
                        IsChecked="{Binding ElementName=this,Path=PrintCheque,Mode=TwoWay}"/>
                </StackPanel>


                <Button 
                    Grid.Column="1"
                    Command="{Binding ElementName=this,Path=ReturnCommand}"
                    Style="{StaticResource purple_gradient_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,20,0"
                    Content="Вернуть"
                    Width="170"
                    Height="40"/>

            </Grid>
        </Grid>
    </Border>
</abstactions:BaseWindow>
