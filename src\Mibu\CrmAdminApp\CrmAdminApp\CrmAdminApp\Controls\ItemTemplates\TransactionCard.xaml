﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.TransactionCard">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame 
          x:Name="frame"
          Padding="0"
          HasShadow="False"
          BackgroundColor="{x:StaticResource bg_purple}"
          CornerRadius="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="37"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="150"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">

                    <Ellipse
                        x:Name="dutyOpeningSign"
                        IsVisible="False"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        Margin="0,10,10,0"
                        WidthRequest="8"
                        HeightRequest="8"
                        Fill="#26E27C"/>

                    <Image
                        x:Name="incassationSign"
                        IsVisible="False"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        Margin="0,10,10,0"
                        WidthRequest="14"
                        HeightRequest="18"
                        Source="incassation_sign.png"/>


                    <Ellipse
                        x:Name="dutyClosingSign"
                        IsVisible="False"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        Margin="0,10,10,0"
                        WidthRequest="8"
                        HeightRequest="8"
                        Fill="#7265FB"/>


                </Grid>


                <Grid Grid.Column="1">
                    <StackLayout 
                        Margin="0,10,0,0"
                        Spacing="0">
                        <Label 
                             x:Name="operationLabel"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="12"
                             TextColor="{x:StaticResource dark_purple}"
                             Text=""/>
                        <Label 
                             x:Name="transactionSumLabel"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="20"
                             FontAttributes="Bold"
                             TextColor="{x:StaticResource green}"
                             Text=""/>
                    </StackLayout>
                </Grid>

                <Grid Grid.Column="2">
                    <StackLayout 
                        Margin="0,10,10,0"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        Spacing="6"
                        Orientation="Horizontal">
                        <Image 
                            VerticalOptions="Center"
                            WidthRequest="16"
                            HeightRequest="16"
                            Source="clock_gray.png"/>
                        <Label 
                             x:Name="transactionTimeLabel"
                             VerticalOptions="Center"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="12"
                             TextColor="{x:StaticResource text_gray}"
                             Text=""/>
                    </StackLayout>
                </Grid>

            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>