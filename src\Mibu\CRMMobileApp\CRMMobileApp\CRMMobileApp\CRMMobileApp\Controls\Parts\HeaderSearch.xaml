﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             HeightRequest="60"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Parts.HeaderSearch">
    <ContentView.Content>
        <Grid 
            BackgroundColor="{StaticResource bg_purple}"
            Grid.Row="0">

            <StackLayout 
                Spacing="0"
                VerticalOptions="Center"
                Orientation="Horizontal">

                <Grid 
                     VerticalOptions="Fill"
                     HorizontalOptions="Start"
                     BackgroundColor="Transparent"
                     WidthRequest="50">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                    </Grid.GestureRecognizers>
                    <ImageButton 
                         InputTransparent="True"
                         Command="{Binding Source={x:Reference this},Path=GoBack}"
                         Margin="30,0,0,0"
                         VerticalOptions="Center"
                         WidthRequest="10"
                         HeightRequest="20"
                         BackgroundColor="Transparent"
                         CornerRadius="0"
                         Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"/>
                </Grid>

                <Image
                    Margin="20,0,0,0"
                    VerticalOptions="Center"
                    Aspect="Fill"
                    Source="{OnPlatform Default=logo.png, WPF='pack://application:,,,/Images/logo.png'}"
                    HeightRequest="50"
                    WidthRequest="60"/>

            </StackLayout>

            <Label
                VerticalOptions="Center"
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="20"
                Text="{Binding Source={x:Reference this},Path=Title}"/>


            <Grid 
                IsVisible="False"
                VerticalOptions="Center"
                Margin="0,0,30,0"
                HorizontalOptions="End">

                <controls:EntryOutlined
                    IsEnabled="False"
                    WidthRequest="220"
                    HeightRequest="30"
                    CornerRadius="10"
                    PlaceholderMargin="40,0,0,0"
                    TextMargin="40,0,0,0"
                    EntryBackground="White"
                    BorderColor="White"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Поиск"/>

                <Image
                    Margin="12,7,7,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Start"
                    WidthRequest="16"
                    HeightRequest="16"
                    Source="{OnPlatform Default=search.png, WPF='pack://application:,,,/Images/search.png'}"/>

            </Grid>

        </Grid>

    </ContentView.Content>
</ContentView>