﻿<UserControl x:Class="CRMDesktopApp.Controls.Modifiers.SomeOptionsModifierView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Modifiers"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>


        <Grid Grid.Row="0">
            <TextBlock 
                Text="{Binding ElementName=this,Path=Model.Modifier.Title}"
                FontSize="20"
                FontWeight="Bold"
                Foreground="{StaticResource dark_purple}"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                Margin="15,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"/>

            <CheckBox
                Visibility="Hidden"
                x:Name="optionalModifierCheckBox"
                Margin="0,0,15,0"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Foreground="{StaticResource dark_purple}"
                IsChecked="{Binding ElementName=this,Path=Model.IsSelected,Mode=TwoWay}"/>

        </Grid>


        <StackPanel 
            Grid.Row="1"
            x:Name="modifiersLayout">



        </StackPanel>

    </Grid>
</UserControl>
