﻿using CRM.Models.Reports.Mobile.ByIngridientSales;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages.Tables
{
    /// <summary>
    /// Логика взаимодействия для SalesByIngridients.xaml
    /// </summary>
    public partial class SalesByIngridients : BasePage
    {
        public SalesByIngridients()
        {
            InitializeComponent();
            Loaded += SalesByIngridients_Loaded;
        }

        private async void SalesByIngridients_Loaded(object sender, RoutedEventArgs e)
        {
            Report = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetIngridientSales(ApplicationState.CurrentDomain,
                                                                                             ApplicationState.CurrentTradeNetwork.Id,
                                                                                             ApplicationState.CurrentStore.Id,
                                                                                             ApplicationState.CurrentDuty.Id);
            if (!Report.Ingridients.Any())
            {
                noItemsTextBlock.Visibility = Visibility.Visible;
            }
        }


        private ByIngridientSalesReport report;
        public ByIngridientSalesReport Report
        {
            get => report;
            set { report = value; OnPropertyChanged(nameof(Report)); }
        }
    }
}
