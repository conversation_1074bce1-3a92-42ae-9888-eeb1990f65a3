﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.SaleListItem">
    <ContentView.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Grid 
            Margin="0,20,0,0"
            HorizontalOptions="Fill"
            HeightRequest="30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="4*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <Label 
                 Grid.Column="0"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 LineBreakMode="TailTruncation"
                 Text="{Binding Source={x:Reference this},Path=Model.Title}"/>


            <Label 
                 Grid.Column="1"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 Margin="10,0,0,0"
                 Text="{Binding Source={x:Reference this},Path=Model.Price}"/>

            <Label 
                 Grid.Column="2"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 Margin="20,0,0,0"
                 Text="{Binding Source={x:Reference this},Path=Model.Amount}"/>

            <Label 
                 Grid.Column="3"
                 FontFamily="TTFirsNeue-Regular"
                 FontSize="14"
                 TextColor="{x:StaticResource text_gray}"
                 VerticalOptions="Center"
                 Margin="13,0,0,0"
                 Text="{Binding Source={x:Reference this},Path=Model.Sum}"/>


        </Grid>
    </ContentView.Content>
</ContentView>