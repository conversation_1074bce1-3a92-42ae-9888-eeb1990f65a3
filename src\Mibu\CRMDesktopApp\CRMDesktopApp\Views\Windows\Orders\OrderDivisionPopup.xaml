﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.OrderDivisionPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Разделение заказа" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="760"
        Height="360">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Background="#FFFFFF"
        Padding="0"
        Width="750"
        Height="350"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="70"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">

                    <customcontrols:ImageButton
                        Command="{Binding ElementName=this,Path=CloseWindow}"
                        Source="pack://application:,,,/Resources/Images/arrowBack.png"
                        Margin="10,0,0,0"
                        Background="Transparent"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Width="6"
                        Height="12"/>


                    <TextBlock
                        Margin="30,0,0,0"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Foreground="{StaticResource dark_purple}"
                        Text="Разделение заказа"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"/>

                    <TextBlock
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Right"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14">
                          <Run Text="Заказ №"/>
                          <Run x:Name="orderNumberSpan" Text=""/>
                    </TextBlock>
                </Grid>


                <Grid Grid.Column="1">
                    <TextBlock
                        Margin="10,0,0,0"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Foreground="{StaticResource dark_purple}"
                        Text="Новый заказ"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"/>


                </Grid>


            </Grid>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <ScrollViewer
                        VerticalScrollBarVisibility="Hidden">
                        <StackPanel 
                            Margin="10,5,10,30"
                            x:Name="orderItemsStackLayout">



                        </StackPanel>
                    </ScrollViewer>

                    <TextBlock
                        Margin="0,0,10,0"
                        VerticalAlignment="Bottom"
                        HorizontalAlignment="Right"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16">
                       <Run Text="Всего "/>
                       <Run Text="{Binding ElementName=this,Path=OrderItemsTotal}"/>
                       <Run Text=" р"/>
                    </TextBlock>
                </Grid>

                <Rectangle
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Right"
                    Fill="{StaticResource dark_purple}"
                    Width="1"/>

                <Grid Grid.Column="1">


                    <ScrollViewer
                        VerticalScrollBarVisibility="Hidden">
                        <StackPanel 
                            Margin="10,5,10,30"
                            x:Name="newOrderItemsStackLayout">



                        </StackPanel>
                    </ScrollViewer>

                    <TextBlock
                        Margin="0,0,10,0"
                        VerticalAlignment="Bottom"
                        HorizontalAlignment="Right"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16">
                        <Run Text="Всего "/>
                        <Run Text="{Binding ElementName=this,Path=NewOrderItemsTotal}"/>
                        <Run Text=" р"/>
                    </TextBlock>

                </Grid>


            </Grid>


            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Button 
                    Grid.Column="0"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Style="{StaticResource bg_purple_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    Margin="20,0,0,0"
                    Content="Отмена"
                    Width="150"
                    Height="40"/>


                <Button 
                    Grid.Column="1"
                    Command="{Binding ElementName=this,Path=DivideOrder}"
                    Style="{StaticResource purple_gradient_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,20,0"
                    Content="Разделить"
                    Width="150"
                    Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
