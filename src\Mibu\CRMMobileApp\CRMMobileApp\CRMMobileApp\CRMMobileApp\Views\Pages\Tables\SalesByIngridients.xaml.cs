﻿using CRM.Models.Reports.Mobile.ByIngridientSales;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Pages.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SalesByIngridients : ContentPage
    {
        public SalesByIngridients()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            Device.InvokeOnMainThreadAsync(async () =>
            {
                Report = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetIngridientSales(ApplicationState.CurrentDomain,
                                                                                                 ApplicationState.CurrentTradeNetwork.Id,
                                                                                                 ApplicationState.CurrentStore.Id,
                                                                                                 ApplicationState.CurrentDuty.Id);
                noItemsLabel.IsVisible = Report.Ingridients.Count == 0;
            });
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }


        private ByIngridientSalesReport report;
        public ByIngridientSalesReport Report
        {
            get => report;
            set { report = value; OnPropertyChanged(nameof(Report)); }
        }
    }
}