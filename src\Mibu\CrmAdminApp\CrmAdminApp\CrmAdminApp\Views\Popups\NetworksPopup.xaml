﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage 
             xmlns:animations="http://rotorgames.com"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:XamForms.Controls;assembly=XamForms.Controls.Calendar"
             x:Name="this"
             xmlns:b="clr-namespace:Corcav.Behaviors;assembly=Corcav.Behaviors" 
             xmlns:effects="http://sharpnado.com" 
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates"
             x:Class="CRMAdminMoblieApp.Views.Popups.NetworksPopup">
    <animations:PopupPage.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </animations:PopupPage.Resources>

    <Frame
        HorizontalOptions="Center"
        VerticalOptions="Start"
        Padding="0"
        BackgroundColor="#FFFFFF"
        WidthRequest="300"
        HeightRequest="300"
        CornerRadius="10"
        Margin="30,135,30,0">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="120"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <effects:CollectionView  
                    ItemHeight="40"
                    Margin="10,5,10,0"
                    TapCommand="{Binding Source={x:Reference this},Path=ItemSelected}"
                    ItemsSource="{Binding Source={x:Reference this},Path=Stores}">
                    <effects:CollectionView.ItemTemplate>
                        <DataTemplate>
                            <ViewCell>
                                <itemtemplates:StoreItem Model="{Binding}"/>
                            </ViewCell>
                        </DataTemplate>
                    </effects:CollectionView.ItemTemplate>
                </effects:CollectionView>
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout
                    VerticalOptions="End"
                    Margin="10,0,10,10">

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=SetStore}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        Text="Выбрать точку"
                        Style="{x:StaticResource green_gradient_btn}"
                        HeightRequest="40"
                        VerticalOptions="Center"
                        HorizontalOptions="Fill"
                        Margin="0,0,0,0"/>

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=ChangeNetwork}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        Text="Сменить сеть"
                        Style="{x:StaticResource purple_gradient_btn}"
                        HeightRequest="40"
                        VerticalOptions="Center"
                        HorizontalOptions="Fill"
                        Margin="0,0,0,0"/>
                </StackLayout>
            </Grid>

        </Grid>
    </Frame>
  
</animations:PopupPage>