<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>MinimumOSVersion</key>
	<string>11.0</string>
	<key>CFBundleDisplayName</key>
	<string>Mibu admin</string>
	<key>CFBundleIdentifier</key>
	<string>com.alfateam.youmiadmin</string>
	<key>CFBundleName</key>
	<string>youmiadmin</string>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/AppIcon.appiconset</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSContactsUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSSiriUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Some message to appease Apple.</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.12</string>
	<key>NSCameraUsageDescription</key>
	<string>Allow device to access camera</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow device to access camera</string>
</dict>
</plist>
