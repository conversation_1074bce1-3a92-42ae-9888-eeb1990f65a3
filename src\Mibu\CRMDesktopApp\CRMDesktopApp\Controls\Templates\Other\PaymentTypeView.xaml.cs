﻿using CRMMobileApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Other
{
    /// <summary>
    /// Логика взаимодействия для PaymentTypeView.xaml
    /// </summary>
    public partial class PaymentTypeView : UserControl
    {
        public PaymentTypeView()
        {
            InitializeComponent();
        }

        public static readonly DependencyProperty ModelProperty =
          DependencyProperty.Register(nameof(Model), typeof(PaymentTypeWrapper), typeof(PaymentTypeView));
        public PaymentTypeWrapper Model
        {
            get { return (PaymentTypeWrapper)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
