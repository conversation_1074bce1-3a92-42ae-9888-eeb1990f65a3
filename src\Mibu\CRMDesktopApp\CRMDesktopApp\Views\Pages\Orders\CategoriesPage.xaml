﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Orders.CategoriesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Orders" 
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts" 
      xmlns:flexboxlayout="clr-namespace:FlexboxLayout" 
      xmlns:controls1="clr-namespace:CRMDesktopApp.CustomControls"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="CategoriesPage">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="350"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <parts:LeftOrderPanel
              Grid.Column="0"/>

        <Grid Grid.Column="1"
                  Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="60"/>
                <RowDefinition Height="8*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <parts:Header 
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Stretch" />
            </Grid>

            <Grid Grid.Row="1">


                <StackPanel
                    Margin="20,0,0,0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <TextBlock
                        Margin="12,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="{StaticResource text_gray}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="18"
                        Text="Все"/>
                    <Image
                        Margin="12,0,0,0"
                        Width="8"
                        Height="16"
                        Stretch="Fill"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Source="pack://application:,,,/Resources/Images/arrowRight.png"/>
                    <TextBlock
                        Margin="12,0,0,0"
                        x:Name="catTitle"
                        VerticalAlignment="Center"
                        Foreground="{StaticResource text_gray}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="18"
                        Text="Добавки"/>
                </StackPanel>

                <Grid 
                    VerticalAlignment="Center"
                    Margin="0,0,30,0"
                    HorizontalAlignment="Right">

                    <controls1:EntryOutlined
                        Text="{Binding ElementName=this,Path=SearchQuery,Mode=TwoWay}"
                        Width="290"
                        Height="45"
                        CornerRadius="10"
                        PlaceholderMargin="40,0,0,0"
                        TextMargin="40,0,0,0"
                        EntryBackground="{StaticResource bg_purple}"
                        BorderColor="{StaticResource bg_purple}"
                        PlaceholderColor="{StaticResource text_gray}"
                        Placeholder="Поиск"/>

                    <Image
                        Margin="12,-2,7,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Width="20"
                        Height="20"
                        Source="pack://application:,,,/Resources/Images/search.png"/>

                </Grid>

            </Grid>


            

            <Grid Grid.Row="2">

                <ScrollViewer 
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    VerticalScrollBarVisibility="Hidden">
                    <WrapPanel x:Name="multiList">

                    </WrapPanel>
                </ScrollViewer>


                <TextBlock
                      x:Name="noItemsLabel"
                      Visibility="Hidden"
                      VerticalAlignment="Center"
                      TextAlignment="Center"
                      HorizontalAlignment="Center"
                      Width="300"
                      Foreground="{StaticResource dark_purple}"
                      FontFamily="TTFirsNeue-Regular"
                      Text="На данный момент здесь нет ничего"
                      FontSize="14" />

            </Grid>
            
        </Grid>
    </Grid>
</abstactions:BasePage>
