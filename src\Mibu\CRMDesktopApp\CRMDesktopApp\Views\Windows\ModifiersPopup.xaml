﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.ModifiersPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Модификаторы" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="670"
        Height="610">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <customcontrols:ClippingBorder
        Width="660"
        Height="605"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid >
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <TextBlock
                    Margin="15,0,0,0"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="20"
                    Text="Модификаторы"/>
            </Grid>

            <Grid Grid.Row="1">
                <ScrollViewer VerticalScrollBarVisibility="Disabled">
                    <StackPanel x:Name="modifiersLayout">

                    </StackPanel>
                </ScrollViewer>
            </Grid>


            <Grid
                ClipToBounds="True"
                Grid.Row="2"
                Background="{StaticResource grayBg}">

                <Button 
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Style="{StaticResource gray_cornered_filled_btn}"
                    Margin="25,0,0,0"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    Content="Отмена"
                    Width="170"
                    Height="40"/>

                <Button 
                    Command="{Binding ElementName=this,Path=ApplyModifiers}"
                    Style="{StaticResource purple_gradient_btn}"
                    Margin="0,0,25,0"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Content="Готово"
                    Width="170"
                    Height="40"/>
            </Grid>

        </Grid>
    </customcontrols:ClippingBorder>
</abstactions:BaseWindow>
