﻿using CRM.Models;
using CRM.Models.Network;
using CrmAdminApp.Helpers;
using CRMAdminMoblieApp.Controls.ItemTemplates;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TradeNetworkStores : ContentPage
    {
        public TradeNetworkStores(TradeNetwork network)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            TradeNetwork = network;

            noItemsLabel.IsVisible = TradeNetwork.Stores.Count == 0;
        }
        protected override async void OnAppearing()
        {
            RenderItems();
        }
        private void RenderItems()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                networksLayout.Children.Clear();
                foreach (var store in TradeNetwork.Stores)
                {
                    var card = new StoreExpandableCard(store)
                    {
                        HeightRequest = 85,
                        Margin = new Thickness(0, 20, 0, 0),
                        HorizontalOptions = LayoutOptions.FillAndExpand,
                    };
                    card.OnSelectBtnTapped += Card_OnSelectBtnTapped;
                    card.OnDeleteBtnTapped += Card_OnDeleteBtnTapped;
                    networksLayout.Children.Add(card);


                }
            });
        }

        private void Card_OnSelectBtnTapped(object sender, Store e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (e is null) return;

                ApplicationState.CurrentStore = e;
                ApplicationState.SaveChangesToMemory();
                await ApplicationState.GetDuty();

                await PagesHelper.BuildDashboardPages();
                await App.Current.MainPage.Navigation.PushAsync(PagesHelper.GetRealTimePage());
            });
		}
		private void Card_OnDeleteBtnTapped(object sender, Store e)
		{
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (e is null) return;

                if (await DisplayAlert("Уведомление", "Вы действительно хотите удалить точку?", "Да", "Нет"))
                {
                    await MobileAPI.AdminMethods.AdminStoresMethods.DeleteStore(ApplicationState.CurrentDomain, e.Id);

                    ApplicationState.CurrentTradeNetwork = await MobileAPI.MainMethods.GetTradeNetwork(ApplicationState.CurrentDomain, TradeNetwork.Id);
                    await App.Current.MainPage.Navigation.PushAsync(new TradeNetworkStores(ApplicationState.CurrentTradeNetwork), false);
                    App.Current.MainPage.Navigation.RemovePage(this);
                }
            });
		}




		private TradeNetwork tradeNetwork;
        public TradeNetwork TradeNetwork
        {
            get => tradeNetwork;
            set { tradeNetwork = value; OnPropertyChanged(nameof(TradeNetwork)); }
        }

        private ICommand goToCreateStore;
        public ICommand GoToCreateStore
        {
            get => goToCreateStore ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new CreateStore());
            });
        }

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
    }
}