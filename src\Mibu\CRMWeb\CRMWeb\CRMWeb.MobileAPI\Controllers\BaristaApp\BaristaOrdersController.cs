﻿using CRM.Database.Core;
using CRM.Models.Network.Finances;
using CRM.Models.Stores;
using CRM.Models.Stores.Orders;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaOrdersController : AbsController
    {
        public BaristaOrdersController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }


        #region Получение заказов
        public static List<Order> GetOrdersStatic(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Orders).ThenInclude(o => o.User)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.Modifier).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.SelectedModifiers).ThenInclude(o => o.SelectedOptions).ThenInclude(o => o.ModifierOption)
                        .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                        .Include(o => o.Orders).ThenInclude(o => o.OrderEnvironment).ThenInclude(o => o.Table)
                        .FirstOrDefault(o => o.Id == storeId);

                    foreach(var order in store.Orders)
                    {
                        if (order.CustomerId != null)
                        {
                            order.Customer = db.Customers.Include(o => o.LoyaltyProgram).ThenInclude(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                                                                                        .Include(o => o.LoyaltyProgram).ThenInclude(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                                                                                        .FirstOrDefault(o => o.Id == order.CustomerId);
                        }


                        if(order.DiscountId != null)
                        {
                            order.Discount = db.Discounts.Include(o => o.MenuCategories)
                                                         .Include(o => o.TechnicalCards)
                                                         .Include(o => o.MenuCategories)
                                                         .Include(o => o.Workshop)
                                                         .FirstOrDefault(o => o.Id == order.DiscountId);
                        }
                        
                        if(order.DeliveryId != null)
                        {
                            order.Delivery = db.Deliveries.Include(o => o.DeliveryMan)
                                                          .Include(o => o.Service)
                                                          .FirstOrDefault(o => o.Id == order.DeliveryId);
                        }



                        foreach(var item in order.Items)
                        {
                            if(item.ProductId != null)
                            {
                                item.Product = db.Products.Include(o => o.MenuCategory).ThenInclude(o => o.Fiscalization).ThenInclude(o => o.Taxation)
                                                          .Include(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                                                          .FirstOrDefault(o => o.Id == item.ProductId);
                            }
                            else if(item.TechnicalCardId != null)
                            {
                                item.TechnicalCard = db.TechnicalCards.Include(o => o.MenuCategory).ThenInclude(o => o.Fiscalization).ThenInclude(o => o.Taxation)
                                                                      .Include(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                                                                      .FirstOrDefault(o => o.Id == item.TechnicalCardId);
                            }
                        }


                        order.Items = order.Items.Where(o => !o.IsDeleted).ToList();
                        order.OrderPayments = order.OrderPayments.Where(o => !o.IsDeleted).ToList();

                        order.User.Store = null;
					}

                    return store.Orders;
                }
                catch { return new List<Order>(); }
            }
        }


        [HttpGet, Route("GetOrderById")]
        public Order GetOrderById(string domain, int storeId, int orderId)
        {
            var order = GetOrdersStatic(domain, storeId).FirstOrDefault(o => o.Id == orderId);

            order.Items = order.Items.Where(o => !o.IsDeleted).ToList();
            order.OrderPayments = order.OrderPayments.Where(o => !o.IsDeleted).ToList();

            return order;
        }

        [HttpGet, Route("GetOrders")]
        public List<Order> GetOrders(string domain, int storeId)
        {
            return GetOrdersStatic(domain, storeId);
        }
        [HttpGet, Route("GetOrdersByDuty")]
        public List<Order> GetOrdersByDuty(string domain, int storeId, int dutyId)
        {
            var orders = GetOrdersStatic(domain, storeId)
                                    .Where(o => o.DutyId == dutyId);
            return orders.ToList();
        }

        [HttpGet, Route("GetActiveOrders")]
        public List<Order> GetActiveOrders(string domain, int storeId, int dutyId)
        {
            var orders = GetOrdersStatic(domain, storeId)
                             .Where(o => o.DutyId == dutyId
                                    && o.ClosedAt == null
                                    && !o.IsCanceled);
            return orders.ToList();
        }

        [HttpGet, Route("GetOrdersWithDelivery")]
        public List<Order> GetOrdersWithDelivery(string domain, int storeId,DateTime date,int deliveryServiceId,int deliveryManId)
        {
            IEnumerable<Order> orders = GetOrdersStatic(domain, storeId);
            orders = orders.Where(o => o.OpenedAt.Date == date.Date
                                         && o.DeliveryId != null
                                         && o.ClosedAt == null
                                         && !o.IsCanceled);

            if (deliveryServiceId != 0)
            {
                orders = orders.Where(o => o.Delivery.ServiceId == deliveryServiceId);
            }
            if (deliveryManId != 0)
            {
                orders = orders.Where(o => o.Delivery.DeliveryManId == deliveryManId);
            }
            return orders.ToList();
        }



        [HttpGet, Route("GetLastOrder")]
        public Order GetLastOrder(string domain, int storeId)
        {
            IEnumerable<Order> orders = GetOrdersStatic(domain, storeId).Where(o => o.IsActive);
            var maxId = orders.Max(o => o.Id);

            return orders.FirstOrDefault(o => o.Id == maxId);
        }
        #endregion


        #region Управление заказами
        [HttpPost, Route("CreateOrder")]
        public async Task<Order> CreateOrder(string domain, int storeId, [FromBody]Order order)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Orders)
                        .FirstOrDefault(o => o.Id == storeId);

                    int number = 1;
                    if (store.Orders.Any())
                        number = store.Orders.Max(o => o.OrderNumber) + 1;

                    order.OrderNumber = number;
                    order.OpenedAt = DateTime.UtcNow;

                    store.Orders.Add(order);
                    db.Stores.Update(store);

                    await db.SaveChangesAsync();

                    return order;
                }
                catch (Exception ex) { return new Order() { Comment = ex.Message + ex.StackTrace }; }
            }
        }
        
        [HttpGet, Route("OpenOrder")]
        public async Task<Order> OpenOrder(string domain,int storeId, int dutyId,int waiterId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Orders)
                        .FirstOrDefault(o => o.Id == storeId);

                    int number = 1;
                    if (store.Orders.Any())
                        number = store.Orders.Max(o => o.OrderNumber) + 1;

                    var order = new Order();
                    order.UserId = waiterId;
                    order.DutyId = dutyId;
                    order.OrderNumber = number;
                    order.OpenedAt = DateTime.UtcNow;

                    store.Orders.Add(order);
                    db.Stores.Update(store);
                  
                    await db.SaveChangesAsync();

                    return order;
                }
                catch { return null; }
            }
        }

        [HttpGet, Route("OpenTableOrder")]
        public async Task<Order> OpenTableOrder(string domain, int storeId, int dutyId, int tableId, int guestsCount, int waiterId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Orders)
                        .FirstOrDefault(o => o.Id == storeId);

                    int number = 1;
                    if (store.Orders.Any())
                        number = store.Orders.Max(o => o.OrderNumber) + 1;

                    var order = new Order();
                    order.UserId = waiterId;
                    order.DutyId = dutyId;
                    order.OrderNumber = number;
                    order.OpenedAt = DateTime.UtcNow;
                    order.OrderEnvironment = new OrderEnvironment
                    {
                        GuestsCount = guestsCount,
                        TableId = tableId,
                    };

                    store.Orders.Add(order);
                    db.Stores.Update(store);

                    await db.SaveChangesAsync();


                    
                    order.User = db.NetworkTerminalUsers.FirstOrDefault(o => o.Id == waiterId);
                    order.User.Store = null; //чтобы не было рекурсии при сериализации

                    return order;
                }
                catch(Exception ex) { return new Order { Comment = ex.ToString()}; }
            }
        }




        [HttpPost, Route("UpdateOrder")]
        public async Task<Order> UpdateOrder(string domain, int storeId, Order order)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var orderFromDB = db.Orders
                        .Include(o => o.Items)
                        .Include(o => o.OrderPayments)
                        .AsNoTracking()
                        .FirstOrDefault(o => o.Id == order.Id);

                    for(int i= orderFromDB.Items.Count-1;i>-1;i--)
                    {
                        var item = orderFromDB.Items[i];
                        if (!order.Items.Any(o => o.Id == item.Id))
                        {
                            item.IsDeleted = true;              
                            db.OrderItems.Update(item);
                        }
                        //item = null;
                    }
                    for (int i = orderFromDB.OrderPayments.Count - 1; i > -1; i--)
                    {
                        var item = orderFromDB.OrderPayments[i];
                        if (!order.OrderPayments.Any(o => o.Id == item.Id))
                        {
                            item.IsDeleted = true;
                            db.OrderPayment.Update(item);
                        }
                       // item = null;
                    }


                    orderFromDB = null;
                    await db.SaveChangesAsync();
                

                    db.Orders.Update(order);

                    await db.SaveChangesAsync();
                    return GetOrderById(domain, storeId, order.Id);
                }
                catch (Exception ex) { return new Order() { Comment = ex.Message + ex.StackTrace }; }
            }
        }

        [HttpPost, Route("CloseOrder")]
        public async Task<Order> CloseOrder(string domain, int networkId, int storeId, Order order)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    order.ClosedAt = DateTime.UtcNow;

                    var network = db.TradeNetworks
                        .Include(o => o.CriticalBalanceNotificationEntries).ThenInclude(o => o.NotifiedIngridient)
                        .Include(o => o.CriticalBalanceNotificationEntries).ThenInclude(o => o.NotifiedProduct)
                        .Include(o => o.CriticalBalanceNotificationEntries).ThenInclude(o => o.Store)
                        .Include(o => o.NetworkNotifications)
                        .FirstOrDefault(o => o.Id == networkId);

                    var store = db.Stores
                        .Include(o => o.Stock).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                        .Include(o => o.Stock).ThenInclude(o => o.Products).ThenInclude(o => o.Product)
                        .FirstOrDefault(o => o.Id == storeId);

                    foreach(var orderItem in order.Items)
                    {
                        if(orderItem.ProductId != null)
                        {
                            var product = db.Products.FirstOrDefault(o => o.Id == orderItem.ProductId);
                            orderItem.Product = product;


                            store.Stock.AddToStock(product, -orderItem.Amount, false, order.DutyId);
                        }
                        else if(orderItem.TechnicalCardId != null)
                        {
                            var technicalCard = db.TechnicalCards.Include(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                                                                 .Include(o => o.Products).ThenInclude(o => o.Product)
                                                                 .Include(o => o.Subproducts).ThenInclude(o => o.Subproduct).ThenInclude(o => o.Subproducts)
                                                                 .Include(o => o.Subproducts).ThenInclude(o => o.Subproduct).ThenInclude(o => o.Ingridients)
                                                                 .FirstOrDefault(o => o.Id == orderItem.TechnicalCardId);
                            orderItem.TechnicalCard = technicalCard;

                            store.Stock.AddToStock(technicalCard, -orderItem.Amount, false, order.DutyId);
                        }

                        foreach(var option in orderItem.SelectedModifiers.SelectMany(o => o.SelectedOptions))
                        {
                            var modifierOption = db.ModifierOptions.Include(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                                                                   .FirstOrDefault(o => o.Id == option.ModifierOptionId);


                            double orderItemCount = orderItem.Amount;
                            if (orderItem.IsWeightItem)
                            {
                                orderItemCount = 1;
                            }

                            store.Stock.AddToStock(modifierOption, -option.Amount * orderItemCount, false, order.DutyId);
                        }


                        orderItem.Product = null;
                        orderItem.TechnicalCard = null;
                    }

                    
                    store.Orders.Add(order);


                    var criticalProducts = store.Stock.GetCriticalBalancedProducts();
                    var criticalIngridients = store.Stock.GetCriticalBalancedIngridients();
                    network.AddNewCriticalBalanceNotifications(criticalProducts, store);
                    network.AddNewCriticalBalanceNotifications(criticalIngridients, store);
                    db.TradeNetworks.Update(network);





                    db.Stores.Update(store);
                    await db.SaveChangesAsync();


                    return GetOrderById(domain, storeId, order.Id);
                }
                catch(Exception ex) { return new Order() { Comment = ex.Message + ex.StackTrace}; }
            }
        }
      
        [HttpPost, Route("CancelOrder")]
        public async Task<Order> CancelOrder(string domain, int orderId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var order = db.Orders
                        .Include(o => o.Items)
                        .FirstOrDefault(o => o.Id == orderId);
                    order.IsCanceled = true;
                    order.ClosedAt = DateTime.UtcNow;

                   // db.OrderItems.RemoveRange(order.Items);

                    db.Orders.Update(order);
                    await db.SaveChangesAsync();

                    return order;
                }
                catch { return null; }
            }
        }
     
        [HttpPost, Route("ReturnOrder")]
        public async Task<Order> ReturnOrder(string domain, int storeId, int orderId, string reason)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores.Include(o => o.Stock).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                                         .Include(o => o.Stock).ThenInclude(o => o.Products).ThenInclude(o => o.Product)
                                         .FirstOrDefault(o => o.Id == storeId);


                    var order = GetOrderById(domain, storeId, orderId);

                    //Возвращаем, но только продукты. Мы не можем вернуть техкарту и ее составные элементы, т.к. не сможем это же блюдо дать другому клиенту
                    foreach(var item in order.Items.Where(o => o.Product != null))
                    {
                        store.Stock.AddToStock(item.Product, -item.Amount, false);
                    }

                    order.WasReturned = true;
                    order.ReturnDate = DateTime.UtcNow;
                    order.ReturnReason = reason;

                    var client = order.Customer;
                    if (client == null && order.CustomerId != null)
                    {
                        client = db.Customers.FirstOrDefault(o => o.Id == order.CustomerId);

                        client.TotalSpent -= order.PaidByMoney;
                        client.Bonuses -= order.PaidByBonuses;

                        db.Customers.Update(client);
                        db.SaveChanges();
                    }

                    db.Stores.Update(store);

                    db.Orders.Update(order);
                    await db.SaveChangesAsync();

                    return order;
                }
                catch { return null; }
            }
        }



        [HttpPost, Route("UpdateOrderItem")]
        public async Task<OrderItem> UpdateOrderItem(string domain, OrderItem item)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {

                    db.OrderItems.Update(item);
                    await db.SaveChangesAsync();

                    return item;
                }
                catch (Exception ex) { return new OrderItem() { Comment = ex.Message + ex.StackTrace }; }
            }
        }
        [HttpPut, Route("SetOrderItemModifierts")]
        public async Task<OrderItem> SetOrderItemModifierts(string domain, int orderItemId,double total,List<OrderItemModifier> modifiers)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var item = db.OrderItems
                        .Include(o => o.SelectedModifiers)
                        .FirstOrDefault(o => o.Id == orderItemId);

                    item.SelectedModifiers.Clear();
                    db.OrderItems.Update(item);


                    item.SelectedModifiers.AddRange(modifiers);

                    item.Price = total;

                    await db.SaveChangesAsync();
                    return item;
                }
                catch (Exception ex) { return new OrderItem() { Comment = ex.Message + ex.StackTrace }; }
            }
        }

        #endregion
    }
}
