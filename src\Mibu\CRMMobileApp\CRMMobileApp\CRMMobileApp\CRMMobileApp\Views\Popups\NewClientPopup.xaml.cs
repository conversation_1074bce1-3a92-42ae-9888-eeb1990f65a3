﻿using CRM.Models.Enums.Info;
using CRM.Models.Network.LoyaltyProgram;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NewClientPopup : PopupPage
    {
        public NewClientPopup()
        {
            GenderStrings = new List<string> { "Мужской", "Женский" };
            SelectedGenderString = GenderStrings.FirstOrDefault();

            InitializeComponent();
        }



        private Customer newClient = new Customer();
        public Customer NewClient
        {
            get => newClient;
            set { newClient = value; OnPropertyChanged(nameof(NewClient)); }
        }



        private string selectedGenderString;
        public string SelectedGenderString
        {
            get => selectedGenderString;
            set { selectedGenderString = value; OnPropertyChanged(nameof(SelectedGenderString)); }
        }

        private List<string> genderStrings = new List<string>();
        public List<string> GenderStrings
        {
            get => genderStrings;
            set { genderStrings = value; OnPropertyChanged(nameof(GenderStrings)); }
        }



        public event EventHandler<Customer> OnClientCreated;
        #region Команды

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand createClient;
        public ICommand CreateClient
        {
            get => createClient ??= new RelayCommand(async obj =>
            {
                NewClient.BirthDate = (DateTime)dateEdit.Date;
                if (SelectedGenderString == "Мужской")
                {
                    NewClient.Gender = Gender.Male;
                }
                else if (SelectedGenderString == "Женский")
                {
                    NewClient.Gender = Gender.Female;
                }

                var client = await MobileAPI.BaristaMethods.BaristaClientsMethods.CreateClient(ApplicationState.CurrentDomain,ApplicationState.CurrentTradeNetwork.Id,NewClient);

                await Device.InvokeOnMainThreadAsync(() =>
                {
                    OnClientCreated?.Invoke(this, client);
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
        #endregion
    }
}