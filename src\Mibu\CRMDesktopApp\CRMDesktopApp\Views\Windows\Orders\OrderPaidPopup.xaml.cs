﻿using CRMDesktopApp.Abstactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для OrderPaidPopup.xaml
    /// </summary>
    public partial class OrderPaidPopup : BaseWindow
    {
        public OrderPaidPopup(double sum, double change)
        {
            InitializeComponent();
            sumLabel.Text = $"{Math.Round(sum, 2)} р";
            if (change == 0)
            {
                changeLabel.Text = "Без сдачи";
            }
            else
            {
                changeLabel.Text = $"Сдача {Math.Round(change, 2)} р";
            }

        }
    }
}
