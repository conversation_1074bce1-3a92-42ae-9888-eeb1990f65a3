﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для AdminPasswordPopup.xaml
    /// </summary>
    public partial class AdminPasswordPopup : BaseWindow
    {
        public bool IsCompleted { get; set; }
        public bool IsRightPassword { get; set; }
        public AdminPasswordPopup()
        {
            InitializeComponent();


        }
        private string password = string.Empty;
        public string Password
        {
            get => password;
            set { password = value; OnPropertyChanged(nameof(Password)); }
        }

        private ICommand checkPassword;
        public ICommand CheckPassword
        {
            get => checkPassword ??= new RelayCommand(async obj =>
            {
                var settings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
                IsRightPassword = settings.AdminPassword == Password;
                IsCompleted = true;
            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                this.Close();
                IsCompleted = true;
            });
        }
    }
}
