﻿using CRM.Models.Network;
using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using Tech = CRM.Models.Network.ReferenceBooks.Menu.TechnicalCard;
using TechnicalCard = CRMMobileApp.Controls.Templates.TechnicalCard;

namespace CRMMobileApp.Views.Pages.Orders
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ProductsPage : OrdersPage
    {
        MenuCategory _category;
        public ProductsPage(MenuCategory category)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            _category = category;

            categoryLabel.Text = category.Title;
            Load();
        }
        async void Load()
        {
            base.OnAppearing();

            var goods = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetProducts(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            allProducts = new ObservableCollection<Product>(goods.Where(o => o.MenuCategoryId == _category.Id));

            var techCards = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetTechnicalCards(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            allTechnicalCards = new ObservableCollection<Tech>(techCards.Where(o => o.MenuCategoryId == _category.Id));

            CreateControls();
        }

        private ObservableCollection<Product> allProducts = new ObservableCollection<Product>();
        private ObservableCollection<Tech> allTechnicalCards = new ObservableCollection<Tech>();

        private List<View> allControls = new List<View>();
        private void CreateControls()
        {
            multiList.Children.Clear();
            allControls.Clear();
            foreach (var product in allProducts)
            {
                var control = new ProductCard(product)
                {
                    Margin = new Thickness(30,30,0,0),
                    WidthRequest = 200,
                    HeightRequest = 200
                };
                control.CardTapped += ProductCardTapped;
                multiList.Children.Add(control);
                allControls.Add(control);
            }
            foreach (var product in allTechnicalCards)
            {
                var control = new TechnicalCard(product)
                {
                    Margin = new Thickness(30, 30, 0, 0),
                    WidthRequest = 200,
                    HeightRequest = 200
                };
                control.CardTapped += TechCardTapped;
                multiList.Children.Add(control);
                allControls.Add(control);
            }
        }

        private async void TechCardTapped(object sender, Tech e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                return;
            }
            var availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForTechCard(ApplicationState.CurrentDomain,
                                                                                                                 ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                 e.Id);
            var orderItem = new OrderItem()
            {
                TechnicalCard = e,
                TechnicalCardId = e.Id
            };
            if (availableModifiers.Any())
            {
                var popup = new ModifiersPopup(orderItem, availableModifiers, false);
                await App.Current.MainPage.Navigation.PushPopupAsync(popup);

                while (!popup.IsCompleted)
                    await Task.Delay(200);

                if (!popup.IsSuccessfully) return;
            }


            if (e.IsWeightProduct)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new WeightTechCardOptionsPopup(orderItem));
            }
            else
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new TechCardOptionsPopup(orderItem));
            }
        }

        private async void ProductCardTapped(object sender, Product e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление"));
                return;
            }
            var availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForProduct(ApplicationState.CurrentDomain,
                                                                                                                  ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                  e.Id);
            var orderItem = new OrderItem()
            {
                Product = e,
                ProductId = e.Id
            };

            if (availableModifiers.Any())
            {
                var popup = new ModifiersPopup(orderItem, availableModifiers, false);
                await App.Current.MainPage.Navigation.PushPopupAsync(popup);

                while (!popup.IsCompleted)
                    await Task.Delay(200);

                if (!popup.IsSuccessfully) return;
            }

            if (e.IsWeightProduct)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new WeightProductOptionsPopup(orderItem));
            }
            else
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new ProductOptionsPopup(orderItem));
            }
        }







        private string searchQuery;
        public string SearchQuery
        {
            get => searchQuery;
            set
            {
                var filteredControls = new List<View>();
                multiList.Children.Clear();
                if (string.IsNullOrEmpty(value))
                {
                    filteredControls = new List<View>(allControls);
                }
                else
                {
                    var productControls = filteredControls.Where(o => o is ProductCard).Cast<ProductCard>().Where(o => o.Model.Title.Contains(value));
                    var techCardControls = filteredControls.Where(o => o is TechnicalCard).Cast<TechnicalCard>().Where(o => o.Model.Title.Contains(value));

                    filteredControls.AddRange(productControls);
                    filteredControls.AddRange(techCardControls);
                }

                foreach(var view in filteredControls)
                {
                    multiList.Children.Add(view);
                }

                searchQuery = value;
                OnPropertyChanged(nameof(SearchQuery));
            }
        }

    }
}