﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CRMMobileApp.Controls;assembly=CRMMobileApp" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts"
             xmlns:effects="http://sharpnado.com"
             xmlns:other="clr-namespace:CRMMobileApp.Controls.Templates.Other"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Orders.CreateOrder">
    <ContentPage.Content>
        <Grid BackgroundColor="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="130"/>
            </Grid.RowDefinitions>


            <parts:Header Grid.Row="0"/>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="6*"/>
                    <ColumnDefinition Width="4*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">

                    <StackLayout 
                        VerticalOptions="Center"
                        HorizontalOptions="Center">

                        <Label
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text="Оплата"/>


                        <StackLayout
                            Orientation="Horizontal">

                            <Label
                                VerticalOptions="Center"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14">
                                <Label.FormattedText>
                                    <FormattedString>
                                        <FormattedString.Spans>
                                            <Span 
                                                TextColor="{StaticResource text_gray}"
                                                Text="Заказ "/>
                                            <Span 
                                                TextColor="{StaticResource purple}"
                                                Text="№"/>
                                            <Span 
                                                x:Name="orderNumberSpan"
                                                TextColor="{StaticResource dark_purple}"
                                                Text="1234"/>
                                        </FormattedString.Spans>
                                    </FormattedString>
                                </Label.FormattedText>
                            </Label>

                            <Frame 
                                HasShadow="False"
                                Margin="24,0,0,0"
                                Padding="0"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                CornerRadius="10"
                                HeightRequest="35"
                                WidthRequest="180"
                                BackgroundColor="{StaticResource bg_purple}">
                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=CancelOrder}"/>
                                </Frame.GestureRecognizers>
                                <Grid>
                                    <StackLayout 
                                        Spacing="0"
                                        Orientation="Horizontal">
                                        <Image 
                                            Margin="23,0,0,0"
                                            HeightRequest="14"
                                            WidthRequest="14"
                                            Source="{OnPlatform Default=close_duty.png, WPF='pack://application:,,,/Images/close_duty.png'}"/>
                                        <Label
                                            Margin="13,0,0,0"
                                            FontSize="14"
                                            TextColor="{StaticResource dark_purple}"
                                            Text="Отменить заказ"
                                            FontFamily="TTFirsNeue-Regular"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"/>
                                    </StackLayout>

                                </Grid>
                            </Frame>

                        </StackLayout>

                        <StackLayout
                            Spacing="10">
                            <Label
                                x:Name="prepaymentSumLabel"
                                TextColor="{StaticResource text_gray}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"      
                                Text=""/>
                            <Label
                                x:Name="totalSumLabel"
                                TextColor="{StaticResource text_gray}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"      
                                Text="Итого: 210.00₽"/>
                            <Label
                                x:Name="clientLabel"
                                TextColor="{StaticResource text_gray}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                Text="Клиент: Гость"/>
                            <Label
                                x:Name="clientBonusesLabel"
                                IsVisible="False"
                                TextColor="{StaticResource text_gray}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14">
                                <Label.FormattedText>
                                    <FormattedString>
                                        <FormattedString.Spans>
                                            <Span Text="Бонусов: "/>
                                            <Span x:Name="clientBonusesCountSpan"
                                                  TextColor="{StaticResource purple}"
                                                  Text="666"/>
                                        </FormattedString.Spans>
                                    </FormattedString>
                                </Label.FormattedText>
                            </Label>
                            <Label
                                x:Name="discountLabel"
                                TextColor="{StaticResource text_gray}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                Text="Скидка: 0%"/>
                        </StackLayout>

                        <listviews:CustomCrossHorizontalListView
                            x:Name="listView"
                            Margin="0,20,0,0"
                            ItemSpacing="10"
                            ItemWidth="160"
                            ItemHeight="160"
                            TapCommand="{Binding Source={x:Reference this},Path=OnSelectedPaymentType}"
                            WidthRequest="580"
                            HeightRequest="220">
                            <listviews:CustomCrossHorizontalListView.ItemTemplateForMobile>
                                <DataTemplate>
                                    <ViewCell>
                                        <other:PaymentTypeView Model="{Binding}"/>
                                    </ViewCell>
                                </DataTemplate>
                            </listviews:CustomCrossHorizontalListView.ItemTemplateForMobile>
                            <listviews:CustomCrossHorizontalListView.ItemTemplateForWPF>
                                <DataTemplate>
                                    <ViewCell>
                                        <other:PaymentTypeView Model="{Binding}"/>
                                    </ViewCell>
                                </DataTemplate>
                            </listviews:CustomCrossHorizontalListView.ItemTemplateForWPF>
                        </listviews:CustomCrossHorizontalListView>

                        <Label
                            x:Name="toPayLabel"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text="К оплате: 210.00₽"/>
                        <Label
                            x:Name="changeLabel"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20"
                            Text="Сдача: 210.00₽"/>
                        <Label
                            x:Name="bonusesToAddLabel"
                            IsVisible="False"
                            TextColor="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="20">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span Text="Бонусы: "/>
                                        <Span TextColor="{StaticResource text_green}"
                                              Text="+"/>
                                        <Span x:Name="bonusesToAddCountSpan"
                                              TextColor="{StaticResource text_green}"
                                              Text="666"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>

                    </StackLayout>
                  
                </Grid>

                <Grid Grid.Column="1">

                    <Frame 
                        HasShadow="False"
                        CornerRadius="10"
                        BackgroundColor="{StaticResource bg_purple}"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        WidthRequest="330"
                        HeightRequest="420">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="40"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">

                                <!--<Grid Margin="30,0,30,0">

                                    <Label
                                        x:Name="keyboardText"
                                        VerticalOptions="Start"
                                        HorizontalOptions="Start"
                                        TextColor="{StaticResource dark_purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="26"
                                        Text="210"/>

                                    <Label
                                        VerticalOptions="Start"
                                        HorizontalOptions="End"
                                        TextColor="{StaticResource purple}"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="26"
                                        Text="₽"/>

                                    <BoxView 
                                        BackgroundColor="{StaticResource light_grey}"
                                        HeightRequest="1"
                                        VerticalOptions="End"
                                        HorizontalOptions="FillAndExpand" />
                                </Grid>-->
                                
                                
                            </Grid>

                            <Grid Grid.Row="0">

                                <controls:AuthKeyboard
                                    x:Name="keyboard"
                                    WidthRequest="300"
                                    HeightRequest="300"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center"/>


                            </Grid>

                            <Grid Grid.Row="1">

                                <FlexLayout
                                    Direction="Row"
                                    AlignItems="Center"
                                    JustifyContent="SpaceAround"
                                    HorizontalOptions="FillAndExpand"
                                    AlignContent="SpaceAround">
                                    <Button 
                                         CornerRadius="10"
                                         Command="{Binding Source={x:Reference this},Path=AddSumBtnPressed}"
                                         CommandParameter="100"
                                         TextColor="{StaticResource dark_purple}"
                                         BackgroundColor="White"
                                         Text="100₽"
                                         Padding="0"
                                         FontSize="12"
                                         TextTransform="None"
                                         WidthRequest="70"
                                         HeightRequest="40"/>
                                    <Button 
                                         CornerRadius="10"
                                         Command="{Binding Source={x:Reference this},Path=AddSumBtnPressed}"
                                         CommandParameter="500"
                                         TextColor="{StaticResource dark_purple}"
                                         BackgroundColor="White"
                                         Text="500₽"
                                         Padding="0"
                                         FontSize="12"
                                         TextTransform="None"
                                         WidthRequest="70"
                                         HeightRequest="40"/>
                                    <Button 
                                         CornerRadius="10"
                                         Command="{Binding Source={x:Reference this},Path=AddSumBtnPressed}"
                                         CommandParameter="1000"
                                         TextColor="{StaticResource dark_purple}"
                                         BackgroundColor="White"
                                         Text="1000₽"
                                         Padding="0"
                                         FontSize="12"
                                         TextTransform="None"
                                         WidthRequest="70"
                                         HeightRequest="40"/>
                                </FlexLayout>
                                
                            </Grid>

                        </Grid>
                    </Frame>
                    
                    
                </Grid>


            </Grid>

            <Grid Grid.Row="2">
                <Button 
                     Command="{Binding Source={x:Reference this},Path=PayOrder}"
                     CornerRadius="30"
                     VerticalOptions="Start"
                     HorizontalOptions="Center"
                     TextColor="White"
                     Text="Оплатить"
                     FontSize="20"
                     TextTransform="None"
                     Background="{StaticResource purple_gradient}"
                     WidthRequest="240"
                     HeightRequest="60"/>

            </Grid>


        </Grid>
    </ContentPage.Content>
</ContentPage>