﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7833A7C7-D40E-4D64-8E17-C1059918DFE9}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{c9e5eea5-ca05-42a1-839b-61506e0a37df}</TemplateGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>CrmAdminApp.Droid</RootNamespace>
    <AssemblyName>CrmAdminApp.Android</AssemblyName>
    <Deterministic>True</Deterministic>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <TargetFrameworkVersion>v13.0</TargetFrameworkVersion>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    <BundleAssemblies>false</BundleAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    <BundleAssemblies>false</BundleAssemblies>
    <AndroidPackageFormat>aab</AndroidPackageFormat>
    <AndroidUseAapt2>true</AndroidUseAapt2>
    <AndroidCreatePackagePerAbi>false</AndroidCreatePackagePerAbi>
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Mono.Android" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Alfateam.Plugins.Popup">
      <Version>2.1.0</Version>
    </PackageReference>
    <PackageReference Include="BottomSheetXF">
      <Version>1.1.0</Version>
    </PackageReference>
    <PackageReference Include="DevExpress.XamarinForms.Editors">
      <Version>22.1.3</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client">
      <Version>6.0.21</Version>
    </PackageReference>
    <PackageReference Include="Plugin.FirebasePushNotification">
      <Version>3.4.35</Version>
    </PackageReference>
    <PackageReference Include="Plugin.XCalendar">
      <Version>2.1.0</Version>
    </PackageReference>
    <PackageReference Include="Sharpnado.CollectionView">
      <Version>2.1.0</Version>
    </PackageReference>
    <PackageReference Include="Sharpnado.MaterialFrame">
      <Version>1.3.0</Version>
    </PackageReference>
    <PackageReference Include="SVGChart.Nuget">
      <Version>1.0.1</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfImageEditor">
      <Version>25.1.37</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfPicker">
      <Version>25.1.37</Version>
    </PackageReference>
    <PackageReference Include="Syncfusion.Xamarin.SfProgressBar">
      <Version>25.1.37</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.AndroidX.Migration">
      <Version>1.0.10</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.CommunityToolkit">
      <Version>2.0.1</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Forms" Version="5.0.0.2662" />
    <PackageReference Include="Xamarin.Essentials" Version="1.7.1" />
    <PackageReference Include="Xamarin.Forms.PancakeView">
      <Version>2.3.0.759</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.GooglePlayServices.Base">
      <Version>118.0.1</Version>
    </PackageReference>
    <PackageReference Include="Xamarin.Plugin.Calendar">
      <Version>2.0.9699</Version>
    </PackageReference>
    <PackageReference Include="XamForms.Controls.Calendar">
      <Version>1.1.1</Version>
    </PackageReference>
    <PackageReference Include="System.Buffers">
      <Version>4.5.1</Version>
      <IncludeAssets>none</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Memory">
      <Version>4.5.4</Version>
      <IncludeAssets>none</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MainActivity.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UI\Effects\PlatformTouchRippleEffect.cs" />
    <Compile Include="UI\Effects\ScrollBarColorEffect.cs" />
    <Compile Include="UI\Renderers\BorderlessEntryRenderer.cs" />
    <Compile Include="UI\Renderers\BordlessPickerRenderer.cs" />
    <Compile Include="UI\Renderers\CustomEditorRenderer.cs" />
    <Compile Include="UI\Renderers\CustomEntryRenderer.cs" />
    <Compile Include="UI\Renderers\DropdownRenderer.cs" />
    <Compile Include="UI\Renderers\TouchableFrameRenderer.cs" />
  </ItemGroup>
  <ItemGroup>
    <GoogleServicesJson Include="google-services.json" />
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\values\styles.xml" />
    <AndroidResource Include="Resources\values\colors.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\icon_round.xml" />
    <AndroidResource Include="Resources\mipmap-hdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-hdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\icon.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\launcher_foreground.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\active_ach.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\MibuGame\SharedGamificationAdmin\App1\App1\App1.csproj">
      <Project>{5D85399D-42F5-4A82-B985-2C5056A69C02}</Project>
      <Name>App1</Name>
    </ProjectReference>
    <ProjectReference Include="..\CrmAdminApp\CrmAdminApp.csproj">
      <Project>{4F686E5B-C618-463A-8B81-0246BBCE3D99}</Project>
      <Name>CrmAdminApp</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowBack.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowDown.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowForward.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\average_check_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\blue_gradient.jpg" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\burger.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\by_bonuses_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cafe_bg.jpg" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\calendar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\calendarCloud.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\calendarViolet.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\calender.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\card.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cash.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cash_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cashless_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cashRegister.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cashRegisterSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cassa.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\cassa_black.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\check.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\check_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\client.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\clock.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\clock_gray.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\criticalBalance.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\dutyClosing.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\dutyOpening.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\hamburger.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\hidePassword.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ico.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\incassation_sign.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\LeftArrow.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\location.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logo_gray.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logo_start.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logo_start_gray.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\logout.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\main_decor.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\networks.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\notifications.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\orders.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\orders_black.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\ordersSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\payment.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\pen.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\photo.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\plus.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profile.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profit.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\realTime.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\realTimeSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\report.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\revenue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sales.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sales_black.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\salesSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\scheduling.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\scheduling_black.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\settings.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\shadowLine.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\showPassword.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sign_in_apple.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sign_in_google.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\stock.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\stock_black.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\stocks.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\stocksSelected.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\unlock.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\visitors_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\wallet.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\active_level.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\add_circle.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrow_down.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrow_right.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\avatar_2.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Avatar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\attachmentIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\article.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\best.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\back.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\bookActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\bronze.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\book.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\chart.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\chartActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\coins.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\close.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\down.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\deadline.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\down_arrow.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\edit.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\filter.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\fire.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gold.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Karma.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\karmas.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\idea.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menu.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\notification.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\notificationActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\plusIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profile_back.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profile.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\profileActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\readable.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\riseActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\rise.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\send.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\send_message.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\shareIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\silver.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\smsActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\star.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\sms.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\small_karma.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\smile.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\small_coin.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\task.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\taskActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\user.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\up.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\vector.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\vectorActive.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\searchPurple.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuProfileIconActive.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuProfileIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowDownWhiteGamification.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuAssignmentIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuAssignmentSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuMessagesIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuMessagesSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuNotificationsActiveIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuNotificationsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuRatingIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\menuRatingSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\arrowBackGamification.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\filterArrowDown.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\filterArrowUp.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationDeadlineIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationFilter.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskTitle.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskKarma.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskDescription.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskAddUser.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskCoins.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationProfileBack.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationLevelUpSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationLevelUpIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationPostsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationPostsSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationSendMessage.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTaskSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTestsIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationTestsSelectedIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\readMsgIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\userAvatar.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\personGamificationMenuIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\userAdd.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\gamificationCheckMarkSet.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\closeWhite.png" />
    <AndroidResource Include="Resources\drawable\gay_logo.png" />
    <AndroidResource Include="Resources\drawable\tasksIcon.png" />
    <AndroidResource Include="Resources\drawable\tasksIconActive.png" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
</Project>