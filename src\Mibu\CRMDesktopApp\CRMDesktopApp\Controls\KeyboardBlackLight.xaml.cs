﻿using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls
{
    /// <summary>
    /// Логика взаимодействия для KeyboardBlackLight.xaml
    /// </summary>
    public partial class KeyboardBlackLight : UserControl
    {
        public event EventHandler<string> ButtonTapped;
        public KeyboardBlackLight()
        {
            InitializeComponent();
        }

        private ICommand tapButton;
        public ICommand TapButton
        {
            get => tapButton ??= new RelayCommand(async obj =>
            {
                ButtonTapped?.Invoke(this, obj as string);
            });
        }
    }
}
