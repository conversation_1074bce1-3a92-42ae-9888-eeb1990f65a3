﻿using CRM.Models.Enums.Equipment;
using CRM.Models.Reports.Mobile.X_CashierReport;
using CRM.Models.Reports.Mobile.X_Report;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates.Tables;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using TermoPrintingLib;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class XCashierReportPopup : PopupPage
    {
        private XCashierReport XReport;
        public XCashierReportPopup()
        {
            InitializeComponent();
        }
        protected override async void OnAppearing()
        {
            base.OnAppearing();

            await Task.Run(async () =>
            {
                XReport = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetCashierXReport(ApplicationState.CurrentDomain,
                                                                               ApplicationState.CurrentTradeNetwork.Id,
                                                                               ApplicationState.CurrentStore.Id,
                                                                               ApplicationState.CurrentDuty.Id);
            });

            pleaseWaitLabel.IsVisible = false;
            FillTable();
        }

        private void FillTable()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                var dutyRow = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"№{ApplicationState.CurrentDuty.Id} от {ApplicationState.CurrentDuty.OpenedAtLocalAuto.ToString("dd.MM.yyyy")}",
                    SecondColumn = ApplicationState.CurrentDuty.OpenedAtLocalAuto.ToString("HH:mm")
                };
                reportStackLayout.Children.Add(dutyRow);
                var createdBy = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"Отчет сформировал",
                    SecondColumn = $"{Auth.User.Name} {Auth.User.Surname}"
                };
                reportStackLayout.Children.Add(createdBy);
                var checksCount = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"Количество чеков",
                    SecondColumn = $"{XReport.ChecksCount}"
                };
                reportStackLayout.Children.Add(checksCount);
                foreach (var workshop in XReport.WorkshopRows)
                {
                    var workshopRevenue = new XReportItem()
                    {
                        HeightRequest = 30,
                        FirstColumn = $"Выручка {workshop.Workshop.Title}",
                        SecondColumn = $"{workshop.Revenue}"
                    };
                    reportStackLayout.Children.Add(workshopRevenue);
                }
                var cashRevenue = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"Выручка наличными",
                    SecondColumn = $"{XReport.ByCash}"
                };
                reportStackLayout.Children.Add(cashRevenue);
                var cashlessRevenue = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"Выручка безналичными",
                    SecondColumn = $"{XReport.ByCashless}"
                };
                reportStackLayout.Children.Add(cashlessRevenue);
                var paidByBonuses = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"Оплачено бонусами",
                    SecondColumn = $"{XReport.PaidByBonuses}"
                };
                reportStackLayout.Children.Add(paidByBonuses);
                var totalRevenue = new XReportItem()
                {
                    HeightRequest = 30,
                    FirstColumn = $"Итого выручка",
                    SecondColumn = $"{XReport.TotalRevenue}"
                };
                reportStackLayout.Children.Add(totalRevenue);
            });
        }



        #region Печать отчета
        private ICommand printReport;
        public ICommand PrintReport
        {
            get => printReport ??= new RelayCommand(async obj =>
            {
                var printers = ApplicationState.StorePrinters.Where(o => o.PrinterType == PrinterType.ForChecksAndReports);
                if (printers.Count() == 0)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Печать невозможна, нет ни одного принтера для печати чеков", "Ошибка"));
                }
                else
                {
                    if (printers.Count() == 1)
                    {
                        var printer = printers.FirstOrDefault();
                        Print(printer);
                    }
                    else
                    {
                        var popup = new AvailablePrintersPopup(PrinterType.ForChecksAndReports);
                        popup.ItemSelected += (o, e) =>
                        {
                            Print(e);
                        };
                        await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                    }
                }
            });
        }
        private async void Print(Printer printer)
        {
            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);
            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintXCashierReport(XReport,Auth.User);
            if (!result)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Ошибка печати", "Ошибка"));
            }
        }
        #endregion

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}