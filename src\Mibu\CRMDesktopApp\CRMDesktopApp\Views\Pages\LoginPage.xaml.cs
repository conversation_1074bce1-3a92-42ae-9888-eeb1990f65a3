﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages.Main;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Pages
{
    /// <summary>
    /// Логика взаимодействия для LoginPage.xaml
    /// </summary>
    public partial class LoginPage : BasePage
    {
        public LoginPage()
        {
            InitializeComponent();
        }

        private string domain = string.Empty;
        public string Domain
        {
            get => domain;
            set { domain = value; OnPropertyChanged(nameof(Domain)); }
        }
        private string username = string.Empty;
        public string Username
        {
            get => username;
            set { username = value; OnPropertyChanged(nameof(Username)); }
        }
        private string password = string.Empty;
        public string Password
        {
            get => password;
            set { password = value; OnPropertyChanged(nameof(Password)); }
        }

        private ICommand auth;
        public ICommand Auth
        {
            get => auth ??= new RelayCommand(async obj =>
            {
                if (string.IsNullOrEmpty(Password) || string.IsNullOrEmpty(Username) || string.IsNullOrEmpty(Domain))
                {
                    new OneButtonedPopup("Все поля должны быть заполнеными", "Ошибка").ShowDialog();
                    return;
                }

                bool authResult = await CRMAdminMoblieApp.Helpers.Auth.AuthorizeToPOS(Domain, Username, Password);
                if (authResult)
                {
                    ApplicationState.Subscription = await MobileAPI.MainMethods.GetDomainSubscription(Domain);
                    ApplicationState.SaveChangesToMemory();

                    if (ApplicationState.Subscription.IsExpired)
                    {
                        new OneButtonedPopup("Необходимо оплатить подписку", "Ошибка").ShowDialog();
                        return;
                    }


                    NavigationService nav = NavigationService.GetNavigationService(this);
                    if (nav != null)
                    {
                        ApplicationState.SaveChangesToMemory();
                        nav.Navigate(new MainPage());
                    }
                }
                else
                {
                    new OneButtonedPopup("Авторизация не удалась", "Ошибка").ShowDialog();
                }
            });
        }
        private ICommand openSupportPopup;
        public ICommand OpenSupportPopup
        {
            get => openSupportPopup ??= new RelayCommand(async obj =>
            {
                 new SupportPopup().ShowDialog();
            });
        }
    }
}
