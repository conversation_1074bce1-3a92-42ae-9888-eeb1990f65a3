﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CRM.Models.Stores.Orders;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Modifiers
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OneOptionModifierOptionItem : ContentView
    {
        public OneOptionModifierOptionItem(OrderItemModifierOption option)
        {
            InitializeComponent();
            Model = option;

            Device.InvokeOnMainThreadAsync(() =>
            {
                radioButton.IsChecked = (bool)option.IsSelected;
            });
        }

        public static readonly BindableProperty ModelProperty =
          BindableProperty.Create(nameof(Model), typeof(OrderItemModifierOption), typeof(OneOptionModifierOptionItem));
        public OrderItemModifierOption Model
        {
            get { return (OrderItemModifierOption)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }

        public event EventHandler<OrderItemModifierOption> OnChecked;
        private void onCheckedChanged(object sender, CheckedChangedEventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                Model.IsSelected = radioButton.IsChecked;
                if (!radioButton.IsChecked) return;
                OnChecked?.Invoke(this, Model);
            });
        }
    }
}