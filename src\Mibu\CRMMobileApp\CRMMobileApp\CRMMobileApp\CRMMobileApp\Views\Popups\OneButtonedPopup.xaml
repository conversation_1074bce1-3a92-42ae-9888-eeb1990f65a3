﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage 
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:xct="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    x:Name="this"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    CloseWhenBackgroundIsClicked="False"
    x:Class="CRMMobileApp.Views.Popups.OneButtonedPopup">
    <Grid>

        <Frame
             HasShadow="{OnPlatform WPF=True, Default=False}"
             WidthRequest="300"
             MinimumHeightRequest="150"
             HorizontalOptions="Center"
             VerticalOptions="{OnPlatform Android=Start, iOS=Start, WPF=Center}"
             Margin="{OnPlatform Android='0,200,0,0',iOS='0,200,0,0',WPF='0,0,0,0'}"
             BackgroundColor="White"
             Background="White"
             CornerRadius="20">
            <StackLayout>
                <Label 
                     FontSize="20"
                     FontAttributes="Bold"
                     HorizontalOptions="Center"
                     Margin="0,12,0,0"
                     HorizontalTextAlignment="Center"
                     TextColor="{StaticResource dark_purple}"
                     Text="{Binding Source={x:Reference this},Path=Caption}"/>
                <Label 
                     FontSize="14"
                     HorizontalOptions="Center"
                     Margin="0,28,0,0"
                     HorizontalTextAlignment="Center"
                     TextColor="{StaticResource dark_purple}"
                     Text="{Binding Source={x:Reference this},Path=Message}"/>
                <Button 
                     Command="{Binding Source={x:Reference this},Path=Close}"
                     Text="{Binding Source={x:Reference this},Path=ButtonText}"
                     HeightRequest="35"
                     Margin="70,28,70,8"
                     Style="{StaticResource blue_cornered_filled_btn}"/>
            </StackLayout>
        </Frame>

    </Grid>


</animations:PopupPage>