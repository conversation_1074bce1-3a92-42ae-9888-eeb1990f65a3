﻿using AcquiringProviders.Abstractions;
using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates.Devices;
using CRMDesktopApp.Views.Pages.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для ProductOptionsPopup.xaml
    /// </summary>
    public partial class AvailableAcquiringPopup : BaseWindow
    {
        public AvailableAcquiringPopup()
        {
            InitializeComponent();
            Loaded += AvailableAcquiringPopup_Loaded;
        }

        private async void AvailableAcquiringPopup_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            ordersStackLayout.Children.Clear();
            foreach (var item in ApplicationState.StoreAcquirings)
            {
                var control = new AcquiringItemTemplate(item)
                {
                    Height = 38,
                    Margin = new Thickness(0, 4, 0, 0)
                };
                control.ItemTapped += Control_ItemTapped;
                ordersStackLayout.Children.Add(control);
            }
        }
        private async void Control_ItemTapped(object sender, Acquiring e)
        {
            var sum = OrdersHelper.CurrentOrder.OrderPayments.Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Card && !o.IsPrepeyment)
                                                             .Sum(o => o.Sum);

            this.Close();
            new AcquringWaitingPopup(e, sum).ShowDialog();

        }


    }
}
