﻿using CRM.Models.Enums;
using CRM.Models.Network;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Templates.Devices.Acquiring;
using CRMDesktopApp.Controls.Templates.Devices;
using CRMDesktopApp.Views.Pages.Delivery;
using CRMDesktopApp.Views.Pages.Orders;
using CRMDesktopApp.Views.Pages.Tables;
using CRMDesktopApp.Views.Windows;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Mibu.Landing.Models.Enums;

namespace CRMDesktopApp.Views.Pages.Main
{
    /// <summary>
    /// Логика взаимодействия для MainMenu.xaml
    /// </summary>
    public partial class MainMenu : BasePage
    {
        public MainMenu()
        {
            InitializeComponent();

            Loaded += MainMenu_Loaded;
        }

        private async void MainMenu_Loaded(object sender, RoutedEventArgs e)
        {
            if(ApplicationState.Subscription.Subscription.Type != SubscriptionType.Full)
            {
                deliveryBorder.Visibility = Visibility.Hidden;
            }
            RenderEquipment();
        }


        #region 1 tab
        private async void GoToClosedOrders(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.ClosedOrdersWatching))
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if (nav != null)
                {
                    nav.Navigate(new ClosedOrdersPage());
                }
            }
        }
        private async void GoToCreateTransaction(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.TransactionCreating))
            {
                new CreateTransactionPopup().ShowDialog();
            }
        }
        private async void ShowXReport(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.XReport))
            {
                //await App.Current.MainPage.Navigation.PushAsync(new ClosedOrdersPage());
                new XReportPopup().ShowDialog();
            }
        }
        private void ShowXCashierReport(object sender, MouseButtonEventArgs e)
        {
            if (Auth.User.Role == CRM.Models.Enums.NetworkTerminalUserRole.Admin)
            {
                // await App.Current.MainPage.Navigation.PushAsync(new ClosedOrdersPage());
                new XCashierReportPopup().ShowDialog();
            }
            else
            {
                new OneButtonedPopup("Вы не имеете прав, чтобы совершить данную операцию", "Ошибка").ShowDialog();
            }
        }
        private async void OpenCashBox(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.CashBoxOpening))
            {
                await MobileAPI.BaristaMethods.BaristaInteractionMethods.OpenCashBox(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            }
        }

        private async void CloseDuty(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.DutyClosing))
            {
                new DutyClosingPopup().ShowDialog();
            }
        }

        private void GoToDeliveriesPage(object sender, MouseButtonEventArgs e)
        {
            NavigationService.Navigate(new DeliveriesPage());
        }
        #endregion


        #region 2 tab
        private async void GoToSalesByIngridient(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.IngridientsReportWatching))
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if (nav != null)
                {
                    nav.Navigate(new SalesByIngridients());
                }
            }
        }
        private async void GoToBalanceAtStock(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.BalanceAtStockReportWatching))
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if (nav != null)
                {
                    nav.Navigate(new BalanceAtStock());
                }
            }
        }
        private async void GoToSalesOnDuty(object sender, MouseButtonEventArgs e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.SalesReportWatching))
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if (nav != null)
                {
                    nav.Navigate(new SalesOnDuty());
                }
            }
        }
        #endregion


        #region 3 tab

        private void RenderEquipment()
        {
            RenderFiscalCards();
            RenderPrinterCards();
            RenderAcquiringCards();

            if (ApplicationState.StoreFiscalRegistrators.Count == 0)
            {
                noFiscalItemsTextBlock.Visibility = Visibility.Visible;
            }
            if (ApplicationState.StorePrinters.Count == 0)
            {
                noPrintersItemsTextBlock.Visibility = Visibility.Visible;
            }
            if (ApplicationState.StoreAcquirings.Count == 0)
            {
                noAcquringItemsTextBlock.Visibility = Visibility.Visible;
            }
        }

        private void RenderFiscalCards()
        {
            fiscalFlexLayout.Children.Clear();
            foreach (var item in ApplicationState.StoreFiscalRegistrators)
            {
                CashMachineCard card = new CashMachineCard(item.Key,item.Value);

                card.Margin = new Thickness(40, 20, 0, 0);
                card.Height = 60;
                card.Width = 220;

                fiscalFlexLayout.Children.Add(card);
            }
        }
        private void RenderPrinterCards()
        {
            printersFlexLayout.Children.Clear();
            foreach (var item in ApplicationState.StorePrinters)
            {
                PrinterCard card = new PrinterCard(item);

                card.Margin = new Thickness(40, 20, 0, 0);
                card.Height = 60;
                card.Width = 220;

                printersFlexLayout.Children.Add(card);
            }
        }
        private void RenderAcquiringCards()
        {
            acquringFlexLayout.Children.Clear();
            foreach (var item in ApplicationState.StoreAcquirings)
            {
                AbsAcquiringCard card = null;
                switch (item.AcquiringProvider)
                {
                    case AcquiringProvider.UCS_Cards:
                        card = new UCSCardsAcquringCard(item);
                        break;
                    case AcquiringProvider.Sberbank:
                        card = new SberAcquringCard(item);
                        break;
                    case AcquiringProvider.Ingenico:
                        card = new IngenicoAcquringCard(item);
                        break;
                }
                card.Margin = new Thickness(40, 20, 0, 0);
                card.Height = 60;
                card.Width = 220;

                acquringFlexLayout.Children.Add(card);
            }
        }

        #endregion

        private ICommand aboutSoftware;
        public ICommand AboutSoftware
        {
            get => aboutSoftware ??= new RelayCommand(async obj =>
            {
                new AboutSoftwarePopup().ShowDialog();
            });
        }

        #region Переключение  tabcontrol
        private void MainTabSelected(object sender, RoutedEventArgs e)
        {
            if (mainTabControl != null)
                mainTabControl.SelectedIndex = 0;
        }

        private void StockTabSelected(object sender, RoutedEventArgs e)
        {
            if (mainTabControl != null)
                mainTabControl.SelectedIndex = 1;
        }

        private void EquipmentSelected(object sender, RoutedEventArgs e)
        {
            if (mainTabControl != null)
                mainTabControl.SelectedIndex = 2;
        }

        #region Оборудование
        private void AquiringSelected(object sender, RoutedEventArgs e)
        {
            if (equipmentTabControl != null)
                equipmentTabControl.SelectedIndex = 0;
        }

        private void PrintersSelected(object sender, RoutedEventArgs e)
        {
            if (equipmentTabControl != null)
                equipmentTabControl.SelectedIndex = 1;
        }

        private void CashRegistersSelected(object sender, RoutedEventArgs e)
        {
            if (equipmentTabControl != null)
                equipmentTabControl.SelectedIndex = 2;
        }


        #endregion

        #endregion

    }
}
