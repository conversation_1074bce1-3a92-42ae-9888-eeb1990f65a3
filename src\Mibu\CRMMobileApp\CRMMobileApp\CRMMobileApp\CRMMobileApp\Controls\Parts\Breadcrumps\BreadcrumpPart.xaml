﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMMobileApp.Controls.Parts.Breadcrumps.BreadcrumpPart">
    <ContentView.Content>
        <StackLayout
          Spacing="12"
          Orientation="Horizontal">

            <Label
                x:Name="categoryTitle"
                VerticalOptions="Center"
                TextColor="{StaticResource text_gray}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="16"
                Text="">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer Tapped="onTapped"/>
                </Label.GestureRecognizers>
            </Label>
            <Image
                x:Name="nextItemArrow"
                IsVisible="False"
                WidthRequest="4"
                HeightRequest="8"
                Aspect="Fill"
                HorizontalOptions="Start"
                VerticalOptions="Center"
                Source="{OnPlatform Default=arrowRight.png, WPF='pack://application:,,,/Images/arrowRight.png'}"/>


        </StackLayout>
  </ContentView.Content>
</ContentView>