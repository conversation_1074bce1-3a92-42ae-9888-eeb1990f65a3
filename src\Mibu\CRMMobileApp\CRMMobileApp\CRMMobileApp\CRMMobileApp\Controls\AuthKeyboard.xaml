﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.AuthKeyboard">
    <ContentView.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button 
                Text="1" 
                Grid.Row="0"
                Grid.Column="0"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="1"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="2" 
                Grid.Row="0"
                Grid.Column="1"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="2"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="3" 
                Grid.Row="0"
                Grid.Column="2"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="3"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="4" 
                Grid.Row="1"
                Grid.Column="0"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="4"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="5" 
                Grid.Row="1"
                Grid.Column="1"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="5"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="6" 
                Grid.Row="1"
                Grid.Column="2"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="6"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="7" 
                Grid.Row="2"
                Grid.Column="0"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="7"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="8" 
                Grid.Row="2"
                Grid.Column="1"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="8"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <Button 
                Text="9" 
                Grid.Row="2"
                Grid.Column="2"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="9"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <ImageButton 
                BackgroundColor="Transparent"
                Grid.Row="3"
                Grid.Column="0"
                WidthRequest="35"
                HeightRequest="35"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                Source="{OnPlatform Default=close_main.png, WPF='pack://application:,,,/Images/close_main.png'}"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="close"/>
            <Button 
                Text="0" 
                Grid.Row="3"
                Grid.Column="1"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="0"
                Style="{StaticResource auth_blue_rounded_btn}"/>
            <ImageButton 
                BackgroundColor="Transparent"
                Grid.Row="3"
                Grid.Column="2"
                WidthRequest="35"
                HeightRequest="35"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                Source="{OnPlatform Default=backspace_main.png, WPF='pack://application:,,,/Images/backspace_main.png'}"
                Command="{Binding Source={x:Reference this},Path=TapButton}"
                CommandParameter="backspace"/>
        </Grid>
    </ContentView.Content>
</ContentView>