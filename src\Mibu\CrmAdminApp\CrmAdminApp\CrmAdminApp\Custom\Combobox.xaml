﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CRMAdminMoblieApp.Custom.Combobox"
             BackgroundColor="Transparent"
             x:Name="this">
    <ContentView.Content>
        <Frame 
            BackgroundColor="{Binding Source={x:Reference this},Path=HeaderBackgroundColor}"
            CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
            HasShadow="False"
            Padding="0">

            <Grid RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="{Binding Source={x:Reference this},Path=CollapsedHeightRequest}" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Grid
                    Grid.Row="0"
                    Padding="0">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="HeaderContent_OnTapped" />
                    </Grid.GestureRecognizers>

                    
                    <Image
                        Margin="0,0,5,10"
                        HorizontalOptions="End"
                        VerticalOptions="End"
                        WidthRequest="14"
                        HeightRequest="14">
                        <Image.Source>
                            <FileImageSource File="{Binding Source={x:Reference this},Path=ExpandIcon}" />
                        </Image.Source>
                    </Image>

                    <BoxView 
                        IsVisible="{Binding Source={x:Reference this},Path=HasUnderLine}"
                        BackgroundColor="{Binding Source={x:Reference this},Path=UnderLineColor}"
                        HeightRequest="1"
                        VerticalOptions="End"
                        HorizontalOptions="FillAndExpand"/>
                </Grid>

                <!-- Header Bottom Border -->
                <!--<BoxView Grid.Row="0"
                 Color="#EBEBEB"
                 HeightRequest="1"
                 Opacity="0.8"
                 VerticalOptions="End"
                 HorizontalOptions="FillAndExpand"
                 IsVisible="{Binding IsExpanded}" />-->

                <!-- Content View -->
                <Frame
                    CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
                    HasShadow="False"
                    BackgroundColor="{Binding Source={x:Reference this},Path=PopupBackgroundColor}"
                    Margin="{Binding Source={x:Reference this},Path=ExpandedContentMargin}"
                    Padding="0"
                    Grid.Row="1"
                    x:Name="BodyContentView"
                    IsVisible="False" >
                    <ListView
                        RowHeight="{Binding Source={x:Reference this},Path=RowHeight,Mode=TwoWay}"
                        ItemTemplate="{Binding Source={x:Reference this},Path=ItemTemplate,Mode=TwoWay}"
                        SelectedItem="{Binding Source={x:Reference this},Path=SelectedItem,Mode=TwoWay}"
                        ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource,Mode=TwoWay}">
                        
                    </ListView>
                </Frame>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>