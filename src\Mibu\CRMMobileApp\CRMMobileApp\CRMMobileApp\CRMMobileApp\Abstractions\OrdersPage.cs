﻿using CRMMobileApp.Core;
using CRMMobileApp.Views.Pages.Main;
using CRMMobileApp.Views.Popups;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMMobileApp.Abstractions
{
    public abstract class OrdersPage : ContentPage
    {
        private ICommand showPopupLocker;
        public ICommand ShowPopupLocker
        {
            get => showPopupLocker ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new LockedScreenPopup());
            });
        }
        private ICommand openMainPage;
        public ICommand OpenMainPage
        {
            get => openMainPage ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new MainMenu());
            });
        }
    }
}
