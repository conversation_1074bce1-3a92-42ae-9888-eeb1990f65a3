﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.TabView
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CustomTabViewItem : ContentView
    {
        public CustomTabViewItem()
        {
            InitializeComponent();
        }


        public static readonly BindableProperty IsSelectedProperty = BindableProperty.Create(nameof(IsSelected), typeof(bool), typeof(CustomTabViewItem), false);
        public bool IsSelected
        {
            get { return (bool)GetValue(IsSelectedProperty); }
            set { SetValue(IsSelectedProperty, value); }
        }


        public static readonly BindableProperty TabHeaderUnselectedStateContentProperty = BindableProperty.Create(nameof(TabHeaderUnselectedStateContent), typeof(View), typeof(CustomTabViewItem), null);
        public View TabHeaderUnselectedStateContent
        {
            get { return (View)GetValue(TabHeaderUnselectedStateContentProperty); }
            set { SetValue(TabHeaderUnselectedStateContentProperty, value); }
        }

        public static readonly BindableProperty TabHeaderSelectedStateContentProperty = BindableProperty.Create(nameof(TabHeaderSelectedStateContent), typeof(View), typeof(CustomTabViewItem), null);
        public View TabHeaderSelectedStateContent
        {
            get { return (View)GetValue(TabHeaderSelectedStateContentProperty); }
            set { SetValue(TabHeaderSelectedStateContentProperty, value); }
        }


        public static readonly BindableProperty TabContentProperty = BindableProperty.Create(nameof(TabContent), typeof(View), typeof(CustomTabViewItem), null);
        public View TabContent
        {
            get { return (View)GetValue(TabContentProperty); }
            set { SetValue(TabContentProperty, value); }
        }
    }
}