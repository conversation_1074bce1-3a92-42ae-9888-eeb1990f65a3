﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.Devices.CashMachineCard">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            CornerRadius="10"
            Padding="0,5,0,0"
            IsClippedToBounds="True"
            BackgroundColor="{StaticResource bg_purple}">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ToggleExpand}"/>
            </Frame.GestureRecognizers>
            <Grid>
                <StackLayout>
                    <StackLayout
                        Margin="20,0,0,0"
                        Spacing="20"
                        Orientation="Horizontal"
                        HorizontalOptions="Start"
                        VerticalOptions="Center">
                        <Image
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            HeightRequest="25"
                            WidthRequest="25"
                            Source="{OnPlatform Default=cash_machine.png, WPF='pack://application:,,,/Images/cash_machine.png'}"/>
                        <StackLayout
                            Spacing="0">
                            <StackLayout Orientation="Horizontal">
                                <Frame
                                    x:Name="connectionCircleFrame"
                                    BackgroundColor="Gray"
                                    Padding="0"
                                    HasShadow="False"
                                    CornerRadius="4"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="8"
                                    WidthRequest="8"/>
                                <Label 
                                    FontSize="16"
                                    VerticalOptions="Center"
                                    TextColor="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    Text="{Binding Source={x:Reference this},Path=Model.Name}"/>
                            </StackLayout>

                            <Label
                                FontSize="16"
                                VerticalOptions="Center"
                                TextColor="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                Text="{Binding Source={x:Reference this},Path=Model.ConnectionPortNumber}"/>
                        </StackLayout>
                    </StackLayout>

                    <StackLayout
                        Margin="0,10,0,0"
                        Spacing="5"
                        IsVisible="False"
                        x:Name="menuLayout"
                        HorizontalOptions="Center">
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=CheckConnection}"
                            Text="Проверка связи"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=CloseShift}"
                            Text="Закрыть смену"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                        <Button 
                            HeightRequest="30"
                            Command="{Binding Source={x:Reference this},Path=CancelCheque}"
                            Text="Аннулировать чек"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                    </StackLayout>

                </StackLayout>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView> 