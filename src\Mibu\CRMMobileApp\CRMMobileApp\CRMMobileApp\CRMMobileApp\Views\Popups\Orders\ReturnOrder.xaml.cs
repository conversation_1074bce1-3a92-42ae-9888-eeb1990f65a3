﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using TermoPrintingLib;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ReturnOrder : PopupPage
    {
        private Order _order;
        public ReturnOrder(Order order)
        {
            _order = order;
            InitializeComponent();
        }


        private string reason = "";
        public string Reason
        {
            get => reason;
            set { reason = value; OnPropertyChanged(nameof(Reason)); }
        }
        private bool printCheque;
        public bool PrintCheque
        {
            get => printCheque;
            set { printCheque = value; OnPropertyChanged(nameof(PrintCheque)); }
        }

        private ICommand returnCommand;
        public ICommand ReturnCommand
        {
            get => returnCommand ??= new RelayCommand(async obj =>
            {
                if (PrintCheque)
                {
                    if (ApplicationState.StorePrinters.Count == 0)
                    {
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Чек не напечатан, т.к. нет ни одного принтера для печати чеков", "Ошибка"));
                        return;
                    }
                    else
                    {
                        if (ApplicationState.StorePrinters.Count == 1)
                        {
                            var printer = ApplicationState.StorePrinters[0];
                            PrintClosedOrderCheque(printer);
                        }
                        else
                        {
                            var popup = new AvailablePrintersPopup(CRM.Models.Enums.Equipment.PrinterType.OrdersForWorkshops);
                            popup.ItemSelected += (o, e) =>
                            {
                                PrintClosedOrderCheque(e);
                            };
                            await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                        }
                    }
                }

                if (string.IsNullOrEmpty(Reason))
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Необходимо указать причину возврата", "Ошибка"));
                    return;
                }

                await OrdersHelper.ReturnOrder(_order,Reason);
                var page = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as ClosedOrdersPage;
                page.LoadOrders();

                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

        #region Печать возврата
        private async void PrintClosedOrderCheque(Printer printer)
        {
            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                   ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);
            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintReturnCheque(_order);
            if (!result)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Ошибка печати", "Ошибка"));
            }
        }
        #endregion


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}