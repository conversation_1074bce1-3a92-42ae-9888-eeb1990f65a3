﻿using CRM.Models.Stores;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderItemTemplate : ContentView
    {
        private OrderItem model;
        public OrderItem Model
        {
            get => model;
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }

        public OrderItemTemplate()
        {
            InitializeComponent();
        }
        public OrderItemTemplate(OrderItem item)
        {
            Model = item;
            InitializeComponent();

            Render();
        }

        public void Render()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
                itemCommentSign.IsVisible = !string.IsNullOrEmpty(Model.Comment);

                RenderModifiers();

                Amount = Model.Amount;

                //while(itemTitleLabel.Height < 1)
                //{
                //    await Task.Delay(120);
                //}
                //var h = itemTitleLabel.Height;
            });
        }


        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if(BindingContext is OrderItem item)
            {
                Model = item;
                Render();

        
            }
        }




        private void RenderModifiers()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                modifiersLayout.Children.Clear();
                foreach (var modifier in Model.SelectedModifiers)
                {
                    var item = new OrderItemModifierTemplate(modifier);
                    modifiersLayout.Children.Add(item);
                }
                //double height = 30;
                //foreach (var modifier in Model.SelectedModifiers)
                //{
                //    //height += modifier.SelectedOptions.Count * 30;
                //}
                //this.HeightRequest = height;
            });
        }



        private double amount;
        public double Amount
        {
            get => amount;
            set
            {
                amount = value;
                OnPropertyChanged(nameof(Amount));

                Model.Amount = amount;
                Sum = amount * Model.Price;
            }
        }
        private double sum;
        public double Sum
        {
            get => sum;
            set
            {
                sum = value;
                OnPropertyChanged(nameof(Sum));
            }
        }


        private string itemTitle;
        public string ItemTitle
        {
            get
            {
                if (Model == null) return "";

                if (Model.TechnicalCard != null)
                   return Model.TechnicalCard.Title;
                if (Model.Product != null)
                    return Model.Product.Title;
                return "";  
            }
            set
            {
                itemTitle = value;
                OnPropertyChanged(nameof(ItemTitle));
            }
        }



        public event EventHandler<OrderItem> DeleteBtnTapped;
        private void onDeleteBtnTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                DeleteBtnTapped?.Invoke(this, Model);
            });
        }



        #region Выбор позиции
        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set 
            { 
                isSelected = value;

                Device.InvokeOnMainThreadAsync(() =>
                {
                    decrementAmountFrameBtn.IsVisible = value && !Model.IsInWork;
                    incrementAmountFrameBtn.IsVisible = value && !Model.IsInWork;
                });

                OnPropertyChanged(nameof(IsSelected)); 
            }
        }

        public event EventHandler<OrderItem> OrderItemTapped;
        private void onOrderItemTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                OrderItemTapped?.Invoke(this, Model);
            });
        }

        #endregion

        #region Редактирование кол-ва через кнопки
        public event EventHandler<OrderItem> AmountChanged;

        private ICommand incrementAmount;
        public ICommand IncrementAmount
        {
            get => incrementAmount ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    Model.Amount++;
                    Amount = Model.Amount;

                    amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
                    AmountChanged?.Invoke(this, Model);
                });
            });
        }
        private ICommand decrementAmount;
        public ICommand DecrementAmount
        {
            get => decrementAmount ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (Model.Amount > 1)
                    {
                        Model.Amount--;
                        Amount = Model.Amount;
                    }

                    amountLabel.Text = Math.Round(Model.Amount, 3).ToString();
                    AmountChanged?.Invoke(this, Model);
                });
            });
        }
        #endregion

    }
}