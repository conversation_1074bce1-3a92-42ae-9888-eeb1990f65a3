﻿namespace CRMWeb.MobileAPI.Helpers
{
    public class BasePaths
    {
        public const string WebDomain = "https://crm.mibu24.ru";
        public const string BaseToGeneratedURL = WebDomain+ "/generated";
        public const string BaseToDefaultsURL = WebDomain + "/defaluts";
        public const string BaseToUploadsURL = WebDomain + "/uploads";

        public static string GetAbsoluteWebPath(string path)
        {
            if (!string.IsNullOrEmpty(path) && !path.StartsWith("/"))
                path = "/" + path;
            if (!string.IsNullOrEmpty(path) && path.Contains(WebDomain)) 
                return path.TrimStart('/');
            return WebDomain + path;
        }
    }
}
