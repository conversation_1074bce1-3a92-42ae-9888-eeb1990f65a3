﻿using CRM.Models;
using CRM.Models.Gamification.General;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace CrmAdminApp.Models.Gamification
{
    public class StoreSelectModel : BindableObject
    {
        public Store Store { get; set; }

        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }

    }
}
