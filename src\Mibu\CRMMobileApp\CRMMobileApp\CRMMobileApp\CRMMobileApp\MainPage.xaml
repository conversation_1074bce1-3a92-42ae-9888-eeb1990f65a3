﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             xmlns:controls1="clr-namespace:CRMMobileApp.Controls"
             x:Name="ThisPage"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             ios:Page.UseSafeArea="true"
             NavigationPage.HasNavigationBar="False"
             x:Class="CRMMobileApp.MainPage">

    <Grid BackgroundColor="{StaticResource bg_purple}">
        <Image
            Aspect="Fill"
            Source="{OnPlatform Default=main_decor.png, WPF='pack://application:,,,/Images/main_decor.png'}"/>

        <ImageButton 
            Command="{Binding Source={x:Reference ThisPage},Path=GoBack}"
            HorizontalOptions="Start"
            VerticalOptions="Start"
            Source="{OnPlatform Default=arrowBack.png, WPF='pack://application:,,,/Images/arrowBack.png'}"
            WidthRequest="10"
            HeightRequest="20"
            BackgroundColor="Transparent"
            Margin="30,40,0,0"/>
        
        
        <StackLayout     
            MinimumHeightRequest="100"
            MinimumWidthRequest="100"
            VerticalOptions="Center"
            HorizontalOptions="Center">
            <Image
                Margin="0,-30,0,0"
                HorizontalOptions="Center"
                VerticalOptions="Start"
                Source="{OnPlatform Default=logo.png, WPF='pack://application:,,,/Images/logo.png'}"
                HeightRequest="120"
                WidthRequest="120"/>
            
            <Label
                FontSize="25"
                Margin="35,0,0,0"
                VerticalOptions="Start"
                FontFamily="TTFirsNeue-Regular"
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                Text="Введите пин-код для входа"/>

            <StackLayout
                Spacing="10"
                Margin="35,0,0,0"
                HeightRequest="70"
                VerticalOptions="Start"
                HorizontalOptions="Center"
                Orientation="Horizontal">

                <custom:AndroidStyleEntry 
                    x:Name="passwordEntry1"
                    HorizontalTextAlignment="Center"
                    TextFontSize="19"
                    PlaceholderFontSize="19"
                    IsReadOnly="True" 
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalOptions="Start"
                    HeightRequest="60"
                    WidthRequest="50"/>
                <custom:AndroidStyleEntry  
                    x:Name="passwordEntry2"
                    HorizontalTextAlignment="Center"
                    TextFontSize="19"
                    PlaceholderFontSize="19"
                    IsReadOnly="True"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalOptions="Start"
                    HeightRequest="60"
                    WidthRequest="50"/>
                <custom:AndroidStyleEntry  
                    x:Name="passwordEntry3"
                    HorizontalTextAlignment="Center"
                    TextFontSize="19"
                    PlaceholderFontSize="19"
                    IsReadOnly="True"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalOptions="Start"
                    HeightRequest="60"
                    WidthRequest="50"/>
                <custom:AndroidStyleEntry  
                    x:Name="passwordEntry4"
                    HorizontalTextAlignment="Center"
                    TextFontSize="19"
                    PlaceholderFontSize="19"
                    IsReadOnly="True"
                    TextChanged="onPasswordEntryTextChanged"
                    VerticalOptions="Start"
                    HeightRequest="60"
                    WidthRequest="50"/>
            </StackLayout>

            <controls1:AuthKeyboard 
                Margin="30,0,0,0"
                VerticalOptions="Start"
                HeightRequest="{OnPlatform Android='220',iOS='300',WPF='300'}"
                WidthRequest="300"
                HorizontalOptions="Center"
                x:Name="keyboard"/>

            <Label
                x:Name="errorMessageLabel"
                FontSize="14"
                Margin="35,20,0,0"
                VerticalOptions="Start"
                FontFamily="TTFirsNeue-Regular"
                HorizontalOptions="Center"
                TextColor="Red"
                Text=""/>

        </StackLayout>

        <Button 
            Command="{Binding Source={x:Reference ThisPage},Path=OpenSupportPopup}"
            Style="{StaticResource transparent_btn}"
            Margin="20,0,0,5"
            Text="Помощь"
            VerticalOptions="End"
            HorizontalOptions="Center" />


        <Grid IsVisible="False">

            <Image
                Aspect="Fill"
                Source="{OnPlatform Default=main_gradient.png, WPF='pack://application:,,,/Images/main_gradient.png'}"/>


            <StackLayout
                VerticalOptions="Center"
                HorizontalOptions="Center">

                <Label
                    FontSize="20"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Center"
                    TextColor="White"
                    Text="Устанавливается соединение с сервером..."/>


                <Image
                    x:Name="loadingWheel"
                    Aspect="Fill"
                    Margin="0,70,0,0"
                    HorizontalOptions="Center"
                    WidthRequest="90"
                    HeightRequest="90"
                    Source="{OnPlatform Default=loading_wheel.png, WPF='pack://application:,,,/Images/loading_wheel.png'}"/>

            </StackLayout>

        </Grid>
        
        
    </Grid>
</ContentPage>
