﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages.Orders;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.Halls
{
    /// <summary>
    /// Логика взаимодействия для GuestsCountPopup.xaml
    /// </summary>
    public partial class GuestsCountPopup : BaseWindow
    {
        public AbsTable Table { get; set; }
        public GuestsCountPopup(AbsTable table)
        {
            InitializeComponent();
            Table = table;
        }

        private int amount;
        public int Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }


        private ICommand apply;
        public ICommand Apply
        {
            get => apply ??= new RelayCommand(async obj =>
            {
                var newOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.OpenTableOrder(ApplicationState.CurrentDomain,
                                                                                                 ApplicationState.CurrentStore.Id,
                                                                                                 ApplicationState.CurrentDuty.Id,
                                                                                                 Table.Table.Id,
                                                                                                 Amount,
                                                                                                 Auth.User.Id);
                Table.AddOrder(newOrder);
                this.Close();

                OrdersHelper.FromTable = Table;

                OrdersHelper.CurrentOrder = newOrder;

                (App.Current.MainWindow as MainWindow).frame.NavigationService.Navigate(new CategoriesPage());
            });
        }



        private void rbChecked(object sender, RoutedEventArgs e)
        {
            var rb = sender as RadioButton;

            if (rb.IsChecked != true) return;
            Amount = Convert.ToInt32(rb.Content);
        }
    }
}
