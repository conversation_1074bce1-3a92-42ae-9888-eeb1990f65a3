﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:CRMMobileApp.Controls"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.ProductAmountKeyboardPopup">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="300"
            HeightRequest="500"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,70,0,0"
            CornerRadius="20">
            <Grid ColumnSpacing="0"
                  RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="1*"/>
                    <RowDefinition Height="5*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>
                
                <Grid Grid.Row="0"
                      HorizontalOptions="FillAndExpand"
                      VerticalOptions="FillAndExpand"
                      Background="{StaticResource bg_purple}">
                    <StackLayout
                          VerticalOptions="Center">
                        <Label 
                            FontSize="20"
                            HorizontalOptions="End"
                            Margin="0,5,10,0"
                            FontAttributes="Bold"
                            TextColor="{StaticResource green}"
                            Text="{Binding Source={x:Reference this},Path=Amount}"/>
                        <Label 
                            x:Name="priceForValueLabel"
                            TextColor="{StaticResource dark_purple}"
                            HorizontalOptions="End"
                            Margin="0,0,10,0"
                            Text="0 руб."/>
                    </StackLayout>
                </Grid>

                <Button 
                    x:Name="weightBtn"
                    IsVisible="False"
                    Command="{Binding Source={x:Reference this},Path=GetWeight}"
                    Margin="10,0,0,0"
                    HeightRequest="40"
                    WidthRequest="50"
                    CornerRadius="0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Style="{StaticResource blue_cornered_filled_btn}"
                    Text="Вес" 
                    Grid.Row="0"/>

                <controls:KeyboardBlackLight 
                    x:Name="keyboard"
                    Grid.Row="1"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"/>

         

                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>
                    <Button 
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Grid.Column="0"
                        Margin="10,0,10,0"
                        VerticalOptions="Center"
                        HeightRequest="40"
                        TextColor="Black"
                        Style="{StaticResource transparent_btn}"
                        Text="Отмена"/>
                    <Button 
                        Command="{Binding Source={x:Reference this},Path=ApplyValue}"
                        Grid.Column="1"
                        Margin="10,0,10,0"
                        VerticalOptions="Center"
                        HeightRequest="40"
                        Style="{StaticResource blue_cornered_filled_btn}"
                        Text="Ок"/>
                </Grid>
            </Grid>
        </Frame>



    </Grid>
</animations:PopupPage>