﻿using CRM.Models.Network;
using CRMAdminMoblieApp.Views.Pages.Settings;
using CRMMobileApp.Core;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CRMMoblieApiWrapper;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Controls.ItemTemplates;
using System.Linq;

namespace CRMAdminMoblieApp.Views.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Notifications : ContentPage
    {
        public Notifications()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

        }
        protected override void OnAppearing()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                base.OnAppearing();

                var notifications = await MobileAPI.AdminMethods.AdminReportsMethods.GetNotifications(ApplicationState.CurrentDomain,
                                                                                                      ApplicationState.CurrentTradeNetwork.Id);

                NotificationsList = new ObservableCollection<NetworkNotification>(notifications.OrderByDescending(o => o.CreatedAtLocalAuto));

                noItemsLabel.IsVisible = notifications.Count == 0;
                RenderNotifications();
            });
        }

        private void RenderNotifications()
        {
            notificationsLayout.Children.Clear();
            foreach (var notification in NotificationsList)
            {
                var card = new NotificationCard(notification)
                {
                    HorizontalOptions = LayoutOptions.FillAndExpand,
                    HeightRequest = 70
                };
                notificationsLayout.Children.Add(card);
            }
        }







        private ObservableCollection<NetworkNotification> notificationsList = new ObservableCollection<NetworkNotification>();
        public ObservableCollection<NetworkNotification> NotificationsList
        {
            get => notificationsList;
            set { notificationsList = value; OnPropertyChanged(nameof(NotificationsList)); }
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
        private ICommand openNotificationSettings;
        public ICommand OpenNotificationSettings
        {
            get => openNotificationSettings ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PushAsync(new NotificationSettings()));
        }
    }
}