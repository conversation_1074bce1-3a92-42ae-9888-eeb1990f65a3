﻿using CRM.Models.Stores;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderItemCommentPopup : PopupPage
    {
        public OrderItemCommentPopup(OrderItem item)
        {
            OrderItem = item;
            Comment = OrderItem.Comment;
            InitializeComponent();
        }
        private OrderItem orderItem;
        public OrderItem OrderItem
        {
            get => orderItem;
            set { orderItem = value; OnPropertyChanged(nameof(OrderItem)); }
        }




        private string comment;
        public string Comment
        {
            get => comment;
            set { comment = value; OnPropertyChanged(nameof(Comment)); }
        }

        private ICommand saveChanges;
        public ICommand SaveChanges
        {
            get => saveChanges ??= new RelayCommand(async obj =>
            {
                OrderItem.Comment = Comment;
                await OrdersHelper.UpdateOrderItem(OrderItem);
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}