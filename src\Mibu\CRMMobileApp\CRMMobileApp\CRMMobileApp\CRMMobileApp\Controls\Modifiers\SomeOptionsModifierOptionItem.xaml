﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Modifiers.SomeOptionsModifierOptionItem">
    <ContentView.Content>
      <Grid>
           <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="1.5*"/>
                <ColumnDefinition Width="2.5*"/>
           </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">
                <Label 
                    Text="{Binding Source={x:Reference this},Path=Model.ModifierOption.Title}"
                    FontSize="14"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    Margin="15,0,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Center"/>
            </Grid>

            <Grid Grid.Column="1">
                <Label      
                    FontSize="14"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    Margin="5,0,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Center">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="{Binding Source={x:Reference this},Path=Model.ModifierOption.Price}"/>
                                <Span Text=" р"/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
            </Grid>


            <Grid Grid.Column="2">
                <StackLayout
                    Margin="0,0,15,0"
                    Orientation="Horizontal"
                    VerticalOptions="Center"
                    HorizontalOptions="End">

                    <Frame
                        Margin="30,0,0,0"
                        BackgroundColor="#F6F6FB"
                        HasShadow="False"
                        Padding="0"
                        CornerRadius="15"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        HeightRequest="30"
                        WidthRequest="30">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=DecrementAmount}"/>
                        </Frame.GestureRecognizers>
                        <Image 
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            Source="{OnPlatform Default=minus.png, WPF='pack://application:,,,/Images/minus.png'}"
                            WidthRequest="20"     
                            HeightRequest="20"/>
                    </Frame>

                    <Label
                        Margin="12,0,0,0"
                        x:Name="amountLabel"
                        VerticalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="16"
                        FontAttributes="Bold"
                        Text="{Binding Source={x:Reference this},Path=Model.Amount}"/>

                    <Frame
                        Margin="12,0,0,0"
                        BackgroundColor="#7265FB"
                        HasShadow="False"
                        Padding="0"
                        CornerRadius="15"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        HeightRequest="30"
                        WidthRequest="30">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=IncrementAmount}"/>
                        </Frame.GestureRecognizers>
                        <Image 
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            Source="{OnPlatform Default=plus.png, WPF='pack://application:,,,/Images/plus.png'}"
                            WidthRequest="20"
                            HeightRequest="20"/>
                    </Frame>

                </StackLayout>
            </Grid>

        </Grid>
  </ContentView.Content>
</ContentView>