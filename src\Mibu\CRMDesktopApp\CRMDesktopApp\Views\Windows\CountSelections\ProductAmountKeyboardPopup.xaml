﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.CountSelections.ProductAmountKeyboardPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.CountSelections" 
        xmlns:controls="clr-namespace:CRMDesktopApp.Controls" 
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Выбор количества позиции"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="300"
        Height="500">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <customcontrols:ClippingBorder
        Width="290"
        Height="495"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <customcontrols:ClippingBorder.Effect>
            <DropShadowEffect Color="LightGray"/>
        </customcontrols:ClippingBorder.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="5*"/>
                <RowDefinition Height="1*"/>
            </Grid.RowDefinitions>
            <Grid Grid.Row="0"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Stretch"
                  Background="{StaticResource bg_purple}">
                <StackPanel
                    VerticalAlignment="Center">
                    <TextBlock 
                        FontSize="20"
                        HorizontalAlignment="Right"
                        Margin="0,5,10,0"
                        FontWeight="Bold"
                        Foreground="{StaticResource green}"
                        Text="{Binding ElementName=this,Path=Amount}"/>
                    <TextBlock 
                        x:Name="priceForValueLabel"
                        HorizontalAlignment="Right"
                        Margin="0,0,10,0"
                        Text="0 руб."/>
                </StackPanel>
            </Grid>

            <controls:KeyboardBlackLight 
                x:Name="keyboard"
                Grid.Row="1"
                VerticalAlignment="Center"
                HorizontalAlignment="Center"/>

            <Button 
                Command="{Binding GetWeight, ElementName=this}"
                Margin="0,0,10,10"
                Height="40"
                Width="40"
                VerticalAlignment="Bottom"
                HorizontalAlignment="Right"
                Style="{StaticResource blue_cornered_filled_btn}"
                Content="Вес" Grid.Row="1"/>


            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>
                <Button 
                    Command="{Binding ElementName=this,Path=Close}"
                    Grid.Column="0"
                    Margin="10,0,10,0"
                    Height="40"
                    VerticalAlignment="Center"
                    Foreground="Black"
                    Style="{StaticResource transparent_btn}"
                    Content="Отмена"/>
                <Button 
                    Command="{Binding ElementName=this,Path=ApplyValue}"
                    Grid.Column="1"
                    Margin="10,0,10,0"
                    Height="40"
                    VerticalAlignment="Center"
                    Style="{StaticResource blue_cornered_filled_btn}"
                    Content="Ок"/>
            </Grid>
    
        </Grid>
    </customcontrols:ClippingBorder>
</abstactions:BaseWindow>
