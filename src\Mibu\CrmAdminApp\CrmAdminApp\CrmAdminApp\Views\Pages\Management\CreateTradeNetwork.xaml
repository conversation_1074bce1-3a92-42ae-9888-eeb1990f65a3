﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             x:Name="this"
             xmlns:editors="clr-namespace:DevExpress.XamarinForms.Editors;assembly=DevExpress.XamarinForms.Editors" 
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             ios:Page.UseSafeArea="true"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             x:Class="CRMAdminMoblieApp.CreateTradeNetwork">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">

            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <parts:Header x:Name="header" Grid.Row="0"/>

            <Grid Grid.Row="1">


                <StackLayout
                    Margin="30,20,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Start"
                    Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                    </StackLayout.GestureRecognizers>
                    <ImageButton 
                        Source="arrowBack.png"
                        HeightRequest="10"
                        WidthRequest="5"
                        BackgroundColor="Transparent"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"/>
                    <Label 
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="14"
                        TextColor="{x:StaticResource dark_purple}"
                        VerticalOptions="Center"
                        Text="Создание торговой сети"/>
                </StackLayout>


                <StackLayout
                    HorizontalOptions="Center"
                    Margin="0,75,0,0">


                    <StackLayout
                        Margin="30,0,30,0"
                        HorizontalOptions="Center">

                        <StackLayout Spacing="2">


                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Название сети"/>
                            <controls:EntryOutlined
                                HeightRequest="45"
                                Margin="0,3,0,0"
                                Style="{x:StaticResource white_cornered_entry}"
                                Text="{Binding Source={x:Reference this},Path=NewTradeNetwork.NetworkSettings.Title,Mode=TwoWay}"
                                FontFamily="TTFirsNeue-Regular"
                                Placeholder=""/>
                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Название продавца"/>
                            <editors:ComboBoxEdit   
                                Margin="0,3,0,0"
                                BackgroundColor="#F6F6FB"
                                TextColor="{x:StaticResource dark_purple}"
                                BorderColor="#F6F6FB"
                                HeightRequest="40"
                                HorizontalOptions="Fill"
                                VerticalOptions="Start"
                                SelectedIndex="0"
                                CornerRadius="10"
                                PlaceholderText="Название продавца"
                                PlaceholderColor="#9795B1"
                                CornerMode="Round"
                                IsLabelFloating="False"
                                SelectedItem="{Binding Source={x:Reference this},Path=SelectedSalesmanString,Mode=TwoWay}"
                                ItemsSource="{Binding Source={x:Reference this},Path=SalesmanStrings,Mode=TwoWay}" >
                                <editors:ComboBoxEdit.ItemTemplate>
                                    <DataTemplate>
                                        <Grid HeightRequest="40">
                                            <Label
                                                Margin="8,8,0,0"
                                                TextColor="{x:StaticResource dark_purple}" Text="{Binding}"/>
                                        </Grid>
                                    </DataTemplate>
                                </editors:ComboBoxEdit.ItemTemplate>
                            </editors:ComboBoxEdit>
                            <Label 
                                Margin="0,20,0,0"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="12"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Аунтефикация"/>
                            <editors:ComboBoxEdit   
                                Margin="0,3,0,0"
                                BackgroundColor="#F6F6FB"
                                TextColor="{x:StaticResource dark_purple}"
                                BorderColor="#F6F6FB"
                                HeightRequest="40"
                                HorizontalOptions="Fill"
                                VerticalOptions="Start"
                                SelectedIndex="0"
                                CornerRadius="10"
                                PlaceholderText="Аунтефикация"
                                PlaceholderColor="#9795B1"
                                CornerMode="Round"
                                IsLabelFloating="False"
                                SelectedItem="{Binding Source={x:Reference this},Path=SelectedPosAuthTypesString,Mode=TwoWay}"
                                ItemsSource="{Binding Source={x:Reference this},Path=PosAuthTypesStrings,Mode=TwoWay}" >
                                <editors:ComboBoxEdit.ItemTemplate>
                                    <DataTemplate>
                                        <Grid HeightRequest="40">
                                            <Label
                                                Margin="8,8,0,0"
                                                TextColor="{x:StaticResource dark_purple}" Text="{Binding}"/>
                                        </Grid>
                                    </DataTemplate>
                                </editors:ComboBoxEdit.ItemTemplate>
                            </editors:ComboBoxEdit>

                        </StackLayout>


                        <StackLayout>

                            <Button 
                                Command="{Binding Source={x:Reference this},Path=CreateNetwork}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                Text="Создать"
                                Style="{x:StaticResource purple_gradient_btn}"
                                WidthRequest="240"
                                HeightRequest="40"
                                Margin="0,30,0,0"/>

                        </StackLayout>
                    </StackLayout>

                </StackLayout>
            </Grid>
        </Grid>
    </ContentPage.Content>
</ContentPage>
