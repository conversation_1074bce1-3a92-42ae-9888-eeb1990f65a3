﻿using CRM.Models.Enums.Info;
using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Models.Wrappers;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ClientsPopup : PopupPage
    {
        private bool _newOrder;


        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }

        private ObservableCollection<Gender> genders = new ObservableCollection<Gender>()
        {
            Gender.Male,
            Gender.Female
        };
        public ObservableCollection<Gender> Genders
        {
            get => genders;
            set { genders = value; OnPropertyChanged(nameof(Genders)); }
        }
        public ClientsPopup(Order order, bool newOrder = false)
        {
            InitializeComponent();

            Order = order;
            _newOrder = newOrder;
        }


        private ClientsAndBonusesStoreSettings _clientSettings;
        public ClientsAndBonusesStoreSettings ClientSettings
        {
            get => _clientSettings;
            set { _clientSettings = value; OnPropertyChanged(nameof(ClientSettings)); }
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();

            var customers = await MobileAPI.BaristaMethods.BaristaClientsMethods.GetClients(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);

            var wrappers = new List<SelectionWrapper<Customer>>();
            foreach (var customer in customers)
            {
                var wrapper = new SelectionWrapper<Customer>(customer);

                if (Order.CustomerId == customer.Id) 
                {
                    wrapper.IsSelected = true;
					SelectedClient = wrapper;
				}

				wrappers.Add(wrapper);
            }

            Clients = new ObservableCollection<SelectionWrapper<Customer>>(wrappers);
            allClients = new ObservableCollection<SelectionWrapper<Customer>>(wrappers);

            ClientSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetClientsAndBonusesStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                   ApplicationState.CurrentStore.Id);
        }


        private ObservableCollection<SelectionWrapper<Customer>> allClients = new ObservableCollection<SelectionWrapper<Customer>>();

        private ObservableCollection<SelectionWrapper<Customer>> clients = new ObservableCollection<SelectionWrapper<Customer>>();
        public ObservableCollection<SelectionWrapper<Customer>> Clients
        {
            get => clients;
            set { clients = value; OnPropertyChanged(nameof(Clients)); }
        }


        private SelectionWrapper<Customer> selectedClient;
        public SelectionWrapper<Customer> SelectedClient
        {
            get => selectedClient;
            set { selectedClient = value; OnPropertyChanged(nameof(SelectedClient)); }
        }


        private string searchQuery = string.Empty;
        public string SearchQuery
        {
            get => searchQuery;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    Clients = new ObservableCollection<SelectionWrapper<Customer>>(allClients);
                }
                else
                {
                    Clients = new ObservableCollection<SelectionWrapper<Customer>>(allClients.Where(o => o.Model.IsThisClient(ClientSettings.ClientSearchAllowance, value)));
                }
                searchQuery = value;
                OnPropertyChanged(nameof(SearchQuery));
            }
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand goToNewClient;
        public ICommand GoToNewClient
        {
            get => goToNewClient ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    var popup = new NewClientPopup();
                    popup.OnClientCreated += Popup_OnClientCreated;
                    App.Current.MainPage.Navigation.PushPopupAsync(popup);
                });
            });
        }

        private void Popup_OnClientCreated(object sender, Customer e)
        {    
            foreach(var item in Clients)
            {
                item.IsSelected = false;
            }

			var wrapper = new SelectionWrapper<Customer>(e);
            wrapper.IsSelected = true;

			Clients.Add(wrapper);
            SelectedClient = wrapper;

        }
        private void onClientTapped(object sender, SelectionWrapper<Customer> e)
        {
            SelectedClient = e;

            foreach (var customer in Clients)
            {
                customer.IsSelected = false;
            }

            if (SelectedClient != null)
            {
                SelectedClient.IsSelected = true;
            }
        }



        public event EventHandler<Customer> OnCustomerSelected;

        private ICommand selectClient;
        public ICommand SelectClient
        {
            get => selectClient ??= new RelayCommand(async obj =>
            {
                if(SelectedClient is null)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Выберите клиента, чтобы продолжить", "Ошибка"));
                    return;
                }

                if (_newOrder)
                {
                    Order.Customer = SelectedClient.Model;
                    Order.CustomerId = SelectedClient.Model.Id;
                }
                else
                {
                    OrdersHelper.SetCustomer(SelectedClient.Model);
                }


                OnCustomerSelected?.Invoke(this, SelectedClient.Model);
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

        
    }
}