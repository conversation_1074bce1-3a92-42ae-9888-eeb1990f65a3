﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.ProductCard">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            CornerRadius="10"
            Padding="0"
            BackgroundColor="{StaticResource bg_purple}">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onCardTapped"/>
            </Frame.GestureRecognizers>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="6*"/>
                    <RowDefinition Height="2.5*"/>
                    <RowDefinition Height="1.5*"/>
                </Grid.RowDefinitions>
                
                
                
                <Image 
                    Grid.Row="0"
                    Aspect="Fill"
                    Source="{Binding Source={x:Reference this},Path=Model.ImgPath}"/>
                <Label 
                    x:Name="lettersLabel"
                    Grid.Row="0"
                    FontSize="65"
                    TextTransform="None"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"/>






                <Label 
                    Text="{Binding Source={x:Reference this},Path=Model.Title}"
                    Grid.Row="1"
                    Margin="3,0,3,3"
                    FontSize="14"
                    MaxLines="2"
                    LineBreakMode="{OnPlatform Default=TailTruncation, WPF=TailTruncation}"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    VerticalOptions="Center"/>
                <Label 
                    x:Name="priceLabel"
                    Grid.Row="2"
                    Margin="0,0,0,0"
                    Text="0 р"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"/>
            </Grid>
        </Frame>
  </ContentView.Content>
</ContentView>