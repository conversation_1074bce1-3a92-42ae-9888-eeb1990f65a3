﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.OrderDivisionItemTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="50">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary>
                    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        Background="{StaticResource dark_purple}"
        CornerRadius="6"
        MouseDown="onOrderItemTapped"
        Padding="0">
        <StackPanel>
            <Grid
                VerticalAlignment="Top"
                Height="30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1"/>
                    <ColumnDefinition Width="130"/>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="60"/>
                </Grid.ColumnDefinitions>

                <Border
                    Visibility="{Binding ElementName=this,Path=IsSelected,Converter={StaticResource BooleanToVisibilityConverter}}"
                    Background="Transparent"
                    Grid.Column="0"
                    Padding="0"
                    MouseDown="onDeleteBtnTapped">
                    <Image                    
                        Width="15"
                        Height="15"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Source="pack://application:,,,/Resources/Images/close_white.png"/>
                </Border>


                <TextBlock 
                    Grid.Column="1"
                    Margin="5,0,0,0"
                    Text="{Binding ElementName=this,Path=ItemTitle}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    FontSize="14"
                    Foreground="White"/>



                <Grid
                    Margin="-20,0,0,0"
                    HorizontalAlignment="Left"
                    Grid.Column="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="15"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="15"/>
                    </Grid.ColumnDefinitions>

                    <Border
                        Grid.Column="0"
                        Visibility="{Binding ElementName=this,Path=IsSelected,Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="0,0,0,0"
                        Background="#F6F6FB"
                        Padding="0"
                        CornerRadius="7"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Height="15"
                        Width="15"
                        MouseDown="DecrementAmount">
                        <Image 
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Source="pack://application:,,,/Resources/Images/minus.png"
                            Width="10"     
                            Height="10"/>
                    </Border>

                    <TextBlock 
                        x:Name="amountLabel"
                        Grid.Column="1"
                        FontSize="14"
                        Foreground="White"
                        Text=""
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"/>

                    <Border
                        Grid.Column="2"
                        Visibility="{Binding ElementName=this,Path=IsSelected,Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="0,0,0,0"
                        Background="#7265FB"
                        Padding="0"
                        CornerRadius="7"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Height="15"
                        Width="15"
                        MouseDown="IncrementAmount">
                        <Image 
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Source="pack://application:,,,/Resources/Images/plus.png"
                            Width="10"
                            Height="10"/>
                    </Border>

                </Grid>

                <TextBlock 
                    Grid.Column="3"
                    FontSize="14"
                    Foreground="White"
                    Text="{Binding ElementName=this,Path=Model.Price}"
                    Margin="-8,0,0,0"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"/>
                <TextBlock 
                    Grid.Column="4"
                    FontSize="14"
                    Foreground="White"
                    Text="{Binding ElementName=this,Path=Model.Total}"
                    VerticalAlignment="Center"
                    Margin="-8,0,0,0"
                    HorizontalAlignment="Left"/>
            </Grid>

            <StackPanel 
                    Margin="30,0,0,0"
                    x:Name="modifiersLayout">



            </StackPanel>


        </StackPanel>
    </Border>
</UserControl>
