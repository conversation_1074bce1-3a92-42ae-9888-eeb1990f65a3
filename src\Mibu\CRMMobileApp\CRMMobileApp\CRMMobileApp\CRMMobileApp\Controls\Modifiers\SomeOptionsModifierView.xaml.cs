﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Modifiers
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SomeOptionsModifierView : ContentView
    {
        public SomeOptionsModifierView(OrderItemModifier itemModifier)
        {
            Model = itemModifier;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                if (!itemModifier.Modifier.IsRequired)
                    optionalModifierCheckBox.IsVisible = true;

                RenderOptions();
            });
        }

        private void RenderOptions()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                foreach (var option in Model.AllOptions)
                {
                    var optionView = new SomeOptionsModifierOptionItem(option)
                    {
                        HeightRequest = 40
                    };
                    modifiersLayout.Children.Add(optionView);
                }
                this.HeightRequest = Model.AllOptions.Count * 40 + 60;
            });
        }


        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(OrderItemModifier), typeof(SomeOptionsModifierView));
        public OrderItemModifier Model
        {
            get { return (OrderItemModifier)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}