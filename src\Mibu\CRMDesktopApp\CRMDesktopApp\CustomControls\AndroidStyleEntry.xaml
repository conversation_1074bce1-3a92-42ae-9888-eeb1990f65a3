﻿<abstractions:BaseEntry
             xmlns:abstractions="clr-namespace:CRMDesktopApp.CustomControls.Abstractions"
             x:Class="CRMDesktopApp.CustomControls.AndroidStyleEntry"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="35" 
             d:DesignWidth="200">
    <StackPanel>
        <Grid>

            <TextBlock x:Name="PlaceHolderLabel"
                VerticalAlignment="Bottom"
                Height="{Binding ElementName=this,Path=Height}"
                FontFamily="{Binding ElementName=this,Path=FontFamily}"
                FontSize="{Binding ElementName=this,Path=PlaceholderFontSize}"
                HorizontalAlignment="{Binding ElementName=this,Path=PlaceholderHorizontalOptions}"
                Margin="{Binding PlaceholderMargin,ElementName=this}"      
                Foreground="{Binding PlaceholderColor,ElementName=this}"
                Text="{Binding Placeholder,ElementName=this}" />

            <TextBox
                BorderThickness="0"
                Background="Transparent"  
                VerticalAlignment="Bottom"
                Height="{Binding ElementName=this,Path=Height}"
                IsReadOnly="{Binding ElementName=this,Path=IsReadOnly}"
                FontFamily="{Binding ElementName=this,Path=FontFamily}"
                FontSize="{Binding ElementName=this,Path=TextFontSize}"
                HorizontalContentAlignment="{Binding ElementName=this,Path=HorizontalTextAlignment}"
                TextAlignment="{Binding ElementName=this,Path=HorizontalTextAlignment}"
                x:Name="TextBox"                         
                Text="{Binding Text,ElementName=this,Mode=TwoWay}"
                Foreground="{Binding TextColor,ElementName=this,Mode=TwoWay}"
                Margin="{Binding TextMargin,ElementName=this}"                            
                KeyDown="onKeyDown"
                TextChanged="OnTextChanged" >
            </TextBox>

            <Rectangle
                StrokeThickness="0"
                Fill="{Binding ElementName=this,Path=BorderColor}"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Bottom"
                Height="1"/>
        </Grid>
    </StackPanel>
</abstractions:BaseEntry>
