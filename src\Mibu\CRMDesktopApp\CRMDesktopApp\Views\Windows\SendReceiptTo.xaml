﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"   
        x:Class="CRMDesktopApp.Views.Windows.SendReceiptTo"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Отправка чека"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="400"
        Height="250">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Background="White"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Width="390"
        Height="245"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="25">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="70"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <StackPanel
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center">
                    <TextBlock
                        Foreground="{StaticResource dark_purple}"
                        FontSize="16"
                        HorizontalAlignment="Center"
                        Text="Отправить чек на телефон"/>
                    <TextBlock 
                        Foreground="{StaticResource dark_purple}"
                        FontSize="15"
                        HorizontalAlignment="Center"
                        Text=" или электронную почту"/>
                    <TextBlock 
                        Margin="0,5,0,0"
                        Foreground="{StaticResource dark_purple}"
                        FontSize="10"
                        HorizontalAlignment="Center"
                        Text="Заполнение обоих полей не обязательно"/>
                </StackPanel>
            </Grid>


            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="265*"/>
                    <ColumnDefinition Width="123*"/>
                </Grid.ColumnDefinitions>
                <StackPanel 
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left" Grid.ColumnSpan="2" Margin="74,0,0,0">
                    <controls:EntryOutlined   
                        Margin="20,10,20,0"
                        PlaceholderMargin="20,0,0,0"
                        TextMargin="20,0,0,0"
                        Placeholder="E-mail"
                        Width="200"
                        Height="35"
                        HorizontalAlignment="Center"
                        Style="{StaticResource white_cornered_entry}"/>
                    <controls:EntryOutlined    
                        Margin="20,10,20,0"
                        PlaceholderMargin="20,0,0,0"
                        TextMargin="20,0,0,0"
                        Placeholder="Телефон"
                        HorizontalAlignment="Center"
                        Width="200"
                        Height="35"
                        Style="{StaticResource white_cornered_entry}"/>
                </StackPanel>
            </Grid>

            <Grid Grid.Row="2">
                <StackPanel
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Margin="0,0,0,30"
                    Orientation="Horizontal">

                    <Button 
                        Margin="13,0,0,0"
                        Style="{StaticResource bg_purple_btn}"
                        VerticalAlignment="Center"
                        Content="Назад"
                        Width="170"
                        Height="40"/>
                    <Button 
                        Margin="13,0,0,0"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Center"
                        Content="Отправить"
                        Width="170"
                        Height="40"/>

                </StackPanel>
            </Grid>
        </Grid>



    </Border>
</abstactions:BaseWindow>
