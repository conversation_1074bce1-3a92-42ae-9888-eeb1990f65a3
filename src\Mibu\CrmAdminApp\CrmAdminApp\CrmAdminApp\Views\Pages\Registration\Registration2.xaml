﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:custom="clr-namespace:CRMAdminMoblieApp.Custom"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Registration2">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid  Background="#ffffff">
            <Image 
                Aspect="Fill"
                Source="main_decor.png"/>

            <ImageButton 
                Source="arrowBack.png"
                WidthRequest="12"
                HeightRequest="28"
                Margin="30,40,0,0"
                BackgroundColor="Transparent"
                HorizontalOptions="Start"
                VerticalOptions="Start"/>

            <StackLayout
                HorizontalOptions="Center"
                Margin="0,150,0,0">

                <Image 
                    WidthRequest="240"
                    HeightRequest="140"
                    Source="logo.png"/>

                <StackLayout
                    HorizontalOptions="Center">

                    <StackLayout Spacing="20">
                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="Имя"/>
                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="Фамилия"/>
                        <Grid HeightRequest="40">
                            <Entry 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Дата рождения"/>
                            <ImageButton 
                                Source="calendarViolet.png"
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,10,0"
                                WidthRequest="18"
                                HeightRequest="18"
                                BackgroundColor="Transparent"/>
                        </Grid>
                        <Grid HeightRequest="40">
                            <Entry 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                PlaceholderColor="{x:StaticResource text_gray}"
                                Placeholder="Должность"/>
                            <ImageButton 
                                Source="arrowDown.png"
                                VerticalOptions="Center"
                                HorizontalOptions="End"
                                Margin="0,0,10,0"
                                WidthRequest="10"
                                HeightRequest="5"
                                BackgroundColor="Transparent"/>
                        </Grid>
                        <custom:AndroidStyleEntry  
                            FontFamily="TTFirsNeue-Regular"
                            TextFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderFontSize="{OnPlatform Android=17,iOS=14}"
                            PlaceholderColor="{x:StaticResource text_gray}"
                            Placeholder="Ответственный за точку"/>
                    </StackLayout>


                    <StackLayout>

                        <Button 
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            Text="Далее"
                            Style="{x:StaticResource purple_gradient_btn}"
                            WidthRequest="240"
                            HeightRequest="40"
                            Margin="0,30,0,0"/>

                    </StackLayout> 
                    
                  

                    
                </StackLayout>

            </StackLayout>

        </Grid>
    </ContentPage.Content>

</ContentPage>
