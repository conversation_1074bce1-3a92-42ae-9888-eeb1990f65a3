﻿<?xml version="1.0" encoding="UTF-8"?>
<abstractions:AbsTable
    xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions"
    xmlns="http://xamarin.com/schemas/2014/forms" 
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Name="this"
    x:Class="CRMMobileApp.Controls.Tables.SquareTable">
  <ContentView.Content>
    <Frame 
        x:Name="frame"
        HasShadow="False"
        Padding="0"
        BackgroundColor="#AFADC5">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onCardTapped"/>
            </Frame.GestureRecognizers>
            <Grid>
                <Label
                   x:Name="titleLabel"
                   HorizontalOptions="Center"
                   VerticalOptions="Center"
                   TextColor="#524E7D"
                   Text="{Binding Source={x:Reference this},Path=Table.Title}"
                   FontSize="20" />

                <Label
                   Margin="5,0,0,0"
                   x:Name="ordersCountLabel"
                   HorizontalOptions="Start"
                   VerticalOptions="End"
                   TextColor="#AFADC5"
                   FontSize="14" />

                <Label      
                   Margin="0,0,5,0"
                   x:Name="ordersSumLabel"
                   HorizontalOptions="End"
                   VerticalOptions="End"
                   TextColor="#AFADC5"
                   FontSize="14" />
            </Grid>
        </Frame>
  </ContentView.Content>
</abstractions:AbsTable>