﻿using CRM.Models.Network;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Devices
{
    /// <summary>
    /// Логика взаимодействия для PosTerminalCard.xaml
    /// </summary>
    public partial class PosTerminalCard : UserControl
    {
        public PosTerminalCard()
        {
            InitializeComponent();
        }
        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(POSTerminal), typeof(PosTerminalCard));
        public POSTerminal Model
        {
            get { return (POSTerminal)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
