﻿using CRM.Database.Core;
using CRM.Models;
using CRM.Models.Network;
using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Network.ReferenceBooks;
using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Network.Settings;
using CRM.Models.Reports.Mobile.Models.Halls;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings;
using CRM.Models.Stores.Settings.Equipment;
using CRM.Models.Stores.Settings.Tables;
using CRMWeb.MobileAPI.Abstractions;
using CRMWeb.MobileAPI.Helpers;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Numerics;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaCommonController : AbsController
    {
        public BaristaCommonController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        #region Скидки, программы лояльности
        [HttpGet, Route("GetDiscounts")]
        public List<Discount> GetDiscounts(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Discounts).ThenInclude(o => o.MenuCategories)
                        .Include(o => o.Discounts).ThenInclude(o => o.TechnicalCards)
                        .Include(o => o.Discounts).ThenInclude(o => o.MenuCategories)
                        .Include(o => o.Discounts).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.Discounts.Where(o => o.IsActive).Where(o => !o.IsDeleted).ToList();
                }
                catch { return new List<Discount>(); }
            }
        }
        [HttpGet, Route("GetDiscountsSchedules")]
        public List<DiscountSchedule> GetDiscountsSchedules(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.DiscountSchedules).ThenInclude(o => o.Discount)
                        .Include(o => o.DiscountSchedules).ThenInclude(o => o.WeekDaysStatuses)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.DiscountSchedules.Where(o => !o.IsDeleted).ToList();
                }
                catch { return new List<DiscountSchedule>(); }
            }
        }
      
        [HttpGet,Route("GetLoyaltyPrograms")]
        public List<LoyaltyProgram> GetLoyaltyPrograms(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.LoyaltyPrograms)
                        .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                        .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.LoyaltyPrograms.Where(o => !o.IsDeleted).ToList();
                }
                catch { return new List<LoyaltyProgram>(); }
            }
        }

        [HttpGet, Route("GetDefaultLoyaltyProgram")]
        public LoyaltyProgram GetDefaultLoyaltyProgram(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.LoyaltyPrograms)
                        .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                        .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                        .FirstOrDefault(o => o.Id == networkId);

                    return network.LoyaltyPrograms.FirstOrDefault(o => o.IsDefaultProgram && !o.IsDeleted);
                }
                catch { return null; }
            }
        }
        #endregion

        #region MenuCategories
        [HttpGet, Route("GetMenuCategories")]
        public List<CRM.Models.Network.MenuCategory> GetMenuCategories(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.MenuCategories).ThenInclude(o => o.Category)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.Fiscalization)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);
                    var categories = network.MenuCategories.Where(o => !o.IsDeleted && o.IsActive).ToList();
                    foreach(var cat in categories)
                    {
                        cat.ImgPath = BasePaths.GetAbsoluteWebPath(cat.ImgPath);
                    }

                    return categories;
                }
                catch { return new List<CRM.Models.Network.MenuCategory>(); }
            }
        }

        [HttpGet, Route("GetMenuCategoriesTree")]
        public MenuCategoriesTree GetMenuCategoriesTree(string domain, int networkId, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.MenuCategories).ThenInclude(o => o.Category)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.Fiscalization)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.Workshop)
                        .Include(o => o.MenuCategories).ThenInclude(o => o.StoreVisibilities)
                        .FirstOrDefault(o => o.Id == networkId);

                    var categories = network.MenuCategories.Where(o => !o.IsDeleted && o.IsActive).ToList();
                    foreach (var cat in categories)
                    {
                        cat.ImgPath = BasePaths.GetAbsoluteWebPath(cat.ImgPath);
                    }


                    //Убираем, если указаны настройки видимости точек
                    for (int i=categories.Count-1;i>-1;i--)
                    {
                        var cat = categories[i];
                        if (cat.StoreVisibilities.Count > 0
                            && !cat.StoreVisibilities.Any(o => o.StoreId == storeId && o.IsVisible))
                        {
                            categories.RemoveAt(i);
                        }
                    }


                    var root = categories.FirstOrDefault(o => o.IsDefault);

                    var tree = new MenuCategoriesTree();
                    var item = new MenuCategoriesTreeItem()
                    {
                        Category = root
                    };
                    FillItemRecursively(categories, item);
                    tree.Nodes.Add(item);
                    return tree;
                }
                catch { return new MenuCategoriesTree(); }
            }
        }


        private IEnumerable<MenuCategory> GetChilds(List<MenuCategory> allCats, MenuCategory parent) 
        {
            return allCats.Where(o => o.CategoryId == parent.Id);
        }

        private void FillItemRecursively(List<MenuCategory> allCats, MenuCategoriesTreeItem parent)
        {
            var childs = GetChilds(allCats,parent.Category);
            foreach(var child in childs)
            {
                var node = new MenuCategoriesTreeItem()
                {
                    Category = child
                };
                FillItemRecursively(allCats, node);
                parent.ChildNodes.Add(node);
            }
        }
        #endregion

        #region Залы, столы (если режим - кафе)

        [HttpGet, Route("GetHalls")]
        public List<Hall> GetHalls(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                         .Include(o => o.Halls).ThenInclude(o => o.Tables)
                         .FirstOrDefault(o => o.Id == storeId);

                    store.Halls = store.Halls.Where(o => !o.IsDeleted).ToList();
                    foreach(var hall in store.Halls)
                    {
                        hall.Tables = hall.Tables.Where(o => !o.IsDeleted).ToList();
                    }
                    return store.Halls;
                }
                catch { return new List<Hall>(); }
            }
        }

        [HttpGet, Route("GetHallsWithOrders")]
        public List<HallWithOrders> GetHallsWithOrders(string domain, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var hallsWithOrders = new List<HallWithOrders>();

                    var store = db.Stores
                         .Include(o => o.Halls).ThenInclude(o => o.Tables)
                         .FirstOrDefault(o => o.Id == storeId);

                    foreach (var hall in store.Halls.Where(o => !o.IsDeleted))
                    {
                        var hallWithOrders = new HallWithOrders()
                        {
                            Hall = hall
                        };

                        foreach(var table in hall.Tables.Where(o => !o.IsDeleted))
                        {
                            hallWithOrders.Tables.Add(new TableWithOrders
                            {
                                Table = table,
                                Orders = GetTableCurrentOrders(domain, storeId, dutyId, table.Id)
                            });
                        }

                        hallsWithOrders.Add(hallWithOrders);
                    }

                    return hallsWithOrders;
                }
                catch { return new List<HallWithOrders>(); }
            }
        }


        [HttpGet, Route("GetTableCurrentOrders")]
        public List<Order> GetTableCurrentOrders(string domain, int storeId,int dutyId, int tableId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var orders = BaristaOrdersController.GetOrdersStatic(domain, storeId)
                            .Where(o => o.DutyId == dutyId)
                            .Where(o => o.ClosedAt == null)
                            .Where(o => !o.IsCanceled);
                    return orders.Where(o => o.OrderEnvironment?.TableId == tableId).ToList();
                }
                catch { return new List<Order>(); }
            }
        }
        #endregion


        #region Продукты и техкарты
        [HttpGet, Route("GetProducts")]
        public List<Product> GetProducts(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Products).ThenInclude(o => o.MeasureUnit)
                        .Include(o => o.Products).ThenInclude(o => o.IngridientCategory)
                        .Include(o => o.Products).ThenInclude(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Products).ThenInclude(o => o.ProductPackagings)
                        .FirstOrDefault(o => o.Id == networkId);
                    var products = network.Products.Where(o => !o.IsDeleted && o.IsActive).ToList();
                    foreach(var product in products)
                    {
                        product.ImgPath = BasePaths.GetAbsoluteWebPath(product.ImgPath);
                    } 
                    return products;
                }
                catch { return new List<Product>(); }
            }
        }
        [HttpGet, Route("GetTechnicalCards")]
        public List<TechnicalCard> GetTechnicalCards(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.TechnicalCards).ThenInclude(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.TechnicalCards).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient).ThenInclude(o => o.Category)
                        .Include(o => o.TechnicalCards).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                        .FirstOrDefault(o => o.Id == networkId);

                    var cards = network.TechnicalCards.Where(o => !o.IsDeleted && o.IsActive).ToList();
                    foreach (var card in cards)
                    {
                        card.ImgPath = BasePaths.GetAbsoluteWebPath(card.ImgPath);
                    }
                    return cards;
                }
                catch { return new List<TechnicalCard>(); }
            }
        }
        #endregion


        #region Устройства

        [HttpGet, Route("GetPrinters")]
        public async Task<List<Printer>> GetPrinters(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Printers).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.Printers.Where(o => !o.IsDeleted).ToList();
                }
                catch { return new List<Printer>(); }
            }
        }



        [HttpGet, Route("GetFiscalRegistrators")]
        public async Task<List<FiscalRegistrator>> GetFiscalRegistrators(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.FiscalRegistrators).ThenInclude(o => o.LegalEntity)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.FiscalRegistrators.Where(o => !o.IsDeleted).ToList();
                }
                catch { return new List<FiscalRegistrator>(); }
            }
        }


        [HttpGet, Route("GetScales")]
        public async Task<List<Scales>> GetScales(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Scales)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.Scales.Where(o => o.IsActive && !o.IsDeleted).ToList();
                }
                catch { return new List<Scales>(); }
            }
        }


        [HttpGet, Route("GetPOSTerminals")]
        public async Task<List<POSTerminal>> GetPOSTerminals(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.POSTerminals)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.POSTerminals.Where(o => !o.IsDeleted).ToList();
                }
                catch { return new List<POSTerminal>(); }
            }
        }



        [HttpGet, Route("GetAcquirings")]
        public async Task<List<Acquiring>> GetAcquirings(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Acquirings)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.Acquirings.Where(o => o.IsActive && !o.IsDeleted).ToList();
                }
                catch { return new List<Acquiring>(); }
            }
        }
        #endregion


        #region Настройки

        #region Сеть
        [HttpGet, Route("GetNetworkSettings")]
        public NetworkSettings GetNetworkSettings(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.NetworkSettings)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.NetworkSettings;
                }
                catch { return null; }
            }
        }
        [HttpGet, Route("GetNetworkSettingSafety")]
        public SettingSafety GetNetworkSettingSafety(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.SettingSafety)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.SettingSafety;
                }
                catch { return null; }
            }
        }
        #endregion

        #region Точка
        [HttpGet, Route("GetClientsAndBonusesStoreSettings")]
        public ClientsAndBonusesStoreSettings GetClientsAndBonusesStoreSettings(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.ClientsAndBonusesStoreSettings)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.ClientsAndBonusesStoreSettings;
                }
                catch { return null; }
            }
        }
        [HttpGet, Route("GetCommonStoreSettings")]
        public CommonStoreSettings GetCommonStoreSettings(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.CommonStoreSettings)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.CommonStoreSettings;
                }
                catch { return null; }
            }
        }
        [HttpGet, Route("GetStoreFiscalisationSettings")]
        public StoreFiscalisationSettings GetStoreFiscalisationSettings(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.StoreFiscalisationSettings)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.StoreFiscalisationSettings;
                }
                catch { return null; }
            }
        }
        [HttpGet, Route("GetStorePrintSettings")]
        public StorePrintSettings GetStorePrintSettings(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.StorePrintSettings)
                        .FirstOrDefault(o => o.Id == storeId);
                    return store.StorePrintSettings;
                }
                catch { return null; }
            }
        }
        #endregion


        #endregion



        [HttpGet, Route("GetPaymentTypes")]
        public List<PaymentType> GetPaymentTypes(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.PaymentTypes)
                        .FirstOrDefault(o => o.Id == networkId);
                    var types = network.PaymentTypes.Where(o => o.IsActive && !o.IsDeleted).ToList();
                    foreach(var type in types)
                    {
                        type.ImgPath = BasePaths.GetAbsoluteWebPath(type.ImgPath);
                    }
                    return types;
                }
                catch { return new List<PaymentType>(); }
            }
        }


        #region Получение доступных модификаторов

        [HttpGet, Route("GetAvailableModifiersForProduct")]
        public List<Modifier> GetAvailableModifiersForProduct(string domain, int networkId,int productId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Modifiers).ThenInclude(o => o.AvailableForProducts)
                        .Include(o => o.Modifiers).ThenInclude(o => o.AvailableForTechnicalCards)
                        .Include(o => o.Modifiers).ThenInclude(o => o.Options).ThenInclude(o => o.MaxCount)
                        .Include(o => o.Modifiers).ThenInclude(o => o.Options).ThenInclude(o => o.Ingridients)
                        .Include(o => o.Modifiers).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Modifiers).ThenInclude(o => o.LegalEntity)
                        .Include(o => o.Modifiers).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);
                    var modifiers = network.Modifiers.Where(o => o.IsActive && !o.IsDeleted).Where(o => o.AvailableForProducts.Any(o => o.Id == productId)).ToList();
                    modifiers.ForEach(o =>
                    {
                        o.AvailableForTechnicalCards.Clear();
                        o.AvailableForProducts.Clear();
                    });
                    return modifiers;
                }
                catch (Exception ex)
                {
                    return new List<Modifier>();
                }
            }
        }
        [HttpGet, Route("GetAvailableModifiersForTechCard")]
        public List<Modifier> GetAvailableModifiersForTechCard(string domain, int networkId, int techCardId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Modifiers).ThenInclude(o => o.AvailableForProducts)
                        .Include(o => o.Modifiers).ThenInclude(o => o.AvailableForTechnicalCards)
                        .Include(o => o.Modifiers).ThenInclude(o => o.Options).ThenInclude(o => o.MaxCount)
                        .Include(o => o.Modifiers).ThenInclude(o => o.Options).ThenInclude(o => o.Ingridients)
                        .Include(o => o.Modifiers).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Modifiers).ThenInclude(o => o.LegalEntity)
                        .Include(o => o.Modifiers).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);

                    var modifiers = network.Modifiers.Where(o => o.IsActive && !o.IsDeleted).Where(o => o.AvailableForTechnicalCards.Any(o => o.Id == techCardId)).ToList();
                    modifiers.ForEach(o =>
                    {
                        o.AvailableForTechnicalCards.Clear();
                        o.AvailableForProducts.Clear();
                    });
                    return modifiers;
                }
                catch (Exception ex)
                {
                    return new List<Modifier>();
                }
            }
        }
        #endregion

        #region Получение максимально возможной скидки
        [HttpGet, Route("GetAvailableDiscountPercentForProduct")]
        public double GetAvailableDiscountPercentForProduct(string domain, int networkId, int discountId, int productId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Products).ThenInclude(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Discounts).ThenInclude(o => o.MenuCategories)
                        .Include(o => o.Discounts).ThenInclude(o => o.TechnicalCards)
                        .Include(o => o.Discounts).ThenInclude(o => o.Products)
                        .Include(o => o.Discounts).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);

                    var discount = network.Discounts.FirstOrDefault(o => o.Id == discountId);
                    var product = network.Products.FirstOrDefault(o => o.Id == productId);

                    if (!discount.IsActive) return 0;

                    if (discount.HasDiscount(product))
                        return discount.Percent;
                    return 0;

                }
                catch { return 0; }
            }
        }
        [HttpGet, Route("GetAvailableDiscountPercentForTechCard")]
        public double GetAvailableDiscountPercentForTechCard(string domain, int networkId, int discountId, int techCardId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.TechnicalCards).ThenInclude(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Discounts).ThenInclude(o => o.MenuCategories)
                        .Include(o => o.Discounts).ThenInclude(o => o.TechnicalCards)
                        .Include(o => o.Discounts).ThenInclude(o => o.Products)
                        .Include(o => o.Discounts).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);

                    var discount = network.Discounts.FirstOrDefault(o => o.Id == discountId);
                    var techCard = network.TechnicalCards.FirstOrDefault(o => o.Id == techCardId);

                    if (!discount.IsActive) return 0;

                    if (discount.HasDiscount(techCard))
                        return discount.Percent;
                    return 0;

                }
                catch { return 0; }
            }
        }
        [HttpGet, Route("GetAvailableDiscountPercentForModifier")]
        public double GetAvailableDiscountPercentForModifier(string domain, int networkId, int discountId, int modifierId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Modifiers).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Discounts).ThenInclude(o => o.MenuCategories)
                        .Include(o => o.Discounts).ThenInclude(o => o.TechnicalCards)
                        .Include(o => o.Discounts).ThenInclude(o => o.Products)
                        .Include(o => o.Discounts).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);

                    var discount = network.Discounts.FirstOrDefault(o => o.Id == discountId);
                    var modifier = network.Modifiers.FirstOrDefault(o => o.Id == modifierId);

                    if (!discount.IsActive) return 0;

                    if (modifier.MaxDiscount.HasDiscount)
                    {
                        if (discount.Percent > modifier.MaxDiscount.Percent)
                            return modifier.MaxDiscount.Percent;
                        return discount.Percent;
                    }
                    return 0;

                }
                catch { return 0; }
            }
        }

        #endregion


        #region Получение максимального возможного процента оплаты бонусами
   
        [HttpGet, Route("GetAvailableBonusesPercentForProduct")]
        public double GetAvailableBonusesPercentForProduct(string domain, int storeId, int loyaltySystemId, int productId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.ClientsAndBonusesStoreSettings)
                        .FirstOrDefault(o => o.Id == storeId);

                    var loyaltyProgram = db.LoyaltyPrograms
                        .Include(o => o.Tresholds).ThenInclude(o => o.Workshop)
                        .Include(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                        .Include(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                        .FirstOrDefault(o => o.Id == loyaltySystemId);

                    var product = db.Products
                        .Include(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .FirstOrDefault(o => o.Id == productId);


                    double percent = loyaltyProgram.GetPossiblePercent(product);
                    if (store.ClientsAndBonusesStoreSettings.MaxBonusPaymentPercent < percent)
                        percent = store.ClientsAndBonusesStoreSettings.MaxBonusPaymentPercent;

                    return percent;
                }
                catch { return 0; }
            }
        }
 
        [HttpGet, Route("GetAvailableBonusesPercentForTechCard")]
        public double GetAvailableBonusesPercentForTechCard(string domain, int storeId, int loyaltySystemId, int techCardId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.ClientsAndBonusesStoreSettings)
                        .FirstOrDefault(o => o.Id == storeId);

                    var loyaltyProgram = db.LoyaltyPrograms
                        .Include(o => o.Tresholds).ThenInclude(o => o.Workshop)
                        .Include(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                        .Include(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                        .FirstOrDefault(o => o.Id == loyaltySystemId);

                    var techCard = db.TechnicalCards
                        .Include(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .FirstOrDefault(o => o.Id == techCardId);


                    double percent = loyaltyProgram.GetPossiblePercent(techCard);
                    if (store.ClientsAndBonusesStoreSettings.MaxBonusPaymentPercent < percent)
                        percent = store.ClientsAndBonusesStoreSettings.MaxBonusPaymentPercent;

                    return percent;
                }
                catch { return 0; }
            }
        }

        #endregion
    }
}
