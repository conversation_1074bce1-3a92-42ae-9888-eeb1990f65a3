﻿using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Models;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRMMobileApp.Helpers;
using CRM.Models.Enums.Settings.Payments;
using Rg.Plugins.Popup.Extensions;
using CRMMobileApp.Views.Popups;
using CRM.Models.Network.LoyaltyProgram;
using CRMMobileApp.Models.Wrappers;
using CRM.Models.Stores;

namespace CRMMobileApp.Views.Pages.Orders
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderPrepaymentPage : ContentPage
    {
        private Order Order { get; set; }
        private NewDeliveryPopup _ownerPopup;
        public OrderPrepaymentPage(Order order, NewDeliveryPopup ownerPopup)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Order = order;
            _ownerPopup = ownerPopup;

            Load();
        }

        protected void Load()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                keyboard.ButtonTapped += Keyboard_ButtonTapped;

                var types = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetPaymentTypes(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);

                PaymentTypes = new List<PaymentTypeWrapper>();
                foreach (var type in types)
                {
                    var wrapper = new PaymentTypeWrapper();
                    wrapper.PaymentType = type;
                    wrapper.Title = type.Title;
                    wrapper.ImgPath = type.ImgPath;
                    //wrapper.OnSumChanged += Wrapper_OnSumChanged;


                    var existingPrepayment = Order.OrderPayments.FirstOrDefault(o => o.IsPrepeyment && o.PaymentTypeId == type.Id);
                    if (existingPrepayment != null)
                    {
                        wrapper.Sum = existingPrepayment.Sum;
                        wrapper.SumStr = existingPrepayment.Sum.ToString();
                    }


                    PaymentTypes.Add(wrapper);
                }


                listView.ItemsSource = PaymentTypes;
            });
        }
        public double CurrentSum => PaymentTypes.Sum(o => o.Sum);



        private PaymentTypeWrapper selectedPaymentType;
        public PaymentTypeWrapper SelectedPaymentType
        {
            get => selectedPaymentType;
            set { selectedPaymentType = value; OnPropertyChanged(nameof(SelectedPaymentType)); }
        }

        private ICommand onSelectedPaymentType;
        public ICommand OnSelectedPaymentType
        {
            get => onSelectedPaymentType ??= new RelayCommand(async obj =>
            {
                foreach (var discount in PaymentTypes)
                {
                    discount.IsSelected = false;
                }

                SelectedPaymentType = obj as PaymentTypeWrapper;
                if (SelectedPaymentType != null)
                {
                    SelectedPaymentType.IsSelected = true;
                }
            });
        }


        private List<PaymentTypeWrapper> paymentTypes;
        public List<PaymentTypeWrapper> PaymentTypes
        {
            get => paymentTypes;
            set { paymentTypes = value; OnPropertyChanged(nameof(PaymentTypes)); }
        }



        private ICommand addSumBtnPressed;
        public ICommand AddSumBtnPressed
        {
            get => addSumBtnPressed ??= new RelayCommand(async obj =>
            {
                int val = Convert.ToInt32(obj.ToString());

                var sum = SelectedPaymentType.Sum + val;
                SelectedPaymentType.SumStr = sum.ToString();

                //if (SelectedPaymentType != null)
                //{
                //    if (CurrentSum + val > Order.SumAfterPrepayments)
                //    {
                //        if (SelectedPaymentType.PaymentType?.OldPaymentType == OldPaymentType.Card)
                //            return;

                //        //Если способ оплаты - бонусы
                //        if (SelectedPaymentType.PaymentType == null)
                //            return;


                //        var sum = SelectedPaymentType.Sum + val;
                //        SelectedPaymentType.SumStr = sum.ToString();
                //    }
                //    else
                //    {
                //        //Если бонусов недостаточно
                //        if (SelectedPaymentType.PaymentType == null)
                //        {
                //            if (Order.Customer?.Bonuses < val)
                //            {
                //                return;
                //            }
                //        }

                //        var sum = SelectedPaymentType.Sum + val;
                //        SelectedPaymentType.SumStr = sum.ToString();
                //    }
                //}
            });
        }

        private void Keyboard_ButtonTapped(object sender, string e)
        {
            if (SelectedPaymentType is null) return;

            if (e == "backspace" && SelectedPaymentType.SumStr.Length > 0)
            {
                SelectedPaymentType.SumStr = SelectedPaymentType.SumStr.Remove(SelectedPaymentType.SumStr.Length - 1, 1);
            }
            if (e == "close")
            {
                SelectedPaymentType.SumStr = "0";
            }
            if (e != "backspace")
            {
                if (e == "." && SelectedPaymentType.SumStr.Contains("."))
                {
                    return;
                }


                //var sum = Convert.ToDouble(SelectedPaymentType.SumStr + e);

                //if (SelectedPaymentType.PaymentType.OldPaymentType == OldPaymentType.Card) //если оплата картои
                //{
                //    var otherPaymentsSum = PaymentTypes.Where(o => o.PaymentType?.OldPaymentType != OldPaymentType.Card).Sum(o => o.Sum);

                //    if (otherPaymentsSum + sum > Order.SumWithDiscountAfterPrepaymens) return;

                //}



                if (SelectedPaymentType.SumStr == "0")
                {
                    SelectedPaymentType.SumStr = e;
                }
                else
                {
                    SelectedPaymentType.SumStr += e;
                }

            }      
        }



        private ICommand addPrepayment;
        public ICommand AddPrepayment
        {
            get => addPrepayment ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {
                    var payments = PaymentTypes.Where(o => o.Sum > 0);
                    foreach (var payment in payments)
                    {

                        var existingPrepayment = Order.OrderPayments.FirstOrDefault(o => o.IsPrepeyment && o.PaymentTypeId == payment.PaymentType?.Id);
                        if (existingPrepayment != null)
                        {
                            existingPrepayment.Sum = payment.Sum;
                        }
                        else
                        {
                            Order.OrderPayments.Add(new CRM.Models.Stores.OrderPayment
                            {
                                PaymentType = payment.PaymentType,
                                PaymentTypeId = payment.PaymentType?.Id,
                                Sum = payment.Sum,
                                IsPrepeyment = true
                            });
                        }
                    }
                    await App.Current.MainPage.Navigation.PopAsync();
                    await App.Current.MainPage.Navigation.PushPopupAsync(_ownerPopup);
                    _ownerPopup.SetPrepaymentSumText();
                });
            });
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(async () =>
                {
                    await App.Current.MainPage.Navigation.PopAsync();
                    await App.Current.MainPage.Navigation.PushPopupAsync(_ownerPopup);
                    _ownerPopup.SetPrepaymentSumText();
                });
            });
        }
    }
}