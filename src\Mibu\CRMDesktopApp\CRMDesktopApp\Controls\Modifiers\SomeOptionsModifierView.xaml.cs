﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Modifiers
{
    /// <summary>
    /// Логика взаимодействия для SomeOptionsModifierView.xaml
    /// </summary>
    public partial class SomeOptionsModifierView : UserControl
    {
        public SomeOptionsModifierView(OrderItemModifier itemModifier)
        {
            Model = itemModifier;
            InitializeComponent();

            if (!itemModifier.Modifier.IsRequired)
                optionalModifierCheckBox.Visibility = Visibility.Visible;

            RenderOptions();
        }

        private void RenderOptions()
        {
            foreach (var option in Model.AllOptions)
            {
                var optionView = new SomeOptionsModifierOptionItem(option)
                {
                    Height = 40
                };
                modifiersLayout.Children.Add(optionView);
            }
            this.Height = Model.AllOptions.Count * 40 + 60;
        }


        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(OrderItemModifier), typeof(SomeOptionsModifierView));
        public OrderItemModifier Model
        {
            get { return (OrderItemModifier)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
