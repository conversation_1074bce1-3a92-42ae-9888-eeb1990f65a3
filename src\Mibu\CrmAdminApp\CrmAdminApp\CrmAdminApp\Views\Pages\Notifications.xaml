﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates" 
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             x:Name="this"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             x:Class="CRMAdminMoblieApp.Views.Pages.Notifications">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.Padding>
                <OnPlatform x:TypeArguments="Thickness">
                    <OnPlatform.Platforms>
                        <On Platform="iOS" Value="0, 20, 0, 0" />
                        <On Platform="Android" Value="0, 0, 0, 0" />
                    </OnPlatform.Platforms>
                </OnPlatform>
            </Grid.Padding>


            <Grid Grid.Row="0">
                <parts:Header x:Name="header"/>
            </Grid>


            <Grid Grid.Row="1">

                <StackLayout
                        Margin="25,0,0,0"
                        Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                    </StackLayout.GestureRecognizers>

                    <Image 
                            Source="arrowBack.png"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            HeightRequest="10"
                            WidthRequest="5"/>

                    <Label 
                            HorizontalOptions="End"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="14"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Уведомления"/>
                </StackLayout>



                <ImageButton
                    Margin="0,0,20,0"
                    IsVisible="False"
                    BackgroundColor="Transparent"
                    Command="{Binding Source={x:Reference this},Path=OpenNotificationSettings}"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    Source="settings.png"
                    WidthRequest="24"
                    HeightRequest="24"/>

            </Grid>

            <Grid Grid.Row="2">
                
                <ScrollView
                    VerticalOptions="Fill"
                    Margin="20,5,20,0">
                    <StackLayout 
                        Spacing="20"
                        x:Name="notificationsLayout">
                        
                    </StackLayout>
                </ScrollView>

                <Label 
                   x:Name="noItemsLabel"
                   IsVisible="True"
                   FontFamily="TTFirsNeue-Regular"
                   FontSize="14"
                   TextColor="{x:StaticResource dark_purple}"
                   VerticalOptions="Center"
                   HorizontalOptions="Center"
                   HorizontalTextAlignment="Center"
                   WidthRequest="200"
                   Text="На данный момент нет уведомлений"/>


            </Grid>
        </Grid>
    </ContentPage.Content>
</ContentPage>