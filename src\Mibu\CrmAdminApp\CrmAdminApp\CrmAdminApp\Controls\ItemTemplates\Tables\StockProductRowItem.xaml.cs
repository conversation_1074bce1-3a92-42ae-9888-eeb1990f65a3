﻿
using CRMAdminMoblieApp.Models;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRM.Models.Reports.Mobile.Admin.Warehouse;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class StockProductRowItem : ContentView
    {
        public StockProductRowItem(WarehouseReportRow row)
        {
            InitializeComponent();
            Model = row;
        }

        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(WarehouseReportRow), typeof(SaleListItem));
        public WarehouseReportRow Model
        {
            get { return (WarehouseReportRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}