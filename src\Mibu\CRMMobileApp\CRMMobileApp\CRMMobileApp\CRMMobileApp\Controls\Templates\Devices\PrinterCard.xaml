﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.Devices.PrinterCard">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            Padding="0,5,0,0"
            CornerRadius="10"
            IsClippedToBounds="True"
            BackgroundColor="{StaticResource bg_purple}">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ToggleExpand}"/>
            </Frame.GestureRecognizers>
            <Grid>
                <StackLayout>
                    <StackLayout
                        Margin="20,0,0,0"
                        Spacing="20"
                        Orientation="Horizontal"
                        HorizontalOptions="Start"
                        VerticalOptions="Center">
                        <Image
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            HeightRequest="25"
                            WidthRequest="25"
                            Source="{OnPlatform Default=printer.png, WPF='pack://application:,,,/Images/printer.png'}"/>
                        <StackLayout
                            Spacing="0"
                            VerticalOptions="Center">
                            <StackLayout 
                                VerticalOptions="Start"
                                Orientation="Horizontal">
                                <Frame
                                    x:Name="connectionCircleFrame"
                                    BackgroundColor="Gray"
                                    Padding="0"
                                    HasShadow="False"
                                    CornerRadius="4"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="8"
                                    WidthRequest="8"/>
                                <Label 
                                    FontSize="16"
                                    VerticalOptions="Center"
                                    TextColor="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    Text="{Binding Source={x:Reference this},Path=Model.Name}"/>
                            </StackLayout>

                            <Label
                                FontSize="16"
                                VerticalOptions="Start"
                                TextColor="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                Text="{Binding Source={x:Reference this},Path=Model.Path}"/>
                        </StackLayout>
                    </StackLayout>

                    <StackLayout
                        Spacing="6"
                        IsVisible="False"
                        x:Name="menuLayout"
                        HorizontalOptions="Center">
                        <Button 
                            Command="{Binding Source={x:Reference this},Path=CheckConnection}"
                            Text="Проверка связи"
                            HorizontalOptions="Center"
                            Style="{StaticResource transparent_btn}"/>
                    </StackLayout>

                </StackLayout>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>