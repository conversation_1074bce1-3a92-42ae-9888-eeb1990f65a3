﻿using CRM.Models.Network.LoyaltyProgram;
using CRMMobileApp.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для ClientListItem.xaml
    /// </summary>
    public partial class ClientListItem : UserControl
    {
        public ClientListItem()
        {
            InitializeComponent();
        }

        public static readonly DependencyProperty ModelProperty =
           DependencyProperty.Register(nameof(Model), typeof(SelectionWrapper<Customer>), typeof(ClientListItem));
        public SelectionWrapper<Customer> Model
        {
            get { return (SelectionWrapper<Customer>)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
