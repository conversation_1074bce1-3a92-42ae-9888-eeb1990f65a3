﻿using CRM.Models.Network.LoyaltyProgram;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace CRMMobileApp.Models.Wrappers
{
    public class SelectionWrapper<T> : BindableObject
    {
        public SelectionWrapper(T item)
        {
            Model = item;
        }

        private T model;
        public T Model
        {
            get => model;
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }


        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
    }
}
