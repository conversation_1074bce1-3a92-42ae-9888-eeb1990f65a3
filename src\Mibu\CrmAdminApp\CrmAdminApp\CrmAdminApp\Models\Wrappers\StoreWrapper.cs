﻿using CRM.Models;
using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace CRMAdminMoblieApp.Models.Wrappers
{
    public class StoreWrapper : BindableObject
    {
        public Store Store { get; set; }

        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
    }
}
