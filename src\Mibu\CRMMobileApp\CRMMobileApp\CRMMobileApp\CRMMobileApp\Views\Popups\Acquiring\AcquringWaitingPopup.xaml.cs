﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AcquringWaitingPopup : PopupPage
    {

        public AcquringWaitingPopup(Acquiring acquiring, double sumByCard)
        {
            InitializeComponent();
            Sum = sumByCard;
            Acquiring = acquiring;
        }

        private Acquiring acquiring;
        public Acquiring Acquiring
        {
            get => acquiring;
            set { acquiring = value; OnPropertyChanged(nameof(Acquiring)); }
        }
        private double sum;
        public double Sum
        {
            get => sum;
            set { sum = value; OnPropertyChanged(nameof(Sum)); }
        }
        

        private async Task OnGettingSignal()
        {
            //TODO:  Короче когда будет получен сигнал от эквайринга. Пока не реализовано
            await CloseOrder();
        }

        private async Task CloseOrder()
        { 
            var payOrderPage = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as CreateOrder;
            await payOrderPage.CloseOrder();
        }



        private ICommand continueWithoutAquiring;
        public ICommand ContinueWithoutAquiring
        {
            get => continueWithoutAquiring ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                    CloseOrder();
                });
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
    }
}