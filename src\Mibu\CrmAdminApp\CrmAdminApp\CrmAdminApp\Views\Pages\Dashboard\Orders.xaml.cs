﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Abstractions;
using CRMAdminMoblieApp.Controls.ItemTemplates;
using CRMAdminMoblieApp.Enums;
using CRMAdminMoblieApp.Helpers;
using CRMMoblieApiWrapper;
using Sharpnado.CollectionView.RenderedViews;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Pages.Dashboard
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Orders : BaseDashboardPage
    {
        public override DashboardPageType PageType => DashboardPageType.Orders;
        public Orders() : base()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
			//Load();

			DutyLabel = dutyLabel;
		}




        public override async Task RefreshData()
        {

            Task.Run(async () =>
            {
				var orders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrdersByDuty(ApplicationState.CurrentDomain,
																			                     ApplicationState.CurrentStore.Id,
																			                     ApplicationState.CurrentDuty.Id);
				OrdersList = new ObservableCollection<Order>(orders);

                await Device.InvokeOnMainThreadAsync(() =>
                {
                    noItemsLabel.IsVisible = OrdersList.Count == 0;
                    RenderOrderControls();
                });
            });
        }

		public bool IsLoaded { get; private set; }
		public async Task Load()
        {
			if (IsLoaded) return;


			IconImageSource = ImageSource.FromFile("orders.png");
			Title = "Заказы";

			OnDutyChanged(null, ApplicationState.CurrentDuty);

			//await RefreshData();

			IsLoaded = true;
		}

        private ObservableCollection<Order> ordersList = new ObservableCollection<Order>();
        public ObservableCollection<Order> OrdersList
        {
            get => ordersList;
            set { ordersList = value; OnPropertyChanged(nameof(OrdersList)); }
        }

        private void RenderOrderControls()
        {
            collectionView.ItemsSource = OrdersList;
        }
    }
}