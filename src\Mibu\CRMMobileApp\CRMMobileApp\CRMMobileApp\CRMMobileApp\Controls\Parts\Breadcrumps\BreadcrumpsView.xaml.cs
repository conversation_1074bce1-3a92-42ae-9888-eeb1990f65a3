﻿using CRM.Models.Network;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Parts.Breadcrumps
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class BreadcrumpsView : ContentView
    {
        public BreadcrumpsView()
        {
            InitializeComponent();
        }


        public event EventHandler<MenuCategoriesTreeItem> CategoryTapped;
        private List<BreadcrumpPart> Parts = new List<BreadcrumpPart>();
        public void SetRootCategory(MenuCategoriesTreeItem category)
        {
            layout.Children.Clear();
            Parts.Clear();

            AddBreadcrumpPart(category);
        }
        public void AddLevel(MenuCategoriesTreeItem category)
        {
            if (!Parts.Any())
            {
                SetRootCategory(category);
            }
            else
            {
                Parts.LastOrDefault().SetNextItemArrowVisibility(true);
                AddBreadcrumpPart(category);
            }
        }





        private void AddBreadcrumpPart(MenuCategoriesTreeItem category)
        {
            var part = new BreadcrumpPart(category);
            part.Tapped += Part_Tapped;

            layout.Children.Add(part);
            Parts.Add(part);
        }

        private void Part_Tapped(object sender, MenuCategoriesTreeItem e)
        {
            var part = sender as BreadcrumpPart;
            int index = Parts.IndexOf(part);

            for(int i= Parts.Count-1; i>index; i--)
            {
                Parts.RemoveAt(i);
                layout.Children.RemoveAt(i);
            }

            Parts.LastOrDefault().SetNextItemArrowVisibility(false);

            CategoryTapped?.Invoke(this, e);
        }
    }
}