﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.XReportPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        mc:Ignorable="d"
        Title="X-Отчет" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="500"
        Width="600">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <abstactions:BaseWindow.Content>
        <Border
            Background="White"
            CornerRadius="20"
            Padding="0"
            VerticalAlignment="Center"
            HorizontalAlignment="Left"
            Margin="5,0,5,0"
            BorderBrush="LightGray"
            BorderThickness="1"
            Width="590"
            Height="495">
            <Border.Effect>
                <DropShadowEffect Color="LightGray"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="80"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <TextBlock
                    x:Name="tb"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    FontSize="20"
                    Text="X-Отчет"/>
                </Grid>

                <Grid Grid.Row="1">

                    <StackPanel x:Name="reportStackLayout">

                    </StackPanel>
                </Grid>

                <Grid Grid.Row="2">

                    <StackPanel
                        VerticalAlignment="Bottom"
                        HorizontalAlignment="Right"
                        Margin="0,0,30,30"
                        Orientation="Horizontal">

                        <Button 
                            Margin="20,0,0,0"
                            Command="{Binding ElementName=this,Path=CloseWindow}"
                            Style="{StaticResource bg_purple_btn}"
                            VerticalAlignment="Center"
                            Content="Закрыть"
                            Width="170"
                            Height="40"/>
                        <Button 
                            Margin="20,0,0,0"
                            Command="{Binding ElementName=this,Path=PrintReport}"
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalAlignment="Center"
                            Content="Напечатать"
                            Width="170"
                            Height="40"/>
                    </StackPanel>

                </Grid>

            </Grid>
        </Border>
    </abstactions:BaseWindow.Content>
</abstactions:BaseWindow>
