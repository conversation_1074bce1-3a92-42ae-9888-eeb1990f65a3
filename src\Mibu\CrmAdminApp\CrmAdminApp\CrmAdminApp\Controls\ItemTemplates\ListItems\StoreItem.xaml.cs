﻿using CRM.Models.Network.Finances;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using System;
using MarkupCreator.Helpers.Converters;
using CRMAdminMoblieApp.Models.Wrappers;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class StoreItem : ContentView
    {
        public StoreItem()
        {
            InitializeComponent();         
        }

        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(StoreWrapper), typeof(StoreItem));
        public StoreWrapper Model
        {
            get { return (StoreWrapper)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}