﻿using CRM.Models.Network.Finances;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using System;
using MarkupCreator.Helpers.Converters;
using CRMAdminMoblieApp.Models.Wrappers;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DutyItem : ContentView
    {
        public DutyItem()
        {
            InitializeComponent();         
        }

        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(DutyWrapper), typeof(DutyItem));
        public DutyWrapper Model
        {
            get { return (DutyWrapper)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}