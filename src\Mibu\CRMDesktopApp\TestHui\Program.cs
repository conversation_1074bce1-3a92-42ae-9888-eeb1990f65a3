﻿// See https://aka.ms/new-console-template for more information
using Fiscalization;
using System.Diagnostics;

Console.WriteLine("Hello, World!");

//var atolMartaLibPath = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "martaDLLLib.dll");
//Process.Start(new ProcessStartInfo
//{
//    UseShellExecute = true,
//    WorkingDirectory = @"C:\Windows\System32",
//    FileName = @"C:\Windows\System32\cmd.exe",
//    Verb = "runas",
//    Arguments = "/c " + $"regsvr32 {atolMartaLibPath}",
//    WindowStyle = ProcessWindowStyle.Normal
//});




//var scales = new ScalesRecognizer.ScalesRecognizer(new CRM.Models.Stores.Settings.Equipment.Scales
//{
//    ScalesModel = CRM.Models.Enums.Equipment.ScalesModel.AtolMarta,
//    PortNumber = CRM.Models.Enums.Equipment.ConnectionPortNumber.COM4,
//    IsActive = true,
//});
//var a = scales.GetWeight();


var fiscalizer = new Fiscalizer(new CRM.Models.Stores.Settings.Equipment.FiscalRegistrator
{
    TCPIP_IPAddress = "127.0.0.1",
    Connection = CRM.Models.Enums.Equipment.ConnectionType.COM,
    ConnectionPortNumber = CRM.Models.Enums.Equipment.ConnectionPortNumber.COM6,
    LegalEntity = new CRM.Models.Network.ReferenceBooks.LegalEntity
    {
        ИНН = "360410571269",
        Address = "sdfsdf",
        КПП = "13424234234",
        Title = "OOO HYI",
        TaxSystems = new List<CRM.Models.General.TaxSystem>
        {
            new CRM.Models.General.TaxSystem
            {
                Title = "fdfd",      
            }
        },
    },
    Model = CRM.Models.Enums.Equipment.FiscalRegistratorModel.Atol30F,
    Name = "Фискальник 1",
},
new CRM.Models.Network.NetworkTerminalUser
{
    Name = "dsfdfs",
    Patronymic = "sdfsdfds",
    Surname = "fdsfsdf",
    ИНН = "360410571269",
    Status= CRM.Models.Enums.TerminalUserStatus.Active,
    Role = CRM.Models.Enums.NetworkTerminalUserRole.Admin,
});

var isSuccess = fiscalizer.InitFiscalizer();
//var isOpened = fiscalizer.TryOpenConnection();
var hasConnection = fiscalizer.CheckConnection();




fiscalizer.CloseShift();
fiscalizer.OpenShift();

fiscalizer.CancelReceipt();
fiscalizer.FiscalizeOrder(new CRM.Models.Stores.Order
{
    Items = new List<CRM.Models.Stores.OrderItem>
    {
        new CRM.Models.Stores.OrderItem
        {
            TechnicalCard = new CRM.Models.Network.ReferenceBooks.Menu.TechnicalCard
            {
                Title = "fdsfds",
                Price = 200,
                MenuCategory = new CRM.Models.Network.MenuCategory
                {
                    Title = "dsfd",
                    Fiscalization = new CRM.Models.Parts.MenuCategoryFiscalization
                    {
                        CalculationSubject = CRM.Models.Enums.Info.CalculationSubject.Product,
                        Taxation = new CRM.Models.General.Taxation
                        {
                            Title = "fdsfsd",
                            Type = CRM.Models.Enums.Info.TaxationType.Упрощенная_СНО_Доход,
                        },
                        НДС = CRM.Models.Enums.Info.НДС.NO_НДС,
                    },
                }
            },
            SelectedModifiers = new List<CRM.Models.Stores.Orders.OrderItemModifier>
            {
                new CRM.Models.Stores.Orders.OrderItemModifier
                {
                    Modifier = new CRM.Models.Network.ReferenceBooks.Modifier
                    {
                        Title = "sdfds",
                        LegalEntity = null,

                    },
                    SelectedOptions = new List<CRM.Models.Stores.Orders.OrderItemModifierOption>
                    {
                        new CRM.Models.Stores.Orders.OrderItemModifierOption
                        {
                            Amount = 2,
                            Price = 222,
                            Discount = 10,
                            ModifierOption = new CRM.Models.Parts.ModifierOption
                            {
                                Title = "Член",
                                Barcode = "dsfdsfsd"
                            }
                        }
                    }
                }
            },
            ProductPrice = 222,
            Amount = 333,
            Discount = 20
        }
    },
    OrderPayments = new List<CRM.Models.Stores.OrderPayment>
    {
        new CRM.Models.Stores.OrderPayment
        {
            PaymentType = new CRM.Models.Network.Settings.PaymentType
            {
                FiscalizationType = CRM.Models.Enums.Settings.Payments.FiscalizationType.ByTerminalSettings,
                FiscalPaymentType = CRM.Models.Enums.Settings.Payments.FiscalPaymentType.Cash,
                OldPaymentType = CRM.Models.Enums.Settings.Payments.OldPaymentType.Cash,
                Title = "аНал",
            },
            Sum = 1000,
        },
          new CRM.Models.Stores.OrderPayment
        {
            PaymentType = new CRM.Models.Network.Settings.PaymentType
            {
                FiscalizationType = CRM.Models.Enums.Settings.Payments.FiscalizationType.Enabled,
                FiscalPaymentType = CRM.Models.Enums.Settings.Payments.FiscalPaymentType.Cashless,
                OldPaymentType = CRM.Models.Enums.Settings.Payments.OldPaymentType.Card,
                Title = "Карта",
            },
            Sum = 1000,
        },
    }
});



//fiscalizer.CloseReceipt();



  Console.ReadLine();