﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.OrderPaidPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Title="Заказ успешно оплачен" 
        Width="250"
        Height="250">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <customcontrols:ClippingBorder
        Background="#FFFFFF"
        Width="240"
        Height="245"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <customcontrols:ClippingBorder.Effect>
            <DropShadowEffect Color="LightGray"/>
        </customcontrols:ClippingBorder.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="90"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>
            <TextBlock 
                Grid.Row="0"
                FontSize="18"
                FontWeight="Bold"
                Foreground="{StaticResource dark_purple}"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Text="Заказ успешно оплачен"/>
            <StackPanel
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" >
                <TextBlock              
                    HorizontalAlignment="Center"
                    FontSize="20"
                    Foreground="{StaticResource dark_purple}"
                    Text="Cумма заказа"/>
                <TextBlock          
                    x:Name="sumLabel"
                    FontSize="16"
                    HorizontalAlignment="Center"
                    FontWeight="Bold"
                    Foreground="{StaticResource dark_purple}"
                    Text="140 р"/>
                <!--<Label           
                    HorizontalOptions="Center"
                    TextColor="{StaticResource dark_purple}"
                    Text="Карта"/>
                <Label        
                    HorizontalOptions="Center"
                    TextColor="{StaticResource dark_purple}"
                    Text="140 р"/>-->
            </StackPanel>
            <TextBlock 
                Grid.Row="2"
                x:Name="changeLabel"
                FontSize="20"
                FontWeight="Bold"
                Foreground="{StaticResource dark_purple}"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Text="Без сдачи"/>
            <Grid     
                Grid.Row="3">
                <Button 
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    FontSize="16"
                    FontWeight="Bold"
                    Foreground="White"
                    BorderThickness="0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Content="Закрыть"
                    Padding="5"
                    Background="{DynamicResource purple}"/>
            </Grid>
        </Grid>
    </customcontrols:ClippingBorder>
</abstactions:BaseWindow>
