﻿using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CRMMobileApp.Helpers
{
    public static class OrdersHelper
    {
        public static Order CurrentOrder { get; set; }
        public static List<Order> ActiveOrders { get; set; } = new List<Order>();




        /// <summary>
        /// Когда выбираем стол и заказ в режиме "кафе"
        /// </summary>
        public static AbsTable FromTable { get; set; }


        public static event EventHandler<Order> OnOrderItemsCountChanged;

        public static async Task GetCurrentOrder()
        {
            CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetLastOrder(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            if (CurrentOrder is null)
            {
                await OpenOrder();
            }
        }
        public static async Task GetActiveOrders()
        {
            ActiveOrders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetActiveOrders(ApplicationState.CurrentDomain,
                                                                                               ApplicationState.CurrentStore.Id,
                                                                                               ApplicationState.CurrentDuty.Id);
        }

        public static async Task CancelOrder()
        {
            CurrentOrder.ClosedAt = DateTime.Now;
            CurrentOrder.IsCanceled = true;
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.CancelOrder(ApplicationState.CurrentDomain, CurrentOrder.Id);


            if (FromTable != null)
            {
                FromTable.RemoveOrder(CurrentOrder);
            }


            CurrentOrder = null;
            onOrderItemsCountChanged();
        }
        public static async Task ReturnOrder(Order order, string reason)
        {
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.ReturnOrder(ApplicationState.CurrentDomain,
                                                                            ApplicationState.CurrentStore.Id,
                                                                           order.Id,
                                                                           reason);
            // CurrentOrder = null;
            order.WasReturned = true;
            order.ReturnReason = reason;
            onOrderItemsCountChanged();
        }

        public static async Task CancelOrderItem(OrderItem item, string reason)
        {
            CurrentOrder.CancelItem(item, Auth.User, reason);
            onOrderItemsCountChanged();
        }
        public static async Task CloseOrder()
        {
            CurrentOrder.ClosedAt = DateTime.Now;
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.CloseOrder(ApplicationState.CurrentDomain,
                                                                           ApplicationState.CurrentTradeNetwork.Id,
                                                                           ApplicationState.CurrentStore.Id,
                                                                           CurrentOrder);
            CurrentOrder = null;
            onOrderItemsCountChanged();
        }
        public static async Task OpenOrder()
        {
            CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.OpenOrder(
                                                                            ApplicationState.CurrentDomain,
                                                                            ApplicationState.CurrentStore.Id,
                                                                            ApplicationState.CurrentDuty.Id,
                                                                            Auth.User.Id);
            onOrderItemsCountChanged();
        }


        public static void SetActiveOrder(Order order)
        {
            CurrentOrder = order;
            onOrderItemsCountChanged();
        }





        public static void AddOrderItem(OrderItem orderItem)
        {
            CurrentOrder.Items.Add(orderItem);
            if (orderItem.TechnicalCard != null)
            {
                orderItem.Price = orderItem.TechnicalCard.Price;
                orderItem.ProductPrice = orderItem.Price;
            }
            else if (orderItem.Product != null)
            {
                orderItem.Price = orderItem.Product.Price;
                orderItem.ProductPrice = orderItem.Price;
            }
            foreach (var option in orderItem.SelectedModifiers.SelectMany(o => o.SelectedOptions))
            {
                orderItem.Price += option.ModifierOption.Price * option.Amount;
            }
            onOrderItemsCountChanged();
        }
        public static async Task UpdateOrderItem(OrderItem orderItem)
        {
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.UpdateOrderItem(ApplicationState.CurrentDomain,
                                                                                                 orderItem);
            onOrderItemsCountChanged(false);
        }
        public static async Task SetOrderItemModifiers(OrderItem orderItem)
        {
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.SetOrderItemModifierts(ApplicationState.CurrentDomain,
                                                                                       orderItem,
                                                                                       orderItem.SelectedModifiers);
            onOrderItemsCountChanged(false);
        }
        public static void DeleteOrderItem(OrderItem orderItem)
        {
            var item = CurrentOrder.Items.FirstOrDefault(o => o.Id == orderItem.Id);
            CurrentOrder.Items.Remove(item);
            onOrderItemsCountChanged();
        }
        public static void ClearOrderItems()
        {
            CurrentOrder.Items.Clear();
            onOrderItemsCountChanged();
        }





        public static void SetDiscount(Discount discount)
        {
            CurrentOrder.Discount = discount;
            CurrentOrder.DiscountId = discount.Id;

            onOrderItemsCountChanged();
        }
        public static void SetComment(string comment)
        {
            CurrentOrder.Comment = comment;
            onOrderItemsCountChanged();
        }
        public static void SendItemsToWork()
        {
            CurrentOrder.Items.ForEach(o => o.IsInWork = true);
            onOrderItemsCountChanged();
        }


        private static async void onOrderItemsCountChanged(bool saveOrder = true)
        {
            if (saveOrder && CurrentOrder != null)
            {
                CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.UpdateOrder(ApplicationState.CurrentDomain,
                                                                                              ApplicationState.CurrentStore.Id,
                                                                                               CurrentOrder);
            }
            else if(CurrentOrder != null)
            {
                CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrderById(ApplicationState.CurrentDomain,
                                                                                                ApplicationState.CurrentStore.Id,
                                                                                                CurrentOrder.Id);
            }
            OnOrderItemsCountChanged?.Invoke(null, CurrentOrder);
        }

        public static async Task SaveOrder()
        {
            //CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.UpdateOrder(ApplicationState.CurrentDomain,
            //                                                                            CurrentOrder);
            onOrderItemsCountChanged();
        }
    }
}
