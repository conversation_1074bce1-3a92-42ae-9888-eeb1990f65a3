﻿using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms.Internals;

namespace CRMMobileApp.Helpers
{
    public static class OrdersHelper
    {
        public static Order CurrentOrder { get; set; }
        public static List<Order> ActiveOrders { get; set; } = new List<Order>();




        /// <summary>
        /// Когда выбираем стол и заказ в режиме "кафе"
        /// </summary>
        public static AbsTable FromTable { get; set; }


        //public static event EventHandler<Order> OnOrderItemsCountChanged;
        public static event EventHandler<OrderItem> OnOrderItemDeleted;
        public static event EventHandler<OrderItem> OnOrderItemAdded;
        public static event EventHandler<OrderItem> OnOrderItemModified;
        public static event EventHandler<Order> OnOrderChanged;
        public static event EventHandler<Order> OnOrderInfoModified;

        public static async Task GetCurrentOrder()
        {
            CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetLastOrder(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            if(CurrentOrder is null && ApplicationState.CurrentDuty is not null)
            {
                await OpenOrder();
            }
        }
        public static async Task GetActiveOrders()
        {
            ActiveOrders = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetActiveOrders(ApplicationState.CurrentDomain, 
                                                                                               ApplicationState.CurrentStore.Id,
                                                                                               ApplicationState.CurrentDuty.Id);
        }

        public static async Task CancelOrder()
        {
            CurrentOrder.ClosedAt = DateTime.UtcNow;
            CurrentOrder.IsCanceled = true;
            MobileAPI.BaristaMethods.BaristaOrdersMethods.CancelOrder(ApplicationState.CurrentDomain, CurrentOrder.Id);


            if(FromTable != null)
            {
                FromTable.RemoveOrder(CurrentOrder);
            }
          

            CurrentOrder = null;

            OnOrderChanged?.Invoke(null, CurrentOrder);
        }
        public static async Task ReturnOrder(Order order,string reason)
        {          
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.ReturnOrder(ApplicationState.CurrentDomain, 
                                                                            ApplicationState.CurrentStore.Id,
                                                                            order.Id, 
                                                                            reason);
            // CurrentOrder = null;

            order.WasReturned = true;
            order.ReturnReason = reason;

            RefreshOrder();
            OnOrderChanged?.Invoke(null, CurrentOrder);
        }

        public static async Task CancelOrderItem(OrderItem item,string reason)
        {
            CurrentOrder.CancelItem(item,Auth.User, reason);
            CurrentOrder.RefreshDiscounts();

            RefreshOrder();
            OnOrderItemDeleted?.Invoke(null, item);
        }
        public static async Task CancelOrderItems(List<OrderItem> items, string reason)
        {
            foreach(var item in items)
            {
                CurrentOrder.CancelItem(item, Auth.User, reason);
            }      
            CurrentOrder.RefreshDiscounts();

            RefreshOrder();
            OnOrderChanged?.Invoke(null, CurrentOrder);
        }
        public static async Task CloseOrder()
        {
            CurrentOrder.RefreshDiscounts();
            CurrentOrder.ClosedAt = DateTime.UtcNow;
            await MobileAPI.BaristaMethods.BaristaOrdersMethods.CloseOrder(ApplicationState.CurrentDomain,
                                                                           ApplicationState.CurrentTradeNetwork.Id,
                                                                           ApplicationState.CurrentStore.Id, 
                                                                           CurrentOrder);
            CurrentOrder = null;

            OnOrderChanged?.Invoke(null, CurrentOrder);
        }
        public static async Task OpenOrder()
        {
            CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.OpenOrder(
                                                                            ApplicationState.CurrentDomain,
                                                                            ApplicationState.CurrentStore.Id,
                                                                            ApplicationState.CurrentDuty.Id,
                                                                            Auth.User.Id);
            CurrentOrder.RefreshDiscounts();

            OnOrderChanged?.Invoke(null, CurrentOrder);
        }


        public static void SetActiveOrder(Order order)
        {
            CurrentOrder = order;

            CurrentOrder.RefreshDiscounts();
            OnOrderChanged?.Invoke(null, CurrentOrder);
        }





        public static void AddOrderItem(OrderItem orderItem)
        {
            CurrentOrder.Items.Add(orderItem);
            if(orderItem.TechnicalCard != null)
            {
                orderItem.Price = orderItem.TechnicalCard.Price;
                orderItem.ProductPrice = orderItem.Price;
                orderItem.SelfCostForOne = orderItem.TechnicalCard.SelfCost;
            }
            else if (orderItem.Product != null)
            {
                orderItem.Price = orderItem.Product.Price;
                orderItem.ProductPrice = orderItem.Price;
                orderItem.SelfCostForOne = orderItem.Product.SelfCost;
            }
            foreach(var option in orderItem.SelectedModifiers.SelectMany(o => o.SelectedOptions))
            {
                orderItem.Price += option.ModifierOption.Price * option.Amount;

                option.SelfCostForOne = option.ModifierOption.Selfcost;
            }

            try
            {
                CurrentOrder.RefreshDiscounts();
                RefreshOrder();

            }
            catch (Exception ex)
            {

            }


            OnOrderItemAdded?.Invoke(null, CurrentOrder.Items.LastOrDefault());
        }
        public static async Task UpdateOrderItem(OrderItem orderItem)
        {
            CurrentOrder.RefreshDiscounts();
            orderItem = await MobileAPI.BaristaMethods.BaristaOrdersMethods.UpdateOrderItem(ApplicationState.CurrentDomain, orderItem);

         
            RefreshOrder(false);
            OnOrderItemModified?.Invoke(null, orderItem);
        }
        public static async Task SetOrderItemModifiers(OrderItem orderItem)
        {
            CurrentOrder.RefreshDiscounts();
            orderItem = await MobileAPI.BaristaMethods.BaristaOrdersMethods.SetOrderItemModifierts(ApplicationState.CurrentDomain,
                                                                                  orderItem,
                                                                                  orderItem.SelectedModifiers);
       
            RefreshOrder(false);
            OnOrderItemModified?.Invoke(null, orderItem);
        }
        public static void DeleteOrderItem(OrderItem orderItem)
        {
            var item = CurrentOrder.Items.FirstOrDefault(o => o.Id == orderItem.Id);
            CurrentOrder.Items.Remove(item);

            RefreshOrder();
            OnOrderItemDeleted?.Invoke(null, orderItem);
        }
        public static void ClearOrderItems()
        {
            CurrentOrder.Items.Clear();
            OnOrderChanged?.Invoke(null, CurrentOrder);
        }






        public static void SetDiscount(Discount discount)
        {
            CurrentOrder.Discount = discount;
            CurrentOrder.DiscountId = discount.Id;

            CurrentOrder.RefreshDiscounts();
            RefreshOrder();
            OnOrderInfoModified?.Invoke(null, CurrentOrder);
        }
        public static void SetCustomer(Customer customer)
        {
            CurrentOrder.Customer = customer;
            CurrentOrder.CustomerId = customer.Id;

            CurrentOrder.RefreshDiscounts();
            RefreshOrder();
            OnOrderInfoModified?.Invoke(null, CurrentOrder);
        }



        public static void SetComment(string comment)
        {
            CurrentOrder.Comment = comment;
            RefreshOrder();
            OnOrderInfoModified?.Invoke(null, CurrentOrder);
        }
        public static void SendItemsToWork()
        {

            foreach(var item in CurrentOrder.Items)
            {
                item.IsInWork = true;
			}

            RefreshOrder();
            OnOrderChanged?.Invoke(null, CurrentOrder);
        }


        private static async void RefreshOrder(bool saveOrder = true)
        {
            if (saveOrder && CurrentOrder != null)
            {
                CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.UpdateOrder(ApplicationState.CurrentDomain,
                                                                                               ApplicationState.CurrentStore.Id,
                                                                                               CurrentOrder);
            }
            else if (CurrentOrder != null)
            {
                CurrentOrder = await MobileAPI.BaristaMethods.BaristaOrdersMethods.GetOrderById(ApplicationState.CurrentDomain,
                                                                                                ApplicationState.CurrentStore.Id,
                                                                                                CurrentOrder.Id);
            }
        }

        public static async Task SaveOrder()
        {
            RefreshOrder();
            OnOrderChanged?.Invoke(null, CurrentOrder);
        }




    


    }
}
