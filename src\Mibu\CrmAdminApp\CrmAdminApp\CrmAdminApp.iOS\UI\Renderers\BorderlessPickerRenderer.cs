﻿using PovodogMobileApp.Custom;
using PovodogMobileApp.iOS.UI.Renderers;
using System;
using UIKit;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;


[assembly: ExportRenderer(typeof(BorderlessPicker), typeof(BorderlessPickerRenderer))]
namespace PovodogMobileApp.iOS.UI.Renderers
{
    public class BorderlessPickerRenderer : PickerRenderer
    {
        protected override void OnElementChanged(ElementChangedEventArgs<Picker> e)
        {
            base.OnElementChanged(e);

            if (Control == null)
            {
                return;
            }

            Control.Layer.BorderWidth = 0;
            Control.Background = null;
            Control.BackgroundColor = null;
            Control.BorderStyle = UITextBorderStyle.None;
        }

    }
}

