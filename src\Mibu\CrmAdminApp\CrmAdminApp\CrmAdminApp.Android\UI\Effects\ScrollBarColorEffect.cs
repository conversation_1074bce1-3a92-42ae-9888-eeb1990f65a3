﻿using Android.App;
using Android.Content;
using Android.Graphics.Drawables;
using Android.Graphics.Drawables.Shapes;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using CrmAdminApp.Droid.UI.Effects;
using Java.Lang;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xamarin.Forms;
using Xamarin.Forms.Platform.Android;




[assembly: ExportEffect(typeof(ScrollBarColorEffect), nameof(CrmAdminApp.Effects.ScrollBarColorEffect))]

namespace CrmAdminApp.Droid.UI.Effects
{
    public class ScrollBarColorEffect : PlatformEffect
    {
        protected override void OnAttached()
        {
            UpdateUI();
      
        }

        protected override void OnDetached()
        {
        }

        void UpdateUI()
        {
            try
            {
                Java.Lang.Reflect.Field mScrollCacheField = Class.FromType(typeof(Android.Views.View)).GetDeclaredField("mScrollCache");
                mScrollCacheField.Accessible = true;
                var mScrollCache = mScrollCacheField.Get(Control);
                var scrollBarField = mScrollCache.Class.GetDeclaredField("scrollBar");
                scrollBarField.Accessible = true;
                var scrollBar = scrollBarField.Get(mScrollCache);
                var method = scrollBar.Class.GetDeclaredMethod("setVerticalThumbDrawable", Class.FromType(typeof(Drawable)));
                method.Accessible = true;

                var layers = new Drawable[1];
                var shapeDrawable = new ShapeDrawable(new RectShape());
                var scrollBarColor = Color.Default;

                var effect = (CrmAdminApp.Effects.ScrollBarColorEffect)Element.Effects.FirstOrDefault(e => e is CrmAdminApp.Effects.ScrollBarColorEffect);
                if (effect != null)
                {
                    scrollBarColor = effect.ScrollBarColor;
                }

                shapeDrawable.Paint.Color = scrollBarColor.ToAndroid();
                shapeDrawable.SetIntrinsicWidth(5);

                layers[0] = shapeDrawable;
                method.Invoke(scrollBar, layers);
            }
            catch
            {

            }
        }
    }
}