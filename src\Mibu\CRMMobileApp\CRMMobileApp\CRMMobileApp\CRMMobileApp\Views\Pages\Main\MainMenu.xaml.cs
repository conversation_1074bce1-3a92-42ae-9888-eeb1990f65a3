﻿using App1;
using CRM.Models.Enums;
using CRM.Models.Network;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Abstractions;
using CRMMobileApp.Controls.Templates.Devices;
using CRMMobileApp.Core;
using CRMMobileApp.Views.Pages.Delivery;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Pages.Tables;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Mibu.Landing.Models.Enums;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Pages.Main
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class MainMenu : ContentPage
    {
        public MainMenu()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Load();
        }

        private async Task Load()
        {
            await Task.Run(async () =>
            {
                await Auth.AuthorizeInGamification();
            });

            await Device.InvokeOnMainThreadAsync(async () =>
            {
              
                gamificationFrame.IsVisible = Auth.GamificationUser != null && ApplicationState.Subscription.IsGamificationAvailable();
                deliveryFrame.IsVisible = ApplicationState.Subscription.Subscription.Type == SubscriptionType.Full;

                RenderEquipment();
            });
        }

        #region 1 tab
        private ICommand goToClosedOrders;
        public ICommand GoToClosedOrders
        {
            get => goToClosedOrders ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.ClosedOrdersWatching))
                {
                    await App.Current.MainPage.Navigation.PushAsync(new ClosedOrdersPage());
                }
            });
        }
        private ICommand goToCreateTransaction;
        public ICommand GoToCreateTransaction
        {
            get => goToCreateTransaction ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.TransactionCreating))
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new CreateTransactionPopup());
                }
            });
        }
        private ICommand showXReport;
        public ICommand ShowXReport
        {
            get => showXReport ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if(await Auth.CheckAllowance(rights.XReport))
                {
                    //await App.Current.MainPage.Navigation.PushAsync(new ClosedOrdersPage());
                    await App.Current.MainPage.Navigation.PushPopupAsync(new XReportPopup());
                }

           
            });
        }
        private ICommand showXCashierReport;
        public ICommand ShowXCashierReport
        {
            get => showXCashierReport ??= new RelayCommand(async obj =>
            {
                if(Auth.User.Role == CRM.Models.Enums.NetworkTerminalUserRole.Admin)
                {
                   // await App.Current.MainPage.Navigation.PushAsync(new ClosedOrdersPage());
                    await App.Current.MainPage.Navigation.PushPopupAsync(new XCashierReportPopup());
                }
                else
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Вы не имеете прав, чтобы совершить данную операцию", "Ошибка"));
                }
            });
        }
        private ICommand openCashBox;
        public ICommand OpenCashBox
        {
            get => openCashBox ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.CashBoxOpening))
                {
                    await MobileAPI.BaristaMethods.BaristaInteractionMethods.OpenCashBox(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
                }
            });
        }
        private ICommand closeDuty;
        public ICommand CloseDuty
        {
            get => closeDuty ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.DutyClosing))
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new DutyClosingPopup());
                }
            });
        }


        private ICommand goToDeliveriesPage;
        public ICommand GoToDeliveriesPage
        {
            get => goToDeliveriesPage ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new DeliveriesPage());
            });
        }

        private ICommand goToGamification;
        public ICommand GoToGamification
        {
            get => goToGamification ??= new RelayCommand(async obj =>
            {
				await GamificationAppActivator.ActivateApp(App.Current, ApplicationState.CurrentDomain, Auth.User);
			});
        }


        #endregion


        #region 2 tab
        private ICommand goToSalesByIngridient;
        public ICommand GoToSalesByIngridient
        {
            get => goToSalesByIngridient ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.IngridientsReportWatching))
                {
                    await App.Current.MainPage.Navigation.PushAsync(new SalesByIngridients());
                }
            });
        }
        private ICommand goToBalanceAtStock;
        public ICommand GoToBalanceAtStock
        {
            get => goToBalanceAtStock ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.BalanceAtStockReportWatching))
                {
                    await App.Current.MainPage.Navigation.PushAsync(new BalanceAtStock());
                }
            });
        }
        private ICommand goToSalesOnDuty;
        public ICommand GoToSalesOnDuty
        {
            get => goToSalesOnDuty ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.SalesReportWatching))
                {
                    await App.Current.MainPage.Navigation.PushAsync(new SalesOnDuty());
                }
            });
        }
        #endregion


        #region 3 tab
        private void RenderEquipment()
        {
            //noAcquiringItemsLabel.IsVisible = ApplicationState.StoreAcquirings.Count == 0;
            noPrinterItemsLabel.IsVisible = ApplicationState.StorePrinters.Count == 0;
            noFiscalItemsLabel.IsVisible = ApplicationState.StoreFiscalRegistrators.Count == 0;

            RenderFiscalCards();
            RenderPrinterCards();
            RenderAcquiringCards();     
        }
        
        private void RenderFiscalCards()
        {
            fiscalFlexLayout.Children.Clear();
            foreach (var item in ApplicationState.StoreFiscalRegistrators)
            {
                CashMachineCard card = new CashMachineCard(item.Key, item.Value);

                card.Margin = new Thickness(40, 20, 0, 0);
                card.HeightRequest = 60;
                card.WidthRequest = 220;
                card.HorizontalOptions = LayoutOptions.Start;
                card.VerticalOptions = LayoutOptions.Start;

                fiscalFlexLayout.Children.Add(card);
            }
        }
        private void RenderPrinterCards()
        {
            printersFlexLayout.Children.Clear();
            foreach (var item in ApplicationState.StorePrinters)
            {
                PrinterCard card = new PrinterCard(item);
               
                card.Margin = new Thickness(40, 20, 0, 0);
                card.HeightRequest = 60;
                card.WidthRequest = 220;
                card.HorizontalOptions = LayoutOptions.Start;
                card.VerticalOptions = LayoutOptions.Start;

                printersFlexLayout.Children.Add(card);
            }
        }
        private void RenderAcquiringCards()
        {
            //acquringFlexLayout.Children.Clear();
            //foreach (var item in ApplicationState.StoreAcquirings)
            //{
            //    AbsAcquiringCard card = null;
            //    switch (item.AcquiringProvider)
            //    {
            //        case AcquiringProvider.UCS_Cards:
            //            card = new UCSCardsAcquringCard(item);
            //            break;
            //        case AcquiringProvider.Sberbank:
            //            card = new SberAcquringCard(item);
            //            break;
            //        case AcquiringProvider.Ingenico:
            //            card = new IngenicoAcquringCard(item);
            //            break;
            //    }
            //    card.Margin = new Thickness(40, 20, 0, 0);
            //    card.HeightRequest = 60;
            //    card.WidthRequest = 220;
            //    card.HorizontalOptions = LayoutOptions.Start;
            //    card.VerticalOptions = LayoutOptions.Start;

            //    acquringFlexLayout.Children.Add(card);
            //}
        }

        #endregion

        private ICommand aboutSoftware;
        public ICommand AboutSoftware
        {
            get => aboutSoftware ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new AboutSoftwarePopup());
            });
        }

    }
}