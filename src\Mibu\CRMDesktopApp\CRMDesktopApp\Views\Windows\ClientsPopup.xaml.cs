﻿using CRM.Models.Enums;
using CRM.Models.Enums.Info;
using CRM.Models.Network.LoyaltyProgram;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Models.Wrappers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для ClientsPopup.xaml
    /// </summary>
    public partial class ClientsPopup : BaseWindow
    {
        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }

        private ObservableCollection<Gender> genders = new ObservableCollection<Gender>()
        {
            Gender.Male,
            Gender.Female
        };
        public ObservableCollection<Gender> Genders
        {
            get => genders;
            set { genders = value; OnPropertyChanged(nameof(Genders)); }
        }



        public ClientsPopup(Order order)
        {
            InitializeComponent();
            Order = order;
            Loaded += ClientsPopup_Loaded;
        }

        private ClientsAndBonusesStoreSettings _clientSettings;
        public ClientsAndBonusesStoreSettings ClientSettings
        {
            get => _clientSettings;
            set { _clientSettings = value; OnPropertyChanged(nameof(ClientSettings)); }
        }

        private async void ClientsPopup_Loaded(object sender, RoutedEventArgs e)
        {
            var customers = await MobileAPI.BaristaMethods.BaristaClientsMethods.GetClients(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);

            var wrappers = new List<SelectionWrapper<Customer>>();
            foreach (var customer in customers)
            {
                wrappers.Add(new SelectionWrapper<Customer>(customer));
            }

            Clients = new ObservableCollection<SelectionWrapper<Customer>>(wrappers);
            allClients = new ObservableCollection<SelectionWrapper<Customer>>(wrappers);

            ClientSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetClientsAndBonusesStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                   ApplicationState.CurrentStore.Id);


        }


        private ObservableCollection<SelectionWrapper<Customer>> allClients = new ObservableCollection<SelectionWrapper<Customer>>();

        private ObservableCollection<SelectionWrapper<Customer>> clients = new ObservableCollection<SelectionWrapper<Customer>>();
        public ObservableCollection<SelectionWrapper<Customer>> Clients
        {
            get => clients;
            set { clients = value; OnPropertyChanged(nameof(Clients)); }
        }


        private SelectionWrapper<Customer> selectedClient;
        public SelectionWrapper<Customer> SelectedClient
        {
            get => selectedClient;
            set 
            { 
                selectedClient = value; 
                OnPropertyChanged(nameof(SelectedClient));

                foreach (var customer in Clients)
                {
                    customer.IsSelected = false;
                }

                if(value != null)
                {
                    value.IsSelected = true;
                }
       
            }
        }


        private string searchQuery = string.Empty;
        public string SearchQuery
        {
            get => searchQuery;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    Clients = new ObservableCollection<SelectionWrapper<Customer>>(allClients);
                }
                else
                {
                    Clients = new ObservableCollection<SelectionWrapper<Customer>>(allClients.Where(o => o.Model.IsThisClient(ClientSettings.ClientSearchAllowance, value)));
                }
                searchQuery = value;
                OnPropertyChanged(nameof(SearchQuery));
            }
        }

        private ICommand goToNewClient;
        public ICommand GoToNewClient
        {
            get => goToNewClient ??= new RelayCommand(async obj =>
            {
                var popup = new NewClientPopup();
                popup.OnClientCreated += Popup_OnClientCreated;
                popup.ShowDialog();
            });
        }
        private void Popup_OnClientCreated(object sender, Customer e)
        {
            Clients.Add(new SelectionWrapper<Customer>(e));
        }


        private ICommand selectClient;
        public ICommand SelectClient
        {
            get => selectClient ??= new RelayCommand(async obj =>
            {
                if (SelectedClient is null)
                {
                    new OneButtonedPopup("Выберите клиента, чтобы продолжить", "Ошибка").ShowDialog();
                    return;
                }

                OrdersHelper.CurrentOrder.Customer = SelectedClient.Model;
                OrdersHelper.CurrentOrder.CustomerId = SelectedClient.Model.Id;
                this.Close();
            });
        }
    }
}
