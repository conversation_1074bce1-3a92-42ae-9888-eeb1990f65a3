using System;
using Xamarin.Forms;

namespace XamarinSamples.Views.Controls
{
    public class BorderlessEntry : Entry
    {
        #region 
        public delegate void BackspaceEventHandler(object sender, EventArgs e);

        public event BackspaceEventHandler OnBackspace;
        public void OnBackspacePressed()
        {
            if (OnBackspace != null)
            {
                OnBackspace(this, EventArgs.Empty);
            }
        }
        #endregion
    }
}