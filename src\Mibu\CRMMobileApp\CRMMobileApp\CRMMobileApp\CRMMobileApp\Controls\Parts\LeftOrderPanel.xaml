﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             xmlns:datagrid="clr-namespace:Xamarin.Forms.DataGrid;assembly=Xamarin.Forms.DataGrid"
             xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates"
             x:Class="CRMMobileApp.Controls.Parts.LeftOrderPanel">
    <ContentView.Content>
        <Grid
            RowSpacing="0"
            Background="{StaticResource icon_gray}">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="150"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Frame 
                    x:Name="orderIdFrame"
                    Margin="30,0,0,0"
                    Padding="0"
                    HasShadow="False"
                    HorizontalOptions="Start"
                    VerticalOptions="Center"
                    CornerRadius="10"
                    HeightRequest="30"
                    WidthRequest="100"
                    BackgroundColor="{StaticResource bg_purple}">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ShowActiveOrders}"/>
                    </Frame.GestureRecognizers>
                    <Grid>
                        <Label
                            FontSize="14"
                            FontFamily="TTFirsNeue-Regular"
                            VerticalOptions="Center"
                            HorizontalOptions="Center">
                            <Label.FormattedText>
                                <FormattedString>
                                    <FormattedString.Spans>
                                        <Span TextColor="#7265FB" Text="#"/>
                                        <Span TextColor="{StaticResource dark_purple}" x:Name="orderIdSpan"/>
                                    </FormattedString.Spans>
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                    </Grid>
                </Frame>


                <Frame 
                    x:Name="createCancelOrderFrame"
                    HasShadow="False"
                    Margin="0,0,20,0"
                    Padding="0"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    CornerRadius="10"
                    HeightRequest="30"
                    WidthRequest="180"
                    BackgroundColor="{StaticResource bg_purple}">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenOrCloseOrder}"/>
                    </Frame.GestureRecognizers>
                    <Grid>
                        <StackLayout 
                            Spacing="0"
                             VerticalOptions="Center"
                            Orientation="Horizontal">
                            <Image 
                                x:Name="createCancelOrderImg"
                                Margin="23,0,0,0"
                                HeightRequest="14"
                                WidthRequest="14"
                                VerticalOptions="Center"
                                Source="{OnPlatform Default=close_duty.png, WPF='pack://application:,,,/Images/close_duty.png'}"/>
                            <Label
                                x:Name="orderCreateCloseText"
                                Margin="5,0,0,0"
                                FontSize="14"
                                TextColor="{StaticResource dark_purple}"
                                Text="Отменить заказ"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"/>
                        </StackLayout>
                       
                    </Grid>
                </Frame>
            </Grid>

            <Grid 
                Padding="0"
                Grid.Row="1">
                <Grid Padding="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="38"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition x:Name="activeOrderBottomLayoutRowDef" Height="125"/>
                    </Grid.RowDefinitions>
                    
                    <Grid
                        Grid.Row="0"
                        HorizontalOptions="Center"
                        Margin="30,0,40,0"
                        Padding="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="60"/>
                        </Grid.ColumnDefinitions>

                        <Label
                             Grid.Column="0"
                             FontSize="14"
                             TextColor="White"
                             Text="Наименование"
                             FontFamily="TTFirsNeue-Regular"
                             VerticalOptions="Start"
                             HorizontalOptions="Start"/>
                        <Label
                             Grid.Column="1"
                             FontSize="14"
                             TextColor="White"
                             Text="Кол-во"
                             FontFamily="TTFirsNeue-Regular"
                             VerticalOptions="Start"
                             HorizontalOptions="Start"/>
                        <Label
                             Grid.Column="2"
                             FontSize="14"
                             TextColor="White"
                             Text="Цена"
                             FontFamily="TTFirsNeue-Regular"
                             VerticalOptions="Start"
                             HorizontalOptions="Start"/>
                        <Label
                             Grid.Column="3"
                             FontSize="14"
                             TextColor="White"
                             Text="Итого"
                             FontFamily="TTFirsNeue-Regular"
                             VerticalOptions="Start"
                             HorizontalOptions="Start"/>

                        <BoxView 
                            Grid.ColumnSpan="4"
                            VerticalOptions="End"
                            HorizontalOptions="FillAndExpand"
                            BackgroundColor="White"
                            HeightRequest="1"/>

                    </Grid>
                    
                    <ScrollView
                        Grid.Row="1"
                        VerticalScrollBarVisibility="{OnPlatform Default=Default, WPF=Never}"
                        Margin="0,0,20,0">
                        <StackLayout 
                            VerticalOptions="Start"
                            Spacing="6"
                            x:Name="orderItemsLayout">

                        </StackLayout>
                    </ScrollView>

                    <Grid Grid.Row="2">
                        <StackLayout
                             x:Name="activeOrderBottomLayout"
                             Margin="5,0,5,0"
                             VerticalOptions="End"
                             HorizontalOptions="Fill">

                            <Frame
                                 x:Name="orderCommentFrame"
                                 Background="{StaticResource bg_purple}"
                                 CornerRadius="10"
                                 HeightRequest="70"
                                 VerticalOptions="Start"
                                 HorizontalOptions="Fill"
                                 Padding="5"
                                 HasShadow="False">
                                <Label
                                      Grid.Column="1"
                                      FontSize="14"
                                      TextColor="{StaticResource dark_purple}"
                                      Text="{Binding Source={x:Reference this},Path=CurrentOrder.Comment}"
                                      MaxLines="3"
                                      LineBreakMode="TailTruncation"
                                      FontFamily="TTFirsNeue-Regular"
                                      VerticalOptions="Start"
                                      HorizontalOptions="Start"/>
                            </Frame>

                            <Button 
                                 x:Name="sendItemsToWork"
                                 Command="{Binding Source={x:Reference this},Path=SendItemsToWorkCommand}"
                                 CornerRadius="20"
                                 VerticalOptions="Start"
                                 HorizontalOptions="Fill"
                                 TextColor="{StaticResource dark_purple}"
                                 Text="Отправить в работу"
                                 FontSize="14"
                                 TextTransform="None"
                                 Background="{StaticResource bg_purple}"
                                 HeightRequest="40"/>
                        </StackLayout>
                    </Grid>
                    
                    
                </Grid>


               
                
                
            </Grid>

            <Grid Grid.Row="2">
                
                
                <Frame 
                    x:Name="activeOrderFooter"
                    BackgroundColor="{StaticResource bg_purple}"
                    VerticalOptions="End"
                    Margin="{OnPlatform Android='0,0,0,-10',iOS='0,0,0,-20',WPF='0,0,0,-10'}"
                    Padding="0,20,0,10"        
                    HasShadow="False"
                    CornerRadius="10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="3.5*"/>
                            <RowDefinition Height="6.5*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">
                            
                            <ImageButton 
                                Command="{Binding Source={x:Reference this},Path=OpenMenu}"
                                Margin="31,10,0,0"
                                WidthRequest="20"
                                HeightRequest="20"
                                CornerRadius="0"
                                Source="{OnPlatform Default=burger.png, WPF='pack://application:,,,/Images/burger.png'}"
                                Aspect="Fill"
                                HorizontalOptions="Start"
                                VerticalOptions="Start"
                                BackgroundColor="Transparent"/>

                            <Button 
                                Command="{Binding Source={x:Reference this},Path=PayOrder}"
                                CornerRadius="20"
                                Margin="0,0,20,0"
                                VerticalOptions="Start"
                                HorizontalOptions="End"
                                TextColor="White"
                                Text="Оплатить"
                                FontSize="14"
                                TextTransform="None"
                                Background="{StaticResource purple_gradient}"
                                WidthRequest="140"
                                HeightRequest="40"/>

                        </Grid>

                        <Grid Grid.Row="1">

                            <StackLayout Margin="30,0,0,0">
                                <Label
                                    x:Name="orderTotalLabel"
                                    FontSize="14"
                                    TextColor="{StaticResource text_gray}"
                                    Text="Итого: 0.00₽"
                                    FontFamily="TTFirsNeue-Regular"
                                    HorizontalOptions="Start"/>
                                <Label
                                    x:Name="orderDiscountLabel"
                                    FontSize="14"
                                    TextColor="{StaticResource text_gray}"
                                    Text="Скидка: 0%"
                                    FontFamily="TTFirsNeue-Regular"
                                    HorizontalOptions="Start"/>
                                <Label
                                   x:Name="clientLabel"
                                   FontSize="14"
                                   MaxLines="1"
                                   LineBreakMode="TailTruncation"
                                   TextColor="{StaticResource text_gray}"
                                   Text="Клиент: не выбран"
                                   FontFamily="TTFirsNeue-Regular"
                                   HorizontalOptions="Start"/>
                            </StackLayout>

                            <Label
                                 x:Name="orderTotalWithDiscountLabel"
                                 Margin="0,0,25,0"
                                 FontSize="{OnPlatform Android=30,iOS=25}"
                                 TextColor="{StaticResource dark_purple}"
                                 Text="0.00₽"
                                 FontFamily="TTFirsNeue-Regular"
                                 VerticalOptions="Start"
                                 HorizontalOptions="End"/>


                        </Grid>
                        
                    </Grid>
                </Frame>

                <Frame 
                    x:Name="closedOrderFooter"
                    IsVisible="False"
                    BackgroundColor="{StaticResource bg_purple}"
                    VerticalOptions="End"
                    Margin="{OnPlatform Android='0,0,0,-10',iOS='0,0,0,-20'}"
                    Padding="0"
                    HasShadow="False"
                    CornerRadius="10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">

                            <Button 
                                Command="{Binding Source={x:Reference this},Path=PrintClosedOrderCheque}"
                                CornerRadius="20"
                                Margin="0,20,20,20"
                                VerticalOptions="Start"
                                HorizontalOptions="End"
                                TextColor="White"
                                Text="Печать"
                                FontSize="14"
                                TextTransform="None"
                                Background="{StaticResource purple_gradient}"
                                WidthRequest="140"
                                HeightRequest="40"/>

                        </Grid>

                        <Grid Grid.Column="1">

                            <Button 
                                x:Name="returnOrderBtn"
                                Command="{Binding Source={x:Reference this},Path=ReturnClosedOrder}"
                                CornerRadius="20"
                                Margin="0,20,20,20"
                                VerticalOptions="Start"
                                HorizontalOptions="End"
                                TextColor="White"
                                Text="Возврат"
                                FontSize="14"
                                TextTransform="None"
                                Background="{StaticResource purple_gradient}"
                                WidthRequest="140"
                                HeightRequest="40"/>


                        </Grid>

                    </Grid>
                </Frame>

            </Grid>

            
            
            <Frame
                x:Name="menuFrame"
                IsVisible="False"
                Grid.Row="1"
                Grid.RowSpan="2"
                Margin="{OnPlatform Android='20,0,20,63',iOS='20,0,20,44'}"
                VerticalOptions="End"
                HorizontalOptions="FillAndExpand"

                WidthRequest="300"
                HasShadow="False"
                CornerRadius="5"
                BackgroundColor="#FFFFFF"
                Padding="0">
                <Grid>

                    <ImageButton 
                        Command="{Binding Source={x:Reference this},Path=CloseMenu}"
                        Margin="0,5,5,0"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        WidthRequest="32"
                        HeightRequest="32"
                        Padding="10"
                        BackgroundColor="Transparent"
                        Background="Transparent"
                        Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"/>

                    <StackLayout
                        Margin="20,20,0,20"
                        Spacing="12"
                        VerticalOptions="Fill"
                        HorizontalOptions="Start">

                        <StackLayout 
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetClient}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=client.png, WPF='pack://application:,,,/Images/client.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Выбрать клиента"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout 
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetDiscount}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=discount.png, WPF='pack://application:,,,/Images/discount.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Скидка"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout 
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetOrderComment}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=comment.png, WPF='pack://application:,,,/Images/comment.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Комментарии к чеку"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout 
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=ClearOrderItems}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=deleteAll.png, WPF='pack://application:,,,/Images/deleteAll.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Удалить все позиции"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout x:Name="editAmountStackLayout"
                            IsVisible="False"
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=EditAmount}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=setAmount.png, WPF='pack://application:,,,/Images/setAmount.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Количество"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout x:Name="editModifiersStackLayout"
                            IsVisible="False"
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=EditModifiers}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=editModifiers.png, WPF='pack://application:,,,/Images/editModifiers.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Модификаторы"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout x:Name="orderItemCommentStackLayout"
                            IsVisible="False"
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToOrderItemCommentPopup}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=orderComment.png, WPF='pack://application:,,,/Images/orderComment.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Комментарий к позиции"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                        <StackLayout x:Name="splitOrderStackLayout"
                            IsVisible="False"
                            Spacing="13"
                            Orientation="Horizontal">
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SplitOrder}"/>
                            </StackLayout.GestureRecognizers>
                            <Image
                                HeightRequest="13"
                                WidthRequest="13"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Source="{OnPlatform Default=divideOrder.png, WPF='pack://application:,,,/Images/divideOrder.png'}"/>
                            <Label
                                FontSize="16"
                                TextColor="{StaticResource dark_purple}"
                                Text="Разделить заказ"
                                FontFamily="TTFirsNeue-Regular"
                                VerticalOptions="Center"/>
                        </StackLayout>

                    </StackLayout>


                </Grid>
            </Frame>

        </Grid>
  </ContentView.Content>
</ContentView>