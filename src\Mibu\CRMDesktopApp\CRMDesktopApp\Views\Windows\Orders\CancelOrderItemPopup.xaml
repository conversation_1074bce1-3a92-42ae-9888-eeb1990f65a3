﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.Orders.CancelOrderItemPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.Orders" 
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Отмена позиции заказа" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="240" 
        Width="300">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="290"
        Height="230"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="0,0,0,0"
        Background="#FFFFFF"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid 
                Grid.Row="0"
                Margin="0,20,0,0">
                <StackPanel Orientation="Horizontal">
                    <Image 
                        Width="40"
                        Height="40"/>
                    <TextBlock
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="18"
                        FontWeight="Bold"
                        Text="Возврат заказа"/>
                </StackPanel>


                <customcontrols:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,10,35,0"/>
            </Grid>


            <Grid Grid.Row="1">

                <StackPanel
                    VerticalAlignment="Stretch"
                    HorizontalAlignment="Stretch"
                    Margin="30,0,30,0">

                    <Button 
                        Margin="0,12,0,0"
                        Command="{Binding ElementName=this,Path=CancelPosition}"
                        CommandParameter="Ошибка официанта"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Top"
                        Content="Ошибка официанта"
                        Height="40"/>

                    <Button
                        Margin="0,12,0,0"
                        Command="{Binding ElementName=this,Path=CancelPosition}"
                        CommandParameter="Отказ клиента"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Top"
                        Content="Отказ клиента"
                        Height="40"/>

                    <Button
                        Margin="0,12,0,0"
                        Command="{Binding ElementName=this,Path=CancelPosition}"
                        CommandParameter="Нет в наличии"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Top"
                        Content="Нет в наличии"
                        Height="40"/>

                </StackPanel>

            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
