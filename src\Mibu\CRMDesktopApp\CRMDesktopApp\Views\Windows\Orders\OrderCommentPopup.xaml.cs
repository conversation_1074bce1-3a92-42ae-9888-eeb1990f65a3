﻿using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для OrderCommentPopup.xaml
    /// </summary>
    public partial class OrderCommentPopup : BaseWindow
    {
        public OrderCommentPopup(Order order)
        {
            Order = order;
            Comment = Order.Comment;
            InitializeComponent();
        }
        private Order order;
        public Order Order
        {
            get => order;
            set { order = value; OnPropertyChanged(nameof(Order)); }
        }




        private string comment;
        public string Comment
        {
            get => comment;
            set { comment = value; OnPropertyChanged(nameof(Comment)); }
        }

        private ICommand saveChanges;
        public ICommand SaveChanges
        {
            get => saveChanges ??= new RelayCommand(async obj =>
            {
                OrdersHelper.SetComment(Comment);
                this.Close();
            });
        }
    }
}
