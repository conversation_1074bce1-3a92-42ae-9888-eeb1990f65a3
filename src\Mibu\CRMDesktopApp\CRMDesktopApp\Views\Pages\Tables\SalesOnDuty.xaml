﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Tables.SalesOnDuty"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Tables" 
      xmlns:tables="clr-namespace:CRMDesktopApp.Controls.Templates.Reports"
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="SalesOnDuty">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid Background="#ffffff">
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <parts:HeaderSearch 
            Title="Продажи за смену"
            Grid.Row="0"/>

        <Grid Grid.Row="1">

            <Border
                CornerRadius="10"
                ClipToBounds="True"
                Background="{StaticResource bg_purple}"
                Margin="0,25,0,25"
                VerticalAlignment="Stretch"
                HorizontalAlignment="Center"
                Width="670">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="45"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="0.5*"/>
                            <ColumnDefinition Width="0.5*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">
                            <TextBlock
                                Margin="20,0,0,0"
                                VerticalAlignment="Center"
                                HorizontalAlignment="Left"
                                Foreground="{StaticResource dark_purple}"
                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                FontSize="16"
                                Text="Товар"/>
                        </Grid>

                        <Grid Grid.Column="1">
                            <TextBlock
                                VerticalAlignment="Center"
                                HorizontalAlignment="Left"
                                Foreground="{StaticResource dark_purple}"
                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                FontSize="16"
                                Text="Сумма"/>
                        </Grid>


                        <Grid Grid.Column="2">

                            <TextBlock
                                VerticalAlignment="Center"
                                HorizontalAlignment="Left"
                                Foreground="{StaticResource dark_purple}"
                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                FontSize="16"
                                Text="Кол-во"/>


                            <!--<Button 
                                    CornerRadius="20"
                                    Padding="0"
                                    Margin="0,15,40,0"
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    TextColor="White"
                                    Text="Все"
                                    FontSize="Default"
                                    TextTransform="None"
                                    Background="{StaticResource purple_gradient}"
                                    WidthRequest="90"
                                    HeightRequest="30"/>-->

                        </Grid>

                    </Grid>


                    <Grid Grid.Row="1">
                        <ListBox 
                            x:Name="lb"
                            IsHitTestVisible="False"
                            BorderThickness="0"
                            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                            ScrollViewer.VerticalScrollBarVisibility="Hidden"
                            Background="{StaticResource bg_purple}"
                            ItemsSource="{Binding ElementName=this,Path=Rows}">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <tables:SalesOnDutyReportRow 
                                        Margin="0,0,0,0"
                                        Width="{Binding ElementName=lb,Path=ActualWidth}"
                                        Model="{Binding}"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <TextBlock
                           x:Name="noItemsTextBlock"
                           Visibility="Hidden"
                           FontFamily="{StaticResource TTFirsNeue-Regular}"
                           TextAlignment="Center"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Foreground="{StaticResource text_gray}"
                           FontSize="15"
                           Width="300"
                           TextWrapping="Wrap"
                           Text="Отчет по продажам за смену пуст"/>
                    </Grid>

                </Grid>
            </Border>


        </Grid>

    </Grid>
</abstactions:BasePage>
