﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:BaseDashboardPage
             xmlns:abstractions="clr-namespace:CRMAdminMoblieApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:xct="http://xamarin.com/schemas/2020/toolkit" 
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             ios:Page.UseSafeArea="true"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             xmlns:views="clr-namespace:SVGChart.Nuget.Views;assembly=SVGChart.Nuget" 
             Background="#ffffff" 
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             xmlns:itemtemplates="clr-namespace:CRMAdminMoblieApp.Controls.ItemTemplates"
             x:Class="CRMAdminMoblieApp.Views.Pages.Dashboard.RealTimePage">
    <abstractions:BaseDashboardPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </abstractions:BaseDashboardPage.Resources>
    <ContentPage.Content>
        <Grid Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="*" />
                <RowDefinition Height="80" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <parts:Header x:Name="header"/>
            </Grid>

            <Grid Grid.Row="1">
                <ScrollView>
                    <StackLayout Spacing="0">


                        <Frame
                            Margin="20,20,20,0"
                            HasShadow="False"
                            CornerRadius="5"
                            Background="{x:StaticResource purple_gradient}"
                            VerticalOptions="Start"
                            HorizontalOptions="Fill"
                            Padding="0"
                            HeightRequest="40">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenDutyPopup}"/>
                            </Frame.GestureRecognizers>
                            <Grid>
                                <StackLayout
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    Margin="0,0,21,0"
                                    Spacing="21"
                                    Orientation="Horizontal">

                                    <Label 
                                         x:Name="dutyLabel"
                                         FontFamily="TTFirsNeue-Regular"
                                         FontSize="14"
                                         TextColor="White"
                                         VerticalOptions="Center"
                                         Text=""/>
                                    <Image 
                                        WidthRequest="22"
                                        HeightRequest="22"
                                        Source="calendar.png"/>

                                </StackLayout>
                            </Grid>
                        </Frame>


                        <Frame
                            HasShadow="False"
                            CornerRadius="5"
                            Margin="20,20,20,0"
                            Background="{x:StaticResource bg_purple}"
                            VerticalOptions="Start"
                            HorizontalOptions="Fill"
                            Padding="0"
                            HeightRequest="40">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=OpenNetworksPopup}"/>
                            </Frame.GestureRecognizers>
                            <Grid>
                                
                                <Image
                                    WidthRequest="14"
                                    HeightRequest="18"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    Margin="23,0,0,0"
                                    Source="location.png"/>

                                <StackLayout
                                    Spacing="0"
                                    Margin="0,0,20,0"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"
                                    Orientation="Horizontal">
                                    <StackLayout
                                        WidthRequest="170"
                                        VerticalOptions="Center"
                                        Spacing="0">
                                        <Label 
                                            x:Name="storeTitleLabel"
                                            HorizontalOptions="End"
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="14"
                                            TextColor="{x:StaticResource dark_purple}"
                                            VerticalOptions="Center"
                                            Text=""/>
                                        <Label 
                                             x:Name="storeAddressLabel"
                                             HorizontalOptions="End"
                                             FontFamily="TTFirsNeue-Regular"
                                             FontSize="10"
                                             TextColor="{x:StaticResource dark_purple}"
                                             VerticalOptions="Center"
                                             Text=""/>
                                    </StackLayout>

                                    <ImageButton 
                                        Margin="20,0,0,0"
                                        VerticalOptions="Center"
                                        WidthRequest="10"
                                        HeightRequest="5"
                                        Source="arrowDown.png"/>
                                </StackLayout>
                                
                                
                            </Grid>
                        </Frame>

                        <StackLayout 
                            Margin="20,30,20,0"
                            HorizontalOptions="Fill">
                            <Label 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Выручка"/>
                            <Grid HeightRequest="25">
                                <Label 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="20"
                                    TextColor="{x:StaticResource purple}">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.Revenue}"/>
                                                <Span Text="₽"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <Label 
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    TextColor="{x:StaticResource text_gray}">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.RevenuePercent}"/>
                                                <Span Text="%"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                            </Grid>
                            <ProgressBar
                                BackgroundColor="{x:StaticResource bg_purple}"
                                ProgressColor="{x:StaticResource purple}"
                                Progress="{Binding Source={x:Reference this},Path=RealTimeReport.RevenueDecimalPercent}"                          
                                HorizontalOptions="FillAndExpand"
                                HeightRequest="3"/>

                        </StackLayout>

                        <StackLayout 
                            Margin="20,30,20,0"
                            HorizontalOptions="Fill">
                            <Label 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="{x:StaticResource dark_purple}"
                                VerticalOptions="Center"
                                Text="Прибыль"/>
                            <Grid HeightRequest="25">
                                <Label 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="20"
                                    TextColor="{x:StaticResource green}">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.Profit}"/>
                                                <Span Text="₽"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <Label 
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    TextColor="{x:StaticResource text_gray}">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ProfitPercent}"/>
                                                <Span Text="%"/>
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                            </Grid>
                            <ProgressBar
                                BackgroundColor="{x:StaticResource bg_purple}"
                                ProgressColor="{x:StaticResource green}"
                                Progress="{Binding Source={x:Reference this},Path=RealTimeReport.ProfitDecimalPercent}" 
                                HorizontalOptions="FillAndExpand"
                                HeightRequest="3"/>

                        </StackLayout>

                        <xct:TabView                  
                            Margin="20,0,20,0"
                            HorizontalOptions="Center"
                            HeightRequest="400"
                            VerticalOptions="Start"
                            Padding="0"
                            TabIndicatorPlacement="Bottom"
                            TabIndicatorHeight="0"
                            TabIndicatorColor="{x:StaticResource dark_purple}">
                            <xct:TabViewItem 
                                FontFamily="TTFirsNeue-Regular"
                                Margin="0,0,0,0"
                                Padding="0"
                                HorizontalOptions="{OnIdiom Phone=Start,Tablet=Center}"
                                FontSize="{OnPlatform Android=12,iOS=13}"
                                FontSizeSelected="{OnPlatform Android=12,iOS=13}"
                                TextColor="{x:StaticResource text_gray}"
                                TextColorSelected="{x:StaticResource dark_purple}"  
                                Text="Общее">
                                <Frame 
                                    HasShadow="False"
                                    VerticalOptions="Center"
                                    HeightRequest="350"
                                    CornerRadius="10"
                                    BackgroundColor="{x:StaticResource bg_purple}"
                                    Padding="0">
                                    <Grid>
                                        <Grid x:Name="realTimePhoneStats">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="1*"/>
                                                <RowDefinition Height="1*"/>
                                            </Grid.RowDefinitions>

                                            <Grid Grid.Row="0">


                                                <views:DonutChartView           
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    HeightRequest="140"
                                                    WidthRequest="140"
                                                    ItemSource="{Binding Source={x:Reference this},Path=DonutChartSections}"
                                                    x:Name="donutChart" />

                                                <Image 
                                                    Margin="0,-10,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    WidthRequest="38"
                                                    HeightRequest="30"
                                                    Source="wallet.png"/>

                                            </Grid>

                                            <Grid Grid.Row="1">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="1*"/>
                                                    <RowDefinition Height="1*"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="1*"/>
                                                    <ColumnDefinition Width="1*"/>
                                                </Grid.ColumnDefinitions>


                                                <Grid 
                                                    Grid.Column="0"
                                                    Grid.Row="0">
                                                    <StackLayout 
                                                        VerticalOptions="Center"
                                                         HorizontalOptions="Start"
                                                        Margin="40,0,0,0"
                                                        Orientation="Horizontal">
                                                        <Ellipse
                                                            Fill="{x:StaticResource purple}"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            HeightRequest="10"
                                                            WidthRequest="10"/>
                                                        <StackLayout
                                                            VerticalOptions="Start"
                                                            Spacing="1">
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="14"
                                                                TextColor="{x:StaticResource dark_purple}"
                                                                Text="Картой"/>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="12"
                                                                TextColor="{x:StaticResource text_gray}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCardPercent}"/>
                                                                            <Span Text="%"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource purple}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCard}"/>
                                                                            <Span Text="₽"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                        </StackLayout>
                                                    </StackLayout>
                                                </Grid>

                                                <Grid 
                                                    Grid.Column="1"
                                                    Grid.Row="0">
                                                    <StackLayout
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Margin="20,0,0,0"
                                                        Orientation="Horizontal">
                                                        <Ellipse
                                                            Fill="{x:StaticResource green}"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            HeightRequest="10"
                                                            WidthRequest="10"/>
                                                        <StackLayout
                                                            VerticalOptions="Start"
                                                            Spacing="1">
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="14"
                                                                TextColor="{x:StaticResource dark_purple}"
                                                                Text="Наличными"/>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="12"
                                                                TextColor="{x:StaticResource text_gray}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCashPercent}"/>
                                                                            <Span Text="%"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource green}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCash}"/>
                                                                            <Span Text="₽"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                        </StackLayout>
                                                    </StackLayout>
                                                </Grid>

                                                <Grid 
                                                    Grid.Column="0"
                                                    Grid.Row="1">
                                                    <StackLayout 
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Margin="40,0,0,0"
                                                        Orientation="Horizontal">
                                                        <Ellipse
                                                            Fill="#F2994A"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            HeightRequest="10"
                                                            WidthRequest="10"/>
                                                        <StackLayout
                                                            VerticalOptions="Start"
                                                            Spacing="1">
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="14"
                                                                TextColor="{x:StaticResource dark_purple}"
                                                                Text="Бонусами"/>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="12"
                                                                TextColor="{x:StaticResource text_gray}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByBonusesPercent}"/>
                                                                            <Span Text="%"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="#F2994A">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByBonuses}"/>
                                                                            <Span Text="₽"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                        </StackLayout>
                                                    </StackLayout>
                                                </Grid>


                                            </Grid>

                                        </Grid>

                                        <Grid x:Name="realTimeTabletStats">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="1*"/>
                                                <ColumnDefinition Width="1*"/>
                                                <ColumnDefinition Width="1*"/>
                                                <ColumnDefinition Width="1*"/>
                                            </Grid.ColumnDefinitions>

                                            <Grid Grid.Column="0">


                                                <views:DonutChartView   
                                                    Margin="0,20,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Start"
                                                    HeightRequest="340"
                                                    WidthRequest="340"
                                                    ItemSource="{Binding Source={x:Reference this},Path=DonutChartSections}"
                                                    x:Name="donutChart2" />

                                                <Image 
                                                    Margin="110,-25,0,0"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Start"
                                                    WidthRequest="98"
                                                    HeightRequest="90"
                                                    Source="wallet.png"/>

                                            </Grid>

                                            <Grid Grid.Column="1">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="1*"/>
                                                    <RowDefinition Height="1*"/>
                                                    <RowDefinition Height="1*"/>
                                                </Grid.RowDefinitions>



                                                <Grid 
                                                    Grid.Column="0"
                                                    Grid.Row="0">
                                                    <StackLayout 
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Orientation="Horizontal">
                                                        <Ellipse
                                                            Fill="{x:StaticResource purple}"
                                                            Margin="0,4,0,0"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            HeightRequest="25"
                                                            WidthRequest="25"/>
                                                        <StackLayout
                                                            VerticalOptions="Start"
                                                            Spacing="1">
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="25"
                                                                TextColor="{x:StaticResource dark_purple}"
                                                                Text="Картой"/>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource text_gray}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCardPercent}"/>
                                                                            <Span Text="%"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource purple}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCard}"/>
                                                                            <Span Text="₽"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                        </StackLayout>
                                                    </StackLayout>
                                                </Grid>

                                                <Grid 
                                                    Grid.Row="1">
                                                    <StackLayout
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Orientation="Horizontal">
                                                        <Ellipse
                                                            Fill="{x:StaticResource green}"
                                                            Margin="0,4,0,0"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            HeightRequest="25"
                                                            WidthRequest="25"/>
                                                        <StackLayout
                                                            VerticalOptions="Start"
                                                            Spacing="1">
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="25"
                                                                TextColor="{x:StaticResource dark_purple}"
                                                                Text="Наличными"/>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource text_gray}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCashPercent}"/>
                                                                            <Span Text="%"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource green}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByCash}"/>
                                                                            <Span Text="₽"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                        </StackLayout>
                                                    </StackLayout>
                                                </Grid>

                                                <Grid 
                                                    Grid.Column="0"
                                                    Grid.Row="2">
                                                    <StackLayout 
                                                        VerticalOptions="Center"
                                                        HorizontalOptions="Start"
                                                        Orientation="Horizontal">
                                                        <Ellipse
                                                            Fill="#F2994A"
                                                            Margin="0,4,0,0"
                                                            VerticalOptions="Start"
                                                            HorizontalOptions="Start"
                                                            HeightRequest="25"
                                                            WidthRequest="25"/>
                                                        <StackLayout
                                                            VerticalOptions="Start"
                                                            Spacing="1">
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="25"
                                                                TextColor="{x:StaticResource dark_purple}"
                                                                Text="Бонусами"/>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="{x:StaticResource text_gray}">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByBonusesPercent}"/>
                                                                            <Span Text="%"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                            <Label 
                                                                FontFamily="TTFirsNeue-Regular"
                                                                FontSize="20"
                                                                TextColor="#F2994A">
                                                                <Label.FormattedText>
                                                                    <FormattedString>
                                                                        <FormattedString.Spans>
                                                                            <Span Text="{Binding Source={x:Reference this},Path=RealTimeReport.ByBonuses}"/>
                                                                            <Span Text="₽"/>
                                                                        </FormattedString.Spans>
                                                                    </FormattedString>
                                                                </Label.FormattedText>
                                                            </Label>
                                                        </StackLayout>
                                                    </StackLayout>
                                                </Grid>


                                            </Grid>

                                        </Grid>
                                    </Grid>
                                </Frame>
                            </xct:TabViewItem>
                            
                            <xct:TabViewItem 
                                Margin="0,0,0,0"
                                Padding="0"
                                HorizontalOptions="{OnIdiom Phone=Start,Tablet=Center}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="{OnPlatform Android=12,iOS=13}"
                                FontSizeSelected="{OnPlatform Android=12,iOS=13}"
                                TextColor="{x:StaticResource text_gray}"
                                TextColorSelected="{x:StaticResource dark_purple}"  
                                Text="По цехам">
                                <Frame 
                                    HasShadow="False"
                                    VerticalOptions="Center"
                                    HeightRequest="350"
                                    CornerRadius="10"
                                    BackgroundColor="{x:StaticResource bg_purple}"
                                    Padding="0">
                                    <Grid>
                                        <ListView 
                                            SelectionMode="None"
                                            Background="{x:StaticResource bg_purple}"
                                            BackgroundColor="{x:StaticResource bg_purple}"
                                            ItemsSource="{Binding Source={x:Reference this},Path=WorkshopsReport.Workshops}">
                                            <ListView.ItemTemplate>
                                                <DataTemplate>
                                                    <ViewCell>
                                                        <itemtemplates:WorkshopRowItem Model="{Binding}"/>
                                                    </ViewCell>
                                                </DataTemplate>
                                            </ListView.ItemTemplate>
                                        </ListView>

                                        <Label 
                                              x:Name="workshopReportEmptyLabel"
                                              IsVisible="False"
                                              FontFamily="TTFirsNeue-Regular"
                                              FontSize="14"
                                              TextColor="{x:StaticResource dark_purple}"
                                              VerticalOptions="Center"
                                              HorizontalOptions="Center"
                                              HorizontalTextAlignment="Center"
                                              WidthRequest="250"
                                              Text="На данный момент нет цехов, по которым можно было бы составить отчет"/>

                                    </Grid>
                                </Frame>
                               
                            </xct:TabViewItem>
                            <xct:TabViewItem
                                Padding="0"
                                Margin="0,0,0,0"
                                HorizontalOptions="{OnIdiom Phone=Start,Tablet=Center}"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="{OnPlatform Android=12,iOS=13}"
                                FontSizeSelected="{OnPlatform Android=12,iOS=13}"
                                TextColor="{x:StaticResource text_gray}"
                                TextColorSelected="{x:StaticResource dark_purple}"  
                                Text="По официантам">
                                <Frame 
                                    HasShadow="False"
                                    VerticalOptions="Center"
                                    HeightRequest="350"
                                    CornerRadius="10"
                                    BackgroundColor="{x:StaticResource bg_purple}"
                                    Padding="0">
                                    <Grid>
                                        <ListView 
                                            SelectionMode="None"
                                            Background="{x:StaticResource bg_purple}"
                                            BackgroundColor="{x:StaticResource bg_purple}"
                                            ItemsSource="{Binding Source={x:Reference this},Path=WaitersReport.Waiters}">
                                            <ListView.ItemTemplate>
                                                <DataTemplate>
                                                    <ViewCell>
                                                        <itemtemplates:WaiterRowItem Model="{Binding}"/>
                                                    </ViewCell>
                                                </DataTemplate>
                                            </ListView.ItemTemplate>
                                        </ListView>

                                        <Label 
                                           x:Name="waitersReportEmptyLabel"
                                           IsVisible="False"
                                           FontFamily="TTFirsNeue-Regular"
                                           FontSize="14"
                                           TextColor="{x:StaticResource dark_purple}"
                                           VerticalOptions="Center"
                                           HorizontalOptions="Center"
                                           HorizontalTextAlignment="Center"
                                           WidthRequest="250"
                                           Text="На данный момент нет официантов, по которым можно составить отчет"/>

                                    </Grid>
                                </Frame>
                               
                            </xct:TabViewItem>
                        </xct:TabView>

                        <ScrollView 
                            x:Name="scrollCarousel"
                            HorizontalScrollBarVisibility="Never"
                            Margin="30,50,30,40"
                            Orientation="Horizontal">
                            <StackLayout 
                                Padding="50,0,0,0"
                                Spacing="20"
                                Orientation="Horizontal">

                                <Frame 
                                    HasShadow="False"
                                    HeightRequest="{OnIdiom Phone=80,Tablet=160}"
                                    WidthRequest="{OnIdiom Phone=220,Tablet=440}"
                                    Background="{x:StaticResource green_gradient}"
                                    CornerRadius="10"
                                    Padding="0">
                                    <Grid>
                                        <StackLayout
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Orientation="Horizontal">
                                            <Image 
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                WidthRequest="{OnIdiom Phone=27,Tablet=54}"
                                                HeightRequest="{OnIdiom Phone=34,Tablet=68}"
                                                Source="report.png"/>
                                            <StackLayout 
                                                Margin="20,0,0,0"
                                                Spacing="0">
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Default,Tablet=30}"
                                                    TextColor="White"
                                                    Text="Продано"/>
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Large,Tablet=35}"
                                                    FontAttributes="Bold"
                                                    TextColor="White">
                                                    <Label.FormattedText>
                                                        <FormattedString>
                                                            <FormattedString.Spans>
                                                                <Span Text="{Binding Source={x:Reference this},Path=CarouselReport.Sum}"/>
                                                                <Span Text="₽"/>
                                                            </FormattedString.Spans>
                                                        </FormattedString>
                                                    </Label.FormattedText>
                                                </Label>
                                            </StackLayout>
                                        </StackLayout>
                                    </Grid>
                                </Frame>

                                <Frame 
                                    HasShadow="False"
                                    HeightRequest="{OnIdiom Phone=80,Tablet=160}"
                                    WidthRequest="{OnIdiom Phone=220,Tablet=440}"
                                    Background="{x:StaticResource purple_gradient}"
                                    CornerRadius="10"
                                    Padding="0">
                                    <Grid>
                                        <StackLayout
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Orientation="Horizontal">
                                            <Image 
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                WidthRequest="{OnIdiom Phone=27,Tablet=54}"
                                                HeightRequest="{OnIdiom Phone=34,Tablet=68}"
                                                Source="report.png"/>
                                            <StackLayout 
                                                Margin="20,0,0,0"
                                                Spacing="0">
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Default,Tablet=30}"
                                                    TextColor="White"
                                                    Text="Средний чек"/>
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Large,Tablet=35}"
                                                    FontAttributes="Bold"
                                                    TextColor="White">
                                                    <Label.FormattedText>
                                                        <FormattedString>
                                                            <FormattedString.Spans>
                                                                <Span Text="{Binding Source={x:Reference this},Path=CarouselReport.AverageCheck}"/>
                                                                <Span Text="₽"/>
                                                            </FormattedString.Spans>
                                                        </FormattedString>
                                                    </Label.FormattedText>
                                                </Label>
                                            </StackLayout>
                                        </StackLayout>
                                    </Grid>
                                </Frame>

                                <Frame 
                                    HasShadow="False"
                                    HeightRequest="{OnIdiom Phone=80,Tablet=160}"
                                    WidthRequest="{OnIdiom Phone=220,Tablet=440}"
                                    Background="{x:StaticResource orange_gradient}"
                                    CornerRadius="10"
                                    Padding="0">
                                    <Grid>
                                        <StackLayout
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Orientation="Horizontal">
                                            <Image 
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                WidthRequest="{OnIdiom Phone=27,Tablet=54}"
                                                HeightRequest="{OnIdiom Phone=34,Tablet=68}"
                                                Source="check.png"/>
                                            <StackLayout 
                                                Margin="20,0,0,0"
                                                Spacing="0">
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Default,Tablet=30}"
                                                    TextColor="White"
                                                    Text="Чеков"/>
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Large,Tablet=35}"
                                                    FontAttributes="Bold"
                                                    TextColor="White"
                                                    Text="{Binding Source={x:Reference this},Path=CarouselReport.ChecksCount}"/>
                                            </StackLayout>
                                        </StackLayout>
                                    </Grid>
                                </Frame>

                                <Frame 
                                    HasShadow="False"
                                    HeightRequest="{OnIdiom Phone=80,Tablet=160}"
                                    WidthRequest="{OnIdiom Phone=220,Tablet=440}"
                                    Background="{x:StaticResource blue_gradient}"
                                    CornerRadius="10"
                                    Padding="0">
                                    <Grid>
                                        <StackLayout
                                            HorizontalOptions="Center"
                                            VerticalOptions="Center"
                                            Orientation="Horizontal">
                                            <Image 
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                WidthRequest="{OnIdiom Phone=27,Tablet=54}"
                                                HeightRequest="{OnIdiom Phone=34,Tablet=68}"
                                                Source="client.png"/>
                                            <StackLayout 
                                                Margin="20,0,0,0"
                                                Spacing="0">
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Default,Tablet=30}"
                                                    TextColor="White"
                                                    Text="Посетителей"/>
                                                <Label 
                                                    FontFamily="TTFirsNeue-Regular"
                                                    FontSize="{OnIdiom Phone=Large,Tablet=35}"
                                                    FontAttributes="Bold"
                                                    TextColor="White"
                                                    Text="{Binding Source={x:Reference this},Path=CarouselReport.GuestsCount}"/>
                                            </StackLayout>
                                        </StackLayout>
                                    </Grid>
                                </Frame>

                            </StackLayout>
                        </ScrollView>
                        
                    </StackLayout>
                </ScrollView>
            </Grid>



            <Grid Grid.Row="2">
                <parts:Footer IsRealTimePage="True"/>
            </Grid>

        </Grid>
    </ContentPage.Content>
</abstractions:BaseDashboardPage>