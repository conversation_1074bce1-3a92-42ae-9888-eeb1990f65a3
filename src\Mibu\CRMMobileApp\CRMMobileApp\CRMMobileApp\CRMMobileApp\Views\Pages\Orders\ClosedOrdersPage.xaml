﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:OrdersPage
             xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions;assembly=CRMMobileApp"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CRMMobileApp.Controls;assembly=CRMMobileApp" 
             xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates;assembly=CRMMobileApp"
             xmlns:controls1="clr-namespace:CRMMobileApp.Controls.Parts"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core"
             xmlns:effects="http://sharpnado.com" 
             xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview" 
             xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Orders.ClosedOrdersPage">
    <ContentPage.Content>
        <Grid        
            Padding="0"
            RowSpacing="0"
            ColumnSpacing="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <controls1:LeftOrderPanel 
                Grid.Column="0"
                x:Name="leftOrdersPanel"/>

            <Grid 
                Grid.Column="1"
                Background="#ffffff">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <controls1:Header 
                        Title="Завершенные заказы"
                        VerticalOptions="Fill"
                        HorizontalOptions="Fill" />
                </Grid>


                <Grid Grid.Row="1">

                    <listviews:CustomCrossCollectionView
                        x:Name="collectionView"
                        MinItemSize="43"
                        Margin="30,10,30,0">
                        <listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                            <DataTemplate>
                                <templates:ClosedOrderTemplate 
                                    Tapped="View_Tapped"
                                    VerticalOptions="Start"
                                    HeightRequest="43"/>
                            </DataTemplate>
                        </listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                        <listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                            <DataTemplate>
                                <ViewCell>
                                    <templates:ClosedOrderTemplate 
                                        Tapped="View_Tapped"
                                        VerticalOptions="Start"
                                        HeightRequest="43"/>
                                </ViewCell>
                            </DataTemplate>
                        </listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                    </listviews:CustomCrossCollectionView>

                    <Label
                       x:Name="noItemsLabel"
                       IsVisible="False"
                       VerticalOptions="Center"
                       HorizontalOptions="Center"
                       HorizontalTextAlignment="Center"
                       WidthRequest="300"
                       TextColor="{StaticResource dark_purple}"
                       FontFamily="TTFirsNeue-Regular"
                       Text="На данный момент нет закрытых заказов"
                       FontSize="14" />

                </Grid>
                
                
                
                
               
            </Grid>
        </Grid>
    </ContentPage.Content>
</abstractions:OrdersPage>