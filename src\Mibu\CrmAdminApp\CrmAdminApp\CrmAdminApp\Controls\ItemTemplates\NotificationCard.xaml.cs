﻿
using CRM.Models.Network;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NotificationCard : ContentView
    {
        public NotificationCard(NetworkNotification notification)
        {
            InitializeComponent();
            Model = notification;

            Device.InvokeOnMainThreadAsync(() =>
            {
                switch (notification.Category)
                {
                    case CRM.Models.Enums.Statuses.NotificationCategory.Other:
                        break;
                    case CRM.Models.Enums.Statuses.NotificationCategory.DutyOpening:
                        dutyOpeningSign.IsVisible = true;
                        notificationCategoryLabel.TextColor = Color.FromHex("#23CF72");
                        notificationDateLabel.TextColor = Color.FromHex("#23CF72");
                        break;
                    case CRM.Models.Enums.Statuses.NotificationCategory.DutyClosing:
                        dutyClosingSign.IsVisible = true;
                        notificationCategoryLabel.TextColor = Color.FromHex("#7265FB");
                        notificationDateLabel.TextColor = Color.FromHex("#7265FB");
                        break;
                    case CRM.Models.Enums.Statuses.NotificationCategory.Incassation:
                        incassationSign.IsVisible = true;
                        notificationCategoryLabel.TextColor = Color.FromHex("#524E7D");
                        notificationDateLabel.TextColor = Color.FromHex("#524E7D");
                        break;
                    case CRM.Models.Enums.Statuses.NotificationCategory.CriticalBalance:
                        criticalBalanceSign.IsVisible = true;
                        notificationCategoryLabel.TextColor = Color.FromHex("#524E7D");
                        notificationDateLabel.TextColor = Color.FromHex("#524E7D");
                        break;
                }

                notificationDateLabel.Text = notification.CreatedAtLocalAuto.ToString("dd.MM.yyyy");
                notificationTimeLabel.Text = notification.CreatedAtLocalAuto.ToString("HH:mm");
            });
        }

        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(NetworkNotification), typeof(OrderCard));
        public NetworkNotification Model
        {
            get { return (NetworkNotification)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}