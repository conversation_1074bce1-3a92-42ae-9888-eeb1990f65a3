﻿
using CRM.Models.Reports.Mobile.Admin.Workshops;
using CRMAdminMoblieApp.Models;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WorkshopRowItem : ContentView
    {
        public WorkshopRowItem()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(WorkshopsReportRow), typeof(WorkshopRowItem));
        public WorkshopsReportRow Model
        {
            get { return (WorkshopsReportRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}