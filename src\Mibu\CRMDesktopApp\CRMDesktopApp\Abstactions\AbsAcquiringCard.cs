﻿using CRM.Models.Stores.Settings.Equipment;
using CRM.Models.Stores.Settings.Tables;
using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Controls;
using System.Windows.Input;

namespace CRMMobileApp.Abstractions
{
    public class AbsAcquiringCard : UserControl
    {
        protected double ExpandedHeightRequest = -1;
        protected double CollapsedHeightRequest = 60;
        public AbsAcquiringCard()
        {
        }
        public AbsAcquiringCard(Acquiring acquiringDevice)
        {
            Acquiring = acquiringDevice;
        }

        private Acquiring acquiring;
        public Acquiring Acquiring
        {
            get => acquiring;
            set { acquiring = value; OnPropertyChanged(nameof(Acquiring)); }
        }

        private bool isExpanded;
        public bool IsExpanded
        {
            get => isExpanded;
            set
            { 
                isExpanded = value; 
                OnPropertyChanged(nameof(IsExpanded));

                if (value)
                {
                    this.Height = ExpandedHeightRequest;
                }
                else
                {
                    this.Height = CollapsedHeightRequest;
                }
            }
        }
        public void ToggleExpand(object sender, MouseButtonEventArgs e)
        {
            IsExpanded = !IsExpanded;
        }

        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }

    }
}
