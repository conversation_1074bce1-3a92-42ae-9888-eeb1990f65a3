﻿using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AvailableAcquiringPopup : PopupPage
    {
        public AvailableAcquiringPopup()
        {
            InitializeComponent();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            LoadData();
        }
        private void LoadData()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ordersStackLayout.Children.Clear();
                foreach (var item in ApplicationState.StoreAcquirings)
                {
                    var control = new AcquiringItemTemplate(item)
                    {
                        HeightRequest = 38,
                        Margin = new Thickness(0, 4, 0, 0)
                    };
                    control.ItemTapped += Control_ItemTapped;
                    ordersStackLayout.Children.Add(control);
                }
            });
        }
        private void Control_ItemTapped(object sender, Acquiring e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                var sum = OrdersHelper.CurrentOrder.OrderPayments.Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Card && !o.IsPrepeyment)
                                                             .Sum(o => o.Sum);

                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushPopupAsync(new AcquringWaitingPopup(e, sum));
            });
        }



        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
    }
}