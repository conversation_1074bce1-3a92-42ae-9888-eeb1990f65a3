﻿using AcquiringProviders.Abstractions;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Views.Pages.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для ProductOptionsPopup.xaml
    /// </summary>
    public partial class AcquringWaitingPopup : BaseWindow
    {
        public AcquringWaitingPopup(Acquiring acquiring, double sumByCard)
        {
            InitializeComponent();
            Sum = sumByCard;
            Acquiring = acquiring;
        }

        private Acquiring acquiring;
        public Acquiring Acquiring
        {
            get => acquiring;
            set { acquiring = value; OnPropertyChanged(nameof(Acquiring)); }
        }
        private double sum;
        public double Sum
        {
            get => sum;
            set { sum = value; OnPropertyChanged(nameof(Sum)); }
        }


        private async Task OnGettingSignal()
        {
            //TODO:  Короче когда будет получен сигнал от эквайринга. Пока не реализовано
            await CloseOrder();
        }

        private async Task CloseOrder()
        {
            var payOrderPage = (App.Current.MainWindow as MainWindow).frame.Content as CreateOrder;
            await payOrderPage.CloseOrder();
        }



        private ICommand continueWithoutAquiring;
        public ICommand ContinueWithoutAquiring
        {
            get => continueWithoutAquiring ??= new RelayCommand(async obj =>
            {
                this.Close();
                await CloseOrder();
            });
        }


    }
}
