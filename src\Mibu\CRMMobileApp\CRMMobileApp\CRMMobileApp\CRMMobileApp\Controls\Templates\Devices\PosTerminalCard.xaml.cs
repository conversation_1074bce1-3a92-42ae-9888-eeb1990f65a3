﻿using CRM.Models.Network;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Devices
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PosTerminalCard : ContentView
    {
        public PosTerminalCard()
        {
            InitializeComponent();
        }
        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(POSTerminal), typeof(PosTerminalCard));
        public POSTerminal Model
        {
            get { return (POSTerminal)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}