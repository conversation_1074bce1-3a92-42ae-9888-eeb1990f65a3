﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.ErrorPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Ошибка" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        Width="360"
        Height="100">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="350"
        Height="95"
        Background="#ffffff"
        HorizontalAlignment="Right"
        VerticalAlignment="Top"
        Padding="0"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="10">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="60"/>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="50"/>
            </Grid.ColumnDefinitions>
            <Image
                Grid.Column="0"
                Width="30"
                Height="30"
                VerticalAlignment="Top"
                HorizontalAlignment="Left"
                Margin="10,10,0,0"
                Source="pack://application:,,,/Resources/Images/error_red.png"/>
            <TextBlock 
                Grid.Column="1"
                Margin="0,10,0,0"
                Text="Описание ошибки" />
            <customcontrols:ImageButton
                Grid.Column="2"
                Command="{Binding ElementName=this,Path=Close}"
                Width="20"
                Height="20"
                VerticalAlignment="Top"
                HorizontalAlignment="Left"
                Margin="10,10,0,0"
                Background="Transparent"
                Source="pack://application:,,,/Resources/Images/close.png"/>
        </Grid>
    </Border>
</abstactions:BaseWindow>
