﻿using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderWithDeliveryTemplate : ContentView
    {
        public OrderWithDeliveryTemplate()
        {
            InitializeComponent();
        }
        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(Order), typeof(ClosedOrderTemplate));
        public Order Model
        {
            get { return (Order)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }



        public event EventHandler<Order> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, Model);
        }
    }
}