﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.DiscountCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="50"
             d:DesignWidth="700">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        BorderThickness="1"
        CornerRadius="10">
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="True">
                        <Setter Property="Background" Value="{StaticResource purple}"></Setter>
                        <Setter Property="BorderBrush" Value="Transparent"></Setter>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="False">
                        <Setter Property="Background" Value="Transparent"></Setter>
                        <Setter Property="BorderBrush" Value="{StaticResource purple}"></Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </Border.Style>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="1*"/>
                <RowDefinition Height="1*"/>
            </Grid.RowDefinitions>

            <TextBlock 
                Grid.Row="0"
                FontSize="30"
                FontFamily="TTFirsNeue-Regular"
                HorizontalAlignment="Center"
                VerticalAlignment="Center">
                <TextBlock.Style>
                    <Style TargetType="TextBlock">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource purple}"></Setter>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="True">
                                <Setter Property="Foreground" Value="White"></Setter>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
                <Run Text="{Binding ElementName=this,Path=Model.Discount.Percent}"/>
                <Run Text="%"/>
            </TextBlock>

            <TextBlock
                Grid.Row="1"
                Text="{Binding ElementName=this,Path=Model.Discount.Title}"
                FontFamily="TTFirsNeue-Regular"
                FontSize="16"
                HorizontalAlignment="Center"
                VerticalAlignment="Top">
                <TextBlock.Style>
                    <Style TargetType="TextBlock">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource purple}"></Setter>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=this,Path=Model.IsSelected}" Value="True">
                                <Setter Property="Foreground" Value="White"></Setter>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Grid>
    </Border>
</UserControl>
