﻿using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для WeightTechCardOptionsPopup.xaml
    /// </summary>
    public partial class WeightTechCardOptionsPopup : BaseWindow
    {
        private OrderItem technicalCard;
        public OrderItem TechnicalCard
        {
            get => technicalCard;
            set { technicalCard = value; OnPropertyChanged(nameof(TechnicalCard)); }
        }
        public WeightTechCardOptionsPopup(OrderItem product)
        {
            InitializeComponent();
            TechnicalCard = product;
        }
        public WeightTechCardOptionsPopup(OrderItem product, double amount)
        {
            InitializeComponent();
            TechnicalCard = product;
            Amount = amount;

            _isNewItem = false;
        }
        private bool _isNewItem = true;

        #region Количество товара
        private ICommand openAmountKeyboard;
        public ICommand OpenAmountKeyboard
        {
            get => openAmountKeyboard ??= new RelayCommand(async obj =>
            {
                var popup = new ProductAmountKeyboardPopup(TechnicalCard.TechnicalCard.Price);
                popup.onApplyingValue += Popup_onApplyingValue;
                popup.ShowDialog();
            });
        }
        private void Popup_onApplyingValue(object sender, double e)
        {
            Amount = e;
        }

        private double amount = 1.000;
        public double Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }
        #endregion



        private ICommand addToOrder;
        public ICommand AddToOrder
        {
            get => addToOrder ??= new RelayCommand(async obj =>
            {
                TechnicalCard.CreatedAt = DateTime.Now;
                TechnicalCard.Amount = Amount;

                if (_isNewItem) OrdersHelper.AddOrderItem(TechnicalCard);
                else await OrdersHelper.UpdateOrderItem(TechnicalCard);

                this.Close();
            });
        }
    }
}
