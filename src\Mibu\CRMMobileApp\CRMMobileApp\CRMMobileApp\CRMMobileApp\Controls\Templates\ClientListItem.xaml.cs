﻿using CRM.Models.Network.LoyaltyProgram;
using CRMMobileApp.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ClientListItem : ContentView
    {
        public ClientListItem()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ModelProperty =
             BindableProperty.Create(nameof(Model), typeof(SelectionWrapper<Customer>), typeof(ClientListItem));
        public SelectionWrapper<Customer> Model
        {
            get { return (SelectionWrapper<Customer>)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


        public event EventHandler<SelectionWrapper<Customer>> Tapped;
        private void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, Model);
        }
    }
}