﻿using CRM.Models.Stores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Orders
{
    /// <summary>
    /// Логика взаимодействия для TableOrderTemplate.xaml
    /// </summary>
    public partial class TableOrderTemplate : UserControl
    {
        public static readonly DependencyProperty OrderProperty =
             DependencyProperty.Register(nameof(Order), typeof(Order), typeof(TableOrderTemplate));
        public Order Order
        {
            get { return (Order)GetValue(OrderProperty); }
            set { SetValue(OrderProperty, value); }
        }

        public TableOrderTemplate(Order order)
        {
            InitializeComponent();
            Order = order;
            InitControl();
        }

        private void InitControl()
        {
            orderNumberLabel.Text = $"№ {Order.OrderNumber}";


            if (Order.Items.Count == 0)
            {
                orderItemsLabel.Text = "";
            }
            else
            {
                List<string> titles = new List<string>();
                foreach (var item in Order.Items)
                {
                    if (item.TechnicalCard != null)
                    {
                        titles.Add(item.TechnicalCard.Title);
                    }
                    else if (item.Product != null)
                    {
                        titles.Add(item.Product.Title);
                    }
                }
                orderItemsLabel.Text = string.Join(", ", titles);
            }


            orderTimeLabel.Text = $"{Order.OpenedAt.ToString("HH:mm")}";
            orderWaiterLabel.Text = $"{Order.User.Name} {Order.User.Surname}";
            orderPriceLabel.Text = $"{Math.Round(Order.Sum, 2)} Р";
        }




        public event EventHandler<Order> ItemTapped;
        private void onItemTapped(object sender, MouseButtonEventArgs e)
        {
            ItemTapped?.Invoke(this, Order);
        }
    }
}
