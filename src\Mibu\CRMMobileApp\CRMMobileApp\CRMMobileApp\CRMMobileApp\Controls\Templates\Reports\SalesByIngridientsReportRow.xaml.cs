﻿using CRM.Models.Reports.Mobile.BalancesAtStock;
using CRM.Models.Reports.Mobile.ByIngridientSales;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates.Tables
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SalesByIngridientsReportRow : ContentView
    {
        public SalesByIngridientsReportRow()
        {
            InitializeComponent();
        }
        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(ByIngridientSalesReportRow), typeof(SalesByIngridientsReportRow));
        public ByIngridientSalesReportRow Model
        {
            get { return (ByIngridientSalesReportRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
       
    }
}