﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.OrderItemModifierTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="50">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <ListBox   
            ScrollViewer.VerticalScrollBarVisibility="Disabled"
            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
            IsEnabled="False"
            Background="Transparent"
            BorderThickness="0"
            ItemsSource="{Binding ElementName=this,Path=Model.SelectedOptions}">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid
                         Height="18">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="190"/>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="90"/>
                        </Grid.ColumnDefinitions>


                        <TextBlock 
                            Grid.Column="0"
                            Margin="40,0,0,0"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Left"
                            FontSize="14"
                            Foreground="White">
                            <Run Text="* "/>
                            <Run Text="{Binding ModifierOption.Title}"/>
                        </TextBlock>


                        <TextBlock 
                            Grid.Column="2"
                            FontSize="14"
                            Foreground="White"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center">
                            <Run Text="{Binding Amount}"/>
                            <Run Text=" x "/>
                            <Run Text="{Binding ModifierOption.Price}"/>
                        </TextBlock>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>

        </ListBox>
    </Grid>
</UserControl>
