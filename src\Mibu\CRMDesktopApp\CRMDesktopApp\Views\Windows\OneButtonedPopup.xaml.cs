﻿using CRMDesktopApp.Abstactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для OneButtonedPopup.xaml
    /// </summary>
    public partial class OneButtonedPopup : BaseWindow
    {
        public OneButtonedPopup(string msg, string caption)
        {
            InitializeComponent();
            Message = msg;
            Caption = caption;
        }

        private string caption;
        public string Caption
        {
            get => caption;
            set { caption = value; OnPropertyChanged(nameof(Caption)); }
        }
        private string message;
        public string Message
        {
            get => message;
            set { message = value; OnPropertyChanged(nameof(Message)); }
        }
    }
}
