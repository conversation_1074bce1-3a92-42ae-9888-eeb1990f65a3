﻿using CRM.Models.Enums.Info;
using CRM.Models.Network.LoyaltyProgram;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для NewClientPopup.xaml
    /// </summary>
    public partial class NewClientPopup : BaseWindow
    {
        public NewClientPopup()
        {
            GenderStrings = new List<string> { "Мужской", "Женский" };

            InitializeComponent();

           // dateEdit.DateTime = DateTime.Now;
        }
        private Customer newClient = new Customer();
        public Customer NewClient
        {
            get => newClient;
            set { newClient = value; OnPropertyChanged(nameof(NewClient)); }
        }



        private string selectedGenderString;
        public string SelectedGenderString
        {
            get => selectedGenderString;
            set { selectedGenderString = value; OnPropertyChanged(nameof(SelectedGenderString)); }
        }

        private List<string> genderStrings = new List<string>();
        public List<string> GenderStrings
        {
            get => genderStrings;
            set { genderStrings = value; OnPropertyChanged(nameof(GenderStrings)); }
        }



        public event EventHandler<Customer> OnClientCreated;
        #region Команды
        private ICommand createClient;
        public ICommand CreateClient
        {
            get => createClient ??= new RelayCommand(async obj =>
            {
                NewClient.BirthDate = dateEdit.DateTime;
                if (SelectedGenderString == "Мужской")
                {
                    NewClient.Gender = Gender.Male;
                }
                else if (SelectedGenderString == "Женский")
                {
                    NewClient.Gender = Gender.Female;
                }

                var client = await MobileAPI.BaristaMethods.BaristaClientsMethods.CreateClient(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id, NewClient);

                OnClientCreated?.Invoke(this, NewClient);

                this.Close();
            });
        }
        #endregion
    }
}
