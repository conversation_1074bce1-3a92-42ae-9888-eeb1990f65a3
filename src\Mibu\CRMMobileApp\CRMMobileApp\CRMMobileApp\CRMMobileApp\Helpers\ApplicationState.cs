﻿using CRM.Models;
using CRM.Models.Core;
using CRM.Models.Gamification;
using CRM.Models.Network;
using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CrmAdminApp.Helpers;
using CRMGamificationAP<PERSON>Wrapper;
using CRMMobileApp;
using CRMMobileApp.Enums;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using CRMWeb.MobileAPI.Models.MenuCategoryTree;
using Fiscalization;
using Newtonsoft.Json;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace CRMAdminMoblieApp.Helpers
{
    public static class ApplicationState
    {
        public static string CurrentDomain { get; set; }
        public static UserDomainSubscription Subscription { get; set; }


        public static TradeNetwork CurrentTradeNetwork { get; set; }
        public static Store CurrentStore { get; set; }


        public static Duty CurrentDuty { get; set; }




        public static List<Product> StoreAllProducts { get; set; } = new List<Product>();
        public static List<TechnicalCard> StoreAllTechnicalCards { get; set; } = new List<TechnicalCard>();
        public static MenuCategoriesTree StoreMenuTree { get; set; }



        public static List<Acquiring> StoreAcquirings { get; set; } = new List<Acquiring>();
        public static List<Printer> StorePrinters { get; set; } = new List<Printer>();
        public static List<Scales> StoreScales { get; set; } = new List<Scales>();
        public static Dictionary<FiscalRegistrator, Fiscalizer> StoreFiscalRegistrators { get; set; } = new Dictionary<FiscalRegistrator, Fiscalizer>();
        public static Fiscalizer FiscalizerInUse { get; set; }


        public static List<Store> SelectedStoresInGamification { get; set; } = new List<Store>();
        public static List<int> SelectedStoresInGamificationIds => SelectedStoresInGamification.Select(o => o.Id).ToList();

        public static GamificationSettings GamificationSettings { get; set; }


        public static async Task LoadEquipment()
        {
            StorePrinters = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetPrinters(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            StoreScales = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetScales(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            // StoreAcquirings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAcquirings(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);

            await LoadFiscalizers();
        }
        public static async Task LoadFiscalizers()
        {
            bool dontHaveConnectionForAny = false;
            StoreFiscalRegistrators.Clear();


            var fiscalizers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetFiscalRegistrators(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            foreach (var fiscalizer in fiscalizers)
            {
                var driver = new Fiscalizer(fiscalizer, Auth.User);

                if(!dontHaveConnectionForAny)
                {
                    dontHaveConnectionForAny = !driver.InitFiscalizer();
                }

                StoreFiscalRegistrators.Add(fiscalizer, driver);
            }
            //if (dontHaveConnectionForAny && Device.RuntimePlatform == Device.WPF)
            //{
            //    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Проверьте подключение к ФР и/или установите драйвера с официального сайта", "Ошибка"));
            //}

        }





        public static async Task LoadMenu()
        {
            StoreAllProducts = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetProducts(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            StoreAllTechnicalCards = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetTechnicalCards(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);

            StoreMenuTree = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetMenuCategoriesTree(ApplicationState.CurrentDomain,
                                                                                                      ApplicationState.CurrentTradeNetwork.Id,
                                                                                                      ApplicationState.CurrentStore.Id);
        }


        #region Фискализация

        public static FiscalizationStatus IsFiscalizationAvailable()
        {
            if (!CurrentStore.StoreFiscalisationSettings.UseFiscalRegistrator)
            {
                return FiscalizationStatus.NotRequired;
            }
            else if (!StoreFiscalRegistrators.Any())
            {
                return FiscalizationStatus.HasNoRegistrators;
            }
            return FiscalizationStatus.CanBeDone;
        }
        
        public static bool IsFiscalizerAvailableOnThisPlatform()
        {
            if (Device.RuntimePlatform != Device.WPF)
            {
                return false;
            }
            return true;
        }

        #endregion

        #region Работа со сменами
        public static async Task GetLastDuty()
        {
            CurrentDuty = await MobileAPI.BaristaMethods.BaristaDutiesMethods.GetCurrentDuty(CurrentDomain, CurrentStore.Id);
        }
        public static async Task OpenDuty(double cashInCassa)
        {
            CurrentDuty = await MobileAPI.BaristaMethods.BaristaDutiesMethods.OpenDuty(
                                                            ApplicationState.CurrentDomain,
                                                            ApplicationState.CurrentTradeNetwork.Id,
                                                            ApplicationState.CurrentStore.Id,
                                                            Auth.User.Id,
                                                            cashInCassa);
        }
        public static async Task CloseDuty(double sum,string comment)
        {
            await MobileAPI.BaristaMethods.BaristaDutiesMethods.CloseDuty(CurrentDomain,
                                                                          CurrentTradeNetwork.Id,
                                                                          CurrentStore.Id,
                                                                          CurrentDuty.Id,
                                                                          sum,
                                                                          comment);
            CurrentDuty = null;
        }
        #endregion

        #region Сохранение и загрузка
        public async static Task LoadData()
        {
            var data = GetFromFile<SavedData>(SavedData.FilePath);

            CurrentDomain = data.CurrentDomain;
            if (!string.IsNullOrEmpty(CurrentDomain))
            {
                Subscription = await MobileAPI.MainMethods.GetDomainSubscription(CurrentDomain);
            }
          

            if (data.CurrentTradeNetworkId != null)
            {
                CurrentTradeNetwork = await MobileAPI.MainMethods.GetTradeNetwork(CurrentDomain, (int)data.CurrentTradeNetworkId);
            }
            if (data.CurrentStoreId != null)
            {
                CurrentStore = await MobileAPI.MainMethods.GetStore(CurrentDomain, (int)data.CurrentStoreId);
                SelectedStoresInGamification = new List<Store> { CurrentStore };
            }

            if (data.POSTerminalId != null)
            {
                Auth.POSTerminal = await MobileAPI.MainMethods.GetPosTerminal(CurrentDomain, (int)data.POSTerminalId);
            }
            if (!string.IsNullOrEmpty(data.NetworkTerminalUserToken))
            {
                var user = await MobileAPI.MainMethods.GetBaristaUserBySessionToken(CurrentDomain, data.NetworkTerminalUserToken);
                Auth.User = user;

                if (data.GamificationUserId != null && Auth.User != null)
                {
                    var gamificationUser = await GamificationAPI.Barista.Profile.GetProfileByTerminalCRMUser(CurrentDomain, Auth.User.Id);
                    Auth.GamificationUser = gamificationUser;
                }
            }
           
        }
        public static void SaveChangesToMemory()
        {
            var data = new SavedData()
            {
                CurrentDomain = CurrentDomain,
                CurrentStoreId = CurrentStore?.Id,
                CurrentTradeNetworkId = CurrentTradeNetwork?.Id,

                POSTerminalId = Auth.POSTerminal?.Id,
                NetworkTerminalUserToken = Auth.User?.SessionToken,
                GamificationUserId = Auth.GamificationUser?.Id
            };

            SaveToFile(data, SavedData.FilePath);
        }


        public static T GetFromFile<T>(string filepath) where T : class, new()
        {
            T obj = null;
            if (File.Exists(filepath))
            {
                using (StreamReader file = File.OpenText(filepath))
                {
                    try
                    {
                        var json = file.ReadToEnd();
                        obj = JsonConvert.DeserializeObject<T>(json);

                    }
                    catch { }
                }
            }
            if (obj is null) obj = new T();
            return obj;
        }
        public static void SaveToFile(object file, string filepath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(file);
                File.WriteAllText(filepath, json);
            }
            catch { }
        }

        #endregion

    }
}
