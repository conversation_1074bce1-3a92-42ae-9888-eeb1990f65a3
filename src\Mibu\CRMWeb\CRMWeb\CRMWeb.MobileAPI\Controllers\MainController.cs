﻿using CRM.Database.Core;
using CRM.Models;
using CRM.Models.Core;
using CRM.Models.Enums;
using CRM.Models.Network;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class MainController : AbsController
    {
        public MainController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        #region Авторизация
        [HttpGet, Route("AuthorizeToAdminApp")]
        public NetworkAdminTabUser AuthorizeToAdminApp(string domain,string login,string password)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.NetworkAdminTabUsers
                        .Where(o => !o.IsDeleted)
                        .FirstOrDefault(o => (o.Login == login || o.Email == login) 
                                            && o.Password == password
                                            && o.Status == AdminTabUserStatus.Active
                                            && (o.Role == NetworkUserRole.Owner || o.Role == NetworkUserRole.Admin));
                 }
                catch (Exception ex)
                {
                    //return new NetworkAdminTabUser { Name =ex.Message + ex.StackTrace}; 
                    return null;
                }
            }
        }


        [HttpGet, Route("AuthorizeToBaristaApp")]
        public POSTerminal AuthorizeToBaristaApp(string domain, string login, string password)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.POSTerminals
                            .Include(o => o.Store).ThenInclude(o => o.StoreFiscalisationSettings)
                            .Include(o => o.Store).ThenInclude(o => o.ClientsAndBonusesStoreSettings)
                            .Include(o => o.Store).ThenInclude(o => o.CommonStoreSettings)
                            .Include(o => o.Store).ThenInclude(o => o.StorePrintSettings)
                            .Where(o => !o.IsDeleted)
                            .FirstOrDefault(o => o.Login == login
                                                && o.Password == password
                                                && o.Status == PosTermanalStatuses.Active);
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }

        [HttpGet, Route("AuthorizeToBaristaAppByPin")]
        public NetworkTerminalUser AuthorizeToBaristaAppByPin(string domain, int posId, string pin)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.POSTerminals)
                        .Include(o => o.TerminalUsers)
                         .Where(o => !o.IsDeleted)
                        .FirstOrDefault(o => o.POSTerminals.Any(o => o.Id == posId));
                    return network.TerminalUsers.FirstOrDefault(o => o.PIN == pin && !o.IsDeleted);
                }
                catch (Exception ex)
                {
                    return new NetworkTerminalUser 
                    {
                        Name = ex.ToString()
                    };
                }
            }
        }

        [HttpGet, Route("GetBaristaUser")]
        public NetworkTerminalUser GetBaristaUser(string domain, int userId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.NetworkTerminalUsers.FirstOrDefault(o => !o.IsDeleted && o.Id == userId);
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }
        [HttpGet, Route("GetBaristaUserBySessionToken")]
        public NetworkTerminalUser GetBaristaUserBySessionToken(string domain, string token)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.NetworkTerminalUsers.FirstOrDefault(o => !o.IsDeleted && o.SessionToken == token);
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }
        [HttpGet, Route("HasBaristaUserBySessionToken")]
        public bool HasBaristaUserBySessionToken(string domain, string token)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                return db.NetworkTerminalUsers.FirstOrDefault(o => !o.IsDeleted && o.SessionToken == token) != null;
            }
        }


        #endregion

        #region Получение сети по POS терминалу

        [HttpGet, Route("GetTradeNetworkByPOS")]
        public TradeNetwork GetTradeNetworkByPOS(string domain, int posId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var networks = db.TradeNetworks.Include(o => o.POSTerminals);
                    return networks.FirstOrDefault(o => o.POSTerminals.Any(o => o.Id == posId));
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }
    
        [HttpGet, Route("GetPosTerminal")]
        public POSTerminal GetPosTerminal(string domain, int posId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.POSTerminals.FirstOrDefault(o => o.Id == posId);
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
        }


        #endregion

        #region Торговые сети и точки

        [HttpGet, Route("GetTradeNetworks")]
        public List<TradeNetwork> GetTradeNetworks(string domain)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var networks = db.TradeNetworks.Include(o => o.Stores).ThenInclude(o => o.StoreFiscalisationSettings)
						                           .Include(o => o.Stores).ThenInclude(o => o.ClientsAndBonusesStoreSettings)
						                           .Include(o => o.Stores).ThenInclude(o => o.CommonStoreSettings)
						                           .Include(o => o.Stores).ThenInclude(o => o.StorePrintSettings)
						                           .Include(o => o.SettingSafety)
						                           .Include(o => o.NetworkSettings)
						                           .Where(o => !o.IsDeleted)
						                           .ToList();

                    foreach(var network in networks)
                    {
                        network.Stores = new System.Collections.ObjectModel.ObservableCollection<Store>(network.Stores.Where(o => !o.IsDeleted));
					}

                    return networks;
				}
                catch (Exception ex)
                {
                    return new List<TradeNetwork>();
                }
            }
        }
        [HttpGet, Route("GetTradeNetwork")]
        public TradeNetwork GetTradeNetwork(string domain,int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks.Include(o => o.Stores).ThenInclude(o => o.StoreFiscalisationSettings)
						                          .Include(o => o.Stores).ThenInclude(o => o.ClientsAndBonusesStoreSettings)
						                          .Include(o => o.Stores).ThenInclude(o => o.CommonStoreSettings)
						                          .Include(o => o.Stores).ThenInclude(o => o.StorePrintSettings)
						                          .Include(o => o.SettingSafety)
						                          .Include(o => o.NetworkSettings)
						                          .Where(o => !o.IsDeleted)
						                          .FirstOrDefault(o => o.Id == networkId);

					network.Stores = new System.Collections.ObjectModel.ObservableCollection<Store>(network.Stores.Where(o => !o.IsDeleted));

                    return network;
				}
                catch (Exception ex) { return null; }
            }
        }
        [HttpGet, Route("GetStore")]
        public Store GetStore(string domain, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    return db.Stores
                        .Include(o => o.CommonStoreSettings)
                        .Include(o => o.ClientsAndBonusesStoreSettings)
                        .Include(o => o.StoreFiscalisationSettings)
                        .Include(o => o.StorePrintSettings)
                        .Where(o => !o.IsDeleted)
                        .FirstOrDefault(o => o.Id == storeId);
                }
                catch (Exception ex) { return null; }
            }
        }
        #endregion


        [HttpGet, Route("GetDomainSubscription")]
        public UserDomainSubscription GetDomainSubscription(string domain)
        {
            using (CoreDBContext coreDB = new CoreDBContext())
            {
                var userDomain = coreDB.UserDomains.Include(o => o.Subscription).ThenInclude(o => o.Subscription)
                                                   .FirstOrDefault(o => o.Name == domain);
                return userDomain.Subscription;
            }
        }

        [HttpGet, Route("DoesDomainExist")]
        public bool DoesDomainExist(string domain)
        {
            using (CoreDBContext coreDB = new CoreDBContext())
            {
                var userDomain = coreDB.UserDomains.Include(o => o.Subscription).ThenInclude(o => o.Subscription)
                                                   .FirstOrDefault(o => o.Name == domain);


                return userDomain?.Subscription != null;
            }
        }

        [HttpGet, Route("IsSubscriptionExpired")]
        public bool IsSubscriptionExpired(string domain)
        {
            using (CoreDBContext coreDB = new CoreDBContext())
            {
                var userDomain = coreDB.UserDomains.Include(o => o.Subscription).ThenInclude(o => o.Subscription)
                                                   .FirstOrDefault(o => o.Name == domain);


                if(userDomain?.Subscription != null)
                {
                    return userDomain.Subscription.IsExpired;
                }

                return true;
            }
        }



        [HttpGet, Route("Хуй")]
        public string Хуй()
        {
            return "Хуй";
        }
    }
}
