﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="http://xamarin.com/schemas/2020/toolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:tables="clr-namespace:CRMMobileApp.Controls.Templates.Tables"
    x:Name="this"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    CloseWhenBackgroundIsClicked="False"
    x:Class="CRMMobileApp.Views.Popups.XCashierReportPopup">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            CornerRadius="20"
            Padding="0"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,50,0,0"
            WidthRequest="600"
            HeightRequest="500">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="80"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Label
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="X-Отчет официанта"/>
                </Grid>

                <Grid Grid.Row="1">

                    <StackLayout x:Name="reportStackLayout">

                    </StackLayout>

                    <Label
                       x:Name="pleaseWaitLabel"
                       VerticalOptions="Center"
                       HorizontalOptions="Center"
                       TextColor="{StaticResource dark_purple}"
                       FontFamily="TTFirsNeue-Regular"
                       FontSize="14"
                       Text="Подождите. Отчет загружается..."/>

                </Grid>

                <Grid Grid.Row="2">

                    <StackLayout
                        VerticalOptions="End"
                        HorizontalOptions="End"
                        Margin="0,0,30,30"
                        Spacing="20"
                        Orientation="Horizontal">

                        <Button 
                            Command="{Binding Source={x:Reference this},Path=Close}"
                            Style="{StaticResource bg_purple_btn}"
                            VerticalOptions="Center"
                            Text="Закрыть"
                            WidthRequest="170"
                            HeightRequest="40"/>
                        <Button 
                            Command="{Binding Source={x:Reference this},Path=PrintReport}"
                            Style="{StaticResource purple_gradient_btn}"
                            VerticalOptions="Center"
                            Text="Напечатать"
                            WidthRequest="170"
                            HeightRequest="40"/>
                    </StackLayout>

                </Grid>

            </Grid>
        </Frame>


    </Grid>
</animations:PopupPage>