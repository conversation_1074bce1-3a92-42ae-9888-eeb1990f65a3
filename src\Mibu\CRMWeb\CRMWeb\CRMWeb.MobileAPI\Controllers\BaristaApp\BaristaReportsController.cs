﻿using CRM.Database.Core;
using CRM.Models.Enums;
using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Reports.Mobile.BalancesAtStock;
using CRM.Models.Reports.Mobile.ByIngridientSales;
using CRM.Models.Reports.Mobile.SalesOnDuty;
using CRM.Models.Reports.Mobile.X_CashierReport;
using CRM.Models.Reports.Mobile.X_Report;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaReportsController : AbsController
    {
        public BaristaReportsController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpGet, Route("GetCashierXReport")]
        public async Task<XCashierReport> GetCashierXReport(string domain, int networkId, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Ingridients)
                        .Include(o => o.Workshops)
                        .Include(o => o.Products).ThenInclude(o => o.MeasureUnit)
                        .FirstOrDefault(o => o.Id == networkId);

                    var store = db.Stores
                        .Include(o => o.Orders).ThenInclude(o => o.Duty)
                        .Include(o => o.Orders).ThenInclude(o => o.Items)
                        .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                        .Include(o => o.Stock).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                        .Include(o => o.Stock).ThenInclude(o => o.Products).ThenInclude(o => o.Product).ThenInclude(o => o.MeasureUnit)
                        .FirstOrDefault(o => o.Id == storeId);


                    var duty = db.Duties.FirstOrDefault(o => o.Id == dutyId);
                    var orders = store.Orders.Where(o => o.DutyId == dutyId && o.ClosedAt != null && !o.IsCanceled && !o.WasReturned);

                    var report = new XCashierReport();
                    report.ChecksCount = orders.Count();

                    //TODO:  o.WorkshopId == workshop.Id
                    //foreach (var workshop in network.Workshops)
                    //{
                    //    var workshopOrders = orders.Where(o => o.WorkshopId == workshop.Id);
                    //    report.WorkshopRows.Add(new XCashierReportRow
                    //    {
                    //        Workshop = workshop,
                    //        Revenue = workshopOrders.Sum(o => o.Sum)
                    //    });
                    //}

                    report.ByCashless = orders.SelectMany(o => o.OrderPayments).Where(o => o.PaymentType?.OldPaymentType != OldPaymentType.Cash).Sum(o => o.Sum);
                    report.ByCash = orders.SelectMany(o => o.OrderPayments).Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Cash).Sum(o => o.Sum);
                    report.PaidByBonuses = orders.Sum(o => o.PaidByBonuses);

                    return report;
                }
                catch { return new(); }
            }
        }
     
        [HttpGet, Route("GetXReport")]
        public async Task<XReport> GetXReport(string domain, int networkId, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Ingridients)
                        .Include(o => o.Workshops)
                        .Include(o => o.Products).ThenInclude(o => o.MeasureUnit)
                        .FirstOrDefault(o => o.Id == networkId);

                    var store = db.Stores
                        .Include(o => o.Orders).ThenInclude(o => o.Duty)
                        .Include(o => o.Orders).ThenInclude(o => o.Items)
                        .Include(o => o.Orders).ThenInclude(o => o.OrderPayments).ThenInclude(o => o.PaymentType)
                        .Include(o => o.Stock).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                        .Include(o => o.Stock).ThenInclude(o => o.Products).ThenInclude(o => o.Product).ThenInclude(o => o.MeasureUnit)
                        .FirstOrDefault(o => o.Id == storeId);


                    var duty = db.Duties.FirstOrDefault(o => o.Id == dutyId);
                    var orders = store.Orders.Where(o => o.DutyId == dutyId && o.ClosedAt != null && !o.IsCanceled && !o.WasReturned);

                    var report = new XReport();
                    report.ChecksCount = orders.Count();

                    //TODO:  o.WorkshopId == workshop.Id
                    //foreach (var workshop in network.Workshops)
                    //{
                    //    var workshopOrders = orders.Where(o => o.WorkshopId == workshop.Id);
                    //    report.WorkshopRows.Add(new XReportWorkshopRow
                    //    {
                    //        Workshop = workshop,
                    //        Revenue = workshopOrders.Sum(o => o.Sum)
                    //    });
                    //}

                    report.ByCashless = orders.SelectMany(o => o.OrderPayments).Where(o => o.PaymentType?.OldPaymentType != OldPaymentType.Cash).Sum(o => o.Sum);
                    report.ByCash = orders.SelectMany(o => o.OrderPayments).Where(o => o.PaymentType?.OldPaymentType == OldPaymentType.Cash).Sum(o => o.Sum);
                    report.CashInCassa = duty.CashInCassaAtOpening;
                    report.PaidByBonuses = orders.Sum(o => o.PaidByBonuses);

                    return report;
                }
                catch { return new(); }
            }
        }




        [HttpGet, Route("GetBalancesAtStock")]
        public async Task<BalancesAtStockReport> GetBalancesAtStock(string domain, int networkId, int storeId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Ingridients)
                        .Include(o => o.Products).ThenInclude(o => o.MeasureUnit)
                        .FirstOrDefault(o => o.Id == networkId);

                    var store = db.Stores
                        .Include(o => o.Stock).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                        .Include(o => o.Stock).ThenInclude(o => o.Products).ThenInclude(o => o.Product).ThenInclude(o => o.MeasureUnit)
                        .FirstOrDefault(o => o.Id == storeId);


                    var report = new BalancesAtStockReport();
                    foreach(var ingridient in network.Ingridients)
                    {
                        report.Rows.Add(new BalancesAtStockReportRow
                        {
                            Title = ingridient.Title,
                            MeasureUnit = ingridient.MeasureUnit,
                            Amount = store.Stock.GetBalance(ingridient)
                        });
                    }
                    foreach (var product in network.Products)
                    {
                        report.Rows.Add(new BalancesAtStockReportRow
                        {
                            Title = product.Title,
                            MeasureUnit = product.MeasureUnit.Title,
                            Amount = store.Stock.GetBalance(product)
                        });
                    }

                    return report;
                }
                catch { return new(); }
            }
        }
    
        [HttpGet, Route("GetSalesAtDuty")]
        public async Task<SalesPerDutyReport> GetSalesAtDuty(string domain, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var store = db.Stores
                        .Include(o => o.Orders).ThenInclude(o => o.Duty)
                        .Include(o => o.Orders).ThenInclude(o => o.Discount)
                        .Include(o => o.Orders).ThenInclude(o => o.Customer)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.Product).ThenInclude(o => o.MeasureUnit)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.Product).ThenInclude(o => o.IngridientCategory)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.Product).ThenInclude(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.Product).ThenInclude(o => o.ProductPackagings)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.TechnicalCard).ThenInclude(o => o.MenuCategory).ThenInclude(o => o.MaxDiscount)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.TechnicalCard).ThenInclude(o => o.Ingridients).ThenInclude(o => o.Ingridient)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.TechnicalCard).ThenInclude(o => o.Products).ThenInclude(o => o.Product)
                        .Include(o => o.Orders).ThenInclude(o => o.Items).ThenInclude(o => o.TechnicalCard).ThenInclude(o => o.Subproducts).ThenInclude(o => o.Subproduct)
                        .FirstOrDefault(o => o.Id == storeId);

                    var orders = store.Orders.Where(o => o.DutyId == dutyId && o.ClosedAt != null && !o.IsCanceled && !o.WasReturned);
                    var orderItems = orders.SelectMany(o => o.Items);

                    var report = new SalesPerDutyReport();

                    var productGrouping = orderItems.Where(o => o.Product != null).GroupBy(o => o.Product);
                    foreach(var product in productGrouping)
                    {
                        report.ProductRows.Add(new SalesPerDutyReportProductRow
                        {
                            Product = product.Key,
                            Amount = product.ToList().Sum(o => o.Amount),
                        });
                    }

                    var techCardsGrouping = orderItems.Where(o => o.TechnicalCard != null).GroupBy(o => o.TechnicalCard);
                    foreach (var product in techCardsGrouping)
                    {
                        report.TechCardRows.Add(new SalesPerDutyReportTechRow
                        {
                            TechnicalCard = product.Key,
                            Amount = product.ToList().Sum(o => o.Amount)
                        });
                    }

                    return report;
                }
                catch { return new(); }
            }
        }
     
        [HttpGet, Route("GetIngridientSales")]
        public async Task<ByIngridientSalesReport> GetIngridientSales(string domain, int networkId, int storeId, int dutyId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks.Include(o => o.Ingridients)
                                                  .FirstOrDefault(o => o.Id == networkId);

                    var store = db.Stores.Include(o => o.Stock).ThenInclude(o => o.IngridientWriteOffs)
                                         .FirstOrDefault(o => o.Id == storeId);


                    var report = new ByIngridientSalesReport();

                    foreach(var ingridient in network.Ingridients.Where(o => !o.IsDeleted))
                    {
                        var writeOffs = store.Stock.IngridientWriteOffs.Where(o => o.DutyId == dutyId && o.IngridientId == ingridient.Id);
                        report.Ingridients.Add(new ByIngridientSalesReportRow
                        {
                            Title = ingridient.Title,
                            Amount = writeOffs.Sum(o => o.Count),
                            Sum = writeOffs.Sum(o => o.ActualPrice),
                        });
                    }


                    return report;
                }
                catch { return new(); }
            }
        }
    }
}
