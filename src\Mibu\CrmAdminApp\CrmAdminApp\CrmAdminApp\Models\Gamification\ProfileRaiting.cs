﻿using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace MobPhone.Model
{
    public class ProfileRaiting
    {


        public Color bgColor
        {
            get
            {
                if (notifi != null) return Color.FromHex("#E1DFFE");
                return Color.Transparent;
            }
        }

        public ImageSource notifi { get; set; }
        public string avatar { get; set; }
        public string FIO { get; set; }
        public string Position { get; set; }
        public string Address{ get; set; }
        public int level { get; set; }
        public double Coin { get; set; }
        public double Karma { get; set; }
        public double Karma_Total { get; set; }
    }
}
