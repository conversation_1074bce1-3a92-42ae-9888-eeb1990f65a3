﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Devices.PrinterItemTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Devices"
             x:Name="this"
             mc:Ignorable="d" 
             d:DesignHeight="150"
             d:DesignWidth="150">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border MouseDown="onItemTapped">
        <StackPanel VerticalAlignment="Center">
            <TextBlock 
                x:Name="providerLabel"
                FontFamily="TTFirsNeue-Regular"
                HorizontalAlignment="Left"
                FontSize="14"
                Text="{Binding ElementName=this,Path=Printer.Name}"
                Foreground="{StaticResource dark_purple}"/>
            <TextBlock 
                Margin="0,0,0,0"
                FontFamily="TTFirsNeue-Regular"
                HorizontalAlignment="Left"
                FontSize="14"
                Text="{Binding ElementName=this,Path=Printer.Path}"
                Foreground="{StaticResource text_gray}"/>
        </StackPanel>

    </Border>
</UserControl>
