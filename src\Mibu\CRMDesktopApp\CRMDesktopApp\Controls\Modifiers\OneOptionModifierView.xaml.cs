﻿using CRM.Models.Stores.Orders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Modifiers
{
    /// <summary>
    /// Логика взаимодействия для OneOptionModifierView.xaml
    /// </summary>
    public partial class OneOptionModifierView : UserControl
    {
        public OneOptionModifierView(OrderItemModifier itemModifier)
        {
            Model = itemModifier;
            InitializeComponent();

            if (!itemModifier.Modifier.IsRequired)
                optionalModifierCheckBox.Visibility = Visibility.Visible;

            optionalModifierCheckBox.IsChecked = itemModifier.SelectedOptions.Any();

            RenderOptions();
        }

        private void RenderOptions()
        {


            foreach (var option in Model.AllOptions)
            {
                var optionView = new OneOptionModifierOptionItem(option)
                {
                    Height = 40
                };
                optionView.radioButton.IsChecked = option.IsSelected;

                optionView.OnChecked += OptionView_OnChecked;
                modifiersLayout.Children.Add(optionView);

            }
            this.Height = Model.AllOptions.Count * 40 + 60;
        }

        private void OptionView_OnChecked(object sender, OrderItemModifierOption e)
        {
            foreach (OneOptionModifierOptionItem child in modifiersLayout.Children)
            {
                if (child != sender)
                {
                    child.Model.IsSelected = false;
                    child.radioButton.IsChecked = false;
                }
            }
        }

        public static readonly DependencyProperty ModelProperty =
            DependencyProperty.Register(nameof(Model), typeof(OrderItemModifier), typeof(OneOptionModifierView));
        public OrderItemModifier Model
        {
            get { return (OrderItemModifier)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
