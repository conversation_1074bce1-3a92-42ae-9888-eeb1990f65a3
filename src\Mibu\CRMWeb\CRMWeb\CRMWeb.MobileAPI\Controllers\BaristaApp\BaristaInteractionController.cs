﻿using CRM.Database.Core;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaInteractionController : AbsController
    {
        public BaristaInteractionController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpGet, Route("OpenCashBox")]
        public async Task<string> OpenCashBox(string domain, int storeId)
        {
            return "Хуй";
        }
        [HttpGet, Route("PrintCheck")]
        public async Task<string> PrintCheck(string domain, int storeId)
        {
            return "Хуй";
        }
    }
}
