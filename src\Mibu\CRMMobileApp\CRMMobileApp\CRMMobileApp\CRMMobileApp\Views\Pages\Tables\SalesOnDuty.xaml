﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:dg="clr-namespace:Xamarin.Forms.DataGrid;assembly=Xamarin.Forms.DataGrid" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts"
             xmlns:tables="clr-namespace:CRMMobileApp.Controls.Templates.Tables"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:collectionview="http://schemas.devexpress.com/xamarin/2014/forms/collectionview" 
             xmlns:listviews="clr-namespace:CRMMobileApp.Controls.Parts.ListViews"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Tables.SalesOnDuty">
    <ContentPage.Content>
        <Grid BackgroundColor="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <parts:HeaderSearch 
                Title="Продажи за смену"
                Grid.Row="0"/>

            <Grid Grid.Row="1">

                <Frame
                    CornerRadius="10"
                    BackgroundColor="{StaticResource bg_purple}"
                    VerticalOptions="Fill"
                    HorizontalOptions="Center"
                    Margin="0,20,0,40"
                    WidthRequest="670">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="45"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="0.5*"/>
                                <ColumnDefinition Width="0.5*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">
                                <Label
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    TextColor="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    Text="Товар"/>
                            </Grid>

                            <Grid Grid.Column="1">
                                <Label
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    TextColor="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    Text="Сумма"/>
                            </Grid>


                            <Grid Grid.Column="2">

                                <Label
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    TextColor="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="14"
                                    Text="Кол-во"/>


                                <!--<Button 
                                    CornerRadius="20"
                                    Padding="0"
                                    Margin="0,15,40,0"
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    TextColor="White"
                                    Text="Все"
                                    FontSize="14"
                                    TextTransform="None"
                                    Background="{StaticResource purple_gradient}"
                                    WidthRequest="90"
                                    HeightRequest="30"/>-->

                            </Grid>

                        </Grid>


                        <Grid Grid.Row="1">


                            <listviews:CustomCrossCollectionView
                                BackgroundColor="{StaticResource bg_purple}"
                                Background="{StaticResource bg_purple}"
                                SelectionMode="None"
                                MinItemSize="50"
                                ItemSpacing="7"
                                ItemsSource="{Binding Source={x:Reference this},Path=Rows}">
                                <listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                                    <DataTemplate>
                                        <tables:SalesOnDutyReportRow
                                            HeightRequest="50"
                                            Padding="0,7,0,0"
                                            VerticalOptions="Start"
                                            Model="{Binding}"/>
                                    </DataTemplate>
                                </listviews:CustomCrossCollectionView.ItemTemplateForMobile>
                                <listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                                    <DataTemplate>
                                        <ViewCell>
                                            <tables:SalesOnDutyReportRow
                                                HeightRequest="50"
                                                Padding="0,7,0,0"
                                                VerticalOptions="Start"
                                                Model="{Binding}"/>
                                        </ViewCell>
                                    </DataTemplate>
                                </listviews:CustomCrossCollectionView.ItemTemplateForWPF>
                            </listviews:CustomCrossCollectionView>

                            <Label
                              x:Name="noItemsLabel"
                              IsVisible="False"
                              VerticalOptions="Center"
                              HorizontalOptions="Center"
                              TextColor="{StaticResource dark_purple}"
                              FontFamily="TTFirsNeue-Regular"
                              FontSize="14"
                              Text="Отчет по продажам за смену пуст"/>

                        </Grid>

                    </Grid>
                </Frame>


            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>