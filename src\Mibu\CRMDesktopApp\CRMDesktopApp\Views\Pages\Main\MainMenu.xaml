﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.Main.MainMenu"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages.Main" 
      xmlns:parts="clr-namespace:CRMDesktopApp.Controls.Parts" 
      xmlns:devices="clr-namespace:CRMDesktopApp.Controls.Templates.Devices" xmlns:flexboxlayout="clr-namespace:FlexboxLayout"
    mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450" 
      d:DesignWidth="800"
      Title="MainMenu">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <parts:HeaderSearch 
            Title="Меню"
            Grid.Row="0"/>

        <Grid Grid.Row="1"
            Background="White">

            <WrapPanel
                HorizontalAlignment="Center"
                Margin="0,20,0,0">
                
                <RadioButton
                    IsChecked="True"
                    Checked="MainTabSelected"
                    Height="25"
                    Width="150">
                    <RadioButton.Style>
                        <Style TargetType="RadioButton">
                            <Style.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Главное"/>
                                                        <Rectangle
                                                            Fill="#524E7D"
                                                            Width="65"
                                                            StrokeThickness="0"
                                                            VerticalAlignment="Bottom"
                                                            HorizontalAlignment="Stretch"
                                                            Height="1"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                                <Trigger Property="IsChecked" Value="False">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Главное"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </RadioButton.Style>
                </RadioButton>

                <RadioButton
                    Checked="StockTabSelected"
                    Height="25"
                    Width="150">
                    <RadioButton.Style>
                        <Style TargetType="RadioButton">
                            <Style.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Склад"/>
                                                        <Rectangle
                                                            Fill="#524E7D"
                                                            Width="45"
                                                            StrokeThickness="0"
                                                            VerticalAlignment="Bottom"
                                                            HorizontalAlignment="Stretch"
                                                            Height="1"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                                <Trigger Property="IsChecked" Value="False">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Склад"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </RadioButton.Style>
                </RadioButton>

                <RadioButton
                    Checked="EquipmentSelected"
                    Height="25"
                    Width="150">
                    <RadioButton.Style>
                        <Style TargetType="RadioButton">
                            <Style.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Оборудование"/>
                                                        <Rectangle
                                                            Fill="#524E7D"
                                                            Width="110"
                                                            StrokeThickness="0"
                                                            VerticalAlignment="Bottom"
                                                            HorizontalAlignment="Stretch"
                                                            Height="1"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                                <Trigger Property="IsChecked" Value="False">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Оборудование"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </RadioButton.Style>
                </RadioButton>
            </WrapPanel>
            
            
            <TabControl
                x:Name="mainTabControl"
                BorderThickness="0"
                Background="Transparent"
                HorizontalAlignment="Center"
                VerticalAlignment="Stretch"
                Margin="0,30,0,0"
                Width="600">

                <TabItem
                    Visibility="Hidden"
                    FontSize="20"
                    Header="Главное"
                    Foreground="{StaticResource text_gray}">
                    <Grid 
                        HorizontalAlignment="Center">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="75"/>
                            <RowDefinition Height="75"/>
                            <RowDefinition Height="75"/>
                            <RowDefinition Height="75"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="240"/>
                            <ColumnDefinition Width="240"/>
                        </Grid.ColumnDefinitions>


                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="GoToClosedOrders"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="0"
                            Grid.Row="0">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/closedOrders.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="Закрытые заказы"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="GoToCreateTransaction"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="1"
                            Grid.Row="0">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/create_transaction.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="Создать транзакцию"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="ShowXCashierReport"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="0"
                            Grid.Row="1">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/x_report_cashier.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="X-отчет кассира"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="ShowXReport"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="1"
                            Grid.Row="1">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/x_report.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="X-отчет"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="OpenCashBox"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="0"
                            Grid.Row="2">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/open_cashbox.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="Открыть денежный ящик"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="CloseDuty"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="1"
                            Grid.Row="2">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/close_duty.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="Закрыть смену"/>
                            </StackPanel>
                        </Border>

                        <Border
                            x:Name="deliveryBorder"
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="GoToDeliveriesPage"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="0"
                            Grid.Row="3">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/delivery.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="14"
                                    Text="Доставка"/>
                            </StackPanel>
                        </Border>

                    </Grid>
                </TabItem>

                <TabItem
                    Visibility="Hidden"
                    FontSize="20"
                    Header="Склад"
                    Foreground="{StaticResource text_gray}">
                    <Grid
                        HorizontalAlignment="Center">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="75"/>
                            <RowDefinition Height="75"/>
                            <RowDefinition Height="75"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="240"/>
                            <ColumnDefinition Width="240"/>
                        </Grid.ColumnDefinitions>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="GoToBalanceAtStock"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="0"
                            Grid.Row="0">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/balance_at_stock.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="12"
                                    Text="Остатки на складе"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="GoToSalesOnDuty"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="1"
                            Grid.Row="0">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/sales_on_duty.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="12"
                                    Text="Продажи за смену"/>
                            </StackPanel>
                        </Border>

                        <Border
                            Height="60"
                            Width="220"
                            Background="{StaticResource bg_purple}"
                            MouseDown="GoToSalesByIngridient"
                            CornerRadius="10"
                            Padding="0"
                            Grid.Column="0"
                            Grid.Row="1">
                            <StackPanel
                                Margin="10,0,0,0"
                                Orientation="Horizontal">
                                <Image 
                                    Source="pack://application:,,,/Resources/Images/sales_by_ingridients.png"
                                    VerticalAlignment="Center"
                                    Stretch="Fill"
                                    Height="22"
                                    Width="22"/>
                                <TextBlock
                                    Margin="14,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                                    FontSize="12"
                                    Text="Продажи по ингридиентам"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </TabItem>

                <TabItem
                    Visibility="Hidden"
                    FontSize="20"
                    Header="Оборудование"
                    Foreground="{StaticResource text_gray}">
                    <Grid HorizontalAlignment="Center">

                        <Border
                             CornerRadius="10"
                             Height="35"
                             Padding="0"
                             VerticalAlignment="Top"
                             Background="{StaticResource bg_purple}"
                             HorizontalAlignment="Center"
                             IsHitTestVisible="True"
                             Margin="0,0,0,0">
                            <WrapPanel      
                                IsHitTestVisible="True"
                                VerticalAlignment="Center">
                                <RadioButton
                                    IsChecked="True"
                                    Checked="AquiringSelected"
                                    Height="30"
                                    VerticalAlignment="Center"
                                    Width="150">
                                    <RadioButton.Style>
                                        <Style TargetType="RadioButton">
                                            <Style.Triggers>
                                                <Trigger Property="IsChecked" Value="True">
                                                    <Trigger.Setters>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Border
                                                                        CornerRadius="10"
                                                                        Padding="0"
                                                                        Background="{StaticResource purple}">
                                                                        <Grid>
                                                                            <TextBlock
                                                                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                                                TextAlignment="Center"
                                                                                HorizontalAlignment="Center"
                                                                                VerticalAlignment="Center"
                                                                                Foreground="#FFFFFF"
                                                                                FontSize="15"
                                                                                Text="Эквайринг"/>
                                                                        </Grid>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger.Setters>
                                                </Trigger>
                                                <Trigger Property="IsChecked" Value="False">
                                                    <Trigger.Setters>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Grid>
                                                                        <TextBlock
                                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                                            TextAlignment="Center"
                                                                            HorizontalAlignment="Center"
                                                                            VerticalAlignment="Center"
                                                                            Foreground="{StaticResource text_gray}"
                                                                            FontSize="15"
                                                                            Text="Эквайринг"/>
                                                                    </Grid>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger.Setters>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </RadioButton.Style>
                                </RadioButton>

                                <RadioButton
                                    Checked="PrintersSelected"
                                    Height="30"
                                    VerticalAlignment="Center"
                                    Width="150">
                                    <RadioButton.Style>
                                        <Style TargetType="RadioButton">
                                            <Style.Triggers>
                                                <Trigger Property="IsChecked" Value="True">
                                                    <Trigger.Setters>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Border
                                                                        CornerRadius="10"
                                                                        Padding="0"
                                                                        Background="{StaticResource purple}">
                                                                        <Grid>
                                                                            <TextBlock
                                                                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                                                TextAlignment="Center"
                                                                                HorizontalAlignment="Center"
                                                                                VerticalAlignment="Center"
                                                                                Foreground="#FFFFFF"
                                                                                FontSize="15"
                                                                                Text="Принтеры"/>
                                                                        </Grid>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger.Setters>
                                                </Trigger>
                                                <Trigger Property="IsChecked" Value="False">
                                                    <Trigger.Setters>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Grid>
                                                                        <TextBlock
                                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                                            TextAlignment="Center"
                                                                            HorizontalAlignment="Center"
                                                                            VerticalAlignment="Center"
                                                                            Foreground="{StaticResource text_gray}"
                                                                            FontSize="15"
                                                                            Text="Принтеры"/>
                                                                    </Grid>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger.Setters>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </RadioButton.Style>
                                </RadioButton>

                                <RadioButton
                                    Checked="CashRegistersSelected"
                                    Height="30"
                                    VerticalAlignment="Center"
                                    Width="150">
                                    <RadioButton.Style>
                                        <Style TargetType="RadioButton">
                                            <Style.Triggers>
                                                <Trigger Property="IsChecked" Value="True">
                                                    <Trigger.Setters>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Border
                                                                        CornerRadius="10"
                                                                        Padding="0"
                                                                        Background="{StaticResource purple}">
                                                                        <Grid>
                                                                            <TextBlock
                                                                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                                                TextAlignment="Center"
                                                                                HorizontalAlignment="Center"
                                                                                VerticalAlignment="Center"
                                                                                Foreground="#FFFFFF"
                                                                                FontSize="15"
                                                                                Text="Кассовая техника"/>
                                                                        </Grid>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger.Setters>
                                                </Trigger>
                                                <Trigger Property="IsChecked" Value="False">
                                                    <Trigger.Setters>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Grid>
                                                                        <TextBlock
                                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                                            TextAlignment="Center"
                                                                            HorizontalAlignment="Center"
                                                                            VerticalAlignment="Center"
                                                                            Foreground="{StaticResource text_gray}"
                                                                            FontSize="15"
                                                                            Text="Кассовая техника"/>
                                                                    </Grid>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Trigger.Setters>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </RadioButton.Style>
                                </RadioButton>
                            </WrapPanel>
                        </Border>
                        
                        <TabControl
                            x:Name="equipmentTabControl"
                            Background="Transparent"
                            BorderThickness="0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Stretch"
                            Margin="0,7,0,0"
                            Width="600">

                            <TabItem
                                Visibility="Hidden"
                                FontSize="17"
                                Header="Эквайринг"
                                Foreground="{StaticResource text_gray}">
                                <Grid>
                                    <WrapPanel
                                        Margin="0,30,0,0"
                                        x:Name="acquringFlexLayout">

                                    </WrapPanel>
                                    
                                    <TextBlock
                                        x:Name="noAcquringItemsTextBlock"
                                        Visibility="Hidden"
                                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                                        TextAlignment="Center"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Foreground="{StaticResource text_gray}"
                                        FontSize="15"
                                        Width="300"
                                        TextWrapping="Wrap"
                                        Text="Нет доступных POS-терминалов. Добавить терминал можно в web панели Mibu"/>

                                </Grid>
                            </TabItem>
                            <TabItem
                                Visibility="Hidden"
                                FontSize="17"
                                Header="Принтеры"
                                Foreground="{StaticResource text_gray}">
                                <Grid>
                                    <WrapPanel
                                        Margin="0,30,0,0"
                                        x:Name="printersFlexLayout">

                                    </WrapPanel>

                                    <TextBlock
                                        x:Name="noPrintersItemsTextBlock"
                                        Visibility="Hidden"
                                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                                        TextAlignment="Center"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Foreground="{StaticResource text_gray}"
                                        FontSize="15"
                                        Width="300"
                                        TextWrapping="Wrap"
                                        Text="Нет доступных чековых принтеров. Добавить принтер можно в web панели Mibu"/>

                                </Grid>
                            </TabItem>
                            <TabItem
                                Visibility="Hidden"
                                FontSize="17"
                                Header="Кассовая техника"
                                Foreground="{StaticResource text_gray}">
                                <Grid>
                                    <WrapPanel
                                        Margin="0,30,0,0"
                                        x:Name="fiscalFlexLayout">

                                    </WrapPanel>

                                    <TextBlock
                                     x:Name="noFiscalItemsTextBlock"
                                     Visibility="Hidden"
                                     FontFamily="{StaticResource TTFirsNeue-Regular}"
                                     TextAlignment="Center"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     Foreground="{StaticResource text_gray}"
                                     FontSize="15"
                                     Width="300"
                                     TextWrapping="Wrap"
                                     Text="Нет доступных фискальных регистраторов. Добавить регистратор можно в web панели Mibu"/>

                                </Grid>
                            </TabItem>

                        </TabControl>
                    </Grid>
                </TabItem>


            </TabControl>

            <StackPanel
                Margin="0,0,0,15"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"
                Orientation="Horizontal">
                <Image
                    VerticalAlignment="Center"
                    Width="16"
                    Height="16"
                    Source="pack://application:,,,/Resources/Images/info.png"/>
                <Button 
                    Margin="10,0,0,0"
                    Command="{Binding ElementName=this,Path=AboutSoftware}"
                    Style="{StaticResource transparent_btn}"
                    Content="Информация о программе"
                    VerticalAlignment="Center"/>
            </StackPanel>

        </Grid>

    </Grid>
</abstactions:BasePage>
