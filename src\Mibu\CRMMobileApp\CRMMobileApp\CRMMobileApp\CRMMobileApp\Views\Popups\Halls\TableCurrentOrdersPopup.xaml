﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:CRMMobileApp.Controls"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.TableCurrentOrdersPopup">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            WidthRequest="450"
            HeightRequest="250"
            Padding="0"
            BackgroundColor="White"
            Background="White"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,70,0,0"
            CornerRadius="20">
            <Grid
                ColumnSpacing="0"
                RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="60"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Label
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span Text="Стол"/>
                                    <Span Text=" "/>
                                    <Span Text="{Binding Source={x:Reference this},Path=Table.Table.Title}"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </Grid>

                <Grid Grid.Row="1">
                    <ScrollView VerticalScrollBarVisibility="{OnPlatform Default=Default, WPF=Never}">
                        <StackLayout 
                            Margin="10,0,10,0"
                            x:Name="ordersStackLayout">



                        </StackLayout>
                    </ScrollView>
                </Grid>

                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Button 
                        Grid.Column="0"
                        Command="{Binding Source={x:Reference this},Path=Close}"
                        Style="{StaticResource gray_cornered_filled_btn}"
                        VerticalOptions="Center"
                        HorizontalOptions="End"
                        Margin="0,0,10,0"
                        Text="Отмена"
                        WidthRequest="160"
                        HeightRequest="40"/>

                    <Button 
                        Grid.Column="1"
                        Command="{Binding Source={x:Reference this},Path=CreateOrder}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Margin="10,0,0,0"
                        Text="Создать заказ"
                        WidthRequest="160"
                        HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>


    </Grid>
</animations:PopupPage>