﻿using CRM.Models.Network.Finances;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using System;
using MarkupCreator.Helpers.Converters;
using System.Transactions;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TransactionCard : ContentView
    {
        public TransactionCard(CRM.Models.Network.Finances.Transaction transaction)
        {
            InitializeComponent();
            Model = transaction;

            Render();
        }
        public TransactionCard()
        {
            InitializeComponent();
        }


        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if(BindingContext is CRM.Models.Network.Finances.Transaction transaction)
            {
                Model = transaction;
                Render();
            }
        }


        private void Render()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                operationLabel.Text = EnumDescriptionHelper.GetDescription(Model.Operation);
                transactionSumLabel.Text = $"{Math.Round(Model.Sum, 2)}₽";
                transactionTimeLabel.Text = Model.TransactionDate.ToString("dd.MM.yyyy HH:mm");

                switch (Model.Operation)
                {
                    case CRM.Models.Enums.FinanceOperation.DutyOpening:
                        dutyOpeningSign.IsVisible = true;
                        operationLabel.TextColor = Color.FromHex("#524E7D");
                        transactionSumLabel.TextColor = Color.FromHex("#26E27C");
                        break;
                    case CRM.Models.Enums.FinanceOperation.Incassaction:
                        dutyClosingSign.IsVisible = true;
                        operationLabel.TextColor = Color.FromHex("#FFFFFF");
                        transactionSumLabel.TextColor = Color.FromHex("#FFFFFF");
                        frame.BackgroundColor = Color.FromHex("#7265FB");
                        break;
                    case CRM.Models.Enums.FinanceOperation.DutyClosing:
                    case CRM.Models.Enums.FinanceOperation.Expense:
                    case CRM.Models.Enums.FinanceOperation.Income:
                        incassationSign.IsVisible = true;
                        operationLabel.TextColor = Color.FromHex("#524E7D");
                        transactionSumLabel.TextColor = Color.FromHex("#7265FB");
                        break;
                }
            });
        }





        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(CRM.Models.Network.Finances.Transaction), typeof(TransactionCard));
        public CRM.Models.Network.Finances.Transaction Model
        {
            get { return (CRM.Models.Network.Finances.Transaction)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}