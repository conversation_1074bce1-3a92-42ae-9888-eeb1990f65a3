﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    CloseWhenBackgroundIsClicked="False"
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Name="this"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.OrderPaidPopup">
    <Grid>


        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            WidthRequest="250"
            HeightRequest="250"
            Padding="0"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            Margin="0,200,0,0"
            CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>
                <Label 
                    Grid.Row="0"
                    FontSize="18"
                    FontAttributes="Bold"
                    TextColor="{StaticResource dark_purple}"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    Text="Заказ успешно оплачен"/>
                <StackLayout
                    Spacing="1"
                    Grid.Row="1"
                    HorizontalOptions="Center"
                    VerticalOptions="Center" >
                    <Label              
                        HorizontalOptions="Center"
                        FontSize="20"
                        TextColor="{StaticResource dark_purple}"
                        Text="Cумма заказа"/>
                    <Label          
                        x:Name="sumLabel"
                        FontSize="16"
                        HorizontalOptions="Center"
                        FontAttributes="Bold"
                        TextColor="{StaticResource dark_purple}"
                        Text="140 р"/>
                    <!--<Label           
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                Text="Карта"/>
            <Label        
                HorizontalOptions="Center"
                TextColor="{StaticResource dark_purple}"
                Text="140 р"/>-->
                </StackLayout>
                <Label 
                    Grid.Row="2"
                    x:Name="changeLabel"
                    FontSize="20"
                    FontAttributes="Bold"
                    TextColor="{StaticResource dark_purple}"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    Text="Без сдачи"/>
                <Button 
                    Grid.Row="3"
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    FontSize="16"
                    FontAttributes="Bold"
                    TextColor="White"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill"
                    Text="Закрыть"
                    Padding="3"
                    BackgroundColor="{DynamicResource purple}"
                    Background="{DynamicResource purple}"/>
            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>