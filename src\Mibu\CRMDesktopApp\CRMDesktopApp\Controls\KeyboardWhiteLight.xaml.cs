﻿using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls
{
    /// <summary>
    /// Логика взаимодействия для KeyboardWhiteLight.xaml
    /// </summary>
    public partial class KeyboardWhiteLight : UserControl
    {
        public event EventHandler<string> ButtonTapped;
        public KeyboardWhiteLight()
        {
            InitializeComponent();
        }

        private ICommand tapButton;
        public ICommand TapButton
        {
            get => tapButton ??= new RelayCommand(async obj =>
            {
                ButtonTapped?.Invoke(this, obj as string);
            });
        }
    }
}
