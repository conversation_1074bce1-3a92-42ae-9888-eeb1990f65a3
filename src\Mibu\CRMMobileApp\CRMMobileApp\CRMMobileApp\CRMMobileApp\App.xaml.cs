﻿using CRMAdminMoblieApp.Helpers;
using CRMGamificationAP<PERSON>Wrapper;
using CRMMobileApp.Views.Pages.Main;
using CRMMobileApp.Views.Pages.Orders;
using CRMMobileApp.Views.Pages.Tables;
using CRMMobileApp.Views.Popups;
using Rg.Plugins.Popup.Extensions;
using System;
using System.Threading.Tasks;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRMMoblieApiWrapper;

namespace CRMMobileApp
{
    public partial class App : Application
    {
        public App()
        {
     
            InitializeComponent();
            Sharpnado.CollectionView.Initializer.Initialize(true, false);

            DevExpress.XamarinForms.Editors.Initializer.Init();
            DevExpress.XamarinForms.DataForm.Initializer.Init();
            DevExpress.XamarinForms.Core.Initializer.Init();

            MainPage = new NavigationPage(new SplashPage());
        }

       


        protected async override void OnStart()
        {
            await ApplicationState.LoadData();
            if (Auth.POSTerminal == null)
            {
                MainPage = new NavigationPage(new LoginPage());
            }
            else
            {
                MainPage = new NavigationPage(new MainPage());
            }

            StartCheckSessionLoop();
        }

        protected override void OnSleep()
        {
        }

        protected override void OnResume()
        {
        }



        private static async Task StartCheckSessionLoop()
        {
            Device.StartTimer(TimeSpan.FromSeconds(60), () =>
            {
                CheckSessionLoop();
                return true;
            });
        }

        private static async Task CheckSessionLoop()
        {
            if (Auth.User != null)
            {
                if (await MobileAPI.MainMethods.HasBaristaUserBySessionToken(ApplicationState.CurrentDomain, Auth.User.SessionToken) == false)
                {
                    Auth.User = null;
                    await Device.InvokeOnMainThreadAsync(async () =>
                    {
                        App.Current.MainPage = new NavigationPage(new LoginPage());
                        await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Пароль пользователя был изменен.", "Ошибка", "Хорошо"));
                    });
                }
                else
                {
                    if (await MobileAPI.MainMethods.DoesDomainExist(ApplicationState.CurrentDomain) == false)
                    {
                        Auth.User = null;
                        ApplicationState.Subscription = null;

                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            App.Current.MainPage = new NavigationPage(new LoginPage());
                            await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Домен не существует или был удален", "Ошибка", "Хорошо"));
                        });
                    }
                    else if (await MobileAPI.MainMethods.IsSubscriptionExpired(ApplicationState.CurrentDomain) == true)
                    {
                        Auth.User = null;
                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            App.Current.MainPage = new NavigationPage(new LoginPage());
                            await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Необходимо оплатить подписку", "Ошибка", "Хорошо"));
                        });
                    }
                }
            }
        }
    }
}
