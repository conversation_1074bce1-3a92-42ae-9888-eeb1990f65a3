﻿using CRM.Models;
using CRM.Models.Core;
using CRM.Models.Network;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CrmAdminApp.Helpers;
using CRMDesktopApp.Views.Windows;
using CRMMoblieApiWrapper;
using Fiscalization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace CRMAdminMoblieApp.Helpers
{
    public static class ApplicationState
    {
        public static string CurrentDomain { get; set; }
        public static UserDomainSubscription Subscription { get; set; }


        public static TradeNetwork CurrentTradeNetwork { get; set; }
        public static Store CurrentStore { get; set; } 


        public static Duty CurrentDuty { get; set; }




        public static List<Scales> StoreScales { get; set; } = new List<Scales>();
        public static List<Acquiring> StoreAcquirings { get; set; } = new List<Acquiring>();
        public static List<Printer> StorePrinters { get; set; } = new List<Printer>();
        public static Dictionary<FiscalRegistrator,Fiscalizer> StoreFiscalRegistrators { get; set; } = new Dictionary<FiscalRegistrator, Fiscalizer>();



        public static async Task LoadEquipment()
        {
            StoreScales = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetScales(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            StorePrinters = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetPrinters(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            StoreAcquirings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAcquirings(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);     
        }

        public static async Task LoadFiscalizers()
        {
            bool hasDriverOrConnection = false;

            var fiscalizers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetFiscalRegistrators(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
            foreach (var fiscalizer in fiscalizers)
            {
                var driver = new Fiscalizer(fiscalizer, Auth.User);
                hasDriverOrConnection = driver.InitFiscalizer();

                if (!hasDriverOrConnection)
                {
                    break;
                }

                StoreFiscalRegistrators.Add(fiscalizer, driver);
            }
            if (!hasDriverOrConnection)
            {
                new OneButtonedPopup("Проверьте подключение к ФР и/или установите драйвера с официального сайта", "Ошибка").ShowDialog();
            }

        }


        #region Работа со сменами
        public static async Task GetLastDuty()
        {
            CurrentDuty = await MobileAPI.BaristaMethods.BaristaDutiesMethods.GetCurrentDuty(CurrentDomain, CurrentStore.Id);
        }
        public static async Task OpenDuty(double cashInCassa)
        {
            CurrentDuty = await MobileAPI.BaristaMethods.BaristaDutiesMethods.OpenDuty(
                                                            ApplicationState.CurrentDomain,
                                                            ApplicationState.CurrentTradeNetwork.Id,
                                                            ApplicationState.CurrentStore.Id,
                                                            Auth.User.Id,
                                                            cashInCassa);
        }
        public static async Task CloseDuty(double sum,string comment)
        {
            await MobileAPI.BaristaMethods.BaristaDutiesMethods.CloseDuty(CurrentDomain,
                                                                          CurrentTradeNetwork.Id,
                                                                          CurrentStore.Id,
                                                                          CurrentDuty.Id,
                                                                          sum,
                                                                          comment);
            CurrentDuty = null;
        }
        #endregion

        #region Сохранение и загрузка
        public async static Task LoadData()
        {
            var data = GetFromFile<SavedData>(SavedData.FilePath);

            CurrentDomain = data.CurrentDomain;
            if (!string.IsNullOrEmpty(CurrentDomain))
            {
                Subscription = await MobileAPI.MainMethods.GetDomainSubscription(CurrentDomain);
            }


            if (data.CurrentTradeNetworkId != null)
            {
                CurrentTradeNetwork = await MobileAPI.MainMethods.GetTradeNetwork(CurrentDomain, (int)data.CurrentTradeNetworkId);
            }
            if (data.CurrentStoreId != null)
            {
                CurrentStore = await MobileAPI.MainMethods.GetStore(CurrentDomain, (int)data.CurrentStoreId);
            }

            if (data.POSTerminalId != null)
            {
                Auth.POSTerminal = await MobileAPI.MainMethods.GetPosTerminal(CurrentDomain, (int)data.POSTerminalId);
            }
            if (data.NetworkTerminalUserId != null)
            {
                var user = await MobileAPI.MainMethods.GetBaristaUser(CurrentDomain, (int)data.NetworkTerminalUserId);
                Auth.User = user;

                //if (data.GamificationUserId != null)
                //{
                //    var gamificationUser = await GamificationAPI.Barista.Profile.GetProfileByTerminalCRMUser(CurrentDomain, (int)data.NetworkTerminalUserId);
                //    Auth.GamificationUser = gamificationUser;
                //}
            }

        }
        public static void SaveChangesToMemory()
        {
            var data = new SavedData()
            {
                CurrentDomain = CurrentDomain,
                CurrentStoreId = CurrentStore?.Id,
                CurrentTradeNetworkId = CurrentTradeNetwork?.Id,

                POSTerminalId = Auth.POSTerminal?.Id,
                NetworkTerminalUserId = Auth.User?.Id,
                //GamificationUserId = Auth.GamificationUser?.Id
            };

            SaveToFile(data, SavedData.FilePath);
        }


        public static T GetFromFile<T>(string filepath) where T : class, new()
        {
            T obj = null;
            if (File.Exists(filepath))
            {
                using (StreamReader file = File.OpenText(filepath))
                {
                    try
                    {
                        var json = file.ReadToEnd();
                        obj = JsonConvert.DeserializeObject<T>(json);

                    }
                    catch { }
                }
            }
            if (obj is null) obj = new T();
            return obj;
        }
        public static void SaveToFile(object file, string filepath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(file);
                File.WriteAllText(filepath, json);
            }
            catch { }
        }

        #endregion
    }
}
