﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.DutyOpeningPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Открытие смены"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        x:Name="this"
        WindowStartupLocation="CenterScreen"
        Width="670"
        Height="260">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="660"
        Height="255"
        Background="#FFFFFF"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="80"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">

                <StackPanel
                    Margin="15,30,0,0"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal">

                    <Image 
                        Margin="25,0,0,0"
                        Stretch="Fill"
                        Source="pack://application:,,,/Resources/Images/client.png"
                        Width="30"
                        Height="30"
                        VerticalAlignment="Center"/>

                    <TextBlock
                        Margin="15,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Открытие смены"/>

                </StackPanel>



                <customcontrols:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=ClosePopup}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,35,35,0"/>



            </Grid>


            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    Margin="35,0,0,0"
                    VerticalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="18"
                    Text="Наличными в кассе"/>

                <customcontrols:EntryOutlined
                    Grid.Column="1"
                    Text="{Binding ElementName=this,Path=CashInCassa,Mode=TwoWay}"
                    Placeholder="Сумма"
                    Margin="20,0,20,0"
                    Height="50"
                    Style="{StaticResource white_cornered_entry}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Center"/>
            </Grid>

            <Grid Grid.Row="2">

                <Button 
                    Command="{Binding ElementName=this,Path=OpenDuty}"
                    Style="{StaticResource bg_purple_btn}"
                    VerticalAlignment="Bottom"
                    HorizontalAlignment="Right"
                    Margin="0,0,20,30"
                    Content="Создать"
                    Width="140"
                    Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
