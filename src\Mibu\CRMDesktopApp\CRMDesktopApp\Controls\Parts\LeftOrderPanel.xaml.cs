﻿using CRM.Models.Enums.General;
using CRM.Models.Network.ReferenceBooks;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Controls.Templates;
using CRMDesktopApp.Views.Pages;
using CRMDesktopApp.Views.Pages.Orders;
using CRMDesktopApp.Views.Windows;
using CRMDesktopApp.Views.Windows.CountSelections;
using CRMDesktopApp.Views.Windows.Orders;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using TermoPrintingLib;

namespace CRMDesktopApp.Controls.Parts
{
    /// <summary>
    /// Логика взаимодействия для LeftOrderPanel.xaml
    /// </summary>
    public partial class LeftOrderPanel : UserControl, INotifyPropertyChanged
    {
        public LeftOrderPanel()
        {
            InitializeComponent();
            OrdersHelper.OnOrderItemsCountChanged += OrdersHelper_OnOrderItemsCountChanged;

            CurrentOrder = OrdersHelper.CurrentOrder;
            RenderItems();
            SetOrderCommentFrameVisibility();
            SetSendToWorkBtnVisibility();
            SetPriceText();

            splitOrderStackLayout.Visibility = ApplicationState.CurrentStore.StoreMode == StoreMode.Сafe ? Visibility.Visible : Visibility.Collapsed;
        }

        private void OrdersHelper_OnOrderItemsCountChanged(object sender, Order e)
        {
            CurrentOrder = OrdersHelper.CurrentOrder;
            if (OrdersHelper.CurrentOrder is null)
            {
                orderItemsLayout.Children.Clear();
                orderTotalLabel.Text = $"0.00₽";
                orderTotalWithDiscountLabel.Text = $"0.00₽";

                orderCreateCloseText.Text = "Открыть заказ";
                return;
            }

            SetPriceText();

            RenderItems();
        }



        #region Для закрытого заказа

        /// <summary>
        /// Метод для показа закрытого заказа
        /// </summary>
        /// <param name="order"></param>
        public void SetItems(Order order)
        {
            orderItemsLayout.Children.Clear();
            if (order is null) return;

            CurrentOrder = order;


            orderIdSpan.Text = order.OrderNumber.ToString();
            returnOrderBtn.Visibility = !order.WasReturned ? Visibility.Visible : Visibility.Collapsed;
            activeOrderFooter.Visibility = Visibility.Collapsed;
            activeOrderBottomLayout.Visibility = Visibility.Collapsed;

            foreach (var item in order.Items)
            {
                var view = new OrderItemTemplate(item);
                view.DeleteBtnTapped += View_DeleteBtnTapped;
                orderItemsLayout.Children.Add(view);
            }
        }
        public void SetClosedOrderMode()
        {
            orderIdSpan.Text = "XX";
            createCancelOrderFrame.Visibility = Visibility.Collapsed ;
            activeOrderFooter.Visibility = Visibility.Collapsed;
            activeOrderBottomLayout.Visibility = Visibility.Collapsed;

            orderCommentFrame.Visibility = Visibility.Collapsed;
            sendItemsToWork.Visibility = Visibility.Collapsed;

            closedOrderFooter.Visibility = Visibility.Visible;

        }

        #region Печать чека закрытого заказа
        private ICommand printClosedOrderCheque;
        public ICommand PrintClosedOrderCheque
        {
            get => printClosedOrderCheque ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.StorePrinters.Count == 0)
                {
                    new OneButtonedPopup("Печать невозможна, нет ни одного принтера для печати чеков", "Ошибка").ShowDialog();
                }
                else
                {

                    if (ApplicationState.StorePrinters.Count == 1)
                    {
                        var printer = ApplicationState.StorePrinters[0];
                        PrintClosedOrder(printer);
                    }
                    else
                    {
                        var popup = new AvailablePrintersPopup(CRM.Models.Enums.Equipment.PrinterType.OrdersForWorkshops);
                        popup.ItemSelected += (o, e) =>
                        {
                            PrintClosedOrder(e);
                        };
                        popup.ShowDialog();
                    }
                }
            });
        }

        private async void PrintClosedOrder(Printer printer)
        {
            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                    ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);

            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintOrderCheque(CurrentOrder);
            if (!result)
            {
                new OneButtonedPopup("Ошибка печати", "Ошибка").ShowDialog();
            }
        }
        #endregion


        private ICommand returnClosedOrder;
        public ICommand ReturnClosedOrder
        {
            get => returnClosedOrder ??= new RelayCommand(async obj =>
            {
                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (await Auth.CheckAllowance(rights.OrderReturning))
                {
                    new ReturnOrder(CurrentOrder).ShowDialog();
                }
            });
        }

        #endregion


        #region Рендер и т.д.
        private void RenderItems()
        {
            SelectedOrderItem = null;
            orderItemsLayout.Children.Clear();

            if (CurrentOrder is null) return;
            foreach (var item in CurrentOrder.Items)
            {
                var view = new OrderItemTemplate(item);
                view.DeleteBtnTapped += View_DeleteBtnTapped;
                view.OrderItemTapped += View_OrderItemTapped;
                view.AmountChanged += View_AmountChanged;
                orderItemsLayout.Children.Add(view);
            }
        }
        public void SetPriceText()
        {
            if (CurrentOrder is null) return;

            orderTotalLabel.Text = $"Итого: {Math.Round(CurrentOrder.Sum, 2)}₽";

            if (CurrentOrder.Discount != null)
                orderDiscountLabel.Text = $"Скидка: {CurrentOrder.Discount.Percent}%";
            else
                orderDiscountLabel.Text = "Скидка: 0%";

            orderTotalWithDiscountLabel.Text = $"{Math.Round(CurrentOrder.SumWithDiscount, 2)}₽";
        }
        private async void View_AmountChanged(object sender, OrderItem e)
        {
            SetPriceText();
            //  OrdersHelper.SaveOrder();
        }


        public void SetOrderCommentFrameVisibility()
        {
            orderCommentFrame.Visibility = !string.IsNullOrEmpty(CurrentOrder?.Comment) ? Visibility.Visible : Visibility.Collapsed;
        }
        public void SetSendToWorkBtnVisibility()
        {
            if (CurrentOrder is null || ApplicationState.CurrentStore.StoreMode == StoreMode.Point)
            {
                sendItemsToWork.Visibility = Visibility.Collapsed;
            }
            else
            {
                sendItemsToWork.Visibility = CurrentOrder.Items.Any(o => !o.IsInWork) ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        #endregion
        private Order currentOrder;
        public Order CurrentOrder
        {
            get => currentOrder;
            set
            {
                if (value != null)
                {
                    if (value.ClosedAt.HasValue)
                    {
                        orderIdSpan.Text = "XX";
                        orderCreateCloseText.Text = "Открыть заказ";
                    }
                    else
                    {
                        orderCreateCloseText.Text = "Отменить заказ";
                        orderIdSpan.Text = value.OrderNumber.ToString();
                    }
                }
                else
                {
                    orderIdSpan.Text = "XX";
                    orderCreateCloseText.Text = "Открыть заказ";
                }
                SetOrderCommentFrameVisibility();
                SetSendToWorkBtnVisibility();

                currentOrder = value;
                OnPropertyChanged(nameof(CurrentOrder));
            }
        }

        #region Верхние кнопки
        private async void ShowActiveOrders(object sender, MouseButtonEventArgs e)
        {
            await OrdersHelper.GetActiveOrders();
            if (OrdersHelper.ActiveOrders.Count == 0)
            {
                new OneButtonedPopup("На данный момент нет открытых заказов", "Уведомление").ShowDialog();
            }
            else
            {
                new ActiveOrdersPopup().ShowDialog();
            }
        }

        private async void OpenOrCloseOrder(object sender, MouseButtonEventArgs e)
        {
            if (CurrentOrder != null)
            {
                if (CurrentOrder.ClosedAt.HasValue)
                {
                    await OrdersHelper.OpenOrder();
                    CurrentOrder = OrdersHelper.CurrentOrder;
                }
                else
                {
                    await NavigationHelper.GoBackIfCafe();

                    await OrdersHelper.CancelOrder();
                    CurrentOrder = OrdersHelper.CurrentOrder;
                }
            }
            else
            {
                await OrdersHelper.OpenOrder();
                CurrentOrder = OrdersHelper.CurrentOrder;
            }
        }
        #endregion

        #region Меню внизу
        private ICommand openMenu;
        public ICommand OpenMenu
        {
            get => openMenu ??= new RelayCommand(async obj =>
            {
                menuFrame.Visibility = Visibility.Visible;
            });
        }
        private ICommand closeMenu;
        public ICommand CloseMenu
        {
            get => closeMenu ??= new RelayCommand(async obj =>
            {
                menuFrame.Visibility = Visibility.Collapsed;
            });
        }


        private void SplitOrder(object sender, MouseButtonEventArgs e)
        {
            new OrderDivisionPopup().ShowDialog();
        }
        private async void SetClient(object sender, MouseButtonEventArgs e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление").ShowDialog();
                return;
            }
            new ClientsPopup(CurrentOrder).ShowDialog();
            menuFrame.Visibility = Visibility.Collapsed;
        }

        private async void SetDiscount(object sender, MouseButtonEventArgs e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление").ShowDialog();
                return;
            }
            new DiscountPopup(CurrentOrder).ShowDialog();
            menuFrame.Visibility = Visibility.Collapsed;
        }

        private async void SetOrderComment(object sender, MouseButtonEventArgs e)
        {
            if (OrdersHelper.CurrentOrder is null)
            {
                new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление").ShowDialog();
                return;
            }
            new OrderCommentPopup(CurrentOrder).ShowDialog();
            menuFrame.Visibility = Visibility.Collapsed;
        }


        private void ClearOrderItems(object sender, MouseButtonEventArgs e)
        {
            if (OrdersHelper.CurrentOrder is null || OrdersHelper.CurrentOrder.ClosedAt.HasValue)
            {
                new OneButtonedPopup("Нужно сначала открыть заказ", "Уведомление").ShowDialog();
                return;
            }
            OrdersHelper.ClearOrderItems();
            menuFrame.Visibility = Visibility.Collapsed;
        }


        #endregion

        #region Работа с выбранной позицией заказа

        private OrderItem selectedOrderItem = null;
        private OrderItem SelectedOrderItem
        {
            get => selectedOrderItem;
            set
            {
                editAmountStackLayout.Visibility = value != null ? Visibility.Visible : Visibility.Collapsed;
                editModifiersStackLayout.Visibility = value != null ? Visibility.Visible : Visibility.Collapsed;
                orderItemCommentStackLayout.Visibility = value != null ? Visibility.Visible : Visibility.Collapsed;

                selectedOrderItem = value;
                OnPropertyChanged(nameof(SelectedOrderItem));
            }
        }

        private void View_OrderItemTapped(object sender, OrderItem e)
        {
            foreach (var item in orderItemsLayout.Children.Cast<OrderItemTemplate>())
            {
                item.IsSelected = false;
            }
            (sender as OrderItemTemplate).IsSelected = true;
            SelectedOrderItem = e;
        }

        private async void View_DeleteBtnTapped(object sender, OrderItem e)
        {
            var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
            if (await Auth.CheckAllowance(rights.CheckPositionRemoving))
            {
                if (!e.IsInWork)
                {
                    SelectedOrderItem = null;
                    OrdersHelper.DeleteOrderItem(e);
                }
                else
                {
                    new CancelOrderItemPopup(e).ShowDialog();
                }
            }
        }

        private void EditAmount(object sender, MouseButtonEventArgs e)
        {
            if (SelectedOrderItem.TechnicalCard != null)
            {
                if (SelectedOrderItem.TechnicalCard.IsWeightProduct)
                {
                    new WeightTechCardOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount);
                }
                else
                {
                    new TechCardOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount).ShowDialog();
                }
            }
            else if (SelectedOrderItem.Product != null)
            {
                if (SelectedOrderItem.Product.IsWeightProduct)
                {
                    new WeightProductOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount).ShowDialog();
                }
                else
                {
                    new ProductOptionsPopup(SelectedOrderItem, SelectedOrderItem.Amount).ShowDialog();
                }
            }
        }

        private async void EditModifiers(object sender, MouseButtonEventArgs e)
        {
            List<Modifier> availableModifiers = new List<Modifier>();
            if (SelectedOrderItem.TechnicalCard != null)
            {
                availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForTechCard(ApplicationState.CurrentDomain,
                                                                                                                          ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                          SelectedOrderItem.TechnicalCard.Id);
            }
            else if (SelectedOrderItem.Product != null)
            {
                availableModifiers = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetAvailableModifiersForProduct(ApplicationState.CurrentDomain,
                                                                                                                         ApplicationState.CurrentTradeNetwork.Id,
                                                                                                                         SelectedOrderItem.Product.Id);
            }


            if (availableModifiers.Any())
            {
                var popup = new ModifiersPopup(SelectedOrderItem, availableModifiers, true);
                popup.ShowDialog();

                while (!popup.IsCompleted)
                    await Task.Delay(200);

                if (!popup.IsSuccessfully)
                {
                    return;
                }

                await OrdersHelper.SetOrderItemModifiers(SelectedOrderItem);
            }
            else
            {
                new OneButtonedPopup("Для данной позиции нет доступных модификаторов", "Уведомление").ShowDialog();
            }
        }



        private void GoToOrderItemCommentPopup(object sender, MouseButtonEventArgs e)
        {
            new OrderItemCommentPopup(SelectedOrderItem).ShowDialog();
        }
        #endregion


        private ICommand sendItemsToWorkCommand;
        public ICommand SendItemsToWorkCommand
        {
            get => sendItemsToWorkCommand ??= new RelayCommand(async obj =>
            {
                OrdersHelper.SendItemsToWork();
            });
        }


        private ICommand lockScreen;
        public ICommand LockScreen
        {
            get => lockScreen ??= new RelayCommand(async obj =>
            {
                (App.Current.MainWindow as MainWindow).frame.Navigate(new MainPage());
            });
        }
        private ICommand payOrder;
        public ICommand PayOrder
        {
            get => payOrder ??= new RelayCommand(async obj =>
            {

                var rights = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetNetworkSettingSafety(ApplicationState.CurrentDomain, ApplicationState.CurrentTradeNetwork.Id);
                if (CurrentOrder is null)
                {
                    new OneButtonedPopup("Нельзя оплатить заказ, не открыв его", "Ошибка").ShowDialog();
                }
                else if (CurrentOrder.ClosedAt.HasValue)
                {
                    new OneButtonedPopup("Нельзя оплатить уже закрытый заказ", "Ошибка").ShowDialog();
                }
                else if (await Auth.CheckAllowance(rights.CheckPayment))
                {
                    if (CurrentOrder.Items.Count == 0)
                        new OneButtonedPopup("Нельзя оплатить пустой заказ", "Ошибка").ShowDialog();
                    else
                    {
                        (App.Current.MainWindow as MainWindow).frame.Navigate(new CreateOrder());
                    }
                }
            });
        }



        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }

        
    }
}
