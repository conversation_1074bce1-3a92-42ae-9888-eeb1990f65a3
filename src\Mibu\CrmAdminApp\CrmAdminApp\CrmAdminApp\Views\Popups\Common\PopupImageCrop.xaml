﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:ReturnPopupPage
             xmlns:abstractions="clr-namespace:MibuAdminApp.Abstractions"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xct="http://xamarin.com/schemas/2020/toolkit"
             xmlns:effects="clr-namespace:Sharpnado.MaterialFrame;assembly=Sharpnado.MaterialFrame"
             xmlns:pages="http://rotorgames.com"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:xforms="clr-namespace:Syncfusion.SfImageEditor.XForms;assembly=Syncfusion.SfImageEditor.XForms"
             x:Class="CRMAdminApp.Popups.PopupImageCrop">
    <abstractions:ReturnPopupPage.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </abstractions:ReturnPopupPage.Resources>
    <Grid>
        <effects:MaterialFrame
               Margin="-70"
               BackgroundColor="#1E1584"
               AndroidBlurRadius="10"
               Opacity="0.93"
               HorizontalOptions="FillAndExpand"
               VerticalOptions="FillAndExpand"
               MaterialTheme="AcrylicBlur">
            <Grid 
                  Opacity="0.2"
                  BackgroundColor="#1E1584">
                <Grid.GestureRecognizers>
                    <TapGestureRecognizer Tapped="ClosePopup"/>
                </Grid.GestureRecognizers>
            </Grid>
        </effects:MaterialFrame>

        <Frame 
           CornerRadius="12" 
           Margin="0" 
           Padding="0" 
           HasShadow="False"
           WidthRequest="350"
           HeightRequest="600"
           IsClippedToBounds="True"
           HorizontalOptions="Center"
           VerticalOptions="Center"
           Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="85"/>
                </Grid.RowDefinitions>

                <xforms:SfImageEditor
                   x:Name="cropper" 
                   Opacity="0.001"
                   HorizontalOptions="Fill"
                   VerticalOptions="Fill"
                   Grid.Row="0" >
                    <xforms:SfImageEditor.ToolbarSettings>
                        <xforms:ToolbarSettings 
                            HeaderToolbarHeight="0"/>
                    </xforms:SfImageEditor.ToolbarSettings>
                </xforms:SfImageEditor>

                <Button 
                    Grid.Row="1"
                    Margin="10, 0, 10, 0" 
                    Text="Выбрать" 
                    Clicked="ConfirmCrop"
                    VerticalOptions="Center"
                    HeightRequest="55"
                    Background="{StaticResource green}" 
                    TextTransform="None" 
                    BackgroundColor="#5242FF" 
                    CornerRadius="12" 
                    FontFamily="noto_sans_regular" 
                    TextColor="White"/>
            </Grid>
        </Frame>

    </Grid>
    
    
 
</abstractions:ReturnPopupPage>