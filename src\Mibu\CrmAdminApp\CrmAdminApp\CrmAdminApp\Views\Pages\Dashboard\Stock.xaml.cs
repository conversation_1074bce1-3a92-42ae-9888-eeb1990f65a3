﻿using CRMAdminMoblieApp.Abstractions;
using CRMAdminMoblieApp.Enums;
using CRMAdminMoblieApp.Helpers;
using CRM.Models.Reports.Mobile.Admin.Warehouse;
using CRMMoblieApiWrapper;
using System.Collections.ObjectModel;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRMAdminMoblieApp.Controls.ItemTemplates;
using System.Threading.Tasks;
using Sharpnado.CollectionView.RenderedViews;

namespace CRMAdminMoblieApp.Views.Pages.Dashboard
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Stock : BaseDashboardPage
    {
        public override DashboardPageType PageType => DashboardPageType.Stock;
        public Stock() : base()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

			DutyLabel = dutyLabel;

			//Load();
		}

        public override async Task RefreshData()
        {

            Task.Run(async () =>
            {

                WarehouseReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetWarehouseReport(ApplicationState.CurrentDomain,
                                                                                                     ApplicationState.CurrentTradeNetwork.Id,
                                                                                                     ApplicationState.CurrentStore.Id,
                                                                                                     ApplicationState.CurrentDuty.Id);
                await Device.InvokeOnMainThreadAsync(() => RenderReport());
            });
        }


		public bool IsLoaded { get; private set; }
		public async Task Load()
        {
			if (IsLoaded) return;

			IconImageSource = ImageSource.FromFile("stock.png");
			Title = "Склад";

			OnDutyChanged(null, ApplicationState.CurrentDuty);

			//await RefreshData();

			IsLoaded = true;
		}

        private void RenderReport()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                collectionView.ItemsSource = WarehouseReport.CategoryGroups;
                noItemsLabel.IsVisible = WarehouseReport.CategoryGroups.Count == 0;
            });
        }


        private WarehouseReport warehouseReport = new WarehouseReport();
        public WarehouseReport WarehouseReport
        {
            get => warehouseReport;
            set { warehouseReport = value; OnPropertyChanged(nameof(WarehouseReport)); }
        }

    }
}