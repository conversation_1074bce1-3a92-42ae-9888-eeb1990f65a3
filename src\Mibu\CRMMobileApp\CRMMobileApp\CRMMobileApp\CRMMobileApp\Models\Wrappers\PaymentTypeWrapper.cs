﻿using CRM.Models.Network.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using Xamarin.Forms;

namespace CRMMobileApp.Models
{
    public class PaymentTypeWrapper : BindableObject
    {
        public PaymentType PaymentType { get; set; }

        private bool isSelected;
        public bool IsSelected
        {
            get => isSelected;
            set { isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }
        private string title;
        public string Title
        {
            get => title;
            set { title = value; OnPropertyChanged(nameof(Title)); }
        }
        private string imgPath;
        public string ImgPath
        {
            get => imgPath;
            set { imgPath = value; OnPropertyChanged(nameof(ImgPath)); }
        }



        private double sum;
        public double Sum
        {
            get => sum;
            set
            {
                sum = value;
                OnSumChanged?.Invoke(this,value);
            }
        }
        private string sumStr = "0";
        public string SumStr
        {
            get => sumStr;
            set
            {
                double val = 0;
                if (string.IsNullOrEmpty(value))
                {
                    Sum = 0;
                    SumStr = "0";
                    return;
                }

                if(double.TryParse(value, out val))
                {
                    Sum = val;
                }
                sumStr = value;
                OnPropertyChanged(nameof(SumStr));
            }
        }


        public event EventHandler<double> OnSumChanged;

    }
}
