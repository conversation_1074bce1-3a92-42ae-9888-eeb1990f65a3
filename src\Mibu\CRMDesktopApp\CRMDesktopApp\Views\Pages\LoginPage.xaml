﻿<abstactions:BasePage
      xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
      x:Class="CRMDesktopApp.Views.Pages.LoginPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:CRMDesktopApp.Views.Pages"
      xmlns:custom="clr-namespace:CRMDesktopApp.CustomControls"
      mc:Ignorable="d" 
      x:Name="this"
      d:DesignHeight="450"
      d:DesignWidth="800"
      Title="LoginPage">
    <abstactions:BasePage.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BasePage.Resources>
    <Grid Background="{StaticResource bg_purple}">
        <Image
            Stretch="Fill"
            Source="pack://application:,,,/Resources/Images/main_decor.png"/>

        <!--<ImageButton 
            HorizontalOptions="Start"
            VerticalOptions="Start"
            Source="arrowBack.png"
            WidthRequest="10"
            HeightRequest="20"
            BackgroundColor="Transparent"
            Margin="30,40,0,0"/>-->

        <StackPanel   
            Margin="0,120,0,0"
            HorizontalAlignment="Center">
            <Image
                HorizontalAlignment="Center"
                Source="pack://application:,,,/Resources/Images/logo.png"
                Height="120"
                Width="120"/>
            <TextBlock
                FontSize="20"
                FontFamily="{StaticResource TTFirsNeue}"
                HorizontalAlignment="Center"
                Text="Введите ваши данные"/>
            <TextBlock
                FontSize="20"
                FontFamily="{StaticResource TTFirsNeue}"
                HorizontalAlignment="Center"
                Text="для авторизации"/>

            <StackPanel
                Margin="0,25,0,0"
                Width="280"
                HorizontalAlignment="Center" >
                <custom:AndroidStyleEntry 
                    Margin="0,10,0,0"
                    TextFontSize="17"
                    PlaceholderFontSize="17"
                    Height="25"
                    Text="{Binding ElementName=this,Path=Domain,Mode=TwoWay}"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Домен"/>
                <custom:AndroidStyleEntry  
                    Margin="0,10,0,0"
                    TextFontSize="17"
                    PlaceholderFontSize="17"
                    Height="25"
                    Text="{Binding ElementName=this,Path=Username,Mode=TwoWay}"
                    HorizontalAlignment="Stretch"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Логин"/>
                <custom:AndroidStyleEntry  
                    Margin="0,10,0,0"
                    TextFontSize="17"
                    Height="25"
                    PlaceholderFontSize="17"
                    Text="{Binding ElementName=this,Path=Password,Mode=TwoWay}"
                    HorizontalAlignment="Stretch"
                    PlaceholderColor="{StaticResource text_gray}"
                    Placeholder="Пароль"/>
                <Button 
                     Command="{Binding ElementName=this,Path=Auth}"
                     Margin="0,40,0,0"
                     HorizontalAlignment="Stretch"
                     Foreground="White"
                     Content="Войти"
                     FontSize="20"
                     Style="{StaticResource purple_gradient_btn}"
                     Height="50"/>
            </StackPanel>

        </StackPanel>

        <Button 
            Command="{Binding ElementName=this,Path=OpenSupportPopup}"
            Style="{StaticResource transparent_btn}"
            Margin="0,0,0,15"
            Content="Помощь"
            VerticalAlignment="Bottom"
            HorizontalAlignment="Center" />


        <Grid Visibility="Hidden">

            <Image
                Stretch="Fill"
                Source="pack://application:,,,/Resources/Images/main_gradient.png"/>


            <StackPanel
                VerticalAlignment="Center"
                HorizontalAlignment="Center">

                <TextBlock
                    FontSize="20"
                    FontFamily="{StaticResource TTFirsNeue-Regular}"
                    HorizontalAlignment="Center"
                    Foreground="White"
                    Text="Устанавливается соединение с сервером..."/>


                <Image
                    x:Name="loadingWheel"
                    Stretch="Fill"
                    Margin="0,70,0,0"
                    HorizontalAlignment="Center"
                    Width="90"
                    Height="90"
                    Source="pack://application:,,,/Resources/Images/loading_wheel.png">
                    <Image.LayoutTransform>
                        <RotateTransform  x:Name="imgRotate" Angle="0"/>
                    </Image.LayoutTransform>
                </Image>

            </StackPanel>

        </Grid>


    </Grid>
</abstactions:BasePage>
