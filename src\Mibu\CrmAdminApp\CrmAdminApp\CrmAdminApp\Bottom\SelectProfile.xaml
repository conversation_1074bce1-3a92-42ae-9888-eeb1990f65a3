﻿<?xml version="1.0" encoding="utf-8" ?>
<bottomSheet:BaseBottomSheet xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             ParentHeight="600"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             xmlns:bottomSheet="clr-namespace:BottomSheet.Core;assembly=BottomSheetXF" 
             xmlns:gamification="clr-namespace:CrmAdminApp.Controls.Gamification"
             x:Class="MobPhone.Bottom.SelectProfile">
    <bottomSheet:BaseBottomSheet.View>
        <ContentView>
            <Frame 
                CornerRadius="20" 
                Padding="16, 26, 16, 0" 
                BackgroundColor="#ffffff" 
                HeightRequest="600">
                <StackLayout 
                    Spacing="32">
                    <StackLayout 
                        Orientation="Horizontal" 
                        HorizontalOptions="Fill">
                        
                        <StackLayout 
                            Orientation="Horizontal" 
                            HorizontalOptions="CenterAndExpand">
                            
                            <Image 
                                VerticalOptions="Center"
                                Source="send_message" />
                            
                            <Label 
                                VerticalOptions="Center"
                                Text="Написать сообщение" 
                                Style="{x:StaticResource BoldText}" 
                                FontSize="14"/>
                            
                        </StackLayout>
                        
                        <ImageButton 
                            Source="close" 
                            Clicked="closeBtnClicked"
                            HeightRequest="15"
                            WidthRequest="15"
                            Margin="0,0,10,0"
                            VerticalOptions="Start"
                            HorizontalOptions="End" 
                            Padding="0" 
                            BackgroundColor="Transparent"/>
                        
                    </StackLayout>
                    
                    <CollectionView 
                        x:Name="itemsCollectionView"
                        VerticalScrollBarVisibility="Never">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <gamification:UserChatListCard 
                                    BindingContext="{Binding}">
                                    <gamification:UserChatListCard.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="onUserTapped"/>
                                    </gamification:UserChatListCard.GestureRecognizers>
                                </gamification:UserChatListCard>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                </StackLayout>
            </Frame>
        </ContentView>
    </bottomSheet:BaseBottomSheet.View>

</bottomSheet:BaseBottomSheet>