tr[role="search_panel"] td {
    border-width: 0px !important;
    padding: 2px 2px 0px 2px !important;
}

tr[role="search_panel2"] td {
    border-width: 0px !important;
    padding: 0px 2px 2px 2px !important;
}

/* validation */

.invalid-tooltip {
    padding: 0px;
    font-size: 0.9em;
    border: #B94A48 2px solid;
    background-color: #B94A48;
    color: #ffffff;
    border-radius: 2px;
}

.invalid-tooltip .ui-tooltip-content{
    background-color: #B94A48 !important;
    padding: 4px 6px;
}

.has-error div.select2-container {
    border: 1px solid #B94A48;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
}

/* Таблица - DBGrid */

table.table-form {
    font-size: 13px;
}


/* таблица в которой располагается более одного DBGrid */
table.common-grids td {
    vertical-align: top;
}

table.common-grids th {
    font-size: 14px;
    font-weight: bold;
}

/* storechoice */

ul.stores li { list-style: none; padding: 4px 0px; width: 100%;}
ul.stores li a img { width: 400px; height: 140px;}
ul.stores { text-align: center; width: 100%; padding: 0px; margin: 0px;}
ul.stores li.active {background-color: #2b81af;}

/* common grids */
table[data-viewname] {
    margin: 0px;
}
table[data-viewname] td {vertical-align: middle;}
table[data-viewname] .ui-selecting { background: #F0F8FF !important; }
table[data-viewname] .ui-selecting td { background: #F0F8FF !important; }
table[data-viewname] .ui-selected  { background: #EBF4FA !important; /*color: white;*/ }
table[data-viewname] .ui-selected td { background: #EBF4FA !important; /*color: white;*/ }
.ui-dialog { padding: 0px !important; }
/*form input.form-control {max-width: 256px;}*/

div.icon-btns div.shad {
    background-color: #f5f5f5;
    border-color: #E1E1E1;
    border-style: solid;
    border-width: 1px;
    box-shadow: 0px 0px 6px rgb(136, 136, 136);
    padding: 2px;
}

div.icon-btns span.glyphicon, div.icon-btns img.btn2, td.tview-ctrls span.glyphicon, div.icon-btns i[role="btn"] {
    cursor: pointer;
}

td.tview-ctrls {
    vertical-align: middle !important;
}

td.tview-ctrls div span.glyphicon {
    font-size: 1.2em;
}

td.tview-ctrls div span.glyphicon-ok {
    color: green;
}

td.tview-ctrls div span.glyphicon-remove {
    color: #ff0000;
}

div.icon-btns span[role="btn"], div.icon-btns i[role="btn"] {
    margin-left: 6px;
}

div.icon-btns a.abtn {
    padding: 4px;
}

div.icon-btns a.abtn:hover {
    background-color: #ebebeb !important;
}


/* Span TH + sort icon */
.x-column-header-sort-DESC .x-column-header-text {
    background-image: url("grid-images/sort_desc.png");
}
.x-column-header-sort-ASC .x-column-header-text, .x-column-header-sort-DESC .x-column-header-text {
    background-position: right center;
    padding-right: 17px;
}

.x-column-header-text {
    background-repeat: no-repeat;
    display: inline-block;
    white-space: nowrap;
}


/* Верхний DIV ^^ внутри header-over становится видимым */

table.table-form th {
    padding: 0px;
}

table.table-form th > div {
    position: relative;
    /*height: 100%;
    width: 100%;
    padding: 4px 10px 4px 5px;
    border-right: 1px solid #C0C0C0;*/
}

table.table-form th.th-hover .x-column-header-trigger {
    display: block !important;
}

table.table-form th {
    padding: 0px !important;
}

table.table-form th > div {
    padding: 5px;
}

table.table-form th.th-hover .opt-arrow {
    /*display: block !important;*/
    visibility: visible !important;
}

.opt-arrow {
    float: right;
    display: block;
    visibility: hidden;
    cursor: pointer;
    height: 100%;
}


/* TH right DIV */

.x-column-header-trigger {
    background-image: url("grid-images/hd-pop.png");
    border-left: 1px solid #C0C0C0;
}
.x-column-header-trigger {
    background-color: rgba(0, 0, 0, 0);
    background-position: center center;
    cursor: pointer;
    width: 18px;
}
.x-column-header-trigger {
    background-repeat: no-repeat;
    display: none;
    height: 100%;
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 2;
    /*float: right;*/
}




.x-hmenu-sort-asc .x-menu-item-icon {
    background-image: url("grid-images/hmenu-asc.png");
}

.x-hmenu-sort-desc .x-menu-item-icon {
    background-image: url("grid-images/hmenu-desc.png");
}

.x-menu-item-unchecked .x-menu-item-icon, .x-menu-item-unchecked .x-menu-item-icon-right {
    background-image: url("grid-images/unchecked.png");
}

.x-menu-item-checked .x-menu-item-icon, .x-menu-item-checked .x-menu-item-icon-right {
    background-image: url("grid-images/checked.png");
}

.x-menu-item-icon {
    background-position: center center;
    height: 16px;
    left: 3px;
    top: 5px;
    width: 16px;
}
.x-menu-item-icon, .x-menu-item-icon-right, .x-menu-item-arrow {
    position: absolute;
    text-align: center;
}

.x-menu-item-link {
    width: 100%;
    position: relative;
    display: inline-block;
    line-height: 24px;
    padding: 0 4px 0 27px;
}
.x-menu-item-link {
    outline: 0 none;
    text-decoration: none;
}

.x-box-inner {
    background-color: #f5f5f5;
    border-color: #E1E1E1;
    border-style: solid;
    border-width: 1px;
    box-shadow: 0px 0px 6px rgb(136, 136, 136);
}


.x-menu-item-text {
    color: #000000;
    cursor: pointer;
    font-size: 13px;
    margin-right: 16px;
}

.x-box-inner a:hover {
    text-decoration: none;
    background-color: #C3D9FF;
}

a, a:hover, a:active, a:visited {
    text-decoration: none;
}


/* ExtJs Grid Pagination */

.x-box-inner {
    left: 0;
    overflow: hidden;
    position: relative;
    top: 0;
}

    /* icons in paging */


.x-tbar-page-first {
    background-image: url("grid-images/page-first.png");
}
.x-tbar-page-prev {
    background-image: url("grid-images/page-prev.png");
}
.x-tbar-page-next {
    background-image: url("grid-images/page-next.png");
}
.x-tbar-page-last {
    background-image: url("grid-images/page-last.png");
}
.x-tbar-loading {
    background-image: url("grid-images/refresh.png");
}

.x-btn-icon-el {
    background-repeat: no-repeat;
    text-align: center;
    height: 16px;
    width: 16px;
}