﻿using CRM.Models.Reports.Mobile.Admin;
using CRMAdminMoblieApp.Abstractions;
using CRMAdminMoblieApp.Enums;
using CRMAdminMoblieApp.Helpers;
using CRMMoblieApiWrapper;
using System;
using System.Collections.ObjectModel;
using CRMAdminMoblieApp.Controls.ItemTemplates;


using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using System.Threading.Tasks;
using Sharpnado.CollectionView.RenderedViews;

namespace CRMAdminMoblieApp.Views.Pages.Dashboard
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Cassa : BaseDashboardPage
    {
        public override DashboardPageType PageType => DashboardPageType.Cassa;
        public Cassa() : base()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

			DutyLabel = dutyLabel;

			//Load();
		}

        public override async Task RefreshData()
        {
            Task.Run(async () =>
            {
                CashRegisterReport = await MobileAPI.AdminMethods.AdminReportsMethods.GetCashRegisterReport(ApplicationState.CurrentDomain,
                                                                                                      ApplicationState.CurrentStore.Id,
                                                                                                      ApplicationState.CurrentDuty.Id);


                CashRegisterReport.Revenue = Math.Round(CashRegisterReport.Revenue, 1);
                CashRegisterReport.ByCard = Math.Round(CashRegisterReport.ByCard, 1);
                CashRegisterReport.ByCash = Math.Round(CashRegisterReport.ByCash, 1);

                DonutChartSections = new ObservableCollection<Tuple<int, string>>()
                {
                    new Tuple<int, string>((int)Math.Round(CashRegisterReport.ByCardPercent,0), "#7265FB"),
                    new Tuple<int, string>((int)Math.Round(CashRegisterReport.ByCashPercent,0), "#26E27C"),
                };

				await Device.InvokeOnMainThreadAsync(() => RenderTransactions());
            });
        }


		public bool IsLoaded { get; private set; }
		public async Task Load()
        {
            if (IsLoaded) return;

			IconImageSource = ImageSource.FromFile("cassa.png");
			Title = "Касса";


			if (Device.Idiom == TargetIdiom.Tablet)
			{
				phoneStats.IsVisible = false;
			}
			else
			{
				tabletStats.IsVisible = false;
			}
			OnDutyChanged(null, ApplicationState.CurrentDuty);

			//await RefreshData();

			IsLoaded = true;
		}

        private void RenderTransactions()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                collectionView.ItemsSource = CashRegisterReport.Transactions;
                noItemsLabel.IsVisible = CashRegisterReport.Transactions.Count == 0;
            });
        }


        private CashRegisterReport cashRegisterReport = new CashRegisterReport();
        public CashRegisterReport CashRegisterReport
        {
            get => cashRegisterReport;
            set { cashRegisterReport = value; OnPropertyChanged(nameof(CashRegisterReport)); }
        }


        private ObservableCollection<Tuple<int, string>> donutChartSections = new ObservableCollection<Tuple<int, string>>()
		{
			new Tuple<int, string>((int)0, "#7265FB"),
			new Tuple<int, string>((int)0, "#26E27C"),
		};
		public ObservableCollection<Tuple<int, string>> DonutChartSections
        {
            get => donutChartSections;
            set { donutChartSections = value; OnPropertyChanged(nameof(DonutChartSections)); }
        }


    }
}