﻿using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class KeyboardBlackLight : ContentView
    {
        public event EventHandler<string> ButtonTapped;
        public KeyboardBlackLight()
        {
            InitializeComponent();
        }

        private ICommand tapButton;
        public ICommand TapButton
        {
            get => tapButton ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    ButtonTapped?.Invoke(this, obj as string);
                });
            });
        }
    }
}