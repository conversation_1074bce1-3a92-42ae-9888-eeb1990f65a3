﻿using CRM.Models.Enums.Equipment;
using CRM.Models.Enums.Settings.Payments;
using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Equipment;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AvailablePrintersPopup : PopupPage
    {
        PrinterType _type;
        public AvailablePrintersPopup(PrinterType type)
        {
            _type = type;
            InitializeComponent();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            LoadData();
        }
        private void LoadData()
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ordersStackLayout.Children.Clear();
                foreach (var item in ApplicationState.StorePrinters.Where(o => o.PrinterType == _type))
                {
                    var control = new PrinterItemTemplate(item)
                    {
                        HeightRequest = 38,
                        Margin = new Thickness(0, 4, 0, 0)
                    };
                    control.ItemTapped += Control_ItemTapped;
                    ordersStackLayout.Children.Add(control);
                }
            });
        }


        public event EventHandler<Printer> ItemSelected;
        private void Control_ItemTapped(object sender, Printer e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                ItemSelected?.Invoke(this, e);
                App.Current.MainPage.Navigation.RemovePopupPageAsync(this);
            });
        }



        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.RemovePopupPageAsync(this);
                });
            });
        }
    }
}