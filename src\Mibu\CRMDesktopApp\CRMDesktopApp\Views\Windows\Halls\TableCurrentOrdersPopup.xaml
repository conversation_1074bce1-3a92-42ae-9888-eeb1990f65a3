﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.Halls.TableCurrentOrdersPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.Halls"
        mc:Ignorable="d"
        Title="Открытые заказы"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="450"
        Width="800">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="450"
        Height="250"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="0,70,0,0"
        CornerRadius="20"
        BorderThickness="1"
        Background="#FFFFFF"
        BorderBrush="LightGray">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <TextBlock
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="20">
                    <Run Text="Стол"/>
                    <Run Text=" "/>
                    <Run Text="{Binding ElementName=this,Path=Table.Table.Title}"/>
                </TextBlock>
            </Grid>

            <Grid Grid.Row="1">
                <ScrollViewer
                    VerticalScrollBarVisibility="Hidden"
                    HorizontalScrollBarVisibility="Hidden">
                    <StackPanel 
                        Margin="10,0,10,0"
                        x:Name="ordersStackPanel">



                    </StackPanel>
                </ScrollViewer>
            </Grid>

            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Button 
                    Grid.Column="0"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Style="{StaticResource gray_cornered_filled_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,10,0"
                    Content="Отмена"
                    Width="160"
                    Height="40"/>

                <Button 
                    Grid.Column="1"
                    Command="{Binding ElementName=this,Path=CreateOrder}"
                    Style="{StaticResource purple_gradient_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    Margin="10,0,0,0"
                    Content="Создать заказ"
                    Width="160"
                    Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
