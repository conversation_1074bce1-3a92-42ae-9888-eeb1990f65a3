﻿<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <Color x:Key="blueColor">#333B4E</Color>
    <Color x:Key="grayBg">#F9F9F9</Color>
    <Color x:Key="grayBlack">#444444</Color>

    <Color x:Key="textBlack">#2C2C2C</Color>
    <Color x:Key="textBlackLight">#565656</Color>
    <Color x:Key="textLightBlack">#6B6B6B</Color>
    <Color x:Key="textLight">#e5e5e5</Color>

    <Color x:Key="btn_bg_main">#333B4E</Color>

    <Color x:Key="blue_black">#283651</Color>


    <Color x:Key="color_red">#E04435</Color>
    <Color x:Key="color_red_dark">#681F19</Color>

    <Color x:Key="color_ligth_bg">#B8B8B8</Color>



    <Color x:Key="purple">#7265FB</Color>
    <Color x:Key="dark_purple">#524E7D</Color>
    <Color x:Key="green">#26E27C</Color>
    <Color x:Key="bg_purple">#F6F6FB</Color>
    <Color x:Key="text_gray">#9795B1</Color>
    <Color x:Key="icon_gray">#AFADC5</Color>
    <Color x:Key="light_grey">#CBCAD8</Color>
    <Color x:Key="text_green">#23CF72</Color>
    <Color x:Key="light_purple">#F5F4FF</Color>
    <LinearGradientBrush x:Key="purple_gradient" EndPoint="1,0">
        <GradientStop Color="#9A90FF"
                          Offset="0" />
        <GradientStop Color="#594DD2"
                          Offset="1.0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="green_gradient" EndPoint="1,0">
        <GradientStop Color="#7CE9AE"
                          Offset="0.1" />
        <GradientStop Color="#22BA68"
                          Offset="1.0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="orange_gradient" EndPoint="1,0">
        <GradientStop Color="#F5B276"
                          Offset="0.1" />
        <GradientStop Color="#DF8534"
                          Offset="1.0" />
    </LinearGradientBrush>

</ResourceDictionary>