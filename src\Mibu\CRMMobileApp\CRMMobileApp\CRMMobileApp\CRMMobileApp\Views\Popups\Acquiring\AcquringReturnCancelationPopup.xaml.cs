﻿using AcquiringProviders.Abstractions;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AcquringReturnCancelationPopup : PopupPage
    {
        private AbsProvider _provider;
        public AcquringReturnCancelationPopup(AbsProvider provider)
        {
            _provider = provider;
            InitializeComponent();
        }


        private string number = "";
        public string Number
        {
            get => number;
            set { number = value; OnPropertyChanged(nameof(Number)); }
        }

        private ICommand returnSum;
        public ICommand ReturnSum
        {
            get => returnSum ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    if (Number.Length != 4)
                    {
                        App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Укажите 4 цифры для номера транзакции", "Уведомление"));
                    }
                    else
                    {
                        _provider.CancelOperation(Number);
                        App.Current.MainPage.Navigation.PopPopupAsync();
                    }
                });
                
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(obj =>
            {
                Device.InvokeOnMainThreadAsync(() =>
                {
                    App.Current.MainPage.Navigation.PopPopupAsync();
                });
            });
        }
    }
}