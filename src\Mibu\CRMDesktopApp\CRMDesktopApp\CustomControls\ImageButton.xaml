﻿<UserControl x:Class="CRMDesktopApp.CustomControls.ImageButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.CustomControls"
             mc:Ignorable="d" 
             d:DesignHeight="50" 
             x:Name="this"
             d:DesignWidth="50">
    <Button
        BorderThickness="0"
        Padding="0"
        Command="{Binding ElementName=this,Path=Command}"
        CommandParameter="{Binding ElementName=this,Path=CommandParameter}"
        Background="Transparent">
        <Button.Style>
            <Style TargetType="Button">
                <Style.Setters>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Button">
                                <Border 
                                    CornerRadius="0" 
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    BorderThickness="{TemplateBinding BorderThickness}" >
                                    <ContentPresenter
                                        x:Name="contentPresenter" 
                                        ContentTemplate="{TemplateBinding ContentTemplate}" 
                                        Content="{TemplateBinding Content}" 
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        Margin="{TemplateBinding Padding}" 
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style.Setters>
            </Style>
        </Button.Style>
        <Image 
            Source="{Binding ElementName=this,Path=Source}"
            VerticalAlignment="Stretch"
            Stretch="Fill"
            HorizontalAlignment="Stretch"/>
    </Button>
</UserControl>
