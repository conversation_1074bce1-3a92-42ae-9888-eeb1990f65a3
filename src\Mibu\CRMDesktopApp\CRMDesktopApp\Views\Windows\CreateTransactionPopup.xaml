﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.CreateTransactionPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls" 
        xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        mc:Ignorable="d"
        Title="Создание транзакции" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        x:Name="this"
        WindowStartupLocation="CenterScreen"
        Width="690"
        Height="230">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="680"
        Height="225"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="93"/>
            </Grid.RowDefinitions>


            <Grid 
                Grid.Row="0"
                Margin="30,20,0,0">
                <StackPanel Orientation="Horizontal">
                    <Image 
                        VerticalAlignment="Center"
                        HorizontalAlignment="Left"
                        Source="pack://application:,,,/Resources/Images/comment.png"
                        Width="30"
                        Height="30"/>
                    <TextBlock
                        Margin="8,0,0,0"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="{StaticResource TTFirsNeue-Regular}"
                        FontSize="18"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="Новая транзакция"/>
                </StackPanel>

                <controls:ImageButton 
                        Margin="0,-5,15,0"
                        Source="pack://application:,,,/Resources/Images/close.png"
                        Command="{Binding ElementName=this,Path=CloseWindow}"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Width="10"
                        Height="10"/>



                <StackPanel
                    Orientation="Horizontal"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Margin="0,15,35,0">

                    <Border
                        Margin="25,0,0,0"
                        Background="{StaticResource bg_purple}"
                        Padding="0"
                        Width="110"
                        CornerRadius="10"
                        Height="30">
                        <Grid>
                            <TextBlock
                                x:Name="transactionDateLabel"
                                Foreground="{StaticResource purple}"
                                FontFamily="{StaticResource TTFirsNeue-Regular}"
                                FontSize="11"
                                VerticalAlignment="Center"
                                HorizontalAlignment="Center"
                                Text="12 марта 2022"/>
                        </Grid>
                    </Border>


                </StackPanel>


            </Grid>







            <Grid Grid.Row="1">

                <StackPanel
                    Margin="30,0,0,0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">


                    <editors:ComboBoxEdit   
                            Margin="0,0,0,0"
                            Padding="8,0,0,0"
                            Background="#F6F6FB"
                            Foreground="{StaticResource dark_purple}"
                            BorderBrush="#F6F6FB"
                            Width="220"
                            Height="40"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            SelectedIndex="2"
                            CornerRadius="15"
                            Text="Пол"
                            SelectedItem="{Binding ElementName=this,Path=SelectedFinanceOperationString,Mode=TwoWay}"
                            ItemsSource="{Binding ElementName=this,Path=FinanceOperationStrings,Mode=TwoWay}" >
                        <editors:ComboBoxEdit.ItemTemplate>
                            <DataTemplate>
                                <Grid Height="40">
                                    <TextBlock
                                        Margin="8,8,0,0"
                                        Foreground="{StaticResource dark_purple}" Text="{Binding}"/>
                                </Grid>
                            </DataTemplate>
                        </editors:ComboBoxEdit.ItemTemplate>
                    </editors:ComboBoxEdit>

                    <Grid
                        Margin="20,0,0,0"
                        HorizontalAlignment="Left"
                        Width="170"
                        Height="50">

                        <controls:EntryOutlined 
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                            Text="{Binding ElementName=this,Path=NewTransaction.Sum,Mode=TwoWay}"
                            Style="{StaticResource white_cornered_entry}"
                            Placeholder="Сумма"
                            Width="170"
                            Height="40"/>

                        <TextBlock
                            Margin="0,0,10,10"
                            Foreground="{StaticResource purple}"
                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                            FontSize="16"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Text="₽"/>
                    </Grid>

                </StackPanel>


                <StackPanel 
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Margin="0,0,30,0"
                    Orientation="Horizontal">
                    <Border 
                        VerticalAlignment="Top"
                        HorizontalAlignment="Left"
                        Height="30"
                        Width="30"
                        CornerRadius="15"
                        ClipToBounds="True"
                        Padding="0">
                        <Image
                            Stretch="Fill"/>
                    </Border>
                    <StackPanel>
                        <TextBlock
                            x:Name="waiterNameLabel"
                            HorizontalAlignment="Right"
                            Foreground="{StaticResource dark_purple}"
                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                            FontSize="16"
                            Text="Мария Иванова"/>
                        <TextBlock
                            x:Name="waiterRoleLabel"
                            HorizontalAlignment="Right"
                            Foreground="{StaticResource dark_purple}"
                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                            FontSize="16"
                            Text="Официант"/>
                    </StackPanel>

                </StackPanel>

            </Grid>

            <Grid Grid.Row="2">

                <controls:EntryOutlined 
                    Margin="30,0,0,0"
                    AcceptsReturn="True"
                    VerticalTextAlignment="Top"
                    PlaceholderMargin="10,10,0,0"
                    TextMargin="10,10,0,0"
                    Text="{Binding ElementName=this,Path=NewTransaction.Comment,Mode=TwoWay}"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Style="{StaticResource white_cornered_entry}"
                    Placeholder="Ваш комментарий"
                    Width="410"
                    Height="65"/>


                <StackPanel
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,30,0"
                    Orientation="Horizontal">

                    <Button 
                        Margin="0,0,0,0"
                        Command="{Binding ElementName=this,Path=CreateTransaction}"
                        Style="{StaticResource purple_gradient_btn}"
                        VerticalAlignment="Center"
                        Content="Создать"
                        Width="140"
                        Height="40"/>

                </StackPanel>

            </Grid>
        </Grid>
    </Border>
</abstactions:BaseWindow>
