﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"  
        x:Class="CRMDesktopApp.Views.Windows.OrderCommentPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows" 
        xmlns:customcontrols="clr-namespace:CRMDesktopApp.CustomControls"
        mc:Ignorable="d"
        Title="Комментарий к заказу" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="670"
        Height="310">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Padding="0"
        Width="660"
        Height="305"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        Background="#FFFFFF"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="80"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">

                <StackPanel
                    Margin="15,30,0,0"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal">

                    <Image 
                        Margin="25,0,0,0"
                        Stretch="Fill"
                        Source="pack://application:,,,/Resources/Images/client.png"
                        Width="30"
                        Height="30"
                        VerticalAlignment="Center"/>

                    <TextBlock
                        Margin="15,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        FontSize="20"
                        Text="Комментарий к чеку"/>

                </StackPanel>



                <customcontrols:ImageButton 
                    Source="pack://application:,,,/Resources/Images/close.png"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Background="Transparent"
                    VerticalAlignment="Top"
                    HorizontalAlignment="Right"
                    Width="10"
                    Height="10"
                    Margin="0,35,35,0"/>



            </Grid>


            <Grid Grid.Row="1">
                <customcontrols:EntryOutlined
                    Text="{Binding ElementName=this,Path=Comment,Mode=TwoWay}"
                    AcceptsReturn="True"
                    VerticalTextAlignment="Top"
                    PlaceholderMargin="10,10,0,0"
                    TextMargin="10,10,0,0"
                    Margin="20,0,20,20"
                    Height="120"
                    Style="{StaticResource white_cornered_entry}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"/>
            </Grid>

            <Grid Grid.Row="2">

                <Button 
                     Command="{Binding ElementName=this,Path=SaveChanges}"
                     Style="{StaticResource bg_purple_btn}"
                     VerticalAlignment="Bottom"
                     HorizontalAlignment="Right"
                     Margin="0,0,20,30"
                     Content="Создать"
                     Width="140"
                     Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
