﻿using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Views.Pages;
using CRMDesktopApp.Views.Pages.Main;
using CRMMobileApp.Core;
using MarkupCreator.Helpers.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Parts
{
    /// <summary>
    /// Логика взаимодействия для Header.xaml
    /// </summary>
    public partial class Header : UserControl
    {
        public Header()
        {
            InitializeComponent();
            var waiter = Auth.User;
            baristaFIOLabel.Text = $"{waiter.Name} {waiter.Surname}";
            baristaRoleLabel.Text = EnumDescriptionHelper.GetDescription(waiter.Role);
        }
        public static readonly DependencyProperty TitleProperty =
             DependencyProperty.Register(nameof(Title), typeof(string), typeof(Header));
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }
        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                NavigationService nav = NavigationService.GetNavigationService(this);
                if(nav != null)
                {
                    nav.GoBack();
                }
            });
        }

        private void GoToMainMenuBorderClicked(object sender, MouseButtonEventArgs e)
        {
            NavigationService nav = NavigationService.GetNavigationService(this);
            if (nav != null)
            {
                nav.Navigate(new MainMenu());
            }
        }
    }
}
