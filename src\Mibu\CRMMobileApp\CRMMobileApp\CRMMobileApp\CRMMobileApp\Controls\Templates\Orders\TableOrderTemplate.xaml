﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.TableOrderTemplate">
    <ContentView.Content>
        <Grid BackgroundColor="Transparent">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="80"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="100"/>
            </Grid.ColumnDefinitions>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onItemTapped"/>
            </Grid.GestureRecognizers>

            <StackLayout 
                Spacing="2"
                Grid.Column="0">
                <Label 
                     x:Name="orderNumberLabel"
                     FontFamily="TTFirsNeue-Regular"
                     HorizontalOptions="Start"
                     FontSize="16"
                     TextColor="{StaticResource purple}"/>
                <Label 
                    x:Name="orderTimeLabel"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Start"
                    FontSize="14"
                    TextColor="{StaticResource text_gray}"/>
                <Label 
                    Margin="10,0,0,0"
                    IsVisible="False"
                    x:Name="orderWaiterLabel"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalOptions="Start"
                    FontSize="14"
                    TextColor="{StaticResource text_gray}"/>
            </StackLayout>


            <StackLayout
                Grid.Column="1"
                VerticalOptions="Start">
                <Label 
                     Margin="10,0,0,0"
                     x:Name="orderItemsLabel"
                     MaxLines="2"
                     LineBreakMode="TailTruncation"
                     FontFamily="TTFirsNeue-Regular"
                     HorizontalOptions="Start"
                     FontSize="14"
                     TextColor="{StaticResource text_gray}"/>
            </StackLayout>

            <Grid 
                Grid.Column="2"
                Margin="0,0,10,0"
                VerticalOptions="Start"
                HorizontalOptions="End">
                <Label 
                    VerticalOptions="Center"
                    HorizontalOptions="End"
                    x:Name="orderPriceLabel"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"
                    TextColor="{StaticResource blue_black}"/>
            </Grid>
            
        </Grid>
    </ContentView.Content>
</ContentView>