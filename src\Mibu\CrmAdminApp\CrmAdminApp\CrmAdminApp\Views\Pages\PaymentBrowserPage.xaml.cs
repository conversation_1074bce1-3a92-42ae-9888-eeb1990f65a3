﻿using CRMMobileApp.Core;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PaymentBrowserPage : ContentPage
    {
        public PaymentBrowserPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
        }
        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
    }
}