﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.CountSelections.AvailableAcquiringPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.CountSelections" 
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls" 
        xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        mc:Ignorable="d"
        Title="Выбор эквайринга"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Width="410"
        Height="260">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="400"
        Height="255"
        Padding="0"
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="5,0,5,0"
        BorderBrush="LightGray"
        BorderThickness="1"
        CornerRadius="20">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="70"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <TextBlock
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    Text="Выбор эквайринга"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="20"/>
            </Grid>

            <Grid Grid.Row="1">
                <ScrollViewer
                    VerticalScrollBarVisibility="Hidden">
                    <StackPanel                      
                        Margin="10,0,10,30"
                        x:Name="ordersStackLayout">



                    </StackPanel>
                </ScrollViewer>
            </Grid>


            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Button 
                    Grid.Column="1"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Style="{StaticResource purple_gradient_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,20,0"
                    Content="Отмена"
                    Width="120"
                    Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
