﻿using CrmAdminApp.iOS;
using Foundation;
using MobPhone.Custom;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UIKit;
using Xamarin.Forms;
using Xamarin.Forms.Platform.iOS;

[assembly: ExportRenderer(typeof(CustomEditor), typeof(CustomEditorRenderer))]
namespace CrmAdminApp.iOS
{
    [Foundation.Preserve]
    public class CustomEditorRenderer : EditorRenderer
    {
        public static void Load() { }
        protected override void OnElementChanged(ElementChangedEventArgs<Editor> e)
        {
            base.OnElementChanged(e);

            if (Control == null)
            {
                return;
            }

            Control.Layer.BorderWidth = 0;
            Control.BackgroundColor = null;
        }
    }
}