﻿<abstractions:AbsTable 
        xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions"
        x:Class="CRMDesktopApp.Controls.Tables.RoundTable"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:local="clr-namespace:CRMDesktopApp.Controls.Tables"
        mc:Ignorable="d" 
        x:Name="this"
        d:DesignHeight="450"
        d:DesignWidth="800">
    <Grid MouseDown="onCardTapped">
        <Ellipse     
            x:Name="frame"
            Fill="#AFADC5">
        </Ellipse>
        <TextBlock
            x:Name="titleLabel"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Foreground="#524E7D"
            Text="{Binding ElementName=this,Path=Table.Title}"
            FontSize="20" />



        <TextBlock
            Margin="5,0,0,0"
            x:Name="ordersCountLabel"
            HorizontalAlignment="Left"
            VerticalAlignment="Center"
            Foreground="#AFADC5"
            FontSize="14" />

        <TextBlock
            Margin="0,0,5,0"
            x:Name="ordersSumLabel"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"
            Foreground="#AFADC5"
            FontSize="14" />


    </Grid>
</abstractions:AbsTable>
