﻿<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    
    <Style TargetType="Button" x:Key="transparent_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BackgroundColor" Value="Transparent"/>
        <Setter Property="BorderColor" Value="Transparent"/>
        <Setter Property="TextColor" Value="#524E7D"/>
        <Setter Property="TextTransform" Value="None"/>
        <Setter Property="FontSize" Value="Default"/>
    </Style>

    <Style TargetType="Button" x:Key="bg_purple_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BorderColor" Value="#F6F6FB"/>
        <Setter Property="BackgroundColor" Value="#F6F6FB"/>
        <Setter Property="TextColor" Value="#7265FB"/>
        <Setter Property="BorderWidth" Value="0"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="TextTransform" Value="None"/>
        <Setter Property="FontSize" Value="Default"/>
    </Style>


    <Style TargetType="Button" x:Key="purple_gradient_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BorderColor" Value="Transparent"/>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="1,0">
                    <GradientStop Color="#9A90FF"
                          Offset="0.1" />
                    <GradientStop Color="#594DD2"
                          Offset="1.0" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="TextColor" Value="White"/>
        <Setter Property="BorderWidth" Value="0"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="TextTransform" Value="None"/>
        <Setter Property="FontSize" Value="Default"/>
    </Style>

    <Style TargetType="Button" x:Key="green_gradient_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BorderColor" Value="Transparent"/>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="1,0">
                    <GradientStop Color="#7CE9AE"
                          Offset="0.1" />
                    <GradientStop Color="#22BA68"
                          Offset="1.0" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="TextColor" Value="White"/>
        <Setter Property="BorderWidth" Value="0"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="TextTransform" Value="None"/>
        <Setter Property="FontSize" Value="Default"/>
    </Style>


    <Style TargetType="Button" x:Key="auth_blue_rounded_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Medium"/>
        <Setter Property="BackgroundColor" Value="Transparent"/>
        <Setter Property="TextColor" Value="#524E7D"/>
        <Setter Property="FontSize" Value="{OnPlatform Android=36,iOS=28}"/>
        <Setter Property="BorderWidth" Value="0"/>
    </Style>

    <Style TargetType="Button" x:Key="auth_white_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Medium"/>
        <Setter Property="BackgroundColor" Value="Transparent"/>
        <Setter Property="TextColor" Value="White"/>
        <Setter Property="FontSize" Value="{OnPlatform Android=36,iOS=28}"/>
        <Setter Property="BorderWidth" Value="0"/>
    </Style>







    <Style TargetType="Button" x:Key="white_rounded_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BorderColor" Value="White"/>
        <Setter Property="TextColor" Value="#05AEF1"/>
        <Setter Property="BorderWidth" Value="1"/>
        <Setter Property="CornerRadius" Value="20"/>
    </Style>

    <Style TargetType="Button" x:Key="white_rounded_light_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BorderColor" Value="White"/>
        <Setter Property="TextColor" Value="White"/>
        <Setter Property="BackgroundColor" Value="Transparent"/>
        <Setter Property="BorderWidth" Value="1"/>
        <Setter Property="CornerRadius" Value="20"/>
    </Style>

    <Style TargetType="Button" x:Key="black_rounded_light_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="FontSize" Value="35"/>
        <Setter Property="BorderColor" Value="#e5e5e5"/>
        <Setter Property="TextColor" Value="Black"/>
        <Setter Property="BackgroundColor" Value="Transparent"/>
        <Setter Property="BorderWidth" Value="1"/>
        <Setter Property="CornerRadius" Value="35"/>
    </Style>

    <Style TargetType="Button" x:Key="button_back">
        <!--<Setter Property="ImageSource" Value="LeftArrow.png"></Setter>-->
        <Setter Property="BackgroundColor" Value="Transparent"></Setter>
    </Style>

    <Style TargetType="Button" x:Key="blue_cornered_filled_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BackgroundColor" Value="#283651"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="TextColor" Value="White"/>
    </Style>


    <Style TargetType="Button" x:Key="green_cornered_filled_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BackgroundColor" Value="#6CB86D"/>
        <Setter Property="TextColor" Value="White"/>
        <Setter Property="CornerRadius" Value="20"/>
    </Style>

    <Style TargetType="Button" x:Key="gray_cornered_filled_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BackgroundColor" Value="Gray"/>
        <Setter Property="TextColor" Value="White"/>
        <Setter Property="CornerRadius" Value="20"/>
    </Style>

    <Style TargetType="Button" x:Key="blue_bordered_btn">
        <Setter Property="FontFamily" Value="TTFirsNeue-Regular"/>
        <Setter Property="BackgroundColor" Value="Transparent"/>
        <Setter Property="BorderColor" Value="#333B4E"/>
        <Setter Property="BorderWidth" Value="1"/>
        <Setter Property="CornerRadius" Value="20"/>
    </Style>
    
    
</ResourceDictionary>