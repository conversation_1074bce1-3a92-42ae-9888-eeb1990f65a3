﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:custom="clr-namespace:PovodogMobileApp.Custom"
             x:Class="XamarinSamples.Views.Controls.LessSuckyPicker"
             Background="Transparent"
             BackgroundColor="Transparent"
             x:Name="this">
  <ContentView.Content>
        <Frame
            HasShadow="False"
            BorderColor="{Binding Source={x:Reference this},Path=BorderColor}"
            CornerRadius="{Binding Source={x:Reference this},Path=CornerRadius}"
            Background="{Binding Source={x:Reference this},Path=ControlBackground}"
            BackgroundColor="{Binding Source={x:Reference this},Path=ControlBackground}"
            Padding="0"
            Margin="0, 0, 0, 0">
            <Grid>
                <custom:BorderlessPicker
                    x:Name="borderlessPicker1"
                    SelectedIndex="0"
                    TextColor="{Binding Source={x:Reference this},Path=TextColor}"
                    FontSize="{Binding Source={x:Reference this},Path=FontSize}"
                    Title="{Binding Source={x:Reference this},Path=Title,Mode=TwoWay}"
                    ItemsSource="{Binding Source={x:Reference this},Path=ItemsSource,Mode=TwoWay}"
                    SelectedItem="{Binding Source={x:Reference this},Path=SelectedItem,Mode=TwoWay}"
                    HorizontalTextAlignment="{Binding Source={x:Reference this},Path=TextAlignment}"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="Center"
                    Margin="3" />
            </Grid>

        </Frame>
  </ContentView.Content>
</ContentView>
