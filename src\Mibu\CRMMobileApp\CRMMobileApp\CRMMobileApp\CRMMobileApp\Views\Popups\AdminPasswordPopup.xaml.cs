﻿using CRMMobileApp.Core;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using CRMMoblieApiWrapper;
using CRMAdminMoblieApp.Helpers;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class AdminPasswordPopup : PopupPage
    {
        public bool IsCompleted { get; set; }
        public bool IsRightPassword { get; set; }
        public AdminPasswordPopup()
        {
            InitializeComponent();
          

        }
        private string password = string.Empty;
        public string Password
        {
            get => password;
            set { password = value; OnPropertyChanged(nameof(Password)); }
        }

        private ICommand checkPassword;
        public ICommand CheckPassword
        {
            get => checkPassword ??= new RelayCommand(async obj =>
            {
                var settings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);
                IsRightPassword = settings.AdminPassword == Password;
                IsCompleted = true;
            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                IsCompleted = true;
            });
        }
    }
}