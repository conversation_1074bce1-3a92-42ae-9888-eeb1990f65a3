﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Orders.TableOrderTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Orders"
             x:Name="this"
             mc:Ignorable="d" 
             d:DesignHeight="450"
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        MouseDown="onItemTapped">
        <Grid>
            <StackPanel VerticalAlignment="Center">
                <StackPanel Orientation="Horizontal">
                    <TextBlock 
                        x:Name="orderNumberLabel"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalAlignment="Left"
                        FontSize="18"
                        Foreground="{StaticResource purple}"/>
                    <TextBlock 
                        Margin="10,0,0,0"
                        x:Name="orderItemsLabel"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalAlignment="Left"
                        FontSize="14"
                        Foreground="{StaticResource text_gray}"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal">
                    <TextBlock 
                        x:Name="orderTimeLabel"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalAlignment="Left"
                        FontSize="14"
                        Foreground="{StaticResource text_gray}"/>
                    <TextBlock 
                        Margin="10,0,0,0"
                        x:Name="orderWaiterLabel"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalAlignment="Left"
                        FontSize="14"
                        Foreground="{StaticResource text_gray}"/>
                </StackPanel>
            </StackPanel>

            <Grid 
                Margin="0,0,10,0"
                VerticalAlignment="Center"
                HorizontalAlignment="Right">
                <TextBlock 
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    x:Name="orderPriceLabel"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="14"
                    Foreground="{StaticResource blue_black}"/>
            </Grid>

        </Grid>
    </Border>
</UserControl>
