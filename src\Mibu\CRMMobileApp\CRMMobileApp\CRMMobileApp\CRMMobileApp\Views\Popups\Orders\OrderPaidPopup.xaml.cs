﻿using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class OrderPaidPopup : PopupPage
    {
        public OrderPaidPopup(double sum,double change)
        {
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                sumLabel.Text = $"{Math.Round(sum, 2)} р";
                if (change == 0)
                {
                    changeLabel.Text = "Без сдачи";
                }
                else
                {
                    changeLabel.Text = $"Сдача {Math.Round(change, 2)} р";
                }
            });

        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                //await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}