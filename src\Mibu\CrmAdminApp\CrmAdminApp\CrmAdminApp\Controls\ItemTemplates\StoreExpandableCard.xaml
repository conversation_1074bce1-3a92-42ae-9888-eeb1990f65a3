﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             x:Name="this"
             xmlns:views="clr-namespace:SVGChart.Nuget.Views;assembly=SVGChart.Nuget"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.StoreExpandableCard">
    <ContentView.Resources>
        <ResourceDictionary Source="../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame CornerRadius="5"
               BorderColor="{x:StaticResource bg_purple}"
               BackgroundColor="{x:StaticResource bg_purple}"
               HasShadow="False"
               Padding="0">
            

            <Grid RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="90" />
                    <RowDefinition Height="300" />
                </Grid.RowDefinitions>

                <Grid
                    Grid.Row="0"
                    Padding="8,6">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="HeaderContent_OnTapped" />
                    </Grid.GestureRecognizers>



                    <StackLayout 
                        Margin="20,10,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Orientation="Horizontal"> 
                        <Frame
                            BackgroundColor="Gray"
                            HasShadow="False"
                            Padding="0"
                            CornerRadius="20"
                            WidthRequest="40"
                            HeightRequest="40"
                            HorizontalOptions="Start"
                            VerticalOptions="Center">
                            <Image/>
                        </Frame>

                        <StackLayout>
                            <Label 
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="{x:StaticResource dark_purple}"
                                Text="{Binding Source={x:Reference this},Path=Model.CommonStoreSettings.Title}"/>
                            <Label 
                                VerticalOptions="Center"
                                FontFamily="TTFirsNeue-Regular"
                                FontSize="14"
                                TextColor="{x:StaticResource text_gray}"
                                Text="{Binding Source={x:Reference this},Path=Model.CommonStoreSettings.Address}"/>

                        </StackLayout>
                    </StackLayout>
                    
                    <Image
                        Margin="0,17,20,0"
                        HorizontalOptions="End"
                        VerticalOptions="Start"
                        WidthRequest="14"
                        HeightRequest="14">
                         <Image.Source>
                            <FileImageSource File="{Binding ExpandIcon}" />
                        </Image.Source>
                    </Image>

                  

                </Grid>

                <!-- Header Bottom Border -->
                <!--<BoxView
                     Grid.Row="0"
                     Color="#EBEBEB"
                     HeightRequest="1"
                     Opacity="0.8"
                     VerticalOptions="End"
                     HorizontalOptions="FillAndExpand"
                     IsVisible="{Binding IsExpanded}" />-->

                <!-- Content View -->
                <Grid 
                    Margin="20,0,20,0"
                    Grid.Row="1"
                    x:Name="BodyContentView"
                    IsVisible="False" >


                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="70"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">
                            <StackLayout>

                                <StackLayout
                                    Orientation="Horizontal">
                                    <Label 
                                        VerticalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        TextColor="{x:StaticResource text_gray}"
                                        Text="Смена"/>
                                    <Frame
                                        BackgroundColor="White"
                                        HasShadow="False"
                                        CornerRadius="5"
                                        HeightRequest="30"
                                        WidthRequest="50"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        Padding="0">
                                        <Label 
                                            x:Name="dutyNumberLabel"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="14"
                                            TextColor="{x:StaticResource dark_purple}"
                                            Text=""/>
                                    </Frame>
                                </StackLayout>

                                <Grid 
                                    HorizontalOptions="Center"
                                    Margin="20,20,0,0"
                                    HeightRequest="120"
                                    WidthRequest="120">
                                    <views:DonutChartView 
                                         VerticalOptions="Center"
                                         HorizontalOptions="Center"
                                         HeightRequest="120"
                                         WidthRequest="120"
                                         ItemSource="{Binding Source={x:Reference this},Path=RevenueDonutChartSections}" />
                                    <Image 
                                         Margin="0,-10,0,0"
                                         VerticalOptions="Center"
                                         HorizontalOptions="Center"
                                         WidthRequest="38"
                                         HeightRequest="30"
                                         Source="revenue.png"/>
                                </Grid>

                                <StackLayout
                                    Margin="15,0,0,0"
                                    HorizontalOptions="Center">
                                    <Label 
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        TextColor="{x:StaticResource text_gray}"
                                        Text="Выручка"/>
                                    <Label 
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="20"
                                        TextColor="{x:StaticResource dark_purple}">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span Text="{Binding Source={x:Reference this},Path=BriefReport.Revenue}"/>
                                                    <Span Text="₽"/>
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>
                                
                            </StackLayout>
                        </Grid>

                        <Grid Grid.Column="1">
                            <StackLayout>

                                <StackLayout
                                    Orientation="Horizontal">
                                    <Label 
                                        x:Name="dutyStatusLabel"
                                        VerticalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        TextColor="{x:StaticResource text_gray}"
                                        Text="Закрыта"/>
                                    <Frame
                                        BackgroundColor="White"
                                        HasShadow="False"
                                        CornerRadius="5"
                                        HeightRequest="30"
                                        WidthRequest="70"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        Padding="0">
                                        <Label 
                                            x:Name="dutyTimeLabel"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            FontFamily="TTFirsNeue-Regular"
                                            FontSize="14"
                                            TextColor="{x:StaticResource dark_purple}"
                                            Text=""/>
                                    </Frame>
                                </StackLayout>

                                <Grid 
                                    HorizontalOptions="Center"
                                    Margin="20,20,0,0"
                                    HeightRequest="120"
                                    WidthRequest="120">
                                    <views:DonutChartView 
                                         VerticalOptions="Center"
                                         HorizontalOptions="Center"
                                         HeightRequest="120"
                                         WidthRequest="120"
                                         ItemSource="{Binding Source={x:Reference this},Path=ProfitDonutChartSections}" />
                                    <Image 
                                         Margin="0,-10,0,0"
                                         VerticalOptions="Center"
                                         HorizontalOptions="Center"
                                         WidthRequest="38"
                                         HeightRequest="30"
                                         Source="profit.png"/>
                                </Grid>

                                <StackLayout
                                    Margin="15,0,0,0"
                                    HorizontalOptions="Center">
                                    <Label 
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="14"
                                        TextColor="{x:StaticResource text_gray}"
                                        Text="Прибыль"/>
                                    <Label 
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        FontFamily="TTFirsNeue-Regular"
                                        FontSize="20"
                                        TextColor="{x:StaticResource dark_purple}">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span Text="{Binding Source={x:Reference this},Path=BriefReport.Profit}"/>
                                                    <Span Text="₽"/>
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                            </StackLayout>
                        </Grid>
                        
                    </Grid>

                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Button 
                             Grid.Column="0"
                             Margin="10,0,10,0"
                             Command="{Binding Source={x:Reference this},Path=SelectStore}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             Text="Выбрать точку"
                             BackgroundColor="White"
                             Style="{x:StaticResource green_gradient_btn}"
                             HorizontalOptions="FillAndExpand"
                             VerticalOptions="Center"
                             HeightRequest="40"/>


                        <Button 
                             Grid.Column="1"
                             Margin="10,0,10,0"
                             Command="{Binding Source={x:Reference this},Path=DeleteStore}"
                             FontFamily="TTFirsNeue-Regular"
                             FontSize="14"
                             Text="Удалить точку"
                             BackgroundColor="White"
                             Style="{x:StaticResource bg_purple_btn}"
                             HorizontalOptions="FillAndExpand"
                             VerticalOptions="Center"
                             HeightRequest="40"/>


                    </Grid>

                </Grid>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>