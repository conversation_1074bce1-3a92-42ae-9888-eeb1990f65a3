﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.AvailableScalesPopup">
    <Grid>


        <Frame
             HasShadow="{OnPlatform WPF=True, Default=False}"
             WidthRequest="350"
             HeightRequest="250"
             Padding="0"
             BackgroundColor="White"
             Background="White"
             HorizontalOptions="Center"
             VerticalOptions="Start"
             Margin="0,70,0,0"
             CornerRadius="20">
            <Grid
                 ColumnSpacing="0"
                 RowSpacing="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="70"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Label
                         VerticalOptions="Center"
                         HorizontalOptions="Center"
                         TextColor="{StaticResource dark_purple}"
                         Text="Выбор весов"
                         FontFamily="TTFirsNeue-Regular"
                         FontSize="20"/>
                </Grid>

                <Grid Grid.Row="1">
                    <ScrollView VerticalScrollBarVisibility="{OnPlatform Default=Default, WPF=Never}">
                        <StackLayout 
                             Margin="10,0,10,-30"
                             x:Name="ordersStackLayout">



                        </StackLayout>
                    </ScrollView>
                </Grid>


                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Button 
                         Grid.Column="1"
                         Command="{Binding Source={x:Reference this},Path=Close}"
                         Style="{StaticResource purple_gradient_btn}"
                         VerticalOptions="Center"
                         HorizontalOptions="End"
                         Margin="0,0,20,0"
                         Text="Отмена"
                         WidthRequest="120"
                         HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>


    </Grid>
</animations:PopupPage>