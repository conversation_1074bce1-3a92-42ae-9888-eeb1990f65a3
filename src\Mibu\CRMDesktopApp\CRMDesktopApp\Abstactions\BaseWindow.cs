﻿using CRMMobileApp.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace CRMDesktopApp.Abstactions
{
    public class BaseWindow : Window,INotifyPropertyChanged
    {

        private ICommand closeWindow;
        public ICommand CloseWindow
        {
            get => closeWindow ??= new RelayCommand(async obj =>
            {
                this.Close();
            });
        }



        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged([CallerMemberName] string prop = "")
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(prop));
        }
    }
}
