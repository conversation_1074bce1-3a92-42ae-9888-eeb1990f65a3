﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace CRMDesktopApp.CustomControls.Abstractions
{
    public class BaseEntry : UserControl
    {

        protected TextBlock placeHolderLabel { get; set; }
        protected TextBox textBox { get; set; }
        public bool FocusToTextBox()
        {
            return textBox.Focus();
        }



        #region Types
        public static readonly DependencyProperty IsReadOnlyProperty =
              DependencyProperty.Register(nameof(IsReadOnly), typeof(bool), typeof(BaseEntry), new PropertyMetadata(false));
        public bool IsReadOnly
        {
            get { return (bool)GetValue(IsReadOnlyProperty); }
            set { SetValue(IsReadOnlyProperty, value); }
        }

        #endregion

        #region Backspace
        public delegate void BackspaceEventHandler(object sender, EventArgs e);

        public event BackspaceEventHandler OnBackspace;
        public void OnBackspacePressed()
        {
            if (OnBackspace != null)
            {
                OnBackspace(null, null);
            }
        }
        protected void TextBox_OnBackspace(object sender, EventArgs e)
        {
            OnBackspacePressed();
        }
        #endregion


        #region Alignment
        public static readonly DependencyProperty PlaceholderHorizontalOptionsProperty =
             DependencyProperty.Register(nameof(PlaceholderHorizontalOptions), typeof(HorizontalAlignment), typeof(BaseEntry), new PropertyMetadata(HorizontalAlignment.Left));
        public HorizontalAlignment PlaceholderHorizontalOptions
        {
            get { return (HorizontalAlignment)GetValue(PlaceholderHorizontalOptionsProperty); }
            set { SetValue(PlaceholderHorizontalOptionsProperty, value); }
        }
        public static readonly DependencyProperty HorizontalTextAlignmentProperty =
             DependencyProperty.Register(nameof(HorizontalTextAlignment), typeof(TextAlignment), typeof(BaseEntry), new PropertyMetadata(TextAlignment.Left));
        public TextAlignment HorizontalTextAlignment
        {
            get { return (TextAlignment)GetValue(HorizontalTextAlignmentProperty); }
            set { SetValue(HorizontalTextAlignmentProperty, value); }
        }
        #endregion

        #region Fontsize
        public static readonly DependencyProperty PlaceholderFontSizeProperty =
             DependencyProperty.Register(nameof(PlaceholderFontSize), typeof(double), typeof(BaseEntry), new PropertyMetadata(16d));
        public double PlaceholderFontSize
        {
            get { return (double)GetValue(PlaceholderFontSizeProperty); }
            set { SetValue(PlaceholderFontSizeProperty, value); }
        }
        public static readonly DependencyProperty TextFontSizeProperty =
             DependencyProperty.Register(nameof(TextFontSize), typeof(double), typeof(BaseEntry), new PropertyMetadata(16d));
        public double TextFontSize
        {
            get { return (double)GetValue(TextFontSizeProperty); }
            set { SetValue(TextFontSizeProperty, value); }
        }

        #endregion

        #region Text
        public static readonly DependencyProperty TextMarginProperty =
            DependencyProperty.Register(nameof(TextMargin), typeof(Thickness), typeof(BaseEntry));
        public Thickness TextMargin
        {
            get { return (Thickness)GetValue(TextMarginProperty); }
            set { SetValue(TextMarginProperty, value); }
        }

        public static readonly DependencyProperty TextProperty =
             DependencyProperty.Register(nameof(Text), typeof(string), typeof(BaseEntry), new PropertyMetadata(""));
        public string Text
        {
            get { return (string)GetValue(TextProperty); }
            set { SetValue(TextProperty, value); }
        }
        public static readonly DependencyProperty TextColorProperty =
              DependencyProperty.Register(nameof(TextColor), typeof(Brush), typeof(BaseEntry), new PropertyMetadata(Brushes.Black));
        public Brush TextColor
        {
            get { return (Brush)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }
        public static readonly DependencyProperty FontFamilyProperty =
              DependencyProperty.Register(nameof(FontFamily), typeof(FontFamily), typeof(BaseEntry), new PropertyMetadata(new FontFamily("Arial")));
        public FontFamily FontFamily
        {
            get { return (FontFamily)GetValue(FontFamilyProperty); }
            set { SetValue(FontFamilyProperty, value); }
        }
        #endregion

        #region Placeholder

        public static readonly DependencyProperty PlaceholderMarginProperty =
             DependencyProperty.Register(nameof(PlaceholderMargin), typeof(Thickness), typeof(BaseEntry));
        public Thickness PlaceholderMargin
        {
            get { return (Thickness)GetValue(PlaceholderMarginProperty); }
            set { SetValue(PlaceholderMarginProperty, value); }
        }

        public static readonly DependencyProperty PlaceholderProperty =
             DependencyProperty.Register(nameof(Placeholder), typeof(string), typeof(BaseEntry), new PropertyMetadata(""));
        public string Placeholder
        {
            get { return (string)GetValue(PlaceholderProperty); }
            set { SetValue(PlaceholderProperty, value); }
        }

        public static readonly DependencyProperty PlaceholderColorProperty =
             DependencyProperty.Register(nameof(PlaceholderColor), typeof(Brush), typeof(BaseEntry), new PropertyMetadata(Brushes.Blue));
        public Brush PlaceholderColor
        {
            get { return (Brush)GetValue(PlaceholderColorProperty); }
            set { SetValue(PlaceholderColorProperty, value); }
        }

        #endregion


        public event EventHandler<TextChangedEventArgs> TextChanged;
        protected void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            TextChanged?.Invoke(this, e);
            Text = textBox.Text;

            if (string.IsNullOrEmpty(textBox.Text))
                placeHolderLabel.Visibility = Visibility.Visible;
            else
                placeHolderLabel.Visibility = Visibility.Hidden;
        }

        protected void onKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Back)
                OnBackspace?.Invoke(this, EventArgs.Empty);
        }
    }
}
