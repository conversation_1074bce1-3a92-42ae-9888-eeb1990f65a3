﻿<?xml version="1.0" encoding="utf-8" ?>
<abstractions:OrdersPage
             xmlns:abstractions="clr-namespace:CRMMobileApp.Abstractions;assembly=CRMMobileApp"
             xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CRMMobileApp.Controls" 
             xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates;assembly=CRMMobileApp" 
             xmlns:parts="clr-namespace:CRMMobileApp.Controls.Parts"
             xmlns:controls1="clr-namespace:XamarinSamples.Views.Controls" 
             xmlns:effects="http://sharpnado.com"
             x:Name="this"
             xmlns:ios="clr-namespace:Xamarin.Forms.PlatformConfiguration.iOSSpecific;assembly=Xamarin.Forms.Core" 
             xmlns:views="clr-namespace:CRMMobileApp.Views.Views"
             ios:Page.UseSafeArea="true"
             x:Class="CRMMobileApp.Views.Pages.Orders.CategoriesPage">
    <abstractions:OrdersPage.Content>
        <Grid        
            ColumnSpacing="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <parts:LeftOrderPanel
                x:Name="leftOrderPanel"
                Grid.Column="0"/>

            <Grid Grid.Column="1"
                  Background="#ffffff">

                <views:CategoriesView 
                    x:FieldModifier="public"
                    x:Name="CategoriesView" />

            </Grid>
        </Grid>
    </abstractions:OrdersPage.Content>
</abstractions:OrdersPage>