﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions" 
        x:Class="CRMDesktopApp.Views.Windows.NewDeliveryPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows"
        xmlns:converters="clr-namespace:MarkupCreator.Converters" 
        xmlns:controls="clr-namespace:CRMDesktopApp.CustomControls" 
        xmlns:editors="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        mc:Ignorable="d"
        Title="Новая доставка" 
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="380"
        Width="1010">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary>
                    <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
                    <converters:EnumListToStringsConverter x:Key="EnumListToStringsConverter" />
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <controls:ClippingBorder
        Background="White"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="0,10,0,0"
        Padding="0"
        CornerRadius="20"
        Width="1000"
        Height="350">
        <controls:ClippingBorder.Effect>
            <DropShadowEffect Color="LightGray"/>
        </controls:ClippingBorder.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <WrapPanel
                Grid.Row="0"
                HorizontalAlignment="Center"
                Margin="0,20,0,0">

                <RadioButton
                    IsChecked="True"
                    Checked="MainTabSelected"
                    Height="25"
                    Width="150">
                    <RadioButton.Style>
                        <Style TargetType="RadioButton">
                            <Style.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Главное"/>
                                                        <Rectangle
                                                            Fill="#524E7D"
                                                            Width="65"
                                                            StrokeThickness="0"
                                                            VerticalAlignment="Bottom"
                                                            HorizontalAlignment="Stretch"
                                                            Height="1"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                                <Trigger Property="IsChecked" Value="False">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Главное"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </RadioButton.Style>
                </RadioButton>

                <RadioButton
                    Checked="PrepaymentTabSelected"
                    Height="25"
                    Width="150">
                    <RadioButton.Style>
                        <Style TargetType="RadioButton">
                            <Style.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Аванс"/>
                                                        <Rectangle
                                                            Fill="#524E7D"
                                                            Width="45"
                                                            StrokeThickness="0"
                                                            VerticalAlignment="Bottom"
                                                            HorizontalAlignment="Stretch"
                                                            Height="1"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                                <Trigger Property="IsChecked" Value="False">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Аванс"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </RadioButton.Style>
                </RadioButton>

                <RadioButton
                    Checked="DeliveryTabSelected"
                    Height="25"
                    Width="150">
                    <RadioButton.Style>
                        <Style TargetType="RadioButton">
                            <Style.Triggers>
                                <Trigger Property="IsChecked" Value="True">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Исполнители"/>
                                                        <Rectangle
                                                            Fill="#524E7D"
                                                            Width="110"
                                                            StrokeThickness="0"
                                                            VerticalAlignment="Bottom"
                                                            HorizontalAlignment="Stretch"
                                                            Height="1"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                                <Trigger Property="IsChecked" Value="False">
                                    <Trigger.Setters>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Grid>
                                                        <TextBlock
                                                            FontFamily="{StaticResource TTFirsNeue-Regular}"
                                                            TextAlignment="Center"
                                                            HorizontalAlignment="Center"
                                                            Foreground="#524E7D"
                                                            FontSize="16"
                                                            Text="Исполнители"/>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger.Setters>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </RadioButton.Style>
                </RadioButton>
            </WrapPanel>



            <TabControl
                x:Name="tabControl"
                Grid.Row="1"
                BorderThickness="0"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Top"
                TabStripPlacement="Top"
                Margin="0,6,0,0"
                Background="White">



                <TabItem
                    Visibility="Collapsed"
                    FontSize="20"
                    FontFamily="TTFirsNeue-Regular"
                    Header="Главное"
                    Foreground="{StaticResource text_gray}">
                    <Grid Margin="0,0,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="50"/>
                            <RowDefinition Height="50"/>
                            <RowDefinition Height="50"/>
                            <RowDefinition Height="50"/>
                            <RowDefinition Height="50"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4*"/>
                                <ColumnDefinition Width="4*"/>
                                <ColumnDefinition Width="2*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">

                                <controls:EntryOutlined 
                                    Margin="7,0,7,0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Text="{Binding ElementName=this,Path=PhoneNumber,Mode=TwoWay}"
                                    Style="{StaticResource white_cornered_entry}"
                                    Placeholder="Введите номер телефона"
                                    Height="45"/>

                                <Button 
                                     Command="{Binding ElementName=this,Path=GoToNewClient}"
                                     Style="{StaticResource bg_purple_btn}"
                                     VerticalAlignment="Center"
                                     HorizontalAlignment="Right"
                                     Margin="0,0,10,0"
                                     Content="Новый"
                                     Width="80"
                                     Height="40"/>

                            </Grid>

                            <Grid Grid.Column="1">
                                <editors:DateEdit
                                    x:Name="deliveryDateDateEdit"
                                    Margin="7,0,7,0"
                                    Padding="7,0,7,0"
                                    Height="45"
                                    AllowDefaultButton="False"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Text="Дата доставки"
                                    CornerRadius="15"
                                    FontSize="17"
                                    Background="#F6F6FB"
                                    Foreground="{StaticResource dark_purple}"
                                    BorderBrush="#F6F6FB"/>
                                <Image
                                    VerticalAlignment="Center"
                                    HorizontalAlignment="Right"
                                    Margin="0,0,15,0"
                                    Source="pack://application:,,,/Resources/Images/calendar.png"
                                    Width="15"
                                    Height="15"/>
                            </Grid>

                            <Grid Grid.Column="2">
                                <editors:ComboBoxEdit   
                                    x:Name="personsCountCB"
                                    Text="Кол-во персон"
                                    Margin="7,-1,7,0"
                                    Padding="7,0,0,0"
                                    Background="#F6F6FB"
                                    Foreground="{StaticResource dark_purple}"
                                    BorderBrush="#F6F6FB"
                                    Height="45"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    CornerRadius="15"
                                    FontSize="17"
                                    ItemsSource="{Binding ElementName=this,Path=PersonsCount,Mode=TwoWay}"
                                    SelectedItem="{Binding ElementName=this,Path=Order.Delivery.PersonsCount,Mode=TwoWay}">
                                    <editors:ComboBoxEdit.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Height="40">
                                                <TextBlock
                                                    Margin="8,8,0,0"
                                                    FontSize="17"
                                                    Foreground="{StaticResource dark_purple}" Text="{Binding}"/>
                                            </Grid>
                                        </DataTemplate>
                                    </editors:ComboBoxEdit.ItemTemplate>
                                </editors:ComboBoxEdit>
                            </Grid>

                        </Grid>

                        <Grid Grid.Row="1"
                              Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4*"/>
                                <ColumnDefinition Width="6*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0" Grid.ColumnSpan="2">

                                <controls:EntryOutlined 
                                    x:Name="clientFIOEntry"
                                    Margin="7,0,7,0"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    EntryBackground="#EFF9F3"
                                    IsEnabled="False"
                                    Style="{StaticResource white_cornered_entry}"
                                    Placeholder="Фамилия Имя"
                                    Height="45"/>

                            </Grid>

                            <Grid Grid.Column="1">

                            </Grid>

                        </Grid>

                        <Grid Grid.Row="2"
                              Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="4*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="2*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">
                                <controls:EntryOutlined 
                                    Margin="7,0,7,0"
                                    Text="{Binding ElementName=this,Path=Order.Delivery.City}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Style="{StaticResource white_cornered_entry}"
                                    Placeholder="Город"
                                    Height="45"/>
                            </Grid>

                            <Grid Grid.Column="1">
                                <controls:EntryOutlined 
                                    Margin="7,0,7,0"
                                    Text="{Binding ElementName=this,Path=Order.Delivery.Street}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Style="{StaticResource white_cornered_entry}"
                                    Placeholder="Улица"
                                    Height="45"/>
                            </Grid>

                            <Grid Grid.Column="2">
                                <controls:EntryOutlined 
                                    Margin="7,0,7,0"
                                    Text="{Binding ElementName=this,Path=Order.Delivery.House}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Style="{StaticResource white_cornered_entry}"
                                    Placeholder="Дом"
                                    Height="45"/>
                            </Grid>

                            <Grid Grid.Column="3">
                                <controls:EntryOutlined 
                                    Margin="7,0,7,0"
                                    Text="{Binding ElementName=this,Path=Order.Delivery.Apartment}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Center"
                                    Style="{StaticResource white_cornered_entry}"
                                    Placeholder="Квартира"
                                    Height="45"/>
                            </Grid>

                        </Grid>

                        <Grid Grid.Row="3"
                              Margin="0,8,0,0">

                            <controls:EntryOutlined 
                                Margin="7,0,7,0"
                                Text="{Binding ElementName=this,Path=Order.Delivery.Comment}"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                Style="{StaticResource white_cornered_entry}"
                                Placeholder="Комментарий"
                                Height="45"/>

                        </Grid>

                    </Grid>
                </TabItem>

                <TabItem
                    Visibility="Collapsed"
                    FontSize="20"
                    FontFamily="TTFirsNeue-Regular"
                    Header="Аванс"
                    Foreground="{StaticResource text_gray}">
                    <Grid>

                        <TextBlock
                            Margin="15,15,0,0"
                            VerticalAlignment="Top"
                            HorizontalAlignment="Left"
                            Foreground="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular">
                             <Run 
                                FontSize="14"  
                                Text="Всего предоплат: "/>
                             <Run 
                                x:Name="prepaymentSumSpan"
                                FontSize="16"
                                Text="0"/>
                            <Run 
                                FontSize="16"
                                Text="руб."/>
                          </TextBlock>


                        <Button 
                             Command="{Binding ElementName=this,Path=GoToPrepayment}"
                             Style="{StaticResource bg_purple_btn}"
                             VerticalAlignment="Bottom"
                             HorizontalAlignment="Right"
                             Margin="0,0,20,20"
                             Content="Внести"
                             Width="140"
                             Height="40"/>

                    </Grid>
                </TabItem>

                <TabItem
                    Visibility="Collapsed"
                    FontSize="20"
                    FontFamily="TTFirsNeue-Regular"
                    Header="Исполнители"
                    Foreground="{StaticResource text_gray}">
                    <Grid Margin="0,0,0,0">

                        <StackPanel Margin="20,0,0,0">

                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Width="250"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="20"
                                    Text="Сервис доставки: "/>

                                <editors:ComboBoxEdit   
                                    Background="#F6F6FB"
                                    Foreground="{StaticResource dark_purple}"
                                    BorderBrush="#F6F6FB"
                                    Height="45"
                                    Width="200"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    SelectedIndex="0"
                                    CornerRadius="15"
                                    FontSize="17"
                                    Padding="7,0,0,0"
                                    ItemsSource="{Binding ElementName=this,Path=DeliveryServices,Mode=TwoWay}"
                                    SelectedItem="{Binding ElementName=this,Path=SelectedDeliveryService,Mode=TwoWay}">
                                    <editors:ComboBoxEdit.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Height="40">
                                                <TextBlock
                                                    Margin="8,8,0,0"
                                                    FontSize="17"
                                                    Foreground="{StaticResource dark_purple}" Text="{Binding}"/>
                                            </Grid>
                                        </DataTemplate>
                                    </editors:ComboBoxEdit.ItemTemplate>
                                </editors:ComboBoxEdit>
                            </StackPanel>

                            <StackPanel
                                Margin="0,10,0,0"
                                Orientation="Horizontal">
                                <TextBlock
                                    Width="250"
                                    VerticalAlignment="Center"
                                    Foreground="{StaticResource dark_purple}"
                                    FontFamily="TTFirsNeue-Regular"
                                    FontSize="20"
                                    Text="Доставщик: "/>

                                <editors:ComboBoxEdit   
                                    x:Name="deliveryManComboBox"
                                    Background="#F6F6FB"
                                    Foreground="{StaticResource dark_purple}"
                                    BorderBrush="#F6F6FB"
                                    Height="45"
                                    Width="200"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    SelectedIndex="0"
                                    CornerRadius="15"
                                    FontSize="17"
                                    Padding="7,0,0,0"
                                    ItemsSource="{Binding ElementName=this,Path=DeliveryMen,Mode=TwoWay}"
                                    SelectedItem="{Binding ElementName=this,Path=SelectedDeliveryMan,Mode=TwoWay}">
                                    <editors:ComboBoxEdit.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Height="40">
                                                <TextBlock
                                                    Margin="8,8,0,0"
                                                    FontSize="17"
                                                    Foreground="{StaticResource dark_purple}" Text="{Binding}"/>
                                            </Grid>
                                        </DataTemplate>
                                    </editors:ComboBoxEdit.ItemTemplate>
                                </editors:ComboBoxEdit>
                            </StackPanel>

                        </StackPanel>

                    </Grid>
                </TabItem>

            </TabControl>

            <Grid
                Grid.Row="2"
                Background="{StaticResource grayBg}">

                <Button 
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Style="{StaticResource gray_cornered_filled_btn}"
                    Margin="20,0,0,0"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    Content="Отмена"
                    Width="170"
                    Height="40"/>

                <Button 
                    Command="{Binding ElementName=this,Path=CreateOrder}"
                    Style="{StaticResource purple_gradient_btn}"
                    Margin="0,0,20,0"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Content="Создать заказ"
                    Width="170"
                    Height="40"/>
            </Grid>

        </Grid>
    </controls:ClippingBorder>
</abstactions:BaseWindow>
