﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.Devices.PosTerminalCard">
    <ContentView.Content>
        <Frame
            HasShadow="False"
            CornerRadius="10"
            Padding="0"
            IsClippedToBounds="True"
            BackgroundColor="{StaticResource bg_purple}">
            <Grid>
                <StackLayout
                    Margin="20,0,0,0"
                    Spacing="20"
                    Orientation="Horizontal"
                    HorizontalOptions="Start"
                    VerticalOptions="Center">
                    <Image
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        HeightRequest="25"
                        WidthRequest="25"
                        Source="{OnPlatform Default=pos.png, WPF='pack://application:,,,/Images/pos.png'}"/>
                    <Label
                        FontSize="16"
                        VerticalOptions="Center"
                        TextColor="{StaticResource dark_purple}"
                        FontFamily="TTFirsNeue-Regular"
                        Text="127.0.0.1"/>
                </StackLayout>
            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>