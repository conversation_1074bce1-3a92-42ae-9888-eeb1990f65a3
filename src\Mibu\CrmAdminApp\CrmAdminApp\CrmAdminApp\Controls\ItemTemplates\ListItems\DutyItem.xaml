﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMAdminMoblieApp.Controls.ItemTemplates.DutyItem">
    <ContentView.Resources>
        <ResourceDictionary Source="../../../Resources/ButtonStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/EntryStyles.xaml"/>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentView.Resources>
    <ContentView.Content>
        <Frame 
          x:Name="frame"
          Padding="0"
          HasShadow="False"
          CornerRadius="10">
            <Frame.Style>
                <Style TargetType="Frame">
                    <Style.Triggers>
                        <DataTrigger TargetType="Frame" 
                                     Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}"
                                     Value="True">
                            <Setter Property="BackgroundColor" Value="{x:StaticResource green}"/>
                        </DataTrigger>
                        <DataTrigger TargetType="Frame" 
                                     Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}"
                                     Value="False">
                            <Setter Property="BackgroundColor" Value="#FFFFFF"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Frame.Style>
            <Grid>
                <Label 
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    Margin="10,0,40,0"
                    x:Name="operationLabel"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="12">
                    <Label.Style>
                        <Style TargetType="Label">
                            <Style.Triggers>
                                <DataTrigger TargetType="Label" 
                                             Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}"
                                             Value="True">
                                    <Setter Property="TextColor" Value="#ffffff"/>
                                </DataTrigger>
                                <DataTrigger TargetType="Label" 
                                             Binding="{Binding Source={x:Reference this},Path=Model.IsSelected}"
                                             Value="False">
                                    <Setter Property="TextColor" Value="{x:StaticResource text_gray}"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Label.Style>
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span Text="Смена # " />
                                <Span Text="{Binding Source={x:Reference this},Path=Model.Duty.DutyNumber}" />
                                <Span Text=" от " />
                                <Span Text="{Binding Source={x:Reference this},Path=Model.Duty.OpenedAt}" />
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>

            </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>