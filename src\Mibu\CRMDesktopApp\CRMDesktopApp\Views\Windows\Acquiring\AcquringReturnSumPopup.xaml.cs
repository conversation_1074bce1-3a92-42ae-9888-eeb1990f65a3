﻿using AcquiringProviders.Abstractions;
using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для ProductOptionsPopup.xaml
    /// </summary>
    public partial class AcquringReturnSumPopup : BaseWindow
    {
        private AbsProvider _provider;
        public AcquringReturnSumPopup(AbsProvider provider)
        {
            _provider = provider;
            InitializeComponent();
        }


        private double sum;
        public double Sum
        {
            get => sum;
            set { sum = value; OnPropertyChanged(nameof(Sum)); }
        }

        private ICommand returnSum;
        public ICommand ReturnSum
        {
            get => returnSum ??= new RelayCommand(async obj =>
            {
                if (Sum <= 0)
                {
                    new OneButtonedPopup("Укажите сумму для возврата", "Уведомление").ShowDialog();
                }
                else
                {
                    _provider.Refund(Sum);
                    this.Close();
                }
            });
        }


    }
}
