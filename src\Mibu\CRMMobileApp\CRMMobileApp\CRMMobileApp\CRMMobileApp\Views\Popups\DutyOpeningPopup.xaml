﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    CloseWhenBackgroundIsClicked="False"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.DutyOpeningPopup">
    <Grid>

        <Frame
              HasShadow="{OnPlatform WPF=True, Default=False}"
              WidthRequest="670"
              HeightRequest="260"
              BackgroundColor="White"
              Background="White"
              Padding="0"
              HorizontalOptions="Center"
              VerticalOptions="Start"
              Margin="0,50,0,0"
              CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="80"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <StackLayout
                          Margin="15,30,0,0"
                          VerticalOptions="Start"
                          HorizontalOptions="Start"
                          Spacing="0"
                          Orientation="Horizontal">

                        <Image 
                              Margin="25,0,0,0"
                              Aspect="Fill"
                              Source="{OnPlatform Default=client.png, WPF='pack://application:,,,/Images/client.png'}"
                              WidthRequest="30"
                              HeightRequest="30"
                              VerticalOptions="Center"/>

                        <Label
                              Margin="15,0,0,0"
                              VerticalOptions="Center"
                              TextColor="{StaticResource dark_purple}"
                              FontFamily="TTFirsNeue-Regular"
                              FontSize="20"
                              Text="Открытие смены"/>

                    </StackLayout>



                    <ImageButton 
                          Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"
                          Command="{Binding Source={x:Reference this},Path=Close}"
                          BackgroundColor="Transparent"
                          VerticalOptions="Start"
                          HorizontalOptions="End"
                          Padding="10"
                          WidthRequest="36"
                          HeightRequest="36"
                          Margin="0,10,10,0"/>



                </Grid>


                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Label
                      Grid.Column="0"
                      Margin="35,0,0,0"
                      VerticalOptions="Center"
                      TextColor="{StaticResource dark_purple}"
                      FontFamily="TTFirsNeue-Regular"
                      FontSize="16"
                      Text="Наличными в кассе"/>

                    <controls:EntryOutlined
                      Grid.Column="1"
                      Text="{Binding Source={x:Reference this},Path=CashInCassa,Mode=TwoWay}"
                      Placeholder="Сумма"
                      Margin="20,0,20,0"
                      HeightRequest="50"
                      Style="{StaticResource white_cornered_entry}"
                      HorizontalOptions="FillAndExpand"
                      VerticalOptions="Center"/>
                </Grid>

                <Grid Grid.Row="2">

                    <Button 
                       Command="{Binding Source={x:Reference this},Path=OpenDuty}"
                       Style="{StaticResource bg_purple_btn}"
                       VerticalOptions="End"
                       HorizontalOptions="End"
                       Margin="0,0,20,30"
                       Text="Создать"
                       WidthRequest="140"
                       HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>