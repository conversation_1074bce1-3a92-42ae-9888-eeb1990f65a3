﻿using CRM.Database.Core;
using CRM.Models.Network.LoyaltyProgram;
using CRMWeb.MobileAPI.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRMWeb.MobileAPI.Controllers.BaristaApp
{
    [ApiController]
    [Route("[controller]")]
    public class BaristaClientsController : AbsController
    {
        public BaristaClientsController(DatabaseContext db, IWebHostEnvironment appEnv): base(db, appEnv)
        {
        }

        [HttpGet, Route("GetClients")]
        public List<Customer> GetClients(string domain, int networkId)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks
                        .Include(o => o.Customers).ThenInclude(o => o.LoyaltyProgram).ThenInclude(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                        .Include(o => o.Customers).ThenInclude(o => o.LoyaltyProgram).ThenInclude(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                        .Include(o => o.Customers).ThenInclude(o => o.LoyaltyProgram).ThenInclude(o => o.Tresholds).ThenInclude(o => o.Workshop)
                        .FirstOrDefault(o => o.Id == networkId);
                    return network.Customers.Where(o  => !o.IsDeleted).ToList();
                }
                catch { return new List<Customer>(); }
            }
        }
        [HttpPost, Route("CreateClient")]
        public async Task<Customer> CreateClient(string domain, int networkId, Customer client)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var network = db.TradeNetworks.Include(o => o.Customers)
                                                  .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.IncludingMenuCategories)
                                                  .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.ExcludingMenuCategories)
                                                  .Include(o => o.LoyaltyPrograms).ThenInclude(o => o.Tresholds).ThenInclude(o => o.Workshop)
                                                  .FirstOrDefault(o => o.Id == networkId);


                    client.LoyaltyProgram = network.LoyaltyPrograms.FirstOrDefault(o => o.IsDefaultProgram && !o.IsDeleted);

                    network.Customers.Add(client);

                    db.TradeNetworks.Update(network);
                    await db.SaveChangesAsync();

                    return client;
                }
                catch { return null; }
            }
        }
        [HttpPost, Route("AddBonuses")]
        public async Task<Customer> AddBonuses(string domain, int clientId, double bonuses)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var client = db.Customers.FirstOrDefault(o => o.Id == clientId);
                    client.Bonuses += bonuses;

                    db.Customers.Update(client);
                    db.SaveChanges();

                    return client;
                }
                catch { return null; }
            }
        }
        [HttpPost, Route("AddTotalSpentToClient")]
        public async Task<Customer> AddTotalSpentToClient(string domain, int clientId, double spent)
        {
            using (DatabaseContext db = new DatabaseContext(domain))
            {
                try
                {
                    var client = db.Customers.FirstOrDefault(o => o.Id == clientId);
                    client.TotalSpent += spent;

                    db.Customers.Update(client);
                    db.SaveChanges();

                    return client;
                }
                catch { return null; }
            }
        }
    }
}
