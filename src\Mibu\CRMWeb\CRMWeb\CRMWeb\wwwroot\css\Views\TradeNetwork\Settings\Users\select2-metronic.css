/***
Select2 theme created for Metronic by Keenthemes
***/

.form-control .select2-choice {
    border: 1px solid #c2cad8;
    background-color: #fff;
    background-image: none;
    filter: none;
    height: 34px;
    padding: 3px 0 0px 12px;
}

.select2-container.select2-drop-above .select2-choice {
    border-bottom-color: #e5e5e5;
    background-color: #fff;
    background-image: none;
    filter: none;
}

.select2-drop {
    border: 1px solid #e5e5e5;
    background-color: #fff;
    background-image: none;    
    -webkit-box-shadow: none;
            box-shadow: none;
    filter: none;
    border-top: 0;
}

.select2-drop-auto-width {
    border-top: 1px solid #e5e5e5;
}

.select2-drop.select2-drop-above {
    border-top: 1px solid #e5e5e5;
    -webkit-box-shadow: none;
            box-shadow: none;
}

.select2-drop-active {
    border: 1px solid #999999;
    border-top: 0;
}

.select2-container .select2-choice .select2-arrow {
    background-image: none;
    background-color: #fff;
    filter: none;
    border-left: 1px solid #e5e5e5;
}

.select2-container.select2-container-active .select2-arrow,
.select2-container.select2-dropdown-open .select2-arrow {
    border-left: 0 !important;
}

.select2-container .select2-choice .select2-arrow b {
    background: url('select2.png') no-repeat 0 1px;
}

.select2-search input {
    border: 1px solid #e5e5e5;
    background-color: #fff;
    filter: none;
    margin: 0;
    outline: 0;
    border: 1px solid #e5e5e5;
    webkit-appearance: none !important;   
    color: #333333;         
    outline: 0;    
    height: auto !important;
    min-height: 26px;
    padding: 6px 6px !important;
    line-height: 20px;
    font-size: 14px;
    font-weight: normal;
    vertical-align: top;  
    background-color: #ffffff;
    -webkit-box-shadow: none;
            box-shadow: none;
    margin-top: 5px;
}

.form-control.select2-container {
    border: 0;
    height: auto !important;
    padding: 0px;
}

.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
    border: 1px solid #999999 !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
}

.select2-dropdown-open .select2-choice {
    border-bottom: 0 !important;
    background-image: none;
    background-color: #fff;
    filter: none;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
}

.select2-dropdown-open.select2-drop-above .select2-choice,
.select2-dropdown-open.select2-drop-above .select2-choices {
    border: 1px solid #999999 !important;
    border-top: 0 !important;
    background-image: none;
    background-color: #fff;
    filter: none;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;

}

.select2-drop.select2-drop-above.select2-drop-active {
    border: 1px solid #999999 !important;
    border-bottom: 0 !important;
}

.select2-dropdown-open .select2-choice .select2-arrow b {
    background-position: -18px 1px;
}

.select2-results {
    margin: 5px 0;
}

.select2-results .select2-highlighted {
    background: #eee;
    color: #333;
}

.select2-results li em {
    background: #feffde;
    font-style: normal;
}

.select2-results .select2-highlighted em {
    background: transparent;
}

.select2-results .select2-highlighted ul {
    background: #fff;
    color: #000;
}

.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
    padding: 3px 7px 4px;
    background: #f4f4f4;
    display: list-item;
}

.select2-container-multi .select2-choices {
    padding-left: 6px;
    min-height: 34px;
    border: 1px solid #e5e5e5;
    background-image: none;
    background-color: #fff;
    filter: none;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
}

.select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid #999 !important;
    background-image: none;
    background-color: #fff;
    filter: none;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 18px;
    margin: 5px 0 3px 5px;
    border: 1px solid #e5e5e5;
    background-image: none;
    background-color: #fff;
    filter: none;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
}

/* contextual */
.warning .select2-container .select2-choice,
.warning .select2-container .select2-choices,
.warning .select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid #C09853;
}
.warning .select2-container .select2-choice .select2-arrow {
    border-left: 1px solid #C09853;
}

.error .select2-container .select2-choice,
.error .select2-container .select2-choices,
.error .select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid #B94A48;
}
.error .select2-container .select2-choice .select2-arrow {
    border-left: 1px solid #B94A48;
}

.info .select2-container .select2-choice,
.info .select2-container .select2-choices,
.info .select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid #3A87AD;
}
.info .select2-container .select2-choice .select2-arrow {
    border-left: 1px solid #3A87AD;
}

.success .select2-container .select2-choice,
.success .select2-container .select2-choices,
.success .select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid #468847;
}
.success .select2-container .select2-choice .select2-arrow {
    border-left: 1px solid #468847;
}

/* Fix z-index when select2 opened in modals*/
.modal-open .select2-drop-mask {
    z-index: 10101;
}

.modal-open .select2-drop {
    z-index: 10102;
}
 
.modal-open .select2-search {
    z-index: 10102;
}