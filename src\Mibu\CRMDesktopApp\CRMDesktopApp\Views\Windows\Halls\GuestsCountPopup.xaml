﻿<abstactions:BaseWindow
        xmlns:abstactions="clr-namespace:CRMDesktopApp.Abstactions"
        x:Class="CRMDesktopApp.Views.Windows.Halls.GuestsCountPopup"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CRMDesktopApp.Views.Windows.Halls"
        mc:Ignorable="d"
        Title="Выбор количества гостей"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        x:Name="this"
        Height="450"
        Width="800">
    <abstactions:BaseWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary>
                    <Style TargetType="ToggleButton" x:Key="rbStyle">
                        <Style.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type ToggleButton}">
                                            <Border
                                                Padding="0"
                                                Background="{StaticResource dark_purple}"
                                                CornerRadius="22">
                                                <ContentPresenter
                                                    HorizontalAlignment="Center"
                                                    Margin="{TemplateBinding Padding}"
                                                    VerticalAlignment="Center"
                                                    Content="{TemplateBinding Content}"
                                                    TextBlock.FontSize="15"
                                                    TextBlock.FontFamily="TTFirsNeue-Regular"
                                                    TextBlock.Foreground="White"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsChecked" Value="False">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type ToggleButton}">
                                            <Border
                                                Padding="0"
                                                Background="Transparent"
                                                BorderBrush="{StaticResource dark_purple}"  
                                                BorderThickness="1"
                                                CornerRadius="22">
                                                <ContentPresenter
                                                    HorizontalAlignment="Center"
                                                    Margin="{TemplateBinding Padding}"
                                                    VerticalAlignment="Center"
                                                    Content="{TemplateBinding Content}"
                                                    TextBlock.FontSize="15"
                                                    TextBlock.FontFamily="TTFirsNeue-Regular"
                                                    TextBlock.Foreground="{StaticResource dark_purple}"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </abstactions:BaseWindow.Resources>
    <Border
        Width="350"
        Height="250"
        Padding="0"
        HorizontalAlignment="Center"
        VerticalAlignment="Top"
        Margin="0,70,0,0"
        CornerRadius="20"
        BorderThickness="1"
        Background="#FFFFFF"
        BorderBrush="LightGray">
        <Border.Effect>
            <DropShadowEffect Color="LightGray"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <TextBlock
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center"
                    Foreground="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="20"
                    Text="Количество гостей"/>
            </Grid>

            <Grid Grid.Row="1">

                <Grid
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="55"/>
                        <RowDefinition Height="55"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="55"/>
                        <ColumnDefinition Width="55"/>
                        <ColumnDefinition Width="55"/>
                        <ColumnDefinition Width="55"/>
                        <ColumnDefinition Width="55"/>
                    </Grid.ColumnDefinitions>

                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="0"
                        Grid.Column="0"
                        Content="1"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="0"
                        Grid.Column="1"
                        Content="2"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="0"
                        Grid.Column="2"
                        Content="3"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="0"
                        Grid.Column="3"
                        Content="4"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="0"
                        Grid.Column="4"
                        Content="5"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>


                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="1"
                        Grid.Column="0"
                        Content="6"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="1"
                        Grid.Column="1"
                        Content="7"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="1"
                        Grid.Column="2"
                        Content="8"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="1"
                        Grid.Column="3"
                        Content="9"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                    <RadioButton
                        Margin="5"
                        Checked="rbChecked"
                        Grid.Row="1"
                        Grid.Column="4"
                        Content="10"
                        Style="{StaticResource rbStyle}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        Height="45"
                        Width="45"/>
                </Grid>

            </Grid>

            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Button 
                    Grid.Column="0"
                    Command="{Binding ElementName=this,Path=CloseWindow}"
                    Style="{StaticResource gray_cornered_filled_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    Margin="0,0,10,0"
                    Content="Отмена"
                    Width="120"
                    Height="40"/>

                <Button 
                    Grid.Column="1"
                    Command="{Binding ElementName=this,Path=Apply}"
                    Style="{StaticResource purple_gradient_btn}"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    Margin="10,0,0,0"
                    Content="Ок"
                    Width="120"
                    Height="40"/>
            </Grid>

        </Grid>
    </Border>
</abstactions:BaseWindow>
