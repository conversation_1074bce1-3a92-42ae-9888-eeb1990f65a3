﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ProductCard : ContentView
    {
        public ProductCard(Product product)
        {
            Model = product;
            InitializeComponent();

            Device.InvokeOnMainThreadAsync(() =>
            {
                priceLabel.Text = $"{(int)product.Price}₽";

                if (string.IsNullOrEmpty(product.ImgPath) || product.ImgPath == MobileAPI.MAIN_HOST)
                {
                    try
                    {
                        var firstLetter = product.Title.Substring(0, 1).ToUpper();
                        var secondLetter = product.Title.Substring(1, 1).ToLower();
                        lettersLabel.Text = firstLetter + secondLetter;
                    }
                    catch { }
                }
            });

        }
        public static readonly BindableProperty ModelProperty =
            BindableProperty.Create(nameof(Model), typeof(Product), typeof(ProductCard));
        public Product Model
        {
            get { return (Product)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }


        public event EventHandler<Product> CardTapped;
        private void onCardTapped(object sender, EventArgs e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                CardTapped?.Invoke(this, Model);
            });
        }
    }
}