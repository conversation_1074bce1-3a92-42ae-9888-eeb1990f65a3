﻿using CRM.Models.Network.LoyaltyProgram;
using CRMMobileApp.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Controls.Templates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class DiscountCard : ContentView
    {
        public DiscountCard()
        {
            InitializeComponent();
        }
        public static readonly BindableProperty ModelProperty =
        BindableProperty.Create(nameof(Model), typeof(DiscountWrapper), typeof(DiscountCard));
        public DiscountWrapper Model
        {
            get { return (DiscountWrapper)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}