﻿using CRM.Models.Network;
using CRMAdminMoblieApp.Controls.ItemTemplates;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TradeNetworks : ContentPage
    {
        public TradeNetworks()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);
            this.BindingContext = this;
            //Load();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            Load();
        }
        private void Load()
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                var networks = await MobileAPI.MainMethods.GetTradeNetworks(ApplicationState.CurrentDomain);
                TradeNetworksList = new ObservableCollection<TradeNetwork>(networks);

                noItemsLabel.IsVisible = TradeNetworksList.Count == 0;
                RenderItems();
            });
        }
        private void RenderItems()
        {
            networksLayout.Children.Clear();
            foreach (var network in TradeNetworksList)
            {
                var card = new TradeNetworkExpandableCard(network)
                {
                    HeightRequest = 100,
                    Margin = new Thickness(0,20,0,0),
                    HorizontalOptions = LayoutOptions.FillAndExpand,
                };
                card.OnSelectBtnTapped += Card_OnSelectBtnTapped;
				card.OnDeleteBtnTapped += Card_OnDeleteBtnTapped;
				networksLayout.Children.Add(card);
            }

          
        }

        private void Card_OnSelectBtnTapped(object sender, TradeNetwork e)
        {
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (e is null) return;

                if (ApplicationState.CurrentTradeNetwork?.Id != e.Id)
                {
                    ApplicationState.CurrentStore = null;
                    // ApplicationState.SelectedStoresInGamification = new System.Collections.Generic.List<CRM.Models.Store>();

                }

                ApplicationState.CurrentTradeNetwork = e;
                ApplicationState.SaveChangesToMemory();

                await App.Current.MainPage.Navigation.PushAsync(new TradeNetworkStores(e));
            });
        }

		private void Card_OnDeleteBtnTapped(object sender, TradeNetwork e)
		{
            Device.InvokeOnMainThreadAsync(async () =>
            {
                if (e is null) return;

                if (await DisplayAlert("Уведомление", "Вы действительно хотите удалить сеть?", "Да", "Нет"))
                {
                    await MobileAPI.AdminMethods.AdminTradeNetworkMethods.DeleteTradeNetwork(ApplicationState.CurrentDomain, e.Id);

                    await App.Current.MainPage.Navigation.PushAsync(new TradeNetworks(), false);
                    App.Current.MainPage.Navigation.RemovePage(this);
                }

            });
		}


		private ObservableCollection<TradeNetwork> tradeNetworksList;
        public ObservableCollection<TradeNetwork> TradeNetworksList
        {
            get => tradeNetworksList;
            set { tradeNetworksList = value; OnPropertyChanged(nameof(TradeNetworksList)); }
        }


        private ICommand goToCreateNetwork;
        public ICommand GoToCreateNetwork
        {
            get => goToCreateNetwork ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new CreateTradeNetwork());
            });
        }

        private ICommand logout;
        public ICommand Logout
        {
            get => logout ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
                //await App.Current.MainPage.Navigation.PushAsync(new MainPage());
            });
        }
    }
}