﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates.Tables
{
    /// <summary>
    /// Логика взаимодействия для XReportItem.xaml
    /// </summary>
    public partial class XReportItem : UserControl
    {
        public XReportItem()
        {
            InitializeComponent();
        }

        public static readonly DependencyProperty TextFontSizeProperty =
           DependencyProperty.Register(nameof(TextFontSize), typeof(double), typeof(XReportItem), new PropertyMetadata(20.0d));
        public double TextFontSize
        {
            get { return (double)GetValue(TextFontSizeProperty); }
            set { SetValue(TextFontSizeProperty, value); }
        }



        public static readonly DependencyProperty FirstColumnProperty =
           DependencyProperty.Register(nameof(FirstColumn), typeof(string), typeof(XReportItem));
        public string FirstColumn
        {
            get { return (string)GetValue(FirstColumnProperty); }
            set { SetValue(FirstColumnProperty, value); }
        }
        public static readonly DependencyProperty SecondColumnProperty =
             DependencyProperty.Register(nameof(SecondColumn), typeof(string), typeof(XReportItem));
        public string SecondColumn
        {
            get { return (string)GetValue(SecondColumnProperty); }
            set { SetValue(SecondColumnProperty, value); }
        }
    }

}
