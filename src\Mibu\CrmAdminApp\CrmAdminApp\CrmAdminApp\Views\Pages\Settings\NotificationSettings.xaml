﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:parts="clr-namespace:CRMAdminMoblieApp.Controls.Parts"
             x:Name="this"
             Padding="{OnPlatform Android='0,0,0,0', iOS='0,28,0,0'}"
             x:Class="CRMAdminMoblieApp.Views.Pages.Settings.NotificationSettings">
    <ContentPage.Resources>
        <ResourceDictionary Source="../../../Resources/MainResources.xaml"/>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid  Background="#ffffff">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.Padding>
                <OnPlatform x:TypeArguments="Thickness">
                    <OnPlatform.Platforms>
                        <On Platform="iOS" Value="0, 20, 0, 0" />
                        <On Platform="Android" Value="0, 0, 0, 0" />
                    </OnPlatform.Platforms>
                </OnPlatform>
            </Grid.Padding>
            <!--<Image
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand"
                Grid.RowSpan="2"
                Aspect="Fill"
                Source="blue_gradient.jpg"/>-->

            <Grid Grid.Row="0">
                <parts:Header x:Name="header"/>
            </Grid>


            <Grid Grid.Row="1">

                <StackLayout
                        Margin="25,0,0,0"
                        Orientation="Horizontal">
                    <StackLayout.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoBack}"/>
                    </StackLayout.GestureRecognizers>

                    <Image 
                            Source="arrowBack.png"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            HeightRequest="15"
                            WidthRequest="8"/>

                    <Label 
                            HorizontalOptions="End"
                            FontFamily="TTFirsNeue-Regular"
                            FontSize="16"
                            TextColor="{x:StaticResource dark_purple}"
                            VerticalOptions="Center"
                            Text="Настройки уведомлений"/>
                </StackLayout>
            </Grid>

            <Grid
                Grid.Row="2"
                Margin="20,0,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="60"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="50"/>
                    <RowDefinition Height="50"/>
                </Grid.RowDefinitions>
                
                <Label 
                    Grid.Row="0"
                    Grid.Column="0"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="16"
                    TextColor="{x:StaticResource dark_purple}"
                    Text="Критические остатки на складе"/>
                <Switch 
                    Grid.Row="0"
                    ThumbColor="{DynamicResource dark_purple}"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    Margin="0,0,0,0"
                    Grid.Column="1"/>

                <Label 
                    Grid.Row="1"
                    Grid.Column="0"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="16"
                    TextColor="{x:StaticResource dark_purple}"
                    Text="Транзакции"/>
                <Switch 
                    Grid.Row="1"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    ThumbColor="{DynamicResource dark_purple}"
                    Grid.Column="1"/>

                <Label 
                    Grid.Row="2"
                    Grid.Column="0"
                    FontFamily="TTFirsNeue-Regular"
                    FontSize="16"
                    TextColor="{x:StaticResource dark_purple}"
                    Text="Открытия/закрытия смен"/>
                <Switch 
                    Grid.Row="2"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    ThumbColor="{DynamicResource dark_purple}"
                    Grid.Column="1"/>

            </Grid>
        </Grid>
    </ContentPage.Content>
</ContentPage>