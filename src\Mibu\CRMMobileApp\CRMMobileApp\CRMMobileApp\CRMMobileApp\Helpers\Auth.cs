﻿using CRM.Models.Enums;
using CRM.Models.Gamification.General;
using CRM.Models.Network;
using CRMGamificationAPIWrapper;
using CRMMobileApp;
using CRMMobileApp.Views.Popups;
using CRMMoblieApiWrapper;
using Plugin.FirebasePushNotification;
using Rg.Plugins.Popup.Extensions;
using System.Threading.Tasks;

namespace CRMAdminMoblieApp.Helpers
{
    public static class Auth
    {
        public static NetworkTerminalUser User { get; set; }
        public static GamificationUser GamificationUser { get; set; }

        public static POSTerminal POSTerminal { get; set; }



        public static async Task<bool> AuthorizeToPOS(string domain, string login, string password)
        {
            POSTerminal = await MobileAPI.MainMethods.AuthorizeToBaristaApp(domain, login, password);
            if (POSTerminal != null)
            {
                ApplicationState.CurrentDomain = domain;
                ApplicationState.CurrentStore = POSTerminal.Store;
                ApplicationState.CurrentTradeNetwork = await MobileAPI.MainMethods.GetTradeNetworkByPOS(ApplicationState.CurrentDomain, POSTerminal.Id);   
            }
            return POSTerminal != null;
        }
        public static async Task<bool> AuthorizeByPIN(string pin)
        {

            User = await MobileAPI.MainMethods.AuthorizeToBaristaAppByPin(ApplicationState.CurrentDomain, POSTerminal.Id, pin);
            return User != null;
        }
        public static async Task<bool> AuthorizeInGamification()
        {
            GamificationUser = await GamificationAPI.Barista.Profile.GetProfileByTerminalCRMUser(ApplicationState.CurrentDomain, User.Id);
            return GamificationUser != null;
        }


        public static bool CheckPinCode(string pin)
        {
            return User.PIN == pin;
        }





        public static async Task Logout()
        {
            User = null;
            POSTerminal = null;

            GamificationAPI.Shared.PushNotifications.DeleteDevice(CrossFirebasePushNotification.Current.Token);
            await App.Current.MainPage.Navigation.PopToRootAsync();
        }
        public static async Task LogoutUser()
        {
            User = null;
            for (int i = App.Current.MainPage.Navigation.NavigationStack.Count - 1; i > 1; i--)
            {
                var page = App.Current.MainPage.Navigation.NavigationStack[i];
                App.Current.MainPage.Navigation.RemovePage(page);
            }

            GamificationAPI.Shared.PushNotifications.DeleteDevice(CrossFirebasePushNotification.Current.Token);
            await App.Current.MainPage.Navigation.PushAsync(new MainPage());
        }


        public static async Task<bool> CheckAllowance(AllowanceLevel allowance)
        {
            if (User.Role == NetworkTerminalUserRole.Admin) return true;
            if (allowance == AllowanceLevel.Allow) return true;
            if (allowance == AllowanceLevel.AllowUsingAdminPassword)
            {
                var popup = new AdminPasswordPopup();
                await App.Current.MainPage.Navigation.PushPopupAsync(popup);
                while (!popup.IsCompleted)
                {
                    await Task.Delay(200);
                }
                if (!popup.IsRightPassword)
                {
                    await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Вы не имеете прав, чтобы совершить данную операцию", "Ошибка"));
                }
                return popup.IsRightPassword;
            }
            if (allowance == AllowanceLevel.Deny)
            {
                await App.Current.MainPage.Navigation.PushPopupAsync(new OneButtonedPopup("Вы не имеете прав, чтобы совершить данную операцию", "Ошибка"));
                return false;
            }
            return false;
        }
    }
}
