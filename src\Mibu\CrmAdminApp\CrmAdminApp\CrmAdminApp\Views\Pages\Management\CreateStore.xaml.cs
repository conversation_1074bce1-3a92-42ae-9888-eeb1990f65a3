﻿using CRM.Models;
using CRM.Models.Enums.Info;
using CRM.Models.Network;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using MarkupCreator.Helpers.Converters;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMAdminMoblieApp
{
    public partial class CreateStore : ContentPage
    {
        private Store newStore;
        public Store NewStore
        {
            get => newStore;
            set { newStore = value; OnPropertyChanged(nameof(NewStore)); }
        }
        public CreateStore()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            NewStore = new Store();
            NewStore.Stock = new CRM.Models.Stores.Stock.Balance.StoreStock();

            NewStore.CommonStoreSettings = new CRM.Models.Stores.Settings.CommonStoreSettings();
            NewStore.ClientsAndBonusesStoreSettings = new CRM.Models.Stores.Settings.ClientsAndBonusesStoreSettings();
            NewStore.StoreFiscalisationSettings = new CRM.Models.Stores.Settings.StoreFiscalisationSettings();
            NewStore.StorePrintSettings = new CRM.Models.Stores.Settings.StorePrintSettings();
        }


        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }


        private ICommand createStoreCommand;
        public ICommand CreateStoreCommand
        {
            get => createStoreCommand ??= new RelayCommand(async obj =>
            {
                ApplicationState.CurrentTradeNetwork.Stores.Add(NewStore);
                await MobileAPI.AdminMethods.AdminStoresMethods.CreateStore(ApplicationState.CurrentDomain,ApplicationState.CurrentTradeNetwork.Id, NewStore);

                await App.Current.MainPage.Navigation.PopAsync();
                //await App.Current.MainPage.Navigation.PushAsync(new TradeNetworkStores(ApplicationState.CurrentTradeNetwork));
            });
        }
    }
}
