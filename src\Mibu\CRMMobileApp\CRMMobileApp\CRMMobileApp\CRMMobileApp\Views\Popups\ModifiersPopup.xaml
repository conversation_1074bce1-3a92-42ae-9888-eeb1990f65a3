﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    x:Name="this"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:effects="http://sharpnado.com"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    xmlns:templates="clr-namespace:CRMMobileApp.Controls.Templates"
    x:Class="CRMMobileApp.Views.Popups.ModifiersPopup">
    <Grid>

        <Frame
             HasShadow="{OnPlatform WPF=True, Default=False}"
             WidthRequest="670"
             HeightRequest="610"
             Padding="0"
             BackgroundColor="White"
             Background="White"
             HorizontalOptions="Center"
             VerticalOptions="Start"
             Margin="0,20,0,0"
             CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Label
                         Margin="15,0,0,0"
                         VerticalOptions="Center"
                         TextColor="{StaticResource dark_purple}"
                         FontFamily="TTFirsNeue-Regular"
                         FontSize="20"
                         Text="Модификаторы"/>
                </Grid>

                <Grid Grid.Row="1">
                    <ScrollView VerticalScrollBarVisibility="{OnPlatform Default=Default, WPF=Never}">
                        <StackLayout x:Name="modifiersLayout">

                        </StackLayout>
                    </ScrollView>
                </Grid>


                <Grid
                     Grid.Row="2"
                     BackgroundColor="{StaticResource grayBg}">

                    <Button 
                         Command="{Binding Source={x:Reference this},Path=Close}"
                         Style="{StaticResource gray_cornered_filled_btn}"
                         Margin="25,0,0,0"
                         VerticalOptions="Center"
                         HorizontalOptions="Start"
                         Text="Отмена"
                         WidthRequest="170"
                         HeightRequest="40"/>

                    <Button 
                         Command="{Binding Source={x:Reference this},Path=ApplyModifiers}"
                         Style="{StaticResource purple_gradient_btn}"
                         Margin="0,0,25,0"
                         VerticalOptions="Center"
                         HorizontalOptions="End"
                         Text="Готово"
                         WidthRequest="170"
                         HeightRequest="40"/>
                </Grid>

            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>