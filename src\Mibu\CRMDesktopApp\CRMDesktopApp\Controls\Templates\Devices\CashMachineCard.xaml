﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Devices.CashMachineCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Devices"
             x:Name="this"
             mc:Ignorable="d" 
             d:DesignHeight="150"
             d:DesignWidth="150">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Border
        CornerRadius="10"
        Padding="0"
        MouseDown="ToggleExpand"
        Background="{StaticResource bg_purple}">
        <Grid>
            <StackPanel>
                <StackPanel
                    Margin="20,0,0,0"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center">
                    <Image
                        Margin="0,10,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Height="25"
                        Width="25"
                        Source="pack://application:,,,/Resources/Images/cash_machine.png"/>
                    <StackPanel
                         Margin="5,10,0,0">
                        <StackPanel Orientation="Horizontal">
                            <Border
                                x:Name="connectionCircleFrame"
                                Background="Gray"
                                Padding="0"
                                CornerRadius="4"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Height="8"
                                Width="8"/>
                            <TextBlock 
                                Margin="5,0,0,0"
                                FontSize="16"
                                VerticalAlignment="Center"
                                Foreground="{StaticResource dark_purple}"
                                FontFamily="TTFirsNeue-Regular"
                                Text="{Binding ElementName=this,Path=Model.Name}"/>
                        </StackPanel>

                        <TextBlock
                            FontSize="16"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource dark_purple}"
                            FontFamily="TTFirsNeue-Regular"
                            Text="{Binding ElementName=this,Path=Model.ConnectionPortNumber}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel
                    Margin="0,10,0,0"
                    HorizontalAlignment="Center">
                    <Button 
                        Margin="0,5,0,0"
                        Height="30"
                        Command="{Binding ElementName=this,Path=CheckConnection}"
                        Content="Проверка связи"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Margin="0,5,0,0"
                        Height="30"
                        Command="{Binding ElementName=this,Path=CloseShift}"
                        Content="Закрыть смену"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                    <Button 
                        Margin="0,5,0,0"
                        Height="30"
                        Command="{Binding ElementName=this,Path=CancelCheque}"
                        Content="Аннулировать чек"
                        HorizontalAlignment="Center"
                        Style="{StaticResource transparent_btn}"/>
                </StackPanel>

            </StackPanel>
        </Grid>
    </Border>
</UserControl>
