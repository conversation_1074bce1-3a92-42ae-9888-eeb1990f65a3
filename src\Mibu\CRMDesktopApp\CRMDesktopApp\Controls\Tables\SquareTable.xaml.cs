﻿using CRM.Models.Stores.Settings.Tables;
using CRMMobileApp.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Tables
{
    /// <summary>
    /// Логика взаимодействия для SquareTable.xaml
    /// </summary>
    public partial class SquareTable : AbsTable
    {
        public SquareTable(Table table) : base(table)
        {
            InitializeComponent();
            SetStyle();
        }

        protected override void SetStyle()
        {
            if (this.TableOrders.Any())
            {
                frame.Background = (Brush)new BrushConverter().ConvertFromString("#524E7D");
                titleLabel.Foreground = (Brush)new BrushConverter().ConvertFromString("#AFADC5");

                ordersCountLabel.Text = this.TableOrders.Count().ToString();
                ordersSumLabel.Text = $"{this.TableOrders.Sum(o => o.Sum)} Р";
            }
            else
            {
               

                frame.Background = (Brush)new BrushConverter().ConvertFromString("#AFADC5");
                titleLabel.Foreground = (Brush)new BrushConverter().ConvertFromString("#524E7D");

                ordersCountLabel.Text = "";
                ordersSumLabel.Text = "";
            }
        }
    }
}
