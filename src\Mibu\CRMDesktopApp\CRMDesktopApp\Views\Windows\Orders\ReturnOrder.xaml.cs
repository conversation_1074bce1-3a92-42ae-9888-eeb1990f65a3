﻿using CRM.Models.Stores.Settings.Equipment;
using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TermoPrintingLib;
using CRMDesktopApp.Views.Windows.CountSelections;
using CRMMobileApp.Helpers;
using CRMDesktopApp.Views.Pages.Orders;

namespace CRMDesktopApp.Views.Windows
{
    /// <summary>
    /// Логика взаимодействия для ReturnOrder.xaml
    /// </summary>
    public partial class ReturnOrder : BaseWindow
    {
        private Order _order;
        public ReturnOrder(Order order)
        {
            _order = order;
            InitializeComponent();
        }


        private string reason = "";
        public string Reason
        {
            get => reason;
            set { reason = value; OnPropertyChanged(nameof(Reason)); }
        }
        private bool printCheque;
        public bool PrintCheque
        {
            get => printCheque;
            set { printCheque = value; OnPropertyChanged(nameof(PrintCheque)); }
        }

        private ICommand returnCommand;
        public ICommand ReturnCommand
        {
            get => returnCommand ??= new RelayCommand(async obj =>
            {
                if (PrintCheque)
                {
                    if (ApplicationState.StorePrinters.Count == 0)
                    {
                        new OneButtonedPopup("Чек не напечатан, т.к. нет ни одного принтера для печати чеков", "Ошибка").ShowDialog();
                    }
                    else
                    {
                        if (ApplicationState.StorePrinters.Count == 1)
                        {
                            var printer = ApplicationState.StorePrinters[0];
                            PrintClosedOrderCheque(printer);
                        }
                        else
                        {
                            var popup = new AvailablePrintersPopup(CRM.Models.Enums.Equipment.PrinterType.OrdersForWorkshops);
                            popup.ItemSelected += (o, e) =>
                            {
                                PrintClosedOrderCheque(e);
                            };
                            popup.ShowDialog();
                        }
                    }
                }

                await OrdersHelper.ReturnOrder(_order, Reason);
                var page = (App.Current.MainWindow as MainWindow).frame.Content as ClosedOrdersPage;
                await page.LoadOrders();


                this.Close();
            });
        }

        #region Печать возврата
        private async void PrintClosedOrderCheque(Printer printer)
        {
            var commonSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetCommonStoreSettings(ApplicationState.CurrentDomain,
                                                                                                                   ApplicationState.CurrentStore.Id);
            var printSettings = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetStorePrintSettings(ApplicationState.CurrentDomain,
                                                                                                           ApplicationState.CurrentStore.Id);
            bool result = new TermoPrinter(printer, commonSettings, printSettings).PrintReturnCheque(_order);
            if (!result)
            {
                new OneButtonedPopup("Ошибка печати", "Ошибка").ShowDialog();
            }
        }
        #endregion
    }
}
