﻿using CRMMobileApp.Models.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace CRMDesktopApp.Controls.Templates
{
    /// <summary>
    /// Логика взаимодействия для DiscountCard.xaml
    /// </summary>
    public partial class DiscountCard : UserControl
    {
        public DiscountCard()
        {
            InitializeComponent();
        }
        public static readonly DependencyProperty ModelProperty =
           DependencyProperty.Register(nameof(Model), typeof(DiscountWrapper), typeof(DiscountCard));
        public DiscountWrapper Model
        {
            get { return (DiscountWrapper)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}
