﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    CloseWhenBackgroundIsClicked="False"
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Name="this"
    InputTransparent="True"
    BackgroundColor="{OnPlatform WPF=Transparent,Android='#80000000',iOS='#80000000'}"
    x:Class="CRMMobileApp.Views.Popups.AdminPasswordPopup">
    <Grid>
        
        <Frame
            HasShadow="{OnPlatform WPF=True, Default=False}"
            BackgroundColor="White"
            Background="White"
            WidthRequest="350"
            HeightRequest="350"
            Padding="0"
            HorizontalOptions="Center"
            VerticalOptions="{OnPlatform Android=Start, iOS=Start, WPF=Center}"
            Margin="{OnPlatform Android='0,200,0,0',iOS='0,200,0,0',WPF='0,0,0,0'}"
            CornerRadius="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="2.5*"/>
                    <RowDefinition Height="4*"/>
                    <RowDefinition Height="2*"/>
                    <RowDefinition Height="1.5*"/>
                </Grid.RowDefinitions>
                <Label 
                    Grid.Row="0"
                    FontSize="20"
                    FontAttributes="Bold"
                    TextColor="{StaticResource dark_purple}"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    Text="Введите пароль, чтобы продолжить"/>
                <StackLayout
                    Spacing="1"
                    Grid.Row="1"
                    HorizontalOptions="Center"
                    VerticalOptions="Center" >
                    <Entry 
                        FontSize="16"
                        Text="{Binding Source={x:Reference this},Path=Password,Mode=TwoWay}"
                        HorizontalOptions="FillAndExpand"
                        PlaceholderColor="{StaticResource text_gray}"
                        Placeholder="Пароль"/>
                </StackLayout>
                <Button 
                    Grid.Row="3"
                    Command="{Binding Source={x:Reference this},Path=CheckPassword}"
                    FontSize="16"
                    FontAttributes="Bold"
                    TextColor="White"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill"
                    Text="Подтвердить"
                    BackgroundColor="{DynamicResource purple}"
                    Background="{DynamicResource purple}"/>
            </Grid>
        </Frame>
    </Grid>
</animations:PopupPage>