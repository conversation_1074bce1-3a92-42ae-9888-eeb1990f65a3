﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Tables.XReportItem"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Tables"
             x:Name="this"
             mc:Ignorable="d" 
             d:DesignHeight="450"
             d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/Fonts.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <UserControl.Content>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="0"
                Margin="20,0,0,0"
                VerticalAlignment="Center"
                HorizontalAlignment="Left"
                Foreground="{StaticResource text_gray}"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                FontSize="{Binding ElementName=this,Path=TextFontSize}"     
                Text="{Binding ElementName=this,Path=FirstColumn}"/>


            <TextBlock
                Grid.Column="1"
                Margin="0,0,0,0"
                VerticalAlignment="Center"
                HorizontalAlignment="Left"
                Foreground="{StaticResource text_gray}"
                FontFamily="{StaticResource TTFirsNeue-Regular}"
                FontSize="{Binding ElementName=this,Path=TextFontSize}"    
                Text="{Binding ElementName=this,Path=SecondColumn}"/>

        </Grid>
    </UserControl.Content>
</UserControl>
