﻿using AcquiringProviders.Abstractions;
using CRM.Models.Stores;
using CRMDesktopApp.Abstactions;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace CRMDesktopApp.Views.Windows.CountSelections
{
    /// <summary>
    /// Логика взаимодействия для ProductOptionsPopup.xaml
    /// </summary>
    public partial class AcquringReturnCancelationPopup : BaseWindow
    {
        private AbsProvider _provider;
        public AcquringReturnCancelationPopup(AbsProvider provider)
        {
            _provider = provider;
            InitializeComponent();
        }


        private string number = "";
        public string Number
        {
            get => number;
            set { number = value; OnPropertyChanged(nameof(Number)); }
        }

        private ICommand returnSum;
        public ICommand ReturnSum
        {
            get => returnSum ??= new RelayCommand(async obj =>
            {
                if (Number.Length != 4)
                {
                    new OneButtonedPopup("Укажите 4 цифры для номера транзакции", "Уведомление").ShowDialog();
                }
                else
                {
                    _provider.CancelOperation(Number);
                    this.Close();
                }
            });
        }
        
      
    }
}
