﻿<UserControl x:Class="CRMDesktopApp.Controls.Templates.Orders.OrderWithDeliveryTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CRMDesktopApp.Controls.Templates.Orders"
             xmlns:converters="clr-namespace:MarkupCreator.Converters"
             mc:Ignorable="d" 
             x:Name="this"
             d:DesignHeight="450" 
             d:DesignWidth="800">
    <UserControl.Resources>

        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../../Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/EntryStyles.xaml"/>
                <ResourceDictionary Source="../../../Resources/Styles/MainResources.xaml"/>
                <ResourceDictionary>
                    <converters:DateTimeToStringConverter x:Key="DateTimeToStringConverter"/>
                    <converters:DateTimeToStringWithoutDateConverter x:Key="DateTimeToStringWithoutDateConverter"/>
                    <converters:DateTimeToStringWithoutTimeConverter x:Key="DateTimeToStringWithoutTimeConverter"/>
                    <converters:DoubleToStringConverter x:Key="DoubleToStringConverter"/>
                    <converters:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter"/>

                    <converters:NotNullToBooleanConverter x:Key="NotNullToBooleanConverter"/>
                    <converters:NotNullToVisibilityConverter x:Key="NotNullToVisibilityConverter"/>
                    <converters:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
                    <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>


        
    </UserControl.Resources>
    <Border
        Background="Transparent"
        Padding="0"
        CornerRadius="20">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">

                <StackPanel
                        VerticalAlignment="Center"
                        Margin="30,0,0,0">
                    <StackPanel
                            VerticalAlignment="Center"
                            Orientation="Horizontal">
                        <TextBlock 
                            Margin="0,0,0,0"       
                            x:Name="orderNumberLabel"
                            FontFamily="TTFirsNeue-Regular"
                            HorizontalAlignment="Left"
                            FontSize="14"
                            Foreground="{StaticResource text_gray}">
                            <Run Text="#"/>
                            <Run Text="{Binding ElementName=this,Path=Model.OrderNumber}"/>
                        </TextBlock>

                        <TextBlock 
                            Margin="40,0,0,0"
                            x:Name="orderDateLabel"
                            Text="{Binding ElementName=this,Path=Model.Delivery.Date,Converter={StaticResource DateTimeToStringConverter}}"
                            FontFamily="TTFirsNeue-Regular"
                            HorizontalAlignment="Left"
                            FontSize="14"
                            Foreground="{StaticResource text_gray}"/>
                    </StackPanel>

                    <StackPanel
                        Visibility="{Binding ElementName=this,Path=Model.Customer,Converter={StaticResource NotNullToVisibilityConverter}}"
                        VerticalAlignment="Center">
                        <TextBlock  
                            Margin="0,0,0,0"
                            x:Name="orderCustomerNameLabel"
                            FontFamily="TTFirsNeue-Regular"
                            HorizontalAlignment="Left"
                            FontSize="14"
                            Foreground="{StaticResource text_gray}">
                            <Run Text="{Binding ElementName=this,Path=Model.Customer.Name}"/>
                            <Run Text=""/>
                            <Run Text="{Binding ElementName=this,Path=Model.Customer.Surname}"/>
                        </TextBlock>

                        <TextBlock 
                            Margin="0,0,0,0"
                            Text="{Binding ElementName=this,Path=Model.Customer.Phone}"
                            x:Name="orderCustomerPhoneLabel"
                            FontFamily="TTFirsNeue-Regular"
                            HorizontalAlignment="Left"
                            FontSize="14"
                            Foreground="{StaticResource text_gray}"/>
                    </StackPanel>
                </StackPanel>

            </Grid>

            <Grid Grid.Column="1">
                <TextBlock 
                    Margin="0,0,0,0"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    FontSize="14"
                    Foreground="{StaticResource text_gray}">
                    <Run Text="{Binding ElementName=this,Path=Model.Delivery.PersonsCount}"/>
                    <Run Text="персон"/>
                </TextBlock>
            </Grid>

            <Grid Grid.Column="2">
                <StackPanel
                        Visibility="{Binding ElementName=this,Path=Model.Delivery.Service,Converter={StaticResource NotNullToVisibilityConverter}}"
                        VerticalAlignment="Center">
                    <TextBlock 
                        Margin="0,0,0,0"
                        VerticalAlignment="Center"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalAlignment="Left"
                        FontSize="14"
                        FontWeight="Bold"
                        Text="Доставка:"
                        Foreground="{StaticResource text_gray}"/>

                    <TextBlock 
                        Margin="0,0,0,0"
                        VerticalAlignment="Center"
                        x:Name="deliveryServiceLable"
                        FontFamily="TTFirsNeue-Regular"
                        HorizontalAlignment="Left"
                        FontSize="14"
                        Foreground="{StaticResource text_gray}">
                        <Run Text="{Binding ElementName=this,Path=Model.Delivery.Service.Title}"/>
                        <Run Text="    "/>
                        <Run Text="{Binding ElementName=this,Path=Model.Delivery.DeliveryMan.Name}"/>
                        <Run Text=""/>
                        <Run Text="{Binding ElementName=this,Path=Model.Delivery.DeliveryMan.Surname}"/>
                    </TextBlock>
                </StackPanel>
            </Grid>

            <Grid Grid.Column="3">
                <TextBlock 
                    Margin="0,0,30,0"
                    x:Name="orderSumLabel"
                    FontFamily="TTFirsNeue-Regular"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    FontSize="20"
                    FontWeight="Bold"
                    Foreground="{StaticResource text_gray}">
                    <Run Text="{Binding ElementName=this,Path=Model.Sum,Mode=OneWay,Converter={StaticResource DoubleToStringConverter}}"/>
                    <Run Text=" р"/>
                </TextBlock>
            </Grid>


        </Grid>
    </Border>
</UserControl>
