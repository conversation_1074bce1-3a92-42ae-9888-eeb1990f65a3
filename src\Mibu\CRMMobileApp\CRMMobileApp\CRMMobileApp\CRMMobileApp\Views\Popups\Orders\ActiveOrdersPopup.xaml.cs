﻿using CRM.Models.Stores;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Controls.Templates;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using CRMMobileApp.Views.Pages.Orders;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ActiveOrdersPopup : PopupPage
    {
        public ActiveOrdersPopup()
        {
            InitializeComponent();
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            LoadData();
        }
        private async Task LoadData()
        {
            await OrdersHelper.GetActiveOrders();

            await Device.InvokeOnMainThreadAsync(() =>
            {
                ordersStackLayout.Children.Clear();
                foreach (var order in OrdersHelper.ActiveOrders)
                {
                    var control = new TableOrderTemplate(order)
                    {
                        MinimumHeightRequest = 38,
                        Margin = new Thickness(0, 4, 0, 0)
                    };
                    control.ItemTapped += Control_ItemTapped;
                    ordersStackLayout.Children.Add(control);
                }
            });
        }
        private void Control_ItemTapped(object sender, CRM.Models.Stores.Order e)
        {
            Device.InvokeOnMainThreadAsync(() =>
            {
                App.Current.MainPage.Navigation.PopPopupAsync();
                OrdersHelper.SetActiveOrder(e);
            });
            //  await App.Current.MainPage.Navigation.PushAsync(new CategoriesPage());
        }



        private ICommand openNewOrder;
        public ICommand OpenNewOrder
        {
            get => openNewOrder ??= new RelayCommand(async obj =>
            {
                await OrdersHelper.OpenOrder();
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}