<?xml version="1.0" encoding="utf-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
             xmlns:behavior="clr-namespace:DrinkitMobile.Custom"
             x:Class="XamarinSamples.Views.Controls.EntryOutlined"
             Margin="{OnPlatform Android='3,0,3,5', iOS='3,0,3,5'}"
             BackgroundColor="Transparent"
             x:Name="this">
    <ContentView.Content>
        <StackLayout>
            <Grid>
                <Frame HasShadow="False"
                       x:Name="EntryFrame"
                       BorderColor="{Binding BorderColor, Source={x:Reference this}}"
                       CornerRadius="{Binding CornerRadius, Source={x:Reference this}}"
                       BackgroundColor="{Binding EntryBackground, Source={x:Reference this}}"
                       Background="{Binding EntryBackground, Source={x:Reference this}}"
                       Padding="{OnPlatform Android='5,0,5,0', iOS='8,0,8,0'}"
                       Margin="{OnPlatform Android='0,0,0,0', iOS='0,0,0,0'}" />

                <Label x:Name="PlaceHolderLabel"
                       FontSize="{Binding Source={x:Reference this},Path=PlaceholderFontSize}"
                       HorizontalOptions="{Binding Source={x:Reference this},Path=PlaceholderHorizontalOptions}"
                       Margin="{Binding PlaceholderMargin,Source={x:Reference this}}"      
                       TextColor="{Binding PlaceholderColor, Source={x:Reference this}}"
                       Text="{Binding Placeholder,Source={x:Reference this}}"
                       VerticalOptions="{Binding VerticalPlaceholderAlignment,Source={x:Reference this}}" />

                <controls:BorderlessEntry
                    IsVisible="True"
                    Background="{OnPlatform iOS=Transparent}"
                    BackgroundColor="{OnPlatform iOS=Transparent}"
                    IsReadOnly="{Binding Source={x:Reference this},Path=IsReadOnly}"
                    FontFamily="{Binding Source={x:Reference this},Path=FontFamily}"
                    Keyboard="{Binding Source={x:Reference this},Path=Keyboard}"
                    IsPassword="{Binding Source={x:Reference this},Path=IsPassword}"
                    FontSize="{Binding Source={x:Reference this},Path=TextFontSize}"
                    HorizontalTextAlignment="{Binding Source={x:Reference this},Path=HorizontalTextAlignment}"
                    VerticalTextAlignment="{Binding VerticalTextAlignment,Source={x:Reference this}}"
                    HeightRequest="{Binding Source={x:Reference this},Path=HeightRequest}"
                    x:Name="TextBox"
                    VerticalOptions="FillAndExpand"                            
                    Text="{Binding Text,Source={x:Reference this},Mode=TwoWay}"
                    TextColor="{Binding TextColor,Source={x:Reference this},Mode=TwoWay}"
                    Margin="{Binding TextMargin,Source={x:Reference this}}"                            
                    Focused="TextBox_Focused"                            
                    Unfocused="TextBox_Unfocused"
                    TextChanged="OnTextChanged">
                    <controls:BorderlessEntry.Style>
                        <Style TargetType="controls:BorderlessEntry">
                            <Style.Triggers>
                                <DataTrigger TargetType="controls:BorderlessEntry" Binding="{Binding Source={x:Reference this},Path=IsMultiline}" Value="True">
                                    <Setter Property="IsVisible" Value="False"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </controls:BorderlessEntry.Style>
                </controls:BorderlessEntry>

                <controls:BorderlessEditor
                     IsVisible="False"
                     Background="{OnPlatform iOS=Transparent}"
                     BackgroundColor="{OnPlatform iOS=Transparent}"
                     IsReadOnly="{Binding Source={x:Reference this},Path=IsReadOnly}"
                     FontFamily="{Binding Source={x:Reference this},Path=FontFamily}"
                     Keyboard="{Binding Source={x:Reference this},Path=Keyboard}"
                     FontSize="{Binding Source={x:Reference this},Path=TextFontSize}"
                     HeightRequest="{Binding Source={x:Reference this},Path=HeightRequest}"
                     x:Name="TextBoxEditor"
                     VerticalOptions="FillAndExpand"       
                     Text="{Binding Text,Source={x:Reference this},Mode=TwoWay}"
                     TextColor="{Binding TextColor,Source={x:Reference this},Mode=TwoWay}"
                     Margin="{Binding TextMargin,Source={x:Reference this}}"                            
                     Focused="TextBox_Focused"                            
                     Unfocused="TextBox_Unfocused"
                     TextChanged="OnTextChanged">
                    <controls:BorderlessEditor.Style>
                        <Style TargetType="controls:BorderlessEditor">
                            <Style.Triggers>
                                <DataTrigger TargetType="controls:BorderlessEditor" Binding="{Binding Source={x:Reference this},Path=IsMultiline}" Value="True">
                                    <Setter Property="IsVisible" Value="True"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </controls:BorderlessEditor.Style>
                </controls:BorderlessEditor>
            </Grid>            
        </StackLayout>
    </ContentView.Content>
</ContentView>