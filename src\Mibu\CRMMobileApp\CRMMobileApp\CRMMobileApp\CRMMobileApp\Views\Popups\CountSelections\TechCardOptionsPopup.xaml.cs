﻿using CRM.Models.Network.ReferenceBooks.Menu;
using CRM.Models.Stores;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class TechCardOptionsPopup : PopupPage
    {
        private OrderItem technicalCard;
        public OrderItem TechnicalCard
        {
            get => technicalCard;
            set { technicalCard = value; OnPropertyChanged(nameof(TechnicalCard)); }
        }
        public TechCardOptionsPopup(OrderItem product)
        {
            InitializeComponent();
            TechnicalCard = product;
        }
        public TechCardOptionsPopup(OrderItem product,double amount)
        {
            InitializeComponent();
            TechnicalCard = product;
            Amount = (int)amount;

            _isNewItem = false;
        }
        private bool _isNewItem = true;


        #region Количество товара
        private ICommand incrementAmount;
        public ICommand IncrementAmount
        {
            get => incrementAmount ??= new RelayCommand(async obj =>
            {
                Amount++;
            });
        }
        private ICommand decrementAmount;
        public ICommand DecrementAmount
        {
            get => decrementAmount ??= new RelayCommand(async obj =>
            {
                if(Amount > 1)
                    Amount--;
            });
        }
        private int amount = 1;
        public int Amount
        {
            get => amount;
            set { amount = value; OnPropertyChanged(nameof(Amount)); }
        }
        #endregion







        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
        private ICommand addToOrder;
        public ICommand AddToOrder
        {
            get => addToOrder ??= new RelayCommand(async obj =>
            {
                TechnicalCard.CreatedAt = DateTime.Now;
                TechnicalCard.Amount = Amount;

                if (_isNewItem)
                {
					OrdersHelper.AddOrderItem(TechnicalCard);
				}
                else
                {
					OrdersHelper.UpdateOrderItem(TechnicalCard);
				}

                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}