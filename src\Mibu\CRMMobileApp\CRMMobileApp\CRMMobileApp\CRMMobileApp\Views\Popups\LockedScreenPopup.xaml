﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns:behaviors="clr-namespace:Xamarin.CommunityToolkit.UI.Views;assembly=Xamarin.CommunityToolkit"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
    xmlns:controls1="clr-namespace:CRMMobileApp.Controls" 
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="Transparent"
    xmlns:controls="clr-namespace:XamarinSamples.Views.Controls"
    x:Class="CRMMobileApp.Views.Popups.LockedScreenPopup">
    <Grid   
        BackgroundColor="Transparent">
        
        <Image
            Aspect="Fill"
            Source="{OnPlatform Default=main_gradient.png, WPF='pack://application:,,,/Images/main_gradient.png'}"/>
        
        <StackLayout
            Margin="0,110,0,0">
            
            <Label
                HorizontalOptions="Center"
                TextColor="White"
                FontSize="24"
                Text="Экран заблокирован, введите Ваш пин код" />

            <Grid
                 Margin="0,50,0,0">
                <controls:EntryOutlined  
                    x:Name="entry"
                    HorizontalOptions="Center"
                    TextMargin="45,0,0,0"
                    PlaceholderMargin="45,0,0,0"
                    PlaceholderColor="#e5e5e5"
                    BorderColor="White"
                    EntryBackground="Transparent"
                    IsEnabled="False"
                    Placeholder="Введите пин"
                    Text="{Binding Source={x:Reference this},Path=Password,Mode=TwoWay}"
                    WidthRequest="225"
                    Style="{StaticResource gray_bordered_entry}"/>

                <Image 
                     Margin="-180,-4,0,0"
                     HorizontalOptions="Center"
                     HeightRequest="30"
                     WidthRequest="30"
                     Source="{OnPlatform Default=login.png, WPF='pack://application:,,,/Images/login.png'}"/>
            </Grid>
            
              <controls1:AuthWhiteKeyboard
                  Padding="0,0,0,0"
                   HorizontalOptions="Center"
                   x:Name="keyboard" />

            <Button 
                FontSize="15"
                HorizontalOptions="Center"
                Command="{Binding Source={x:Reference this},Path=Logout}"
                BackgroundColor="Transparent"
                BorderWidth="0"
                TextColor="White"
                Text="Выйти" />

        </StackLayout>
   
        
   
        
        

     

    </Grid>
</animations:PopupPage>