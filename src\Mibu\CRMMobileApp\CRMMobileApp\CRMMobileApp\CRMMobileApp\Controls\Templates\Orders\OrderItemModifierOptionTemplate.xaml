﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Templates.Orders.OrderItemModifierOptionTemplate">
  <ContentView.Content>
        <Grid
            HeightRequest="30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="190"/>
                <ColumnDefinition Width="30"/>
                <ColumnDefinition Width="90"/>
            </Grid.ColumnDefinitions>


            <Label 
                  Grid.Column="0"
                  Margin="40,0,0,0"
                  VerticalOptions="Center"
                  HorizontalOptions="Start"
                  FontSize="14"
                  TextColor="White">
                <Label.FormattedText>
                    <FormattedString>
                        <FormattedString.Spans>
                            <Span Text="* "/>
                            <Span Text="{Binding Source={x:Reference this}, Path=ModifierOption.ModifierOption.Title}"/>
                        </FormattedString.Spans>
                    </FormattedString>
                </Label.FormattedText>
            </Label>


            <Label 
                  Grid.Column="2"
                  FontSize="14"
                  TextColor="White"
                  VerticalOptions="Center"
                  HorizontalOptions="Center">
                <Label.FormattedText>
                    <FormattedString>
                        <FormattedString.Spans>
                            <Span Text="{Binding Source={x:Reference this},Path=ModifierOption.Amount}"/>
                            <Span Text=" x "/>
                            <Span Text="{Binding Source={x:Reference this},Path=ModifierOption.Price}"/>
                        </FormattedString.Spans>
                    </FormattedString>
                </Label.FormattedText>
            </Label>
        </Grid>
    </ContentView.Content>
</ContentView>