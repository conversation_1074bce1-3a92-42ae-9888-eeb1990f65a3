﻿
using CRM.Models.Reports.Mobile.Admin.Waiters;
using CRM.Models.Reports.Mobile.Admin.Workshops;
using CRMAdminMoblieApp.Models;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Controls.ItemTemplates
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WaiterRowItem : ContentView
    {
        public WaiterRowItem()
        {
            InitializeComponent();
        }

        public static readonly BindableProperty ModelProperty =
         BindableProperty.Create(nameof(Model), typeof(WaitersReportRow), typeof(WaiterRowItem));
        public WaitersReportRow Model
        {
            get { return (WaitersReportRow)GetValue(ModelProperty); }
            set { SetValue(ModelProperty, value); }
        }
    }
}