﻿using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMMobileApp.Core;
using CRMMoblieApiWrapper;
using Rg.Plugins.Popup.Extensions;
using System.Windows.Input;
using Xamarin.Forms;

namespace CRMAdminMoblieApp
{
    public partial class MainPage : ContentPage
    {
        public MainPage(bool showNeedToPayText = false)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            if (showNeedToPayText)
            {
                authErrorText.Text = "Необходимо оплатить подписку";
            }
        }
        public MainPage(string textToShow)
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            authErrorText.Text = textToShow;
        }
#if DEBUG
        private string domain = "crm";
        private string username = "test_login";
        private string password = "0000";
#else
        private string domain = string.Empty;
        private string username = string.Empty;
        private string password = string.Empty;
#endif

        private bool rememberMe = true;

        public string Domain
        {
            get => domain;
            set { domain = value; OnPropertyChanged(nameof(Domain)); }
        }
        public string Username
        {
            get => username;
            set { username = value; OnPropertyChanged(nameof(Username)); }
        }
        public string Password
        {
            get => password;
            set { password = value; OnPropertyChanged(nameof(Password)); }
        }


        public bool RememberMe
        {
            get => rememberMe;
            set { rememberMe = value; OnPropertyChanged(nameof(RememberMe)); }
        }

        private ICommand auth;
        public ICommand Auth
        {
            get => auth ??= new RelayCommand(async obj =>
            {
                bool valid = true;
                valid &= !string.IsNullOrEmpty(Domain);
                valid &= !string.IsNullOrEmpty(Username);
                valid &= !string.IsNullOrEmpty(Password);

                if (!valid)
                {
                    authErrorText.Text = "Заполните все поля, чтобы продолжить";
                    return;
                }

                bool result = await CRMAdminMoblieApp.Helpers.Auth.Authorize(Domain, Username, Password);
                if (result)
                {
                    ApplicationState.Subscription = await MobileAPI.MainMethods.GetDomainSubscription(Domain);

                    if (ApplicationState.Subscription.IsExpired)
                    {
                        authErrorText.Text = "Необходимо оплатить подписку";
                        return;
                    }

                    ApplicationState.CurrentDomain = Domain;
                    ApplicationState.SaveChangesToMemory();
                    await App.Current.MainPage.Navigation.PushAsync(new TradeNetworks());
                }
                else
                {
                    authErrorText.Text = "Неверный логин или пароль";
                }

            });
        }
        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopAsync());
        }
    }
}
