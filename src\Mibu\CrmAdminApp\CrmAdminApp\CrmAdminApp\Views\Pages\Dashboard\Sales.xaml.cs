﻿using CRMAdminMoblieApp.Abstractions;
using CRMAdminMoblieApp.Enums;
using CRMAdminMoblieApp.Helpers;
using CRMMoblieApiWrapper;
using System.Collections.ObjectModel;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;
using System.Threading.Tasks;
using CRMAdminMoblieApp.Models;
using System;

namespace CRMAdminMoblieApp.Views.Pages.Dashboard
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class Sales : BaseDashboardPage
    {
        public override DashboardPageType PageType => DashboardPageType.Sales;
        public Sales() : base()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

			DutyLabel = dutyLabel;

			//Load();
		}
        public override async Task RefreshData()
        {
            Rows.Clear();
		    await Task.Run(async () =>
			{
				var report = await MobileAPI.BaristaMethods.BaristaReportsMethods.GetSalesAtDuty(ApplicationState.CurrentDomain,
																						         ApplicationState.CurrentStore.Id,
																						         ApplicationState.CurrentDuty.Id);
				await Device.InvokeOnMainThreadAsync(() =>
				{
                    foreach (var sale in report.ProductRows)
                    {
                        Rows.Add(new SaleReportRowModel
                        {
                            Title = sale.Product.Title,
                            Price = $"{Math.Round(sale.Price, 2)}₽",
                            Amount = $"{Math.Round(sale.Amount, 3)}",
                            Sum = $"{Math.Round(sale.Sum, 2)}₽",
                        });
                    }
                    foreach (var sale in report.TechCardRows)
                    {
                        Rows.Add(new SaleReportRowModel
                        {
                            Title = sale.TechnicalCard.Title,
                            Price = $"{Math.Round(sale.Price, 2)}₽",
                            Amount = $"{Math.Round(sale.Amount, 3)}",
                            Sum = $"{Math.Round(sale.Sum, 2)}₽",
                        });
                    }



                    noItemsLabel.IsVisible = Rows.Count == 0;
					totalSumLabel.Text = $"{Math.Round(report.Total, 2)}₽";
				});

			});
        }

		public bool IsLoaded { get; private set; }
		public async Task Load()
        {
			if (IsLoaded) return;


			IconImageSource = ImageSource.FromFile("sales.png");
			Title = "Продажи";

			OnDutyChanged(null, ApplicationState.CurrentDuty);

			//await RefreshData();

			IsLoaded = true;
		}

        private ObservableCollection<SaleReportRowModel> rows = new ObservableCollection<SaleReportRowModel>();
        public ObservableCollection<SaleReportRowModel> Rows
        {
            get => rows;
            set { rows = value; OnPropertyChanged(nameof(Rows)); }
        }
    }
}