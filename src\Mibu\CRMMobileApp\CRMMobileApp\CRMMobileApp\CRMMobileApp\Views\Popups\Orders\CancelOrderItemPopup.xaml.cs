﻿using CRM.Models.Stores;
using CRMAdminMoblieApp.Helpers;
using CRMMobileApp.Core;
using CRMMobileApp.Helpers;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.CommunityToolkit.UI.Views;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMMobileApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class CancelOrderItemPopup : PopupPage
    {
        private List<OrderItem> _items;
        private bool _deleteAllOrderItems;
        public CancelOrderItemPopup(OrderItem item)
        {
            InitializeComponent();
            _items = new List<OrderItem> { item };
        }
        public CancelOrderItemPopup(List<OrderItem> items)
        {
            InitializeComponent();
            _items = items;

            _deleteAllOrderItems = true;
        }


        private ICommand cancelPosition;
        public ICommand CancelPosition
        {
            get => cancelPosition ??= new RelayCommand(async obj =>
            {
                var reason = obj as string;
                await OrdersHelper.CancelOrderItems(_items, reason);
                if (_deleteAllOrderItems)
                {
                    OrdersHelper.ClearOrderItems();
                }

                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
            });
        }
    }
}