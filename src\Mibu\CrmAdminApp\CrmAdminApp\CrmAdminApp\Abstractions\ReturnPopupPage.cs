﻿using CRMAdminMoblieApp;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System;
using System.Collections.Generic;
using System.Text;

namespace MibuAdminApp.Abstractions
{


    public class ReturnPopupPage : PopupPage
    {
        public event EventHandler<object> Dismissed;


        protected void Dismiss(object value)
        {
            try
            {
				App.Current.MainPage.Navigation.PopPopupAsync();
				Dismissed?.Invoke(this, value);
			}
            catch { }
        }
    }
}
