﻿using System;
using Android.Content;
using PovodogMobileApp.Custom;
using PovodogMobileApp.Droid.UI.Renderers;
using Xamarin.Forms;
using Xamarin.Forms.Platform.Android;
using XamarinSamples.Views.Controls;

[assembly: Export<PERSON>enderer(typeof(BorderlessPicker), typeof(BordlessPickerRenderer))]
namespace PovodogMobileApp.Droid.UI.Renderers
{
    public class BordlessPickerRenderer : PickerRenderer
    {
        public BordlessPickerRenderer(Context context) : base(context)
        {
        }

        protected override void OnElementChanged(ElementChangedEventArgs<Picker> e)
        {
            base.OnElementChanged(e);

            if (e.OldElement == null)
            {
                Control.Background = null;
                Control.SetBackgroundColor(Android.Graphics.Color.Transparent);
            }
        }

    }
}

