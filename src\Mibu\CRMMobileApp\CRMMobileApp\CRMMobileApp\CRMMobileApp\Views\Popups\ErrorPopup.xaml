﻿<?xml version="1.0" encoding="utf-8" ?>
<animations:PopupPage
    xmlns:animations="clr-namespace:Rg.Plugins.Popup.Pages;assembly=Rg.Plugins.Popup" 
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="CRMMobileApp.Views.Popups.ErrorPopup"
    x:Name="this"
    CloseWhenBackgroundIsClicked="False"
    BackgroundColor="Transparent">
    <Grid>


        <Frame
            HasShadow="False"
            WidthRequest="360"
            HeightRequest="100"
            HorizontalOptions="End"
            VerticalOptions="Start"
            BackgroundColor="White"
            Background="White"
            Padding="0"
            Margin="0,25,25,0"
            CornerRadius="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="50"/>
                </Grid.ColumnDefinitions>
                <Image
                    Grid.Column="0"
                    WidthRequest="60"
                    HeightRequest="60"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    Margin="10,10,0,0"
                    Source="{OnPlatform Default=error_red.png, WPF='pack://application:,,,/Images/error_red.png'}"/>
                <Label 
                    Grid.Column="1"
                    Margin="0,15,0,0"
                    Text="Описание ошибки" />
                <ImageButton
                    Grid.Column="2"
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    WidthRequest="20"
                    HeightRequest="20"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    Margin="10,10,0,0"
                    BackgroundColor="Transparent"
                    Source="{OnPlatform Default=close.png, WPF='pack://application:,,,/Images/close.png'}"/>
            </Grid>
        </Frame>

    </Grid>
</animations:PopupPage>