﻿using App1;
using CrmAdminApp.Helpers;
using CRMAdminMoblieApp.Helpers;
using CRMAdminMoblieApp.Views.Pages;
using CRMAdminMoblieApp.Views.Pages.Dashboard;
using CRMMobileApp.Core;
using MobPhone;
using Rg.Plugins.Popup.Extensions;
using Rg.Plugins.Popup.Pages;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace CRMAdminMoblieApp.Views.Popups
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class MenuPopup : PopupPage
    {
        public MenuPopup()
        {
            InitializeComponent();
            Init();
        }

        private async Task Init()
        {
            await Task.Run(async() =>
            {
                await Auth.AuthorizeInGamification();
            });
            await Device.InvokeOnMainThreadAsync(async() =>
            {          
                gamificationStackLayout.IsVisible = Auth.GamificationUser != null && ApplicationState.Subscription.IsGamificationAvailable();
            });
        }

        private ICommand goToRealTimePage;
        public ICommand GoToRealTimePage
        {
            get => goToRealTimePage ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.CurrentTradeNetwork is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
                    return;
                }
                if (ApplicationState.CurrentStore is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
                    return;
                }

                Device.BeginInvokeOnMainThread(() => App.Current.MainPage.Navigation.PopPopupAsync());
                RemoveIsHasAndPush(PagesHelper.GetRealTimePage());
            });
        }

        private ICommand goToOrders;
        public ICommand GoToOrders
        {
            get => goToOrders ??= new RelayCommand(async obj =>
            {
                if(ApplicationState.CurrentTradeNetwork is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
                    return;
                }
                if (ApplicationState.CurrentStore is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
                    return;
                }

                Device.BeginInvokeOnMainThread(() => App.Current.MainPage.Navigation.PopPopupAsync());
                RemoveIsHasAndPush(PagesHelper.GetOrdersPage());

            });
        }
        private ICommand goToSales;
        public ICommand GoToSales
        {
            get => goToSales ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.CurrentTradeNetwork is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
                    return;
                }
                if (ApplicationState.CurrentStore is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
                    return;
                }

                Device.BeginInvokeOnMainThread(() => App.Current.MainPage.Navigation.PopPopupAsync());
                RemoveIsHasAndPush(PagesHelper.GetSalesPage());

            });
        }
        private ICommand goToStock;
        public ICommand GoToStock
        {
            get => goToStock ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.CurrentTradeNetwork is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
                    return;
                }
                if (ApplicationState.CurrentStore is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
                    return;
                }


                Device.BeginInvokeOnMainThread(() => App.Current.MainPage.Navigation.PopPopupAsync());
                RemoveIsHasAndPush(PagesHelper.GetStockPage());
            });
        }
        private ICommand goToCassa;
        public ICommand GoToCassa
        {
            get => goToCassa ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.CurrentTradeNetwork is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
                    return;
                }
                if (ApplicationState.CurrentStore is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
                    return;
                }

                Device.BeginInvokeOnMainThread(() => App.Current.MainPage.Navigation.PopPopupAsync());
                RemoveIsHasAndPush(PagesHelper.GetCassaPage());
            });
        }




        private void RemoveIsHasAndPush(Page page)
        {
            Device.BeginInvokeOnMainThread(() =>
            {
                var found = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o == page);
                if (found != null)
                {
                    if (found == App.Current.MainPage.Navigation.NavigationStack.LastOrDefault())
                    {
                        return;
                    }

                    App.Current.MainPage.Navigation.RemovePage(found);
                    found.Parent = null;
                }

                App.Current.MainPage.Navigation.PushAsync(page);
            });
        }



        private ICommand goToNotifications;
        public ICommand GoToNotifications
        {
            get => goToNotifications ??= new RelayCommand(async obj =>
            {
                if (ApplicationState.CurrentTradeNetwork is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
                    return;
                }
                if (ApplicationState.CurrentStore is null)
                {
                    await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
                    return;
                }
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushAsync(new Notifications());
            });
        }
        private ICommand goToPayment;
        public ICommand GoToPayment
        {
            get => goToPayment ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushAsync(new PaymentBrowserPage());
            });
        }

        private ICommand tradeNetworks;
        public ICommand TradeNetworks
        {
            get => tradeNetworks ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                await App.Current.MainPage.Navigation.PushAsync(new TradeNetworks());
            });
        }

        private ICommand goToGamification;
        public ICommand GoToGamification
        {
            get => goToGamification ??= new RelayCommand(async obj =>
            {

				if (ApplicationState.CurrentTradeNetwork is null)
				{
					await DisplayAlert("Предупреждение", "Выберите торговую сеть, чтобы сюда попасть", "Ок");
					return;
				}
				if (ApplicationState.CurrentStore is null)
				{
					await DisplayAlert("Предупреждение", "Выберите точку продаж, чтобы сюда попасть", "Ок");
					return;
				}

				await App.Current.MainPage.Navigation.PopPopupAsync();
				//await App.Current.MainPage.Navigation.PushAsync(new GamificationMainPage());


				await GamificationAppActivator.ActivateApp(new GamificationAppActivatorParams
                {
                    CRMApp = App.Current,
					User = Auth.User,
                    AvatarPressedAction = async () =>
                    {
						await App.Current.MainPage.Navigation.PushPopupAsync(new UserMenuPopup());
					},
					BurgerPressedAction = async () =>
					{
						await App.Current.MainPage.Navigation.PushPopupAsync(new MenuPopup());
					},
				});

			});
        }

        private ICommand logout;
        public ICommand Logout
        {
            get => logout ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopPopupAsync();
                App.Current.MainPage.Navigation.InsertPageBefore(new MainPage(), App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault());
                await Auth.Logout();
                ApplicationState.SaveChangesToMemory();

               

            });
        }


        private ICommand closePopup;
        public ICommand ClosePopup
        {
            get => closePopup ??= new RelayCommand(async obj => await App.Current.MainPage.Navigation.PopPopupAsync());
        }




    }
}