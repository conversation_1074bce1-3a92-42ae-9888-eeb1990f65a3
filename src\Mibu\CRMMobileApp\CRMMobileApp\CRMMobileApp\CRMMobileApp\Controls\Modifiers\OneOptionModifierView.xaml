﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="CRMMobileApp.Controls.Modifiers.OneOptionModifierView">
    <ContentView.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>


            <Grid Grid.Row="0">
                <Label 
                    Text="{Binding Source={x:Reference this},Path=Model.Modifier.Title}"
                    FontSize="20"
                    FontAttributes="Bold"
                    TextColor="{StaticResource dark_purple}"
                    FontFamily="TTFirsNeue-Regular"
                    Margin="15,0,0,0"
                    HorizontalOptions="Start"
                    VerticalOptions="Center"/>

                <CheckBox
                    IsVisible="False"
                    x:Name="optionalModifierCheckBox"
                    Margin="0,0,15,0"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    Color="{StaticResource dark_purple}"
                    IsChecked="{Binding Source={x:Reference this},Path=Model.IsSelected,Mode=TwoWay}"/>
            </Grid>


            <StackLayout 
                Grid.Row="1"
                x:Name="modifiersLayout">



            </StackLayout>

        </Grid>
    </ContentView.Content>
</ContentView>