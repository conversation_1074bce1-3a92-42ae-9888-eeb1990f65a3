﻿using CRM.Models.Enums.Settings;
using CRM.Models.Stores.Settings.Tables;
using CRMAdminMoblieApp.Helpers;
using CRMDesktopApp.Abstactions;
using CRMDesktopApp.Controls.Tables;
using CRMDesktopApp.Views.Windows.Halls;
using CRMMobileApp.Abstractions;
using CRMMoblieApiWrapper;
using DevExpress.Xpo.DB;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Table = CRM.Models.Stores.Settings.Tables.Table;

namespace CRMDesktopApp.Views.Pages
{
    /// <summary>
    /// Логика взаимодействия для HallPage.xaml
    /// </summary>
    public partial class HallPage : BasePage
    {
        private bool _isInit = false;
        public HallPage()
        {
            InitializeComponent();

            Loaded += HallPage_Loaded;
        }

        private async void HallPage_Loaded(object sender, RoutedEventArgs e)
        {
            Load();
        }


        public HallPage(Hall hall)
        {
            InitializeComponent();
            CurrentHall = hall;
       
        }

        private Hall currentHall;
        public Hall CurrentHall
        {
            get => currentHall;
            set { currentHall = value; OnPropertyChanged(nameof(CurrentHall)); }
        }

        private ObservableCollection<Hall> halls = new ObservableCollection<Hall>();
        public ObservableCollection<Hall> Halls
        {
            get => halls;
            set { halls = value; OnPropertyChanged(nameof(Halls)); }
        }

        #region Начальная инициализация зала
        private async void Load()
        {

            var rooms = await MobileAPI.BaristaMethods.BaristaCommonMethods.GetHalls(ApplicationState.CurrentDomain, ApplicationState.CurrentStore.Id);

            Halls = new ObservableCollection<Hall>(rooms);
            if (CurrentHall is null)
                CurrentHall = Halls.FirstOrDefault();

            ArrangeTables();

            _isInit = true;
        }
        private void ArrangeTables()
        {
            tablesLayout.Children.Clear();
            if (CurrentHall is null) return;

            foreach (var table in CurrentHall.Tables)
            {
                var control = MakeTable(table);
                tablesLayout.Children.Add(control);
            }
        }
        private AbsTable MakeTable(Table table)
        {
            AbsTable control = null;
            switch (table.Form)
            {
                case TableForm.Square:
                    control = new SquareTable(table);
                    break;
                case TableForm.Circle:
                    control = new RoundTable(table);
                    break;
                default:
                    throw new NotImplementedException();
            }
            control.TableTapped += Control_TableTapped;
            return control;
        }

        #endregion

        private async void Control_TableTapped(object sender, Table e)
        {
            var table = sender as AbsTable;
            if (table.TableOrders.Any())
            {
                new TableCurrentOrdersPopup(table).ShowDialog();
            }
            else
            {
                new GuestsCountPopup(table).ShowDialog();
            }
        }

        private async void onHallSelected(object sender, SelectionChangedEventArgs e)
        {
            var selected = hallsComboBox.SelectedItem as Hall;
            if (selected == null || CurrentHall == null)
                return;

            if (CurrentHall.Id != selected.Id && _isInit)
            {
                NavigationService.GoBack();
                NavigationService.Navigate(new HallPage(selected));         
            }
        }
    }
}
